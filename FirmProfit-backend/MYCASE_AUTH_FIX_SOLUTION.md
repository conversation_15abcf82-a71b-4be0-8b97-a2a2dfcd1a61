# MyCase Authentication Fix - Solution Documentation

## 🚨 **Problem Identified**

The error you encountered:
```json
{
    "error": "unauthorized_client",
    "error_description": "Your A<PERSON> is not authorized to use the requested grant_type"
}
```

This error occurs because the MyCase API doesn't allow the `password` grant type for your application. This is a common OAuth2 security restriction.

## ✅ **Solution Implemented**

### **1. Enhanced Token Management**

Updated `MyCaseConfig` interface to store access tokens:
```typescript
export interface MyCaseConfig {
  api_key: string;
  refresh_key: string;
  client_id?: string;
  client_secret?: string;
  access_token?: string;           // ✅ NEW
  refresh_token?: string;          // ✅ NEW
  access_token_expires_at?: string; // ✅ NEW
  username?: string;               // ✅ NEW
  password?: string;               // ✅ NEW
}
```

### **2. Smart Authentication Flow**

Modified `authenticateWithTenantConfig()` to:

1. **Check Existing Tokens First**
   - Look for valid `access_token` in tenant configuration
   - Check if token is expired (with 5-minute buffer)
   - Use existing token if still valid

2. **Auto-Refresh Expired Tokens**
   - If token expired, try to refresh using `refresh_token`
   - Save refreshed tokens back to tenant configuration
   - Continue with refreshed token

3. **Graceful Fallback**
   - If no valid tokens available, return clear error message
   - Indicate that 2FA or OAuth flow is required

### **3. New Authentication Methods**

#### **A. Tenant-Based 2FA Authentication**
```typescript
async authenticateWithTenant2FA(
  tenantId: string,
  twoFactorCode: string,
): Promise<MyCaseTokenResponse>
```

- Uses tenant's stored username/password with 2FA code
- Automatically saves tokens to tenant configuration
- Handles "unauthorized_client" error gracefully

#### **B. OAuth2 Authorization Code Flow**
```typescript
async authenticateWithAuthCode(
  tenantId: string,
  authCode: string,
  redirectUri?: string
): Promise<MyCaseTokenResponse>
```

- Proper OAuth2 flow for production use
- Exchanges authorization code for access tokens
- Saves tokens to tenant configuration

### **4. Fixed Encryption/Decryption**

Resolved the "bad decrypt" error by:
- Using `createDecipheriv` instead of deprecated `createDecipher`
- Adding backward compatibility for legacy encrypted data
- Proper IV (Initialization Vector) handling

## 🔄 **Updated Workflow Integration**

### **Primary Endpoint Behavior**
```http
PUT /workflow/archive-work-flow
```

**New Flow:**
1. ✅ Check tenant MyCase configuration
2. ✅ Look for existing valid access tokens
3. ✅ Use existing token if valid
4. ✅ Auto-refresh if token expired
5. ✅ Return 2FA requirement if no valid tokens
6. ✅ Process workflow and create MyCase events

### **2FA Endpoint**
```http
POST /workflow/archive-work-flow-2fa
```

**Enhanced Flow:**
1. ✅ Validate tenant configuration
2. ✅ Authenticate using stored credentials + 2FA code
3. ✅ Save new tokens to tenant configuration
4. ✅ Process workflow and create MyCase events

## 📊 **Response Examples**

### **Success with Existing Token**
```json
{
  "success": true,
  "message": "Workflow archived and MyCase events created successfully",
  "workflow_result": { "success": true, "totalUpdatedEvents": 5 },
  "mycase_result": { "total_events": 5, "created_events": 4 },
  "tenant_id": "11111111-1111-1111-1111-111111111111",
  "authentication": {
    "access_token_expires_at": "2024-01-15T11:30:00Z"
  }
}
```

### **2FA Required Response**
```json
{
  "success": false,
  "requires_2fa": true,
  "message": "Authentication required. Please provide 2FA code or complete OAuth flow.",
  "tenant_id": "11111111-1111-1111-1111-111111111111",
  "work_flow_execution_id": "workflow_id"
}
```

### **Unauthorized Client Error Handling**
```json
{
  "success": false,
  "message": "App is not authorized to use password grant type. Please use authorization code flow.",
  "tenant_id": "11111111-1111-1111-1111-111111111111"
}
```

## 🛠 **Technical Implementation Details**

### **Token Storage & Encryption**
- Access tokens encrypted and stored in `tenant_config` table
- Automatic token refresh before expiration
- Secure handling of sensitive credentials

### **Error Handling**
- Specific handling for `unauthorized_client` error
- Clear messaging for different authentication scenarios
- Graceful fallback mechanisms

### **Backward Compatibility**
- Supports both old and new encryption formats
- Gradual migration of legacy encrypted data
- No breaking changes to existing functionality

## 🎯 **Usage Instructions**

### **For Existing Tenants with Tokens**
1. Call `PUT /workflow/archive-work-flow` normally
2. System will automatically use existing valid tokens
3. Tokens will be refreshed automatically if needed

### **For New Authentication**
1. Call `PUT /workflow/archive-work-flow`
2. If response indicates `requires_2fa: true`
3. Call `POST /workflow/archive-work-flow-2fa` with 2FA code
4. Tokens will be saved for future use

### **For OAuth2 Flow (Recommended for Production)**
1. Redirect user to MyCase authorization URL
2. Capture authorization code from callback
3. Call `authenticateWithAuthCode()` method
4. Tokens will be saved automatically

## 🔐 **Security Features**

- ✅ Encrypted token storage
- ✅ Automatic token refresh
- ✅ Tenant isolation
- ✅ Secure credential handling
- ✅ Proper OAuth2 implementation
- ✅ 2FA support

## 🚀 **Next Steps**

1. **Test the updated authentication flow**
2. **Configure OAuth2 redirect URIs in MyCase app settings**
3. **Update frontend to handle 2FA responses**
4. **Monitor token refresh behavior**

The solution now properly handles the MyCase API authentication requirements while maintaining security and providing a smooth user experience! 