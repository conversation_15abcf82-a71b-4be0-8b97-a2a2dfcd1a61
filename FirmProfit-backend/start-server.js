const { exec } = require('child_process');
const path = require('path');

// Function to start a server in a given directory
function startServer(directory) {
    return exec(`npm run start`, { cwd: directory }, (error, stdout, stderr) => {
        if (error) {
            console.error(`Error starting server in ${directory}:`, error);
            return;
        }
        if (stderr) {
            console.error(`stderr from ${directory}:`, stderr);
            return;
        }
        console.log(`stdout from ${directory}:`, stdout);
    });
}

// Function to stop a server (using PID) - works when we store the process IDs
function stopServer(pid) {
    try {
        process.kill(pid);
        console.log(`Server with PID ${pid} stopped.`);
    } catch (error) {
        console.error('Error stopping server:', error);
    }
}

// Start both servers
const apiGatewayPath = path.join(__dirname, 'api-gateway');
const sharedServicePath = path.join(__dirname, 'shared-service');

// Start the servers
const apiGatewayServer = startServer(apiGatewayPath);
const sharedServiceServer = startServer(sharedServicePath);

// Capture the process IDs for termination later
const apiGatewayPid = apiGatewayServer.pid;
const sharedServicePid = sharedServiceServer.pid;

// Handle exit event to stop the servers
process.on('exit', () => {
    console.log('Process exiting, stopping servers...');
    stopServer(apiGatewayPid);
    stopServer(sharedServicePid);
});

// Handle SIGINT (Ctrl + C)
process.on('SIGINT', () => {
    console.log('Received SIGINT, stopping servers...');
    stopServer(apiGatewayPid);
    stopServer(sharedServicePid);
    process.exit();
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
    console.error('Uncaught exception:', err);
    stopServer(apiGatewayPid);
    stopServer(sharedServicePid);
    process.exit(1);
});
