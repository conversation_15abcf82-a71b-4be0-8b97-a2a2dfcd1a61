# Email Attachment Processing with S3 Storage

## Overview
The Gmail service has been enhanced to automatically parse, process, and save email attachments to Amazon S3 cloud storage when processing emails from IMAP servers. This provides scalable, secure, and cost-effective storage for email attachments.

## Key Features

### 1. Automatic S3 Attachment Processing
- Extracts attachments from parsed emails using the `mailparser` library
- Automatically uploads attachments to S3 bucket with structured organization
- Supports all common file types (PDF, images, documents, etc.)
- Generates unique attachment IDs and S3 keys for conflict prevention
- Optional local storage fallback for high availability

### 2. S3 Storage Structure
Attachments are organized in S3 with the following key structure:
```
s3://elite-assets-dev/
└── attachments/
    ├── {tenantId}/
    │   ├── {YYYY}/
    │   │   ├── {MM}/
    │   │   │   ├── {DD}/
    │   │   │   │   ├── {emailId}/
    │   │   │   │   │   ├── {attachmentId}_{sanitized_filename}
    │   │   │   │   │   └── ...
    │   │   │   │   └── ...
    │   │   │   └── ...
    │   │   └── ...
    │   └── ...
    └── fallback/
        ├── {YYYY}/
        │   └── ... (same structure)
        └── ...
```

### 3. Advanced S3 Key Generation
- Sanitizes filenames for S3 compatibility (`a-zA-Z0-9._-`)
- Converts to lowercase for consistency
- Generates unique attachment IDs to prevent conflicts
- Date-based hierarchical organization for efficient browsing
- Tenant isolation for multi-tenant architecture

### 4. Enhanced Database Integration
Attachment information is stored in MongoDB with comprehensive metadata:
- `hasAttachments`: Boolean flag
- `attachmentCount`: Number of attachments
- `attachments[]`: Array of detailed attachment metadata
- `totalAttachmentSize`: Total size of all attachments
- `storageType`: Storage location ('s3', 'local', or 'both')

### 5. Comprehensive Attachment Metadata
Each processed attachment includes:
- **Basic Info**: Original filename, content type, file size
- **S3 Details**: S3 key, bucket name, presigned URLs
- **Security**: MD5 checksum, content ID verification
- **Tracking**: Unique attachment ID, upload timestamp
- **Fallback**: Optional local path for redundancy

## API Endpoints

### Core Attachment Management

#### Get Attachment Statistics
```http
GET /gmail/attachments/stats
```
Returns comprehensive statistics about processed attachments including total count, size, and averages.

#### Get Emails with Attachments
```http
GET /gmail/attachments/emails?limit=10
```
Retrieves recent emails that contain attachments.

#### Cleanup Old Attachments
```http
POST /gmail/attachments/cleanup?days=30
```
Removes attachment files older than specified days (default: 30).

### S3-Specific Endpoints

#### Get S3 Configuration Info
```http
GET /gmail/attachments/s3/info
```
Returns S3 bucket configuration, path structure, and supported operations.

#### Generate Attachment Download URL
```http
GET /gmail/attachments/s3/download/:emailId/:attachmentId?expiresIn=3600
```
Generates a secure presigned URL for downloading a specific attachment from S3.

#### Delete Email Attachments
```http
DELETE /gmail/attachments/s3/:emailId
```
Removes all attachments associated with a specific email from S3 and local storage.

### Legacy Support

#### Set Attachment Base Path (Local Fallback)
```http
POST /gmail/attachments/base-path
Content-Type: application/json
{
  "basePath": "/custom/path/to/attachments"
}
```
Updates the base directory for local fallback storage (when enabled).

## Configuration

### S3 Settings
- **Bucket**: `elite-assets-dev` (configurable via `AWS_BUCKET_NAME_s3`)
- **Key Structure**: `attachments/{tenant}/{YYYY}/{MM}/{DD}/{emailId}/{attachmentId}_{filename}`
- **Region**: Configurable via `AWS_REGION_s3` (default: us-east-1)
- **ACL**: `bucket-owner-full-control` for security

### Required Environment Variables
```bash
# AWS S3 Configuration
AWS_BUCKET_NAME_s3=elite-assets-dev
AWS_REGION_s3=us-east-1
AWS_ACCESS_KEY_ID_s3=your_access_key
AWS_SECRET_ACCESS_KEY_s3=your_secret_key

# Optional Fallback Configuration
ATTACHMENT_FALLBACK_LOCAL=true  # Enable local storage fallback
```

### Tenant Integration
The attachment processing fully integrates with the existing tenant configuration system and IMAP settings, ensuring proper data isolation and access control.

## Error Handling & Reliability
- **Graceful Failure**: Email processing continues even if S3 upload fails
- **Local Fallback**: Optional automatic fallback to local storage
- **Comprehensive Logging**: Detailed logs for troubleshooting and monitoring
- **Duplicate Prevention**: Unique attachment IDs and hash-based deduplication
- **Retry Logic**: Built-in retry mechanisms for transient S3 failures
- **Data Integrity**: MD5 checksum validation for uploaded files

## File Type Support
Supports all file types with intelligent handling:
- **Documents**: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX
- **Images**: JPEG, PNG, GIF, BMP, TIFF, SVG
- **Archives**: ZIP, RAR, 7Z, TAR, GZ
- **Media**: MP4, AVI, MP3, WAV, etc.
- **Text**: TXT, CSV, XML, JSON, etc.
- **Auto-detection**: MIME type verification and extension mapping

## Enhanced Security Features
- **S3 Bucket Policies**: Secure access control with proper IAM roles
- **Presigned URLs**: Time-limited access for downloads (default: 1 hour)
- **Filename Sanitization**: S3-compatible key generation
- **Content Validation**: MIME type verification and checksum validation
- **Tenant Isolation**: Complete data separation by tenant
- **Encryption**: S3 server-side encryption support

## Monitoring and Maintenance

### Statistics Tracking
- Total attachments processed
- Storage space usage
- Processing success rates
- Per-tenant attachment metrics

### Cleanup Utilities
- Automated old file cleanup
- Error tracking and reporting
- Storage optimization tools

## Usage Example
When an email with attachments is processed:

1. **Email Parsing**: Email is parsed using `mailparser` library
2. **Attachment Extraction**: All attachments are identified and extracted
3. **ID Generation**: Unique attachment IDs are generated for each file
4. **S3 Upload**: Files are uploaded to S3 with structured keys
5. **Presigned URLs**: Temporary download URLs are generated (24-hour expiry)
6. **Database Storage**: Complete metadata is stored in MongoDB
7. **Fallback Handling**: Local storage used if S3 upload fails (if enabled)
8. **Email Processing**: Email processing continues seamlessly

**Example S3 Key**: `attachments/tenant123/2024/07/16/abc123hash/att_1_1721123456789_invoice.pdf`

The system ensures that attachment processing is completely non-blocking and provides enterprise-grade reliability and monitoring.

## Performance & Scalability
- **Concurrent Uploads**: Multiple attachments uploaded in parallel
- **Efficient Storage**: S3's virtually unlimited storage capacity
- **Global CDN**: Fast access through CloudFront integration potential
- **Cost Optimization**: S3 intelligent tiering for long-term storage
- **Bandwidth Efficiency**: Direct S3 uploads reduce server load

## Future Enhancements
- **Advanced Security**: Virus scanning integration with AWS Lambda
- **File Processing**: Automatic thumbnail generation for images
- **ML Integration**: Document classification and text extraction
- **Lifecycle Management**: Automated archival to Glacier for cost optimization
- **CDN Integration**: CloudFront distribution for global access
- **Compression**: Automatic file compression for large attachments
- **Preview Generation**: PDF and document preview capabilities 