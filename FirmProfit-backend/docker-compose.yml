version: '3.8'

services:
  # Shared Service (gRPC Microservice)
  shared-service:
    build:
      context: ./
      dockerfile: ./shared-service/Dockerfile
    container_name: firmprofit-shared-service
    env_file:
      - ./shared-service/.env
    ports:
      - "3001:3001"
      - "3002:3002"
    networks:
      - firmprofit-network
    restart: unless-stopped
    # Health check is defined in Dockerfile

  # API Gateway (Frontend Service)
  api-gateway:
    build:
      context: ./
      dockerfile: ./api-gateway/Dockerfile
    container_name: firmprofit-api-gateway
    depends_on:
      shared-service:
        condition: service_started
    env_file:
      - ./api-gateway/.env
    ports:
      - "3000:3000"
      - "3004:3004"
    networks:
      - firmprofit-network
    restart: unless-stopped
    # Health check is defined in Dockerfile

  # Connectors Service (Gmail IMAP Integration)
  connectors:
    build:
      context: ./
      dockerfile: ./connectors/Dockerfile
    container_name: firmprofit-connectors
    depends_on:
      api-gateway:
        condition: service_started
    env_file:
      - ./connectors/.env
    ports:
      - "3003:3003"
    networks:
      - firmprofit-network
    restart: unless-stopped
    # Health check is defined in Dockerfile

  # # Connectors Service (Gmail IMAP Integration)
  # connectors:
  #   build:
  #     context: ./connectors
  #     dockerfile: Dockerfile
  #   container_name: firmprofit-connectors
  #   depends_on:
  #     api-gateway:
  #       condition: service_started
  #   env_file:
  #     - ./connectors/.env
  #   ports:
  #     - "3003:3003"
  #   networks:
  #     - firmprofit-network
  #   restart: unless-stopped
    # Health check is defined in Dockerfile

networks:
  firmprofit-network:
    driver: bridge