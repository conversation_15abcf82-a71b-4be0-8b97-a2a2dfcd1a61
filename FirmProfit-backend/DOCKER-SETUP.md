# Docker Setup for FirmProfit Backend

This document explains how to run the FirmProfit backend services using Docker.

## Prerequisites

- <PERSON><PERSON> and <PERSON>er Compose installed on your machine
- Git to clone the repository

## Services Overview

The system consists of three main components:

1. **PostgreSQL Database**: Stores all application data
2. **Shared Service**: gRPC microservice that handles authentication and user management
3. **API Gateway**: REST API that serves client requests and communicates with the Shared Service

## Setup Instructions

1. **Clone the repository**:

```bash
git clone <repository-url>
cd FirmProfit-backend
```

2. **Set up environment files**:

The repository contains example Docker environment files. Make sure both environment files exist:
- `api-gateway/.env.docker`
- `shared-service/.env.docker`

3. **Build and start the containers**:

```bash
docker-compose up -d
```

This will build the Docker images and start all services in detached mode.

4. **View logs**:

```bash
# View logs from all services
docker-compose logs -f

# View logs from a specific service
docker-compose logs -f api-gateway
docker-compose logs -f shared-service
```

5. **API Access**:

Once all services are running, the API Gateway will be accessible at:
```
http://localhost:3000
```

## Common Operations

### Stopping the services:

```bash
docker-compose down
```

### Stopping and removing volumes (will delete database data):

```bash
docker-compose down -v
```

### Rebuilding services after code changes:

```bash
docker-compose build
docker-compose up -d
```

## Troubleshooting

### Database connection issues:
- Make sure the PostgreSQL container is running: `docker ps`
- Check the logs for connection errors: `docker-compose logs postgres`

### gRPC connection issues:
- Check if shared-service is running: `docker ps`
- Verify logs for connection issues: `docker-compose logs shared-service`
- Ensure API Gateway environment variables are correctly pointing to shared-service

### API Gateway issues:
- Check logs for errors: `docker-compose logs api-gateway`
- Verify the API Gateway can connect to both PostgreSQL and shared-service

### Container cannot start:
- Check for port conflicts on your host machine
- Ensure required ports (3000, 3001, 5432) are available 