# ConfigGroup Collection Implementation

This document describes the implementation of the new `configGroup` collection to manage templateGroupId configuration dynamically.

## Overview

Previously, templateGroupId was hardcoded in the workflow service. Now it's fetched from a new `configGroup` collection that allows for dynamic configuration of group IDs for different workflow types.

## Changes Made

### 1. New Entity: ConfigGroup

**File**: `src/workflow/entities/config-group.entity.ts`

Created a new MongoDB schema with the following fields:
- `name`: Configuration name
- `description`: Description of the configuration
- `newcourtnoticGroupid`: ObjectId for court notice group assignments
- `followupgroupid`: ObjectId for followup group assignments
- `is_active`: Boolean flag for active status
- `is_deleted`: Boolean flag for soft deletion

### 2. Updated Workflow Module

**File**: `src/workflow/workflow.module.ts`

- Added import for `ConfigGroup` and `ConfigGroupSchema`
- Registered the new schema in the MongooseModule.forFeature array

### 3. Updated Workflow Service

**File**: `src/workflow/workflow.service.ts`

- Added import for `ConfigGroup` entity
- Injected `ConfigGroup` model in the constructor
- Modified the court notice template creation logic (around line 3636-3646) to:
  - Fetch configGroup from the database
  - Use `newcourtnoticGroupid` from configGroup for court notice templates
  - Fall back to the original hardcoded value if no configGroup is found

### 4. Seed Data

**File**: `src/seeds/config-group.seed.ts`

Created a seed file to populate the configGroup collection with initial data.

## Usage

### Setting up ConfigGroup Data

1. Run the seed to create initial configGroup data:
```bash
# You can modify the seed file to set the correct ObjectIds for your UserGroups
# Then run the seed (implementation may vary based on your seeding strategy)
```

2. Alternatively, manually insert data into the configGroup collection:
```javascript
// Example MongoDB insert
db.configGroup.insertOne({
  name: "Court Notice Configuration",
  description: "Configuration for court notice and followup group assignments",
  newcourtnoticGroupid: ObjectId("your_court_notice_group_id"),
  followupgroupid: ObjectId("your_followup_group_id"),
  is_active: true,
  is_deleted: false,
  createdAt: new Date(),
  updatedAt: new Date()
});
```

### How It Works

When creating a court notice template in the workflow service:
1. The system fetches the active configGroup document
2. Uses the `newcourtnoticGroupid` as the templateGroupId for the new template
3. Falls back to the original hardcoded value if no configGroup is found

### Future Enhancements

- The `followupgroupid` field is available for use in followup workflows
- Additional configuration fields can be added to the ConfigGroup schema as needed
- Multiple configGroup documents can be created for different workflow types

## Database Schema

```typescript
interface ConfigGroup {
  _id: ObjectId;
  name: string;
  description: string;
  newcourtnoticGroupid: ObjectId;
  followupgroupid: ObjectId;
  is_active: boolean;
  is_deleted: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

## Testing

The implementation has been tested by:
1. Ensuring all TypeScript compilation succeeds
2. Verifying no linting errors
3. Confirming all imports and dependencies are properly configured

## Notes

- The implementation maintains backward compatibility with a fallback to the original hardcoded value
- The system is designed to be extensible for additional workflow types
- All changes follow the existing code patterns and conventions used in the project
