import 'dotenv/config';
import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { ValidationPipe } from '@nestjs/common';
import { AppModule } from './app.module';
import { HttpExceptionFilter } from './exceptions/http-exception.filter';
import { GrpcExceptionFilter } from './exceptions/grpc-exception.filter';
import { join } from 'path';

async function bootstrap() {
  const grpcUrl = process.env.GRPC_URL || '0.0.0.0:3001';

  const app = await NestFactory.createMicroservice<MicroserviceOptions>(
    AppModule,
    {
      transport: Transport.GRPC,
      options: {
        package: ['auth', 'user', 'workflowapi', 'workflowshared'],
        protoPath: [
          join(__dirname, '../proto/auth.proto'),
          join(__dirname, '../proto/user.proto'),
          join(__dirname, '../proto/workflow-api.proto'),
          join(__dirname, '../proto/workflow-shared.proto'),
        ],
        url: grpcUrl,
        loader: {
          keepCase: true,
          longs: String,
          enums: String,
          defaults: true,
          oneofs: true,
        },
        maxSendMessageLength: 1024 * 1024 * 50, // 50MB
        maxReceiveMessageLength: 1024 * 1024 * 50, // 50MB
      },
    },
  );

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );

  console.log(`gRPC Microservice is listening on: ${grpcUrl}`);
  app.useGlobalFilters(new HttpExceptionFilter(), new GrpcExceptionFilter());

  app.listen();
}
bootstrap();
