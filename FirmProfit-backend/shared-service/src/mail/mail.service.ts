import { Injectable, Logger } from '@nestjs/common';
import { promises as fs } from 'fs';
import * as path from 'path';
import * as nodemailer from 'nodemailer';
import { ConfigService } from '@nestjs/config';

interface EmailOptions {
  template: string;
  recipients: string[];
  data: Record<string, any>;
  subjectData: string;
  attachments?: any[];
}

@Injectable()
export class AwsSesEmailService {
  private logger = new Logger(AwsSesEmailService.name);
  private transporter: nodemailer.Transporter;
  private sourceEmail: string;

  constructor(private readonly configService: ConfigService) {
    this.sourceEmail = this.configService.get('mail.sourceEmail');

    this.transporter = nodemailer.createTransport({
      host: `email-smtp.${this.configService.get('mail.awsRegion')}.amazonaws.com`,
      port: 587, // 587 for TLS
      secure: false,
      auth: {
        user: this.configService.get('mail.awsAccessKeyId'),
        pass: this.configService.get('mail.awsSecretAccessKey'),
      },
    });
  }

  async sendEmail(options: EmailOptions): Promise<void> {
    try {
      const templatePath = path.resolve(
        process.cwd(),
        `src/shared/templates/${options.template}.html`,
      );
      let htmlContent = await fs.readFile(templatePath, 'utf8');

      options.data.year = new Date().getFullYear();
      for (const [key, value] of Object.entries(options.data)) {
        htmlContent = htmlContent.replace(
          new RegExp(`##${key.toUpperCase()}`, 'g'),
          String(value),
        );
      }

      const mailOptions: nodemailer.SendMailOptions = {
        from: `Growexx Team <${this.sourceEmail}>`,
        to: options.recipients.join(','),
        subject: options.subjectData,
        html: htmlContent,
        attachments: options.attachments || [],
      };

      const info = await this.transporter.sendMail(mailOptions);
      this.logger.log(
        `Email sent to ${options.recipients.join(',')}. Info: ${JSON.stringify(info)}`,
      );
    } catch (error) {
      this.logger.error('Failed to send email', error);
      throw error;
    }
  }
}
