import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UsersModule } from './users/users.module';
import { AuthModule } from './auth/auth.module';
import MailConfiguration from './config/mail.config';
import DatabaseConfiguration from './config/db.config';
import { MongooseModule } from '@nestjs/mongoose';
import { User, UserMfa } from '@shared/database';
import { WorkflowModule } from './workflow/workflow.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [MailConfiguration, DatabaseConfiguration],
      isGlobal: true,
      envFilePath: '.env',
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const dbConfig = configService.get('db').postgres; // Access namespace
        console.log('🚀 ~ dbConfig:', dbConfig);
        return {
          type: 'postgres',
          host: dbConfig.host,
          port: dbConfig.port,
          username: dbConfig.username,
          password: dbConfig.password,
          database: dbConfig.database,
          entities: [User, UserMfa],
          synchronize: false,
          schema: 'stephen_schema',
        };
      },
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        const mongoConfig = configService.get('db').mongo;

        console.log('✅ Connecting to MongoDB with URI:', mongoConfig?.uri);
        console.log('✅ Using Database:', mongoConfig?.dbName);

        if (!mongoConfig?.uri) {
          throw new Error(
            '❌ MONGO_URI is undefined! Check your environment variables.',
          );
        }

        return {
          uri: mongoConfig.uri,
          dbName: mongoConfig.dbName,
        };
      },
    }),
    WorkflowModule,
    UsersModule,
    AuthModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
