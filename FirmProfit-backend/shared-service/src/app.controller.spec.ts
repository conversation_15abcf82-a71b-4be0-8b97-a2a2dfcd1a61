import { Test, TestingModule } from '@nestjs/testing';
import { AppController } from './app.controller';
import { AppService } from './app.service';

describe('AppController', () => {
  let appController: AppController;
  let appService: AppService;

  beforeEach(async () => {
    const app: TestingModule = await Test.createTestingModule({
      controllers: [AppController],
      providers: [AppService],
    }).compile();

    appController = app.get<AppController>(AppController);
    appService = app.get<AppService>(AppService);
  });

  describe('root', () => {
    it('should return "Hello World!"', () => {
      expect(appController.getHello()).toBe('Hello World!');
    });
  });

  describe('handleLoginSuccess', () => {
    it('should process login success event via message pattern', () => {
      const loginData = { userId: 1, timestamp: new Date() };

      // Spy on service method
      jest.spyOn(appService, 'handleLoginSuccess');

      // Call the handler
      const result = appController.handleLoginSuccess(loginData);

      // Check if service method was called with correct data
      expect(appService.handleLoginSuccess).toHaveBeenCalledWith(loginData);

      // Verify the result
      expect(result).toEqual({
        success: true,
        message: 'Login event processed',
      });
    });
  });
});
