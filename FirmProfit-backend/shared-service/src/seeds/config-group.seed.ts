import mongoose from 'mongoose';
import { connect, model, Types } from 'mongoose';
import { ConfigGroupSchema } from '../workflow/entities/config-group.entity';

export const seedConfigGroup = async () => {
  try {
    const mongoUri = process.env.MONGO_URI || 'mongodb://localhost:27017';
    const dbName = process.env.MONGO_DB_NAME || 'firmprofit';
    
    console.log(`Connecting to MongoDB: ${mongoUri}`);
    await mongoose.connect(mongoUri, {
      dbName: dbName,
    });

    const ConfigGroupModel = model('ConfigGroup', ConfigGroupSchema);

    const configGroups = [
      {
        name: 'Court Notice Configuration',
        description: 'Configuration for court notice and followup group assignments',
        newcourtnoticGroupid: new Types.ObjectId('6877420fd4928f6a37ba1b95'), // UserGroup ID for court notices
        followupgroupid: new Types.ObjectId('6877420fd4928f6a37ba1b95'), // UserGroup ID for followups
        is_active: true,
        is_deleted: false,
      },
    ];

    await ConfigGroupModel.deleteMany(); // Optional: clean previous entries
    await ConfigGroupModel.insertMany(configGroups);

    console.log('✅ ConfigGroup seeded successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Failed to seed configGroup:', error);
    process.exit(1);
  }
};
