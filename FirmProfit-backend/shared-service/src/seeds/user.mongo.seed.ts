import mongoose from 'mongoose';
import * as dotenv from 'dotenv';
import { UserSchema } from 'src/workflow/entities/user.entity';

dotenv.config();

const User = mongoose.model('User', UserSchema, 'users'); // explicitly use "users" collection

export const seedMongoUsers = async () => {
  await mongoose.connect(process.env.MONGO_URI || '', {
    dbName: process.env.MONGO_DB_NAME,
  });

  console.log('MongoDB connected');

  const users = [
    {
      email: '<EMAIL>',
      role_id: 1,
      is_active: true,
      is_deleted: false,
    },
    {
      email: '<EMAIL>',
      role_id: 2,
      is_active: true,
      is_deleted: false,
    },
    {
      email: '<EMAIL>',
      role_id: 3,
      is_active: false,
      is_deleted: true,
    },
  ];

  await User.insertMany(users);
  console.log('MongoDB user data seeded successfully!');

  await mongoose.disconnect();
};
