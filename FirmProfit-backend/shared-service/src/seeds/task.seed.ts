import mongoose from 'mongoose';
import * as dotenv from 'dotenv';
import { TaskSchema } from 'src/workflow/entities/task.entity';
import { Task_type } from 'src/workflow/enum/template-status.enum';

dotenv.config();

const Task = mongoose.model('Task', TaskSchema, 'task');

export const seedMongoTasks = async () => {
  await mongoose.connect(process.env.MONGO_URI || '', {
    dbName: process.env.MONGO_DB_NAME,
  });

  console.log('MongoDB connected');

  const defaultTasks = [
    {
      task_id: 1,
      name: 'Initialize Template',
      type: Task_type.CONNECTOR,
      previous_node: 'start',
      next_node: 'node_1',
    },
    {
      task_id: 2,
      name: 'Verify Inputs',
      type: Task_type.CONNECTOR,
      previous_node: 'node_1',
      next_node: 'node_2',
    },
    {
      task_id: 3,
      name: 'Calculate Metrics',
      type: Task_type.FORM_COMPONENT,
      previous_node: 'node_2',
      next_node: 'node_3',
    },
    {
      task_id: 4,
      name: 'Generate Report',
      type: Task_type.FORM_COMPONENT,
      previous_node: 'node_3',
      next_node: 'node_4',
    },
    {
      task_id: 5,
      name: 'Send Notification',
      type: Task_type.FORM_COMPONENT,
      previous_node: 'node_4',
      next_node: 'node_5',
    },
    {
      task_id: 6,
      name: 'Check Compliance',
      type: Task_type.FORM_COMPONENT,
      previous_node: 'node_5',
      next_node: 'node_6',
    },
    {
      task_id: 7,
      name: 'Audit Logs',
      type: Task_type.FORM_COMPONENT,
      previous_node: 'node_6',
      next_node: 'node_7',
    },
    {
      task_id: 8,
      name: 'Approval Stage',
      type: Task_type.FORM_COMPONENT,
      previous_node: 'node_7',
      next_node: 'node_8',
    },
    {
      task_id: 9,
      name: 'Final Review',
      type: Task_type.FORM_COMPONENT,
      previous_node: 'node_8',
      next_node: 'node_9',
    },
    {
      task_id: 10,
      name: 'Complete Process',
      type: Task_type.FORM_COMPONENT,
      previous_node: 'node_9',
      next_node: 'end',
    },
  ];

  await Task.insertMany(defaultTasks);
  console.log('MongoDB task data seeded successfully!');

  await mongoose.disconnect();
};
