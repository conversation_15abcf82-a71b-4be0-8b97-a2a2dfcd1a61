import mongoose from 'mongoose';
import * as dotenv from 'dotenv';
import { UserRoleSchema } from 'src/workflow/entities/user-role.entity';

dotenv.config();

const UserRole = mongoose.model('UserRole', UserRoleSchema, 'userrole');

export const seedUserRoles = async () => {
    await mongoose.connect(process.env.MONGO_URI || '', {
        dbName: process.env.MONGO_DB_NAME,
    });

    console.log('MongoDB connected for User Roles');

    const userrole = [
          {
    "roleId": "6877420fd4928f6a37ba1b95",
    "userId": "67f644975909b1b40b527650",
    "status": true,
    "createdAT": "2025-07-01T10:30:00Z",
    "updatedAT": "2025-07-15T14:00:00Z",
  }
        
    ];

    await UserRole.deleteMany(); 
    await UserRole.insertMany(userrole);

    await mongoose.disconnect();
}; 