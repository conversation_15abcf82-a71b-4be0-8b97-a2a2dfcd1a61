import mongoose from 'mongoose';
import * as dotenv from 'dotenv';
import { TaskExecutionSchema } from 'src/workflow/entities/task-execution.entity';
import { Task_status } from 'src/workflow/enum/template-status.enum';

dotenv.config();

const TaskExecution = mongoose.model(
  'TaskExecution',
  TaskExecutionSchema,
  'task_execution',
);
const WorkflowExecution = mongoose.model(
  'WorkflowExecution',
  new mongoose.Schema({}, { strict: false }),
  'work_flow_execution',
);
const Task = mongoose.model(
  'Task',
  new mongoose.Schema({}, { strict: false }),
  'task',
);
const User = mongoose.model(
  'User',
  new mongoose.Schema({}, { strict: false }),
  'users',
);
const Template = mongoose.model(
  'Template',
  new mongoose.Schema({}, { strict: false }),
  'template',
);

export const seedTaskExecutions = async () => {
  await mongoose.connect(process.env.MONGO_URI || '', {
    dbName: process.env.MONGO_DB_NAME,
  });

  console.log('MongoDB connected for TaskExecution');

  const executions = await WorkflowExecution.find().limit(10);
  const tasks = await Task.find().limit(10);
  const users = await User.find().limit(5);
  const templates = await Template.find().limit(5);

  if (
    !executions.length ||
    !tasks.length ||
    users.length < 2 ||
    !templates.length
  ) {
    console.error(
      '❌ Not enough data in reference collections to seed task executions',
    );
    await mongoose.disconnect();
    return;
  }

  const dummyTaskExecutions = Array.from({ length: 10 }, (_, i) => {
    const now = new Date();
    const end = new Date(now.getTime() + 60 * 60 * 1000); // +1hr

    return {
      workflow_execution_id: executions[i % executions.length]._id,
      task_id: tasks[i % tasks.length]._id,
      start_date: now,
      end_date: end,
      task_status:
        Object.values(Task_status)[i % Object.values(Task_status).length],
      last_activity: now,
      assigns: [],
      assign_by: users[(i + 1) % users.length]._id,
      notes: `Task Execution Note ${i + 1}`,
      template_id: templates[i % templates.length]._id,
      is_active: true,
      is_deleted: false,
    };
  });

  await TaskExecution.deleteMany(); // Optional: Clean previous data
  await TaskExecution.insertMany(dummyTaskExecutions);

  console.log('✅ TaskExecution records seeded!');
  await mongoose.disconnect();
};
