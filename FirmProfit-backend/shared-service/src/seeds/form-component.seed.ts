import mongoose from 'mongoose';
import * as dotenv from 'dotenv';
import { FormComponentSchema } from 'src/workflow/entities/form-component.entity';

dotenv.config();

const FormComponent = mongoose.model(
  'FormComponent',
  FormComponentSchema,
  'form_component',
); // explicitly use "users" collection

export const seedMongoFormField = async () => {
  await mongoose.connect(process.env.MONGO_URI || '', {
    dbName: process.env.MONGO_DB_NAME,
  });

  console.log('MongoDB connected');

  const fields = [
    {
      type: 'text',
      placeholder: 'Enter full name',
      form_field_id: 'client-full-name',
    },
    {
      type: 'text',
      placeholder: 'First name',
      form_field_id: 'client-first-name',
    },
    {
      type: 'text',
      placeholder: 'Middle name',
      form_field_id: 'client-middle-name',
    },
    {
      type: 'text',
      placeholder: 'Last name',
      form_field_id: 'client-last-name',
    },
  ];

  await FormComponent.insertMany(fields);
  console.log('MongoDB form component data seeded successfully!');

  await mongoose.disconnect();
};
