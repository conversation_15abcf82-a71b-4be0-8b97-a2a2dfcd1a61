import mongoose, { model, Types } from 'mongoose';
import { CategorySchema } from 'src/workflow/entities/category.entity';
import { TaskSchema } from 'src/workflow/entities/task.entity';
import { TemplateSchema } from 'src/workflow/entities/template.entity';
import { Template_status } from 'src/workflow/enum/template-status.enum';

export const seedTemplates = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI || '', {
      dbName: process.env.MONGO_DB_NAME,
    });

    const TemplateModel = model('Template', TemplateSchema);
    const CategoryModel = model('Category', CategorySchema);
    const TaskModel = model('Task', TaskSchema);

    // Fetch categories from DB
    const categories = await CategoryModel.find({});
    if (categories.length === 0) throw new Error('No categories found');

    // Fetch tasks from DB
    const tasks = await TaskModel.find({});
    if (tasks.length === 0) throw new Error('No tasks found');

    const templates = Array.from({ length: 10 }, (_, i) => {
      const category = categories[i % categories.length];

      return {
        name: `Template ${i + 1}`,
        description: `This is a description for template ${i + 1}`,
        template_id: i + 1,
        category_id: category._id,
        parent_id: null,
        version: 1,
        status: Template_status.DRAFT,
        is_active: true,
        is_deleted: false,
      };
    });

    await TemplateModel.deleteMany(); // optional: clean up existing data
    await TemplateModel.insertMany(templates);

    console.log('✅ Template data seeded successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error seeding template data:', error);
    process.exit(1);
  }
};

seedTemplates();
