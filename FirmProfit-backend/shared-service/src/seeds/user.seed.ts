import { DataSource } from 'typeorm';
import { User } from '@shared/database';

export const seedUsers = async (dataSource: DataSource): Promise<void> => {
  const userRepository = dataSource.getRepository(User);

  const count = await userRepository.count();
  if (count > 0) {
    console.log('User already seeded');
    return;
  }

  const users = [
    {
      email: '<EMAIL>',
      password: '$2b$10$XF0TPFNhkWvqCOBZLac/heGRCaUg2X0pPc75vy19FPZTWm4i6xN62',
      roleId: 1,
      isActive: true,
      isDeleted: false,
      lastInCorrectLogin: null,
    },
    {
      email: '<EMAIL>',
      password: '$2b$10$XF0TPFNhkWvqCOBZLac/heGRCaUg2X0pPc75vy19FPZTWm4i6xN62',
      roleId: 2,
      isActive: true,
      isDeleted: false,
      lastInCorrectLogin: null,
    },
    {
      email: '<EMAIL>',
      password: '$2b$10$XF0TPFNhkWvqCOBZLac/heGRCaUg2X0pPc75vy19FPZTWm4i6xN62',
      roleId: 3,
      isActive: true,
      isDeleted: false,
      lastInCorrectLogin: null,
    },
    {
      email: '<EMAIL>',
      password: '$2b$10$XF0TPFNhkWvqCOBZLac/heGRCaUg2X0pPc75vy19FPZTWm4i6xN62',
      roleId: 3,
      isActive: false,
      isDeleted: true,
      lastInCorrectLogin: null,
    },
  ];

  for (const user of users) {
    const existing = await userRepository.findOne({
      where: { email: user.email },
    });

    if (!existing) {
      await userRepository.save(user);
      console.log(`User ${user.email} seeded successfully!`);
    } else {
      console.log(`User ${user.email} already exists, skipping...`);
    }
  }
};
