import mongoose from 'mongoose';
import { connect, model } from 'mongoose';
import { CategorySchema } from 'src/workflow/entities/category.entity';

export const seedCategories = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI || '', {
      dbName: process.env.MONGO_DB_NAME,
    });

    const CategoryModel = model('Category', CategorySchema);

    const categories = Array.from({ length: 10 }, (_, i) => ({
      name: `Category ${i + 1}`,
      is_active: true,
      is_deleted: false,
    }));

    await CategoryModel.deleteMany(); // Optional: clean previous entries
    await CategoryModel.insertMany(categories);

    console.log('✅ Categories seeded successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Failed to seed categories:', error);
    process.exit(1);
  }
};
