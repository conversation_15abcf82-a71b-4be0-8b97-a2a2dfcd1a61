import mongoose from 'mongoose';
import * as dotenv from 'dotenv';
import { RoleSchema } from 'src/workflow/entities/roles.entity';

dotenv.config();

const Role = mongoose.model('Role', RoleSchema, 'roles');

export const seedRoles = async () => {
    await mongoose.connect(process.env.MONGO_URI || '', {
        dbName: process.env.MONGO_DB_NAME,
    });

    console.log('MongoDB connected for Roles');

    const roles = [
        {
            id: 1,
            name: 'Paralegal',
            type: true, // true indicates this is a group role
            is_active: true,
            is_deleted: false,
        },
        {
            id: 2,
            name: 'Attorney',
            type: true, // true indicates this is a group role
            is_active: true,
            is_deleted: false,
        },
        {
            id: 3,
            name: 'Intake',
            type: true, // true indicates this is a group role
            is_active: true,
            is_deleted: false,
        },
        {
            id: 4,
            name: 'Billing',
            type: true, // true indicates this is a group role
            is_active: true,
            is_deleted: false,
        },
        
    ];

    await Role.deleteMany(); // Clean up existing data
    await Role.insertMany(roles);
    console.log('✅ Roles seeded successfully!');
    console.log('📋 Added roles:', roles.map(r => r.name).join(', '));

    await mongoose.disconnect();
}; 