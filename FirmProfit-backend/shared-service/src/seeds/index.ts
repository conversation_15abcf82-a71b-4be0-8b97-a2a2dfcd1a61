import 'reflect-metadata'; // Make sure this is imported if using TypeORM decorators
import { DataSource } from 'typeorm';
import { seedUsers } from './user.seed';
import { seedMongoUsers } from './user.mongo.seed';
import { User } from '@shared/database';
import { UserMfa } from '@shared/database';
import { seedMongoTasks } from './task.seed';
import { seedCategories } from './category.seed';
import { seedTemplates } from './template.seed';
import { seedWorkflowExecutions } from './workflow-execution.seed';
import { seedTaskExecutions } from './task-execution.seed';
import { seedRoles } from './roles.seed';
import {seedUserRoles} from './user-role.seed';

// Initialize your DataSource
const dataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT),
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  entities: [User, UserMfa],
  synchronize: false, // Always keep this false in production
  logging: true, // Enable to check SQL logs
  schema: 'stephen_schema',
  maxQueryExecutionTime: 10000,
  cache: {
    duration: 1000,
  },
});

// Run the seed
export const runSeeds = async (): Promise<void> => {
  try {
    await dataSource.initialize(); // Initialize the data source
    console.log('DataSource initialized successfully');

    // await seedUsers(dataSource); // Pass the dataSource to the seed function
    // await seedMongoUsers(); // Pass the dataSource to the seed function
    // await seedMongoTasks(); // Pass the dataSource to the seed function
    // await seedCategories(); // Pass the dataSource to the seed function
    // await seedTemplates(); // Pass the dataSource to the seed function
    // await seedWorkflowExecutions();
    await seedTaskExecutions();
    await seedRoles();
    await seedUserRoles();

    console.log('All seeds completed successfully');
  } catch (error) {
    console.error('Error during DataSource initialization or seeding:', error);
  } finally {
    await dataSource.destroy(); // Close the connection
  }
};

// Run the seeds
runSeeds();
