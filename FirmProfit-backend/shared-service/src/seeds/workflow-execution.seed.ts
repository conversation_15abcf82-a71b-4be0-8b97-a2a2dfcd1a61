import mongoose from 'mongoose';
import * as dotenv from 'dotenv';
import { WorkflowExecutionSchema } from 'src/workflow/entities/workflow-execution.entity';
import { Work_flow_execution_status } from 'src/workflow/enum/template-status.enum';

dotenv.config();

const WorkflowExecution = mongoose.model(
  'WorkflowExecution',
  WorkflowExecutionSchema,
  'work_flow_execution',
);

// You may need Template and User schemas minimally to reference
const Template = mongoose.model(
  'Template',
  new mongoose.Schema({}, { strict: false }),
  'template',
);
const User = mongoose.model(
  'User',
  new mongoose.Schema({}, { strict: false }),
  'users',
);

export const seedWorkflowExecutions = async () => {
  await mongoose.connect(process.env.MONGO_URI || '', {
    dbName: process.env.MONGO_DB_NAME,
  });

  console.log('MongoDB connected for WorkflowExecution');

  const templates = await Template.find().limit(10);
  console.log('🚀 ~ seedWorkflowExecutions ~ templates:', templates);
  const users = await User.find().limit(10);
  console.log('🚀 ~ seedWorkflowExecutions ~ users:', users);

  if (templates.length === 0 || users.length < 3) {
    console.error(
      '❌ Not enough templates or users to seed workflow executions.',
    );
    await mongoose.disconnect();
    return;
  }

  const dummyExecutions = Array.from({ length: 10 }, (_, i) => ({
    template_id: templates[i % templates.length]._id,
    execution_id: i + 1,
    start_date: new Date(),
    end_date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // +5 days
    status: Work_flow_execution_status.RUNNING,
    last_activity: new Date(),
    assigns: [users[i % users.length]._id],
    assign_by: users[(i + 1) % users.length]._id,
    run_by: users[(i + 2) % users.length]._id,
    notes: `Auto-generated execution ${i + 1}`,
    is_manually_run: i % 2 === 0,
    is_active: true,
    is_deleted: false,
  }));

  await WorkflowExecution.deleteMany(); // Optional cleanup
  await WorkflowExecution.insertMany(dummyExecutions);

  console.log('✅ WorkflowExecution records seeded!');
  await mongoose.disconnect();
};
