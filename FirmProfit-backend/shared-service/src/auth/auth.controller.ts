import { <PERSON>, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { UsersService } from '../users/users.service';

@Controller()
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(private readonly usersService: UsersService) {}

  @GrpcMethod('AuthService', 'Register')
  async register(data: any) {
    try {
      this.logger.log(`Received register request for email: ${data.email}`);
      return await this.usersService.signUp(data);
    } catch (error) {
      this.logger.error(`Error in register: ${error.message}`);
      throw error;
    }
  }

  @GrpcMethod('AuthService', 'Login')
  async login(data: any) {
    try {
      this.logger.log(`Received login request for email: ${data.email}`);
      return await this.usersService.signIn(data);
    } catch (error) {
      this.logger.error(`Error in login: ${error.message}`);
      throw error;
    }
  }

  @GrpcMethod('AuthService', 'EnableMfa')
  async enableMfa(data: any) {
    try {
      this.logger.log(`Received enable_mfa request for ID: ${data.userId}`);
      return await this.usersService.enableMfa(data.userId);
    } catch (error) {
      this.logger.error(`Error in enable_mfa: ${error.message}`);
      throw error;
    }
  }

  @GrpcMethod('AuthService', 'VerifyMfa')
  async verifyMfa(data: any) {
    try {
      this.logger.log(
        `Received verify_mfa request for user ID: ${data.userId}`,
      );
      return await this.usersService.verifyMfa(data);
    } catch (error) {
      this.logger.error(`Error in verify_mfa: ${error.message}`);
      throw error;
    }
  }

  @GrpcMethod('AuthService', 'ValidateMfa')
  async validateMfa(data: any) {
    try {
      this.logger.log(
        `Received validate_mfa request for user ID: ${data.userId}`,
      );
      return await this.usersService.validateMfa(data.userId, data.otp);
    } catch (error) {
      this.logger.error(`Error in validate_mfa: ${error.message}`);
      throw error;
    }
  }

  @GrpcMethod('AuthService', 'ValidateToken')
  async validateToken(data: any) {
    try {
      this.logger.log(`Received validate_token request`);
      await this.usersService.checkAuthTokenInDB(data);
      return { valid: true, message: 'Token is valid' };
    } catch (error) {
      this.logger.error(`Error in validate_token: ${error.message}`);
      return { valid: false, message: error.message };
    }
  }

  @GrpcMethod('AuthService', 'ResetPassword')
  async resetPassword(data: any) {
    try {
      this.logger.log(`Received reset_password request`);
      await this.usersService.resetPassword(data);
      return {
        success: true,
        message: 'Password reset successfully',
        email: data.email,
      };
    } catch (error) {
      this.logger.error(`Error in reset_password: ${error.message}`);
      throw error;
    }
  }
}
