import { Injectable, ConflictException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { User } from '@shared/database';
import { UserMfa } from '@shared/database';
import { ResetPasswordDto, SignUpDto } from './dto/sign-up.dto';
import { SignInDto } from './dto/sign-in.dto';
import { VerifyMfaDto } from './dto/verify-mfa.dto';
import { JwtPayload } from './interfaces/jwt-payload.interface';
import * as bcrypt from 'bcrypt';
import * as speakeasy from 'speakeasy';
import * as QRCode from 'qrcode';
import { AwsSesEmailService } from 'src/mail/mail.service';
import {
  EMAIL_SUBJECT_NAME,
  LOCK_DURATION_MFA_TIME,
  LOCK_DURATION_TIME,
  RESET_PASSWORD_EXP_TIME,
  TEMPLETE_NAME,
} from '../constant/common.constant';
import { ErrorExceptions } from 'src/exceptions/error.exception';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { User as MongoUser, UserDocument } from 'src/workflow/entities/user.entity';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectModel(MongoUser.name) private userModel: Model<UserDocument>,
    @InjectRepository(UserMfa)
    private userMfaRepository: Repository<UserMfa>,
    private jwtService: JwtService,
    private awsSesEmailService: AwsSesEmailService,
  ) { }

  async signUp(
    signUpDto: SignUpDto,
  ): Promise<{ user: Omit<User, 'password'>; accessToken: string }> {
    // Check if user already exists
    signUpDto.email = signUpDto.email.toLowerCase();
    const existingUser = await this.usersRepository.findOne({
      where: { email: signUpDto.email, isDeleted: false },
    });

    if (existingUser) {
      throw new ConflictException();
    }

    // Hash password if it's not already hashed (api gateway may have already hashed it)
    let hashedPassword = signUpDto.password;
    if (
      !hashedPassword.startsWith('$2b$') &&
      !hashedPassword.startsWith('$2a$')
    ) {
      hashedPassword = await this.hashPassword(signUpDto.password);
    }

    // Create new user
    const user = this.usersRepository.create({
      ...signUpDto,
      password: hashedPassword,
      isActive: signUpDto.isActive !== undefined ? signUpDto.isActive : true,
    });

    await this.usersRepository.save(user);

    // Generate JWT token
    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      roleId: user.roleId,
    };
    const accessToken = this.jwtService.sign(payload);

    // Remove password from response
    const { password, ...result } = user;

    return {
      user: result,
      accessToken,
    };
  }

  async generateToken(payload, expiresIn: string | number = '24h') {
    return await this.jwtService.signAsync(payload, {
      secret: process.env.JWT_SECRET,
      expiresIn,
      algorithm: 'HS256',
    });
  }

  async signIn(signInDto: SignInDto) {
    console.log(signInDto, 'signInDto');

    // postgress
    const user = await this.usersRepository.findOne({
      where: {
        email: signInDto.email.toLowerCase(),
        isDeleted: false,
      },
    });

    if (!user) {
      throw ErrorExceptions.UserNotFound();
    }

    if (!user.isActive) {
      throw ErrorExceptions.UserInActive();
    }

    const currentTime = new Date();
    const twentyFourHoursAgo = new Date(
      currentTime.getTime() - LOCK_DURATION_TIME * 60 * 60 * 1000,
    );

    if (
      user.loginAttemptMFACount >= 4 &&
      user.lastInCorrectMFALogin &&
      new Date(user.lastInCorrectMFALogin) <= currentTime && // Ensure date is not in the future
      new Date(user.lastInCorrectMFALogin) >= twentyFourHoursAgo // Ensure date is within the last 24 hours
    ) {
      // throw ErrorExceptions.MULTIPLE_MFA_ATTEMPTS();
    }

    if (
      user.loginAttemptCount >= 4 &&
      user.lastInCorrectLogin &&
      new Date(user.lastInCorrectLogin) <= currentTime && // Ensure date is not in the future
      new Date(user.lastInCorrectLogin) >= twentyFourHoursAgo // Ensure date is within the last 24 hours
    ) {
      const token = await this.generateToken(
        {
          email: user?.email,
          id: user?.id,
          isActive: user?.isActive,
          roleId: user?.roleId,
        },
        RESET_PASSWORD_EXP_TIME,
      );
      const currentYear = new Date().getFullYear();

      try {
        await this.awsSesEmailService.sendEmail({
          template: TEMPLETE_NAME.INCORRECT_ATTAMP,
          recipients: [user.email],
          data: {
            username: user.email,
            lock_duration: LOCK_DURATION_TIME,
            reset_link: `${process.env.baseUrl}/auth/resetpassword/${token}`,
            year: currentYear,
          },
          subjectData: EMAIL_SUBJECT_NAME.INCOORECT_ATTAMP,
        });
      } catch (error) {
        throw error;
        // Optionally, you can handle the error further, e.g., notify an admin or retry sending
      }

      await this.usersRepository.update(
        { id: user.id },
        {
          token,
        },
      );

      throw ErrorExceptions.MULTIPLE_LOGIN_ATTEMPTS();
    }

    // Verify password
    const isPasswordValid = await this.verifyPassword(
      signInDto.password,
      user.password,
    );
    if (!isPasswordValid) {
      await this.usersRepository.update(
        { id: user.id },
        {
          loginAttemptCount: () => 'loginAttemptCount + 1',
          lastInCorrectLogin: () => 'CURRENT_TIMESTAMP',
        },
      );
      throw ErrorExceptions.INVALID_CRED();
    }

    if (signInDto?.timezone) {
      await this.usersRepository.update(
        { id: user.id },
        { timezone: signInDto.timezone } as any
      );
    }

    await this.userModel.updateOne(
      { id: user.id },
      { $set: { timezone: signInDto.timezone } }
    );

    if (
      user.loginAttemptCount >= 4 &&
      user.lastInCorrectLogin &&
      new Date(user.lastInCorrectLogin) <= currentTime && // Ensure date is not in the future
      new Date(user.lastInCorrectLogin) >= twentyFourHoursAgo // Ensure date is within the last 24 hours
    ) {
      throw ErrorExceptions.MULTIPLE_LOGIN_ATTEMPTS;
    }

    // Generate JWT token (temporary token if MFA is enabled)
    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      roleId: user.roleId,
      isMfaAuthenticated: !user.mfaEnabled,
    };
    const accessToken = this.jwtService.sign(payload);

    await this.usersRepository.update(
      { id: user.id },
      {
        loginAttemptCount: 0,
        loginAttemptMFACount: 0,
        lastInCorrectLogin: null,
        lastInCorrectMFALogin: null,
      },
    );

    // UserSchema


    // Remove password from response
    const { password, ...result } = user;

    return {
      user: { ...result, lockedAccount: false, access_token: accessToken },
      accessToken,
      requiresMfa: user.mfaEnabled,
    };
  }

  async findById(id: number): Promise<Omit<User, 'password'> | null> {
    const user = await this.usersRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!user) {
      return null;
    }

    const { password, ...result } = user;
    return result;
  }

  async validateToken(token: string): Promise<{
    isValid: boolean;
    user?: Omit<User, 'password'>;
    requiresMfa?: boolean;
  }> {
    try {
      const payload = this.jwtService.verify<
        JwtPayload & { isMfaAuthenticated?: boolean }
      >(token);
      const user = await this.findById(payload.sub);

      if (!user) {
        return { isValid: false };
      }

      // If MFA is enabled but not authenticated through MFA
      if (user.mfaEnabled && !payload.isMfaAuthenticated) {
        return { isValid: true, user, requiresMfa: true };
      }

      return { isValid: true, user };
    } catch (error) {
      return { isValid: false };
    }
  }

  async enableMfa(
    userId: number,
  ): Promise<{ secret: string; otpAuthUrl: string; qrCode: string }> {
    const user = await this.usersRepository.findOne({
      where: { id: userId, isDeleted: false },
    });

    if (!user) {
      throw ErrorExceptions.UserNotFound();
    }

    // Generate a secret
    const secret = speakeasy.generateSecret({
      length: 20,
      name: `FirmProfit:${user.email}`,
    });

    // Save MFA details
    const userMfa = this.userMfaRepository.create({
      userId: user.id,
      secret: secret.base32,
      createdAt: new Date(),
      isVerified: false,
    });

    await this.userMfaRepository.save(userMfa);

    // await this.usersRepository.update({ id: userId }, { mfaEnabled: true });

    // Generate QR code
    const qrCode = await QRCode.toDataURL(secret.otpauth_url);

    return {
      secret: secret.base32,
      otpAuthUrl: secret.otpauth_url,
      qrCode,
    };
  }

  async verifyMfa(
    verifyMfaDto: VerifyMfaDto,
  ): Promise<{ user: Omit<User, 'password'>; accessToken: string }> {
    const user = await this.usersRepository.findOne({
      where: { id: verifyMfaDto.userId, isDeleted: false },
    });

    if (!user) {
      throw ErrorExceptions.UserNotFound();
    }

    // Get the MFA record
    const mfaRecord = await this.userMfaRepository.findOne({
      where: { userId: user.id, isVerified: false },
      order: { createdAt: 'DESC' },
    });

    if (!mfaRecord) {
      // throw ErrorExceptions.InCorrectMFASCode();
    }

    // Verify the OTP token
    const verified = speakeasy.totp.verify({
      secret: mfaRecord.secret,
      encoding: 'base32',
      token: verifyMfaDto.otp,
      window: 1, // Allow 30 seconds of leeway
    });

    if (!verified) {
      await this.usersRepository.update(
        { id: user.id },
        {
          loginAttemptMFACount: () => 'loginAttemptMFACount + 1',
          lastInCorrectMFALogin: () => 'CURRENT_TIMESTAMP',
        },
      );
      const token = await this.generateToken(
        {
          email: user?.email,
          id: user?.id,
          isActive: user?.isActive,
          roleId: user?.roleId,
        },
        RESET_PASSWORD_EXP_TIME,
      );
      const currentYear = new Date().getFullYear();

      if (user.loginAttemptMFACount >= 4) {
        await this.awsSesEmailService.sendEmail({
          template: TEMPLETE_NAME.INCORRECT_MFA_ATTAMP,
          recipients: [user.email],
          data: {
            username: user.email,
            lock_duration: LOCK_DURATION_MFA_TIME,
            reset_link: `${process.env.baseUrl}/auth/resetpassword/${token}`,
            year: currentYear,
          },
          subjectData: EMAIL_SUBJECT_NAME.INCOORECT_MFA_ATTAMP,
        });
        await this.usersRepository.update(
          { id: user.id },
          {
            token,
          },
        );
        // throw ErrorExceptions.MULTIPLE_MFA_ATTEMPTS();
      }
      // throw ErrorExceptions.InCorrectMFASCode();
    }

    // Update MFA record
    mfaRecord.isVerified = true;

    mfaRecord.verifiedAt = new Date();
    await this.userMfaRepository.save(mfaRecord);

    // Enable MFA for the user
    user.mfaEnabled = true;
    await this.usersRepository.save(user);

    // Generate a new JWT with MFA authentication
    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      roleId: user.roleId,
      isMfaAuthenticated: true,
    };
    const accessToken = this.jwtService.sign(payload);

    await this.usersRepository.update(
      { id: user.id },
      {
        loginAttemptMFACount: 0,
      },
    );

    // Remove password from response
    const { password, ...result } = user;

    return {
      user: result,
      accessToken,
    };
  }

  async validateMfa(
    userId: number,
    otp: string,
  ): Promise<{ user: Omit<User, 'password'>; accessToken: string }> {
    const user = await this.usersRepository.findOne({
      where: { id: userId, isDeleted: false, mfaEnabled: true },
    });

    if (!user) {
      throw new ConflictException({
        message: 'User not found or MFA not enabled',
        statusCode: HttpStatus.NOT_FOUND,
      });
    }

    // Bypass MFA verification for test account
    if (user.email === '<EMAIL>' && otp === '234567') {
      // Generate a new JWT with MFA authentication
      const payload: JwtPayload = {
        sub: user.id,
        email: user.email,
        roleId: user.roleId,
        isMfaAuthenticated: true,
      };
      const accessToken = this.jwtService.sign(payload);

      await this.usersRepository.update(
        { id: user.id },
        {
          loginAttemptMFACount: 0,
        },
      );

      // Remove password from response
      const { password: _password, ...result } = user;

      return {
        user: result,
        accessToken,
      };
    }

    // Get the MFA record
    const mfaRecord = await this.userMfaRepository.findOne({
      where: { userId: user.id, isVerified: true },
      order: { verifiedAt: 'DESC' },
    });

    if (!mfaRecord) {
      // throw ErrorExceptions.InCorrectMFASCode();
    }

    // Verify the OTP token
    const verified = speakeasy.totp.verify({
      secret: mfaRecord.secret,
      encoding: 'base32',
      token: otp,
      window: 1, // Allow 30 seconds of leeway
    });

    if (!verified) {
      await this.usersRepository.update(
        { id: user.id },
        {
          loginAttemptMFACount: () => 'loginAttemptMFACount + 1',
          lastInCorrectMFALogin: () => 'CURRENT_TIMESTAMP',
        },
      );
      const token = await this.generateToken(
        {
          email: user?.email,
          id: user?.id,
          isActive: user?.isActive,
          roleId: user?.roleId,
        },
        RESET_PASSWORD_EXP_TIME,
      );
      const currentYear = new Date().getFullYear();

      if (user.loginAttemptMFACount >= 4) {
        await this.awsSesEmailService.sendEmail({
          template: TEMPLETE_NAME.INCORRECT_MFA_ATTAMP,
          recipients: [user.email],
          data: {
            username: user.email,
            lock_duration: LOCK_DURATION_MFA_TIME,
            reset_link: `${process.env.baseUrl}/auth/resetpassword/${token}`,
            year: currentYear,
          },
          subjectData: EMAIL_SUBJECT_NAME.INCOORECT_MFA_ATTAMP,
        });
        await this.usersRepository.update(
          { id: user.id },
          {
            token,
          },
        );
        throw ErrorExceptions.InCorrectMFACode();
      } else {
        throw ErrorExceptions.INVALID_MFA_CODE();
      }
    }

    // Generate a new JWT with MFA authentication
    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      roleId: user.roleId,
      isMfaAuthenticated: true,
    };
    const accessToken = this.jwtService.sign(payload);

    await this.usersRepository.update(
      { id: user.id },
      {
        loginAttemptMFACount: 0,
      },
    );

    // Remove password from response
    const { password, ...result } = user;

    return {
      user: result,
      accessToken,
    };
  }

  private async hashPassword(password: string): Promise<string> {
    const saltRounds = 10;
    return bcrypt.hash(password, saltRounds);
  }

  private async verifyPassword(
    plainTextPassword: string,
    hashedPassword: string,
  ): Promise<boolean> {
    return bcrypt.compare(plainTextPassword, hashedPassword);
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto) {
    const hashedPassword = await bcrypt.hash(resetPasswordDto.password, 10);
    const userDetails = await this.usersRepository.findOne({
      where: { email: resetPasswordDto.email },
    });
    const updateObj: any = {
      password: hashedPassword,
      loginAttemptCount: 0,
      loginAttemptMFACount: 0,
      lastInCorrectLogin: null,
      lastInCorrectMFALogin: null,
      token: null,
    };

    if (userDetails.loginAttemptMFACount >= 5) {
      updateObj.mfaEnabled = false;
    }

    const updatePassword = await this.usersRepository.update(
      { email: resetPasswordDto.email },
      updateObj,
    );
    return updatePassword;
  }

  async checkAuthTokenInDB(data) {
    const userDetails = await this.usersRepository.findOne({
      where: { email: data.email, token: data.token },
    });
    if (!userDetails) {
      throw new ConflictException({
        message: 'Invalid Token',
        statusCode: HttpStatus.NOT_FOUND,
      });
    }
    return userDetails;
  }
}
