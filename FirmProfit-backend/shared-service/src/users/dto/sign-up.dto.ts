import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ber,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsBoolean,
  IsO<PERSON>al,
} from 'class-validator';

export class SignUpDto {
  @IsEmail({}, { message: 'Please provide a valid email address' })
  email: string;

  @IsString()
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  password: string;

  @IsNumber()
  roleId: number;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @IsBoolean()
  @IsOptional()
  isDeleted?: boolean;
}

export class ResetPasswordDto {
  @IsString()
  email: string;

  @IsString()
  password: string;
}
