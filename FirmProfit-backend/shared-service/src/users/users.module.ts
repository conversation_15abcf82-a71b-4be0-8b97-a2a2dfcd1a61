import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MongooseModule } from '@nestjs/mongoose';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { User } from '@shared/database';
import { UserMfa } from '@shared/database';
import { AwsSesEmailService } from 'src/mail/mail.service';
import { User as MongoUser, UserSchema } from '../workflow/entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, UserMfa]),
    MongooseModule.forFeature([{ name: MongoUser.name, schema: UserSchema }]),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET', 'firmprofitsecretkey123'),
        signOptions: {
          expiresIn: configService.get('JWT_EXPIRES_IN', '24h'),
        },
      }),
    }),
  ],
  controllers: [UsersController],
  providers: [UsersService, AwsSesEmailService],
  exports: [UsersService],
})
export class UsersModule { }
