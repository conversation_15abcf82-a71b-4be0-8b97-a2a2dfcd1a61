import { NotFoundException, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as speakeasy from 'speakeasy';
import { UsersService } from '../users.service';

class MockRepository {
  public findOne = jest.fn();
}

class MockUserMfa {
  public findOne = jest.fn();
}

class MockAwsSesEmailService {}

// describe('UsersService.validateMfa() validateMfa method', () => {
//   let usersService: UsersService;
//   let mockUsersRepository: MockRepository;
//   let mockUserMfaRepository: MockUserMfa;
//   let mockJwtService: JwtService;
//   let mockAwsSesEmailService: MockAwsSesEmailService;

//   beforeEach(() => {
//     mockUsersRepository = new MockRepository() as any;
//     mockUserMfaRepository = new MockUserMfa() as any;
//     mockJwtService = new JwtService({}) as any;
//     mockAwsSesEmailService = new MockAwsSesEmailService() as any;

//     usersService = new UsersService(
//       mockUsersRepository as any,
//       mockUserMfaRepository as any,
//       mockJwtService as any,
//       mockAwsSesEmailService as any,
      
//     );
//   });

//   describe('Happy paths', () => {
//     it('should validate MFA and return user data with access token', async () => {
//       // Arrange
//       const userId = 1;
//       const otp = '123456';
//       const user = {
//         id: userId,
//         email: '<EMAIL>',
//         roleId: 1,
//         isDeleted: false,
//         mfaEnabled: true,
//       };
//       const mfaRecord = { userId, secret: 'SECRET', isVerified: true };
//       const accessToken = 'access-token';

//       jest
//         .mocked(mockUsersRepository.findOne)
//         .mockResolvedValue(user as any as never);
//       jest
//         .mocked(mockUserMfaRepository.findOne)
//         .mockResolvedValue(mfaRecord as any as never);
//       jest.mocked(speakeasy.totp.verify).mockReturnValue(true);
//       jest.mocked(mockJwtService.sign).mockReturnValue(accessToken);

//       // Act
//       const result = await usersService.validateMfa(userId, otp);

//       // Assert
//       expect(result).toEqual({
//         user: { id: userId, email: '<EMAIL>', roleId: 1 },
//         accessToken,
//       });
//     });
//   });

//   describe('Edge cases', () => {
//     it('should throw NotFoundException if user is not found or MFA is not enabled', async () => {
//       // Arrange
//       const userId = 1;
//       const otp = '123456';

//       jest
//         .mocked(mockUsersRepository.findOne)
//         .mockResolvedValue(null as any as never);

//       // Act & Assert
//       await expect(usersService.validateMfa(userId, otp)).rejects.toThrow(
//         NotFoundException,
//       );
//     });

//     it('should throw NotFoundException if MFA setup is not found', async () => {
//       // Arrange
//       const userId = 1;
//       const otp = '123456';
//       const user = {
//         id: userId,
//         email: '<EMAIL>',
//         roleId: 1,
//         isDeleted: false,
//         mfaEnabled: true,
//       };

//       jest
//         .mocked(mockUsersRepository.findOne)
//         .mockResolvedValue(user as any as never);
//       jest
//         .mocked(mockUserMfaRepository.findOne)
//         .mockResolvedValue(null as any as never);

//       // Act & Assert
//       await expect(usersService.validateMfa(userId, otp)).rejects.toThrow(
//         NotFoundException,
//       );
//     });

//     it('should throw UnauthorizedException if OTP is invalid', async () => {
//       // Arrange
//       const userId = 1;
//       const otp = '123456';
//       const user = {
//         id: userId,
//         email: '<EMAIL>',
//         roleId: 1,
//         isDeleted: false,
//         mfaEnabled: true,
//       };
//       const mfaRecord = { userId, secret: 'SECRET', isVerified: true };

//       jest
//         .mocked(mockUsersRepository.findOne)
//         .mockResolvedValue(user as any as never);
//       jest
//         .mocked(mockUserMfaRepository.findOne)
//         .mockResolvedValue(mfaRecord as any as never);
//       jest.mocked(speakeasy.totp.verify).mockReturnValue(false);

//       // Act & Assert
//       await expect(usersService.validateMfa(userId, otp)).rejects.toThrow(
//         UnauthorizedException,
//       );
//     });
//   });
// });
