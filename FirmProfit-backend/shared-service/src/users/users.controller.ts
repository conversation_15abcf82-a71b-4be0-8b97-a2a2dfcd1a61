import { <PERSON>, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { UsersService } from './users.service';
import { SignUpDto } from './dto/sign-up.dto';
import { SignInDto } from './dto/sign-in.dto';

@Controller()
export class UsersController {
  private readonly logger = new Logger(UsersController.name);
  constructor(private readonly usersService: UsersService) {}

  @GrpcMethod('UserService', 'SignUp')
  async signUp(data: SignUpDto) {
    try {
      this.logger.log(`Received signUp request for email: ${data.email}`);
      return await this.usersService.signUp(data);
    } catch (error) {
      this.logger.error(`Error in signUp: ${error.message}`);
      throw error;
    }
  }

  @GrpcMethod('UserService', 'SignIn')
  async signIn(data: SignInDto) {
    try {
      this.logger.log(`Received signIn request for email: ${data.email}`);
      return await this.usersService.signIn(data);
    } catch (error) {
      this.logger.error(`Error in signIn: ${error.message}`);
      throw error;
    }
  }

  @GrpcMethod('UserService', 'VerifyUser')
  async verifyUser(data: SignInDto) {
    try {
      this.logger.log(`Received verifyUser request for email: ${data.email}`);
      const { user } = await this.usersService.signIn(data);
      return user;
    } catch (error) {
      this.logger.error(`Error in verifyUser: ${error.message}`);
      throw error;
    }
  }

  @GrpcMethod('UserService', 'ValidateUserToken')
  async validateUserToken(data) {
    try {
      this.logger.log('Received validateUserToken request');
      await this.usersService.checkAuthTokenInDB(data);
      return { valid: true, message: 'Token is valid' };
    } catch (error) {
      this.logger.error(`Error in validateUserToken: ${error.message}`);
      return { valid: false, message: error.message };
    }
  }

  @GrpcMethod('UserService', 'GetUserById')
  async getUserById(data: { id: number }) {
    try {
      this.logger.log(`Received getUserById request for ID: ${data.id}`);
      return await this.usersService.findById(data.id);
    } catch (error) {
      this.logger.error(`Error in getUserById: ${error.message}`);
      throw error;
    }
  }

  @GrpcMethod('UserService', 'EnableMfa')
  async enableMfa(data: { userId: number }) {
    try {
      this.logger.log(`Received enableMfa request for ID: ${data.userId}`);
      return await this.usersService.enableMfa(data.userId);
    } catch (error) {
      this.logger.error(`Error in enableMfa: ${error.message}`);
      throw error;
    }
  }

  @GrpcMethod('UserService', 'VerifyMfa')
  async verifyMfa(data) {
    try {
      this.logger.log(`Received verifyMfa request for user ID: ${data.userId}`);
      return await this.usersService.verifyMfa(data);
    } catch (error) {
      this.logger.error(`Error in verifyMfa: ${error.message}`);
      throw error;
    }
  }

  @GrpcMethod('UserService', 'ValidateMfa')
  async validateMfa(data) {
    try {
      this.logger.log(
        `Received validateMfa request for user ID: ${data.userId}`,
      );
      return await this.usersService.validateMfa(data.userId, data.otp);
    } catch (error) {
      this.logger.error(`Error in validateMfa: ${error.message}`);
      throw error;
    }
  }

  @GrpcMethod('UserService', 'HandleMfa')
  async handleMfa(data) {
    try {
      this.logger.log(`Received handleMfa event: ${JSON.stringify(data)}`);
      switch (data.action) {
        case 'enable':
          return await this.usersService.enableMfa(data.userId);
        case 'verify':
          return await this.usersService.verifyMfa({
            userId: data.userId,
            otp: data.otp,
          });
        case 'validate':
          return await this.usersService.validateMfa(data.userId, data.otp);
        default:
          throw new Error(`Unknown MFA action: ${data.action}`);
      }
    } catch (error) {
      this.logger.error(`Error in handleMfa event handler: ${error.message}`);
      throw error;
    }
  }

  @GrpcMethod('UserService', 'ResetPassword')
  async resetPassword(data) {
    try {
      this.logger.log(`Received resetPassword request`);
      await this.usersService.resetPassword(data);
      return { success: true, message: 'Password reset successfully' };
    } catch (error) {
      this.logger.error(`Error in resetPassword: ${error.message}`);
      throw error;
    }
  }

  @GrpcMethod('UserService', 'CheckAuthTokenInDB')
  async checkAuthTokenInDB(data) {
    try {
      this.logger.log(`Received checkAuthTokenInDB request`);
      await this.usersService.checkAuthTokenInDB(data);
      return { valid: true, message: 'Token is valid' };
    } catch (error) {
      this.logger.error(`Error in checkAuthTokenInDB: ${error.message}`);
      return { valid: false, message: error.message };
    }
  }
}
