import { Test, TestingModule } from '@nestjs/testing';
import { AppService } from './app.service';

describe('AppService', () => {
  let service: AppService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AppService],
    }).compile();

    service = module.get<AppService>(AppService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should return "Hello World!"', () => {
    expect(service.getHello()).toBe('Hello World!');
  });

  it('should process login success event', () => {
    const userData = { userId: 123, timestamp: new Date() };
    const result = service.handleLoginSuccess(userData);

    expect(result).toEqual({
      success: true,
      message: 'Login event processed',
    });
  });
});
