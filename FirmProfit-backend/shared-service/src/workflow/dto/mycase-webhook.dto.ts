import {
  IsNotEmpty,
  <PERSON><PERSON><PERSON>al,
  IsString,
  IsObject,
  Val<PERSON>teNested,
  IsJSON,
} from 'class-validator';
import { Type } from 'class-transformer';

export class SaveMyCaseWebhookDataDto {
  @IsNotEmpty()
  @IsString()
  firm_uuid: string;

  @IsNotEmpty()
  @IsString()
  action: string;

  @IsNotEmpty()
  @IsString()
  resource: string;

  @IsNotEmpty()
  @IsString()
  @IsJSON()
  // This is a JSON string containing nested objects and arrays
  // Example: "{\"id\":338308378,\"staff\":[{\"id\":61054170}]}"
  resource_body: string;

  @IsNotEmpty()
  @IsString()
  timestamp: string;
}

export class SaveMyCaseWebhookDataResponseDto {
  success: boolean;
  message: string;
  id?: string;
}

// Example interfaces for common resource_body structures (when parsed from JSON string)
// These are just for documentation purposes
export interface EventResourceBody {
  id: number;
  staff?: StaffMember[];
  [key: string]: any; // Allow additional fields
}

export interface StaffMember {
  id: number;
  required?: boolean;
  [key: string]: any; // Allow additional fields
}

export interface ClientResourceBody {
  id: number;
  email?: string;
  first_name?: string;
  middle_initial?: string;
  last_name?: string;
  cell_phone_number?: string;
  work_phone_number?: string;
  home_phone_number?: string;
  address?: {
    address1?: string;
    address2?: string;
    city?: string;
    state?: string;
    country?: string;
    zip_code?: string;
  };
  notes?: string;
  birthdate?: string;
  archived?: boolean;
  updated_at?: string;
  created_at?: string;
  people_group?: {
    id: number;
  };
  [key: string]: any; // Allow additional fields
}

export interface CaseResourceBody {
  id: number;
  name: string;
  case_number?: string;
  description?: string;
  opened_date?: string;
  closed_date?: string;
  sol_date?: string;
  outstanding_balance?: number;
  clients?: Array<{
    id: number;
  }>;
  staff?: Array<{
    id: number;
    lead_lawyer?: boolean;
    originating_lawyer?: boolean;
    case_rate?: string;
  }>;
  case_stage_reference?: {
    id: number;
  };
  practice_area_reference?: {
    id: number;
  };
  billing_contact?: {
    id: number;
  };
  potential_case?: boolean;
  updated_at?: string;
  created_at?: string;
  [key: string]: any; // Allow additional fields
}

export interface Matter {
  id: number;
  name?: string;
  status?: string;
  [key: string]: any; // Allow additional fields
}

// Helper function to parse resource_body string to object
export function parseResourceBody(resourceBodyString: string): any {
  try {
    return JSON.parse(resourceBodyString);
  } catch (error) {
    console.error('Error parsing resource_body:', error);
    return {};
  }
}

// MyCase Event Webhook Payload Interface
export interface MyCaseEventWebhookPayload {
  id: number;
  name: string;
  description?: string;
  private: boolean;
  all_day: boolean;
  start: string;
  end: string;
  location?: {
    id: number;
  };
  case?: {
    id: number | null;
  };
  created_at: string;
  updated_at: string;
}

// MyCase Case API Response Interfaces
export interface MyCaseClient {
  id: number;
  first_name: string;
  last_name: string;
  middle_initial?: string;
  middle_name?: string;
  email: string;
  cell_phone_number?: string;
  work_phone_number?: string;
  home_phone_number?: string;
  fax_phone_number?: string;
  contact_group?: string;
  birthdate?: string;
  created_at: string;
  updated_at: string;
}

export interface MyCaseCompany {
  id: number;
}

export interface MyCaseStaff {
  id: number;
  lead_lawyer: boolean;
  originating_lawyer: boolean;
  case_rate?: string;
}

export interface MyCaseCustomFieldValue {
  custom_field: {
    id: number;
    field_type: string;
  };
  value: string;
  created_at: string;
  updated_at: string;
}

export interface MyCaseOffice {
  id: number;
  name: string;
  fax_phone?: string;
  main_phone?: string;
  address: {
    address1: string;
    address2?: string;
    city: string;
    state: string;
    zip_code: string;
    country: string;
  };
  updated_at: string;
  created_at: string;
}

export interface MyCaseCase {
  id: number;
  name: string;
  case_number: string;
  description?: string;
  opened_date: string;
  closed_date?: string;
  sol_date?: string;
  practice_area?: string;
  case_stage?: string;
  status: string;
  outstanding_balance: number;
  billing_contact?: {
    id: number;
  };
  billing_type?: string;
  contacts?: any;
  clients: MyCaseClient[];
  companies: MyCaseCompany[];
  staff: MyCaseStaff[];
  custom_field_values: MyCaseCustomFieldValue[];
  office: MyCaseOffice;
  updated_at: string;
  created_at: string;
}

export type MyCaseCaseResponse = MyCaseCase[];

// Helper function to stringify object to resource_body string
export function stringifyResourceBody(resourceBodyObject: any): string {
  try {
    return JSON.stringify(resourceBodyObject);
  } catch (error) {
    console.error('Error stringifying resource_body:', error);
    return '{}';
  }
}
