import { IsOptional, IsString, IsBoolean, IsNumber } from 'class-validator';

export class GetLocationDto {
  @IsOptional()
  @IsString()
  case_id?: string;

  @IsOptional()
  @IsString()
  access_token?: string;
}

export class LocationResponseDto {
  @IsString()
  name: string;

  @IsBoolean()
  is_active: boolean;

  @IsOptional()
  @IsNumber()
  my_case_id?: number;

  @IsOptional()
  @IsString()
  city?: string;

  @IsOptional()
  @IsString()
  country?: string;

  @IsOptional()
  @IsString()
  state?: string;

  @IsOptional()
  @IsString()
  address1?: string;

  @IsOptional()
  @IsString()
  address2?: string;

  @IsOptional()
  @IsString()
  zip_code?: string;

  createdAt?: Date;
  updatedAt?: Date;
  my_case_created_at?: Date;
  my_case_updated_at?: Date;
}

export class SyncLocationsResponseDto {
  locations: LocationResponseDto[];
  synced_count: number;
  total_count: number;
  message: string;
}

export interface MyCaseLocationInterface {
  id: number;
  name: string;
  address?: {
    city?: string;
    country?: string;
    state?: string;
    address1?: string;
    address2?: string;
    zip_code?: string;
  };
  created_at?: string;
  updated_at?: string;
  is_active?: boolean;
}

export interface MyCaseLocationListResponse {
  data: MyCaseLocationInterface[];
  total: number;
  page: number;
  per_page: number;
}
