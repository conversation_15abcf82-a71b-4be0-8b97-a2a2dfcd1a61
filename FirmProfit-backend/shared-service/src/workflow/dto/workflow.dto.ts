import {
  <PERSON><PERSON><PERSON><PERSON>,
  Min,
  <PERSON>NotEmpty,
  IsString,
  IsArray,
  ValidateNested,
  IsBoolean,
  IsNotEmptyObject,
  IsNumber,
  Max,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { FileDto } from './file.dto';
import { RenameFileDto } from './rename.file.dto';

export class GetWorkflowDto {
  @IsOptional()
  @Min(1)
  @Transform(({ value }) =>
    typeof value === 'string' ? parseInt(value, 10) : value,
  )
  page?: number = 1;

  @IsOptional()
  @Min(1)
  @Transform(({ value }) =>
    typeof value === 'string' ? parseInt(value, 10) : value,
  )
  limit?: number = 10;

  @IsOptional()
  @IsString()
  type?: string;

  @IsOptional()
  @IsString()
  userId?: string;

}

export class WorkflowRenderDto {
  @IsOptional()
  work_flow_id?: string;
}

export class FormValueDto {
  @IsOptional()
  @IsString()
  id: string | null;

  @IsString()
  value: string;
}

export class FormDto {
  @IsNotEmpty()
  @IsString()
  form_component_id: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FormValueDto)
  value: FormValueDto[];
}

class EventUpdateDataDto {
  @IsOptional() @IsString() caseNumber?: string;
  @IsOptional() @IsString() clientName?: string;
  @IsOptional() @IsString() description?: string;
  @IsOptional() @IsString() date?: string;
  @IsOptional() @IsString() startTime?: string;
  @IsOptional() @IsString() endTime?: string;
  @IsOptional() @IsBoolean() isCompleted?: boolean;
  @IsOptional() @IsString() subject?: string;
  @IsOptional() @IsString() courtNoticeType?: string;
  @IsOptional() @IsString() courtNoticeActions?: string;
  @IsOptional() @IsString() appointmentAction?: string;
  @IsOptional() @IsString() charge?: string;
  @IsOptional() @IsString() county?: string;
  @IsOptional() @IsString() courtLocation?: string;
  @IsOptional() @IsString() optionalAttendees?: string;
  @IsOptional() @IsString() requiredAttendees?: string;
  @IsOptional() @IsString() clientAttendance?: string;
  @IsOptional() @IsString() meetingLocation?: string;
  @IsOptional() @IsString() phoneDetails?: string;
  @IsOptional() @IsString() meetingAddress?: string;
  @IsOptional() @IsString() startDate?: string;
  @IsOptional() @IsString() endDate?: string;
  @IsOptional() @IsBoolean() allDay?: boolean;
  @IsOptional() @IsString() matterName?: string;
  @IsOptional() @IsString() appointmentToReschedule?: string;
  @IsOptional() @IsString() meetingLink?: string;
  @IsOptional() @IsString() court_notice_date?: string;
  @IsOptional() @IsString() id?: string;
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FileDto)
  files?: FileDto[];
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FileDto)
  deleteFiles?: FileDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RenameFileDto)
  renameFiles?: RenameFileDto[];
}

export class UpdateEventDto {
  @IsString()
  @IsNotEmpty()
  derivedFieldId: string;

  @IsString()
  @IsNotEmpty()
  clientIdentifier: string;

  @IsString()
  @IsNotEmpty()
  clientMatterId: string;

  @IsString()
  @IsNotEmpty()
  matterId: string;

  @IsOptional()
  @IsString()
  eventId?: string; // Now optional - if not provided, a new event will be created

  @IsOptional()
  @IsBoolean()
  createEvent?: boolean; // Flag to explicitly indicate we're creating a new event

  @IsOptional()
  @IsBoolean()
  deleteEvent?: boolean; // Flag to indicate we're deleting an event

  @IsOptional()
  @IsBoolean()
  newClient?: boolean; // Flag to indicate if this is a new client

  @IsNotEmptyObject()
  @ValidateNested()
  @Type(() => EventUpdateDataDto)
  updateData: EventUpdateDataDto;
}

export class UpdateWorkflowDto {
  @IsNotEmpty()
  @IsString()
  workflow_id: string;

  @IsNotEmpty()
  @IsString()
  task_id: string;

  @IsOptional()
  @IsBoolean()
  is_completed?: boolean;

  @IsOptional()
  @IsBoolean()
  deleteMatter?: boolean;

  @IsOptional()
  @IsString()
  clientMatterId?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FormDto)
  forms: FormDto[];

  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateEventDto)
  event?: UpdateEventDto;

  @IsOptional()
  @IsBoolean()
  isChild?: boolean;
}

export class UpdateWorkflowStatusDto {
  @IsNotEmpty()
  @IsString()
  workflow_id: string;

  @IsOptional()
  @IsBoolean()
  isChild?: boolean;
}

export class GetNextTaskIdDto {
  task_id: string;
  @IsString()
  @IsString()
  work_flow_id: string;
}

export class UserListDto {
  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsString()
  client_id?: string;

  @IsOptional()
  @IsString()
  user_group_id?: string;
}

export class UpdateWorkflowEndDateDto {
  @IsNotEmpty()
  @IsString()
  workflow_id: string;

  @IsNotEmpty()
  @IsString()
  task_id: string;

  @IsNotEmpty()
  @IsString()
  end_date: string;
}

export class GetPresignedUrlDto {
  @IsString()
  key: string;

  @IsString()
  operation: string;

  @IsOptional()
  @IsString()
  contentType?: string;
}

export class CleanupFilesDto {
  @IsArray()
  @IsString({ each: true })
  fileKeys: string[];
}

export class ArchiveWorkFlowDto {
  @IsNotEmpty()
  @IsString()
  work_flow_execution_id: string;

  @IsNotEmpty()
  @IsString()
  type: string;

  @IsOptional()
  @IsString()
  archive_at?: string;

  @IsOptional()
  @IsString()
  archive_by?: string;

  @IsOptional()
  @IsString()
  task_id?: string;

  @IsOptional()
  @IsString()
  tenant_id?: string;

  @IsOptional()
  @IsString()
  mycase_access_token?: string;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(3)
  retry_count?: number;

  @IsOptional()
  @IsString()
  last_error?: string;

  @IsOptional()
  @IsString()
  work_child_flow_execution_id?: string;
}

export class GetEventListDto {
  @IsOptional()
  @IsString()
  workflow_id?: string;

  @IsOptional()
  @IsString()
  client_matter_id?: string;
}

export class GetEventByIdDto {
  @IsNotEmpty()
  @IsString()
  event_id: string;
}

export class UpdateWorkflowRetryInfoDto {
  @IsNotEmpty()
  @IsString()
  workflow_id: string;

  @IsOptional()
  @IsString()
  task_id?: string;

  @IsOptional()
  @IsString()
  status?: 'PENDING' | 'IN_PROGRESS' | 'SUCCESS' | 'FAILED';

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(10)
  retry_count?: number;

  @IsOptional()
  @IsString()
  error_message?: string;
}

export class CheckClientAvailabilityDto {
  @IsNotEmpty()
  @IsString()
  client_id: string;

  @IsOptional()
  @IsString()
  tenant_id?: string;

  @IsOptional()
  @IsString()
  access_token?: string;
}
