import { IsString, IsO<PERSON>al, IsArray, IsEnum, ValidateNested, IsMongoId } from 'class-validator';
import { Type } from 'class-transformer';

export class AssigneeDto {
    @IsString()
    id: string;

    @IsEnum(['user', 'role'])
    type: 'user' | 'role';
}

export class AssignmentRequestDto {
    @IsMongoId()
    task_execution_id: string;

    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => AssigneeDto)
    assignees: AssigneeDto[];

    @IsMongoId()
    assigned_by: string;

    @IsOptional()
    @IsString()
    notes?: string;
}

export class GetAssigneesDto {
    @IsOptional()
    @IsString()
    search?: string;

    @IsOptional()
    @IsString()
    category?: string;
}

export class RemoveAssignmentDto {
    @IsMongoId()
    task_execution_id: string;

    @IsString()
    assignee_id: string;

    @IsEnum(['user', 'role'])
    assignment_type: 'user' | 'role';
}

export class GetRoleMembersDto {
    @IsString()
    role_id: string;
} 