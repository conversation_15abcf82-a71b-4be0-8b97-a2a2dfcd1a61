export enum Template_status {
  DRAFT = 'DRAFT',
  COMPLETED = 'COMPLETED',
  PENDING = 'PENDING',
  ON_TRACK = 'ON_TRACK',
  OVERDUE = 'OVERDUE',
  DUE_SOON = 'DUE_SOON',
}

export enum Trigger_type {
  POOLING = 'Pooling',
  INSTANT = 'Instant',
}

export enum Task_status {
  DRAFT = 'DRAFT',
  COMPLETED = 'COMPLETED',
  PENDING = 'PENDING',
}

export enum Task_type {
  FORM_COMPONENT = 'formComponent',
  DERIVED_COMPONENT = 'derivedComponent',
  CONNECTOR = 'connector',
}

export enum Work_flow_execution_status {
  RUNNING = 'RUNNING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  PAUSED = 'PAUSED',
  CANCELLED = 'CANCELLED',
  PENDING = 'PENDING',
  ON_TRACK = 'ON_TRACK',
  OVERDUE = 'OVERDUE',
  DUE_SOON = 'DUE_SOON',
  ARCHIVED = 'ARCHIVED',
}

export enum Form_type {
  TEXT = 'text',
  SELECT = 'select',
  ASSIGN = 'assign',
  CHECKBOX = 'checkbox',
  RADIO_BUTTON_GROUP = 'radio_button_group',
  TEXT_AREA = 'text_area',
  BUTTON = 'button',
}

export enum Appointment_action {
  NEW = 'new',
  RESCHEDULE = 'Reschedule',
  CANCLE = 'cancle',
}

export enum Meeting_location {
  VIRTUAL_MEETING = 'virtual_meeting',
  GOOGLE_MEET = 'google_meet',
}

export enum Court_notice_type {
  JURY_TRIAL = 'jury_trial',
}

export enum TASK_VISIBLE_STATUS {
  DRAFT = 'DRAFT',
  IN_REVIEW = 'IN_REVIEW',
  REVIEWED = 'REVIEWED',
}

export enum EventStatus {
  NEW = 'New',
  PENDING = 'Pending',
  SYNCED = 'Synced',
  SYNC_FAILED = 'SyncFailed',
  MANUAL_ENTRY = 'ManualEntry',
  CANCEL = 'Cancel',
  RESCHEDULE = 'Reschedule',
}

export enum EventAction {
  CANCLE = 'Cancel',
  RESCHEDULE = 'Reschedule',
  RESET = 'Reset',
  NEW = 'New',
  CANCEL_DIS = 'CANCELED',
}
