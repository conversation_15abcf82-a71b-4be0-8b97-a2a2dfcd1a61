import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type RoleDocument = HydratedDocument<Role>;

@Schema({ timestamps: true, collection: 'roles' })
export class Role {
    @Prop({ type: Number })
    id: number;

    @Prop({ type: String, required: true })
    name: string;

     @Prop({ type: Boolean, default: true })
    type: boolean;

    @Prop({ type: Boolean, default: true })
    is_active: boolean;

    @Prop({ type: Boolean, default: false })
    is_deleted: boolean;
}

export const RoleSchema = SchemaFactory.createForClass(Role);
