import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Schema as MongooseSchema } from 'mongoose';

export type LocationDocument = HydratedDocument<Location>;

@Schema({ timestamps: true, collection: 'location' })
export class Location {
  @Prop({ required: true })
  name: string;

  @Prop({ default: true })
  is_active: boolean;
}

export const LocationSchema = SchemaFactory.createForClass(Location);
