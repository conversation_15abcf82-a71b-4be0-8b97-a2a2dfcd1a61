import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Schema as MongooseSchema } from 'mongoose';

export type RolePermissionDocument = HydratedDocument<RolePermission>;

@Schema({ timestamps: true, collection: 'rolepermission' })
export class RolePermission {
    @Prop({ type: Number })
    id: number;

    @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Role', required: true })
    roleId: string;

    @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Permission', required: true })
    permissionId: boolean;

    @Prop({ type: Boolean, default: false })
    status: boolean;
}

export const RolePermissionSchema = SchemaFactory.createForClass(RolePermission);
