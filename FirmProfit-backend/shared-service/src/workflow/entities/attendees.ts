import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type AttendeesDocument = HydratedDocument<Attendees>;

@Schema({ timestamps: true, collection: 'attendees' })
export class Attendees {
  @Prop({ required: true })
  value: string;

  @Prop({ required: true })
  text: string;

  @Prop({ required: true })
  type: number;

  @Prop({ default: true })
  is_active: boolean;
}
export const AttendeesSchema = SchemaFactory.createForClass(Attendees);
