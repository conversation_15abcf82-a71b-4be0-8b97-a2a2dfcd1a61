import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type ViewDocument = HydratedDocument<View>;

@Schema({ timestamps: true, collection: 'view' })
export class View {
  @Prop({ required: true })
  name: string;

  @Prop({ default: true })
  is_active: boolean;

  @Prop({ default: false })
  is_deleted: boolean;
}
export const ViewSchema = SchemaFactory.createForClass(View);
