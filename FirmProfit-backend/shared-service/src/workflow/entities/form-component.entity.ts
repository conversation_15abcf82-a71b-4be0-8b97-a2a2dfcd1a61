import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { Form_type } from '../enum/template-status.enum';

export type FormComponentDocument = HydratedDocument<FormComponent>;

@Schema({ timestamps: true, collection: 'form_component' })
export class FormComponent {
  @Prop({ required: false, default: '' })
  name: string;

  @Prop({ type: String, required: false, default: '' })
  label: string;

  @Prop({
    type: String,
    enum: Form_type,
    required: true,
  })
  type: Form_type;

  @Prop({ required: false })
  placeholder: string;

  @Prop({ required: false })
  form_field_id: string;

  @Prop({ required: false })
  api_end_point: string;

  @Prop({ type: Object, default: {} })
  validation: Record<string, any>;

  @Prop({ default: false })
  mandatory_field: boolean;

  @Prop({ default: false })
  skip_by_ai: boolean;

  @Prop({ default: true })
  is_active: boolean;

  @Prop({
    type: [
      {
        value: { type: String, required: true },
        text: { type: String, required: true },
      },
    ],
    default: [],
  })
  options: { value: string; text: string }[];
}

export const FormComponentSchema = SchemaFactory.createForClass(FormComponent);
