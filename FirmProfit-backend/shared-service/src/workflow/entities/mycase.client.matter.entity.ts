import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Schema as MongooseSchema, Types } from 'mongoose';
import { EventStatus } from '../enum/template-status.enum';
import { EventSub, EventSubSchema } from './derived.field.entity';

export type MycaseClientMatterDocument = HydratedDocument<MycaseClientMatter>;

@Schema({ timestamps: true, collection: 'mycase_client_matter' })
export class MycaseClientMatter {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Matter', required: false })
  client_matter_id?: Types.ObjectId;

  @Prop({ enum: EventStatus, default: EventStatus.NEW })
  eventStatus?: EventStatus;

  @Prop({ type: String })
  event_id?: string;

  @Prop({ enum: EventStatus, default: EventStatus.NEW })
  action?: EventStatus;

  @Prop({ type: String })
  my_case_event_id?: string;

  @Prop({ type: [String] })
  update_event_id?: string[];

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'work_flow_execution',
    required: false,
  })
  workflow_execution_id?: Types.ObjectId;

  @Prop({ type: EventSubSchema, default: {} })
  event: EventSub;

  @Prop({ default: true })
  is_active: boolean;
}

export const MycaseClientMatterSchema =
  SchemaFactory.createForClass(MycaseClientMatter);

// Configure the schema to properly handle nested objects
MycaseClientMatterSchema.set('strict', false);
MycaseClientMatterSchema.set('minimize', false);
