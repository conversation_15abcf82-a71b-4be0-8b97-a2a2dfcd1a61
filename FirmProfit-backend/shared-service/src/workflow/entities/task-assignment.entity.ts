import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Schema as MongooseSchema, Types } from 'mongoose';

export type TaskAssignmentDocument = HydratedDocument<TaskAssignment>;

@Schema({ timestamps: true, collection: 'task_assignments' })
export class TaskAssignment {
    @Prop({
        type: MongooseSchema.Types.ObjectId,
        ref: 'task_execution',
        required: true,
    })
    task_execution_id: Types.ObjectId;

    @Prop({
        type: MongooseSchema.Types.ObjectId,
        ref: 'User',
        required: false,
    })
    assigned_user: Types.ObjectId;

    @Prop({
        type: Number, // Changed from ObjectId to Number to match Role.id (Int)
        required: false,
    })
    assigned_role: number; // This will reference the Role table

    @Prop({
        type: MongooseSchema.Types.ObjectId,
        ref: 'User',
        required: true,
    })
    assigned_by: Types.ObjectId;

    @Prop({ type: String, required: true })
    assignment_type: 'user' | 'role'; // Changed from 'group' to 'role'

    @Prop({ type: String, required: false })
    notes: string;

    @Prop({ type: Boolean, default: true })
    is_active: boolean;

    @Prop({ type: Boolean, default: false })
    is_deleted: boolean;
}

export const TaskAssignmentSchema = SchemaFactory.createForClass(TaskAssignment); 