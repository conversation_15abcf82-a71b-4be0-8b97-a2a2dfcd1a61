import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type PermissionDocument = HydratedDocument<Permission>;

@Schema({ timestamps: true, collection: 'Permissions' })
export class Permission {
    @Prop({ type: Number })
    id: number;

    @Prop({ type: String, required: true })
    name: string;

    @Prop({ type: Boolean, default: true })
    is_active: boolean;

    @Prop({ type: Boolean, default: false })
    is_deleted: boolean;
}

export const PermissionSchema = SchemaFactory.createForClass(Permission);
