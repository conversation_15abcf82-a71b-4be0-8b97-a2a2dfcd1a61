import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type CourtNoticeTypeDocument = HydratedDocument<CourtNoticeType>;

@Schema({ timestamps: true, collection: 'court_notice_type' })
export class CourtNoticeType {
  @Prop({ required: true })
  value: string;

  @Prop({ required: true })
  text: string;

  @Prop({ default: true })
  is_active: boolean;
}

export const CourtNoticeTypeSchema =
  SchemaFactory.createForClass(CourtNoticeType);
