// schemas/config-group.schema.ts
import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Schema as MongooseSchema, Types } from 'mongoose';

export type ConfigGroupDocument = HydratedDocument<ConfigGroup>;

@Schema({ timestamps: true, collection: 'configGroup' })
export class ConfigGroup {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  description: string;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'UserGroup',
    required: true,
  })
  newcourtnoticGroupid: Types.ObjectId;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'UserGroup',
    required: true,
  })
  followupgroupid: Types.ObjectId;

  @Prop({ default: true })
  is_active: boolean;

  @Prop({ default: false })
  is_deleted: boolean;
}

export const ConfigGroupSchema = SchemaFactory.createForClass(ConfigGroup);
