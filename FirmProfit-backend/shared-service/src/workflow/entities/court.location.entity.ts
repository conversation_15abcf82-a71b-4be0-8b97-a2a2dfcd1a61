import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type CourtLocationDocument = HydratedDocument<CourtLocation>;

@Schema({ timestamps: true, collection: 'court_location' })
export class CourtLocation {
  @Prop({ required: true })
  value: string;

  @Prop({ required: true })
  text: string;

  @Prop({ default: true })
  is_active: boolean;

  // MyCase specific fields
  @Prop({ unique: true, sparse: true })
  my_case_id: number;

  @Prop()
  name: string;

  // Address fields
  @Prop()
  city: string;

  @Prop()
  country: string;

  @Prop()
  state: string;

  @Prop()
  address1: string;

  @Prop()
  address2: string;

  @Prop()
  zip_code: string;

  // MyCase timestamps
  @Prop()
  my_case_created_at: Date;

  @Prop()
  my_case_updated_at: Date;
}

export const CourtLocationSchema = SchemaFactory.createForClass(CourtLocation);
