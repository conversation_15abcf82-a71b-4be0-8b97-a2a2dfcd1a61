import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Schema as MongooseSchema, Types } from 'mongoose';

export type RoundRobinStateDocument = HydratedDocument<RoundRobinState>;

@Schema({ timestamps: true, collection: 'round_robin_states' })
export class RoundRobinState {
    @Prop({ type: String, required: true })
    context_type: 'role' | 'workflow' | 'global'; // Type of round robin context

    @Prop({ type: String, required: false })
    context_id: string; // ID of the context (role ID, workflow ID, etc.)

    @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
    last_assigned_user: Types.ObjectId; // Last user assigned in this context

    @Prop({ type: Number, default: 0 })
    assignment_count: number; // Total assignments made in this context

    @Prop({ type: [{ type: MongooseSchema.Types.ObjectId, ref: 'User' }], default: [] })
    user_pool: Types.ObjectId[]; // Pool of users available for round robin

    @Prop({ type: Number, default: 0 })
    current_index: number; // Current index in the user pool

    @Prop({ type: Date, default: Date.now })
    last_assignment_date: Date; // When was the last assignment made

    @Prop({ type: Boolean, default: true })
    is_active: boolean;

    @Prop({ type: Boolean, default: false })
    is_deleted: boolean;
}

export const RoundRobinStateSchema = SchemaFactory.createForClass(RoundRobinState);

// Create compound index for efficient queries
RoundRobinStateSchema.index({ context_type: 1, context_id: 1 }, { unique: true }); 