import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, SchemaTypes } from 'mongoose';

export type WebhookResponseDocument = HydratedDocument<WebhookResponse>;

@Schema({
  timestamps: true,
})
export class WebhookResponse {
  @Prop({ required: false, type: String })
  method_name: string;

  @Prop({ required: false, type: String })
  request_source: string;

  @Prop({
    required: true,
    type: SchemaTypes.Mixed, // Accepts object, array, boolean, etc.
  })
  webhook_response: Record<string, any>;

  @Prop({ default: true, type: Boolean })
  is_active: boolean;

  @Prop({ required: false, type: String })
  error_message: string;

  createdAt?: Date;
  updatedAt?: Date;
}

export const WebhookResponseSchema = SchemaFactory.createForClass(WebhookResponse);