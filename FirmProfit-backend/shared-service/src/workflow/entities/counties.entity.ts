import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type CountyDocument = HydratedDocument<County>;

@Schema({ timestamps: true, collection: 'county' })
export class County {
  @Prop({ required: true })
  value: string;

  @Prop({ required: true })
  text: string;

  @Prop({ default: true })
  is_active: boolean;
}

export const CountySchema = SchemaFactory.createForClass(County);
