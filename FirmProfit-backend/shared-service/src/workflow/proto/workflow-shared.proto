// Client sync messages
message SyncClientsRequest {
  string access_token = 1;
}

message SyncClientsResponse {
  bool success = 1;
  string message = 2;
  repeated ClientData clients = 3;
  int32 synced_count = 4;
  int32 total_count = 5;
}

message ClientData {
  string id = 1;
  string name = 2;
  string first_name = 3;
  string last_name = 4;
  string email = 5;
  string phone = 6;
  string address = 7;
  string city = 8;
  string state = 9;
  string zip = 10;
  string my_case_id = 11;
  string created_at = 12;
  string updated_at = 13;
}

// Location sync methods
rpc SyncLocationsFromMyCase(GetLocationRequest) returns (SyncLocationsResponse);
rpc SyncClientsFromMyCase(SyncClientsRequest) returns (SyncClientsResponse);
rpc GetLocationList(google.protobuf.Empty) returns (LocationListResponse);
rpc GetLocationById(GetLocationByIdRequest) returns (LocationResponse); 