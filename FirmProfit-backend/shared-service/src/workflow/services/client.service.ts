import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, UpdateQuery } from 'mongoose';
import { Client, ClientDocument } from '../entities/client.entity';
export interface CreateClientDto {
  my_case_client_id: number;
  name?: string;
  first_name: string;
  middle_initial?: string;
  middle_name?: string;
  last_name: string;
  email?: string;
  cell_phone_number?: string;
  work_phone_number?: string;
  home_phone_number?: string;
  address?: {
    address1?: string;
    address2?: string;
    city?: string;
    state?: string;
    country?: string;
    zip_code?: string;
  };
  notes?: string;
  birthdate?: string;
  archived?: boolean;
  people_group?: {
    id?: number;
  };
  is_active?: boolean;
  type?: string;
  firm_uuid?: string;
  my_case_created_at?: string;
  my_case_updated_at?: string;
}

export interface UpdateClientDto {
  name?: string;
  first_name?: string;
  middle_initial?: string;
  middle_name?: string;
  last_name?: string;
  email?: string;
  cell_phone_number?: string;
  work_phone_number?: string;
  home_phone_number?: string;
  address?: {
    address1?: string;
    address2?: string;
    city?: string;
    state?: string;
    country?: string;
    zip_code?: string;
  };
  notes?: string;
  birthdate?: string;
  archived?: boolean;
  people_group?: {
    id?: number;
  };
  is_active?: boolean;
  type?: string;
  firm_uuid?: string;
  my_case_updated_at?: string;
}

export interface FindClientsOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
  firm_uuid?: string;
  is_active?: boolean;
  archived?: boolean;
}

export interface ClientListResponse {
  clients: Client[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
export interface MyCaseClientInterface {
  id: number;
  email: string;
  first_name: string;
  middle_initial: string;
  middle_name: string;
  last_name: string;
  address?: {
    address1?: string;
    address2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    country?: string;
  };
  cell_phone_number?: string;
  work_phone_number?: string;
  home_phone_number?: string;
  fax_phone_number?: string;
  contact_group?: string;
  people_group?: {
    id: number;
  };
  notes?: string;
  birthdate?: string;
  archived?: boolean;
  cases?: Array<{
    id: number;
  }>;
  custom_field_values?: Array<{
    custom_field: {
      id: number;
      field_type: string;
    };
    value: string;
    created_at: string;
    updated_at: string;
  }>;
  updated_at?: string;
  created_at?: string;
}

export interface ClientResponseDto {
  id: string;
  name: string;
  first_name: string;
  last_name: string;
  my_case_client_id: string;
  is_active: boolean;
  email?: string;
  created_at: string;
  updated_at: string;
}

export interface SyncClientsResponseDto {
  clients: ClientResponseDto[];
  synced_count: number;
  total_count: number;
  message: string;
}

export interface MyCaseClientListResponse {
  data: MyCaseClientInterface[];
  total: number;
  page: number;
  per_page: number;
}

@Injectable()
export class ClientService {
  private readonly logger = new Logger(ClientService.name);

  constructor(
    @InjectModel(Client.name)
    private readonly clientModel: Model<Client>,
  ) {}

  /**
   * Create a new client
   */
  async createClient(createClientDto: CreateClientDto): Promise<Client> {
    try {
      this.logger.log(
        `Creating client with MyCase ID: ${createClientDto.my_case_client_id}`,
      );

      // Check if client already exists
      const existingClient = await this.clientModel.findOne({
        my_case_client_id: createClientDto.my_case_client_id,
      });

      if (existingClient) {
        throw new ConflictException(
          `Client with MyCase ID ${createClientDto.my_case_client_id} already exists`,
        );
      }

      // Generate full name if not provided
      if (!createClientDto.name) {
        createClientDto.name = this.generateFullName(
          createClientDto.first_name,
          createClientDto.middle_initial || createClientDto.middle_name,
          createClientDto.last_name,
        );
      }

      const client = new this.clientModel(createClientDto);
      const savedClient = await client.save();

      this.logger.log(
        `Client created successfully with ID: ${savedClient._id}`,
      );
      return savedClient.toObject();
    } catch (error) {
      this.logger.error(`Error creating client: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update client by MyCase ID
   */
  async updateByMyCaseId(
    myCaseClientId: number,
    updateClientDto: UpdateClientDto,
  ): Promise<Client> {
    try {
      this.logger.log(`Updating client with MyCase ID: ${myCaseClientId}`);

      // Generate full name if name components are updated
      if (
        updateClientDto.first_name ||
        updateClientDto.middle_initial ||
        updateClientDto.middle_name ||
        updateClientDto.last_name
      ) {
        const currentClient = await this.findByMyCaseId(myCaseClientId);
        if (currentClient) {
          updateClientDto.name = this.generateFullName(
            updateClientDto.first_name || currentClient.first_name,
            updateClientDto.middle_initial ||
              updateClientDto.middle_name ||
              currentClient.middle_initial ||
              currentClient.middle_name,
            updateClientDto.last_name || currentClient.last_name,
          );
        }
      }

      const updatedClient = await this.clientModel.findOneAndUpdate(
        { my_case_client_id: myCaseClientId },
        { $set: updateClientDto },
        { new: true, runValidators: true },
      );

      if (!updatedClient) {
        throw new NotFoundException(
          `Client with MyCase ID ${myCaseClientId} not found`,
        );
      }

      this.logger.log(
        `Client updated successfully with ID: ${updatedClient._id}`,
      );
      return updatedClient.toObject();
    } catch (error) {
      this.logger.error(`Error updating client: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete client by MyCase ID
   */
  async deleteByMyCaseId(myCaseClientId: number): Promise<void> {
    try {
      this.logger.log(`Deleting client with MyCase ID: ${myCaseClientId}`);

      const result = await this.clientModel.findOneAndDelete({
        my_case_client_id: myCaseClientId,
      });

      if (!result) {
        throw new NotFoundException(
          `Client with MyCase ID ${myCaseClientId} not found`,
        );
      }

      this.logger.log(
        `Client deleted successfully with MyCase ID: ${myCaseClientId}`,
      );
    } catch (error) {
      this.logger.error(`Error deleting client: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Soft delete client by MyCase ID (mark as inactive)
   */
  async softDeleteByMyCaseId(myCaseClientId: number): Promise<Client> {
    try {
      this.logger.log(`Soft deleting client with MyCase ID: ${myCaseClientId}`);

      const updatedClient = await this.clientModel.findOneAndUpdate(
        { my_case_client_id: myCaseClientId },
        { $set: { is_active: false, archived: true } },
        { new: true, runValidators: true },
      );

      if (!updatedClient) {
        throw new NotFoundException(
          `Client with MyCase ID ${myCaseClientId} not found`,
        );
      }

      this.logger.log(
        `Client soft deleted successfully with MyCase ID: ${myCaseClientId}`,
      );
      return updatedClient.toObject();
    } catch (error) {
      this.logger.error(
        `Error soft deleting client: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Find multiple clients with pagination and filtering
   */
  async findClients(
    options: FindClientsOptions = {},
  ): Promise<ClientListResponse> {
    try {
      const {
        page = 1,
        limit = 10,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        search,
        firm_uuid,
        is_active,
        archived,
      } = options;

      // Build filter query
      const filter: FilterQuery<ClientDocument> = {};

      if (search) {
        filter.$or = [
          { name: { $regex: search, $options: 'i' } },
          { first_name: { $regex: search, $options: 'i' } },
          { last_name: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } },
        ];
      }

      if (firm_uuid) {
        filter.firm_uuid = firm_uuid;
      }

      if (is_active !== undefined) {
        filter.is_active = is_active;
      }

      if (archived !== undefined) {
        filter.archived = archived;
      }

      // Build sort query
      const sort: { [key: string]: 1 | -1 } = {};
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

      // Calculate pagination
      const skip = (page - 1) * limit;

      // Execute queries
      const [clients, total] = await Promise.all([
        this.clientModel.find(filter).sort(sort).skip(skip).limit(limit).lean(),
        this.clientModel.countDocuments(filter),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        clients: clients as Client[],
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      this.logger.error(`Error finding clients: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Create or update client (upsert operation)
   */
  async upsertClient(
    clientData: CreateClientDto,
  ): Promise<{ client: Client; created: boolean }> {
    try {
      this.logger.log(
        `Upserting client with MyCase ID: ${clientData.my_case_client_id}`,
      );

      // Generate full name if not provided
      if (!clientData.name) {
        clientData.name = this.generateFullName(
          clientData.first_name,
          clientData.middle_initial || clientData.middle_name,
          clientData.last_name,
        );
      }

      console.log(
        '🚀 ~ ClientService ~ upsertClient ~ clientData:',
        clientData,
      );

      const client = await this.clientModel.findOneAndUpdate(
        { my_case_client_id: clientData.my_case_client_id },
        { $set: clientData },
        {
          new: true,
          upsert: true,
          runValidators: true,
          setDefaultsOnInsert: true,
        },
      );

      // Check if document was created or updated by comparing timestamps
      const created =
        !client.get('updatedAt') ||
        client.get('createdAt')?.getTime() ===
          client.get('updatedAt')?.getTime();

      this.logger.log(
        `Client ${created ? 'created' : 'updated'} successfully with ID: ${client._id}`,
      );
      return { client: client.toObject(), created };
    } catch (error) {
      this.logger.error(
        `Error upserting client: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get client statistics
   */
  async getClientStats(firm_uuid?: string): Promise<any> {
    try {
      const matchStage = firm_uuid ? { firm_uuid } : {};

      const stats = await this.clientModel.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: null,
            total: { $sum: 1 },
            active: { $sum: { $cond: [{ $eq: ['$is_active', true] }, 1, 0] } },
            inactive: {
              $sum: { $cond: [{ $eq: ['$is_active', false] }, 1, 0] },
            },
            archived: { $sum: { $cond: [{ $eq: ['$archived', true] }, 1, 0] } },
          },
        },
      ]);

      return stats[0] || { total: 0, active: 0, inactive: 0, archived: 0 };
    } catch (error) {
      this.logger.error(
        `Error getting client statistics: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Find client by MyCase ID
   */
  async findByMyCaseId(myCaseClientId: number): Promise<Client | null> {
    try {
      const client = await this.clientModel.findOne({
        my_case_client_id: myCaseClientId,
      });

      return client ? client.toObject() : null;
    } catch (error) {
      this.logger.error(
        `Error finding client by MyCase ID ${myCaseClientId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Helper method to generate full name
   */
  private generateFullName(
    firstName: string,
    middleName?: string,
    lastName?: string,
  ): string {
    const nameParts = [firstName];

    if (middleName) {
      nameParts.push(middleName);
    }

    if (lastName) {
      nameParts.push(lastName);
    }

    return nameParts.join(' ').trim();
  }

  async saveClientFromMyCase(
    myCaseClient: MyCaseClientInterface,
  ): Promise<ClientResponseDto> {
    try {
      const existingClient = await this.clientModel.findOne({
        my_case_client_id: myCaseClient.id.toString(),
      });

      const clientName =
        `${myCaseClient.first_name || ''} ${myCaseClient.last_name || ''}`.trim();

      if (existingClient) {
        this.logger.log(
          `Client '${clientName}' already exists, updating if needed`,
        );

        // Update existing client with latest data
        existingClient.name = clientName;
        existingClient.first_name = myCaseClient.first_name || '';
        existingClient.last_name = myCaseClient.last_name || '';
        existingClient.email = myCaseClient.email || '';
        existingClient.is_active = !myCaseClient.archived;

        await existingClient.save();

        return this.mapToResponseDto(existingClient);
      }

      const newClient = new this.clientModel({
        name: clientName,
        first_name: myCaseClient.first_name || '',
        last_name: myCaseClient.last_name || '',
        my_case_client_id: myCaseClient.id.toString(),
        is_active: !myCaseClient.archived,
        email: myCaseClient.email || '',
      });

      const savedClient = await newClient.save();
      this.logger.log(`Saved new client: ${savedClient.name}`);

      return this.mapToResponseDto(savedClient);
    } catch (error) {
      this.logger.error(
        `Error saving client '${myCaseClient.first_name} ${myCaseClient.last_name}': ${error.message}`,
      );
      throw error;
    }
  }

  async syncMultipleClientsFromMyCase(
    myCaseClients: MyCaseClientInterface[],
  ): Promise<SyncClientsResponseDto> {
    try {
      this.logger.log(
        `Starting sync of ${myCaseClients.length} clients from MyCase`,
      );

      const syncedClients: ClientResponseDto[] = [];

      for (const myCaseClient of myCaseClients) {
        try {
          const savedClient = await this.saveClientFromMyCase(myCaseClient);
          syncedClients.push(savedClient);
        } catch (error) {
          this.logger.error(
            `Failed to sync client '${myCaseClient.first_name} ${myCaseClient.last_name}': ${error.message}`,
          );
          // Continue with other clients even if one fails
        }
      }

      const result: SyncClientsResponseDto = {
        clients: syncedClients,
        synced_count: syncedClients.length,
        total_count: myCaseClients.length,
        message: `Successfully synced ${syncedClients.length} out of ${myCaseClients.length} clients`,
      };

      this.logger.log(result.message);
      return result;
    } catch (error) {
      this.logger.error(`Error syncing clients: ${error.message}`);
      throw error;
    }
  }

  async getAllClients(): Promise<ClientResponseDto[]> {
    try {
      const clients = await this.clientModel.find({ is_active: true });
      return clients.map((client) => this.mapToResponseDto(client));
    } catch (error) {
      this.logger.error(`Error fetching clients: ${error.message}`);
      throw error;
    }
  }

  async getClientById(id: string): Promise<ClientResponseDto> {
    try {
      const client = await this.clientModel.findById(id);
      if (!client) {
        throw new NotFoundException(`Client with ID ${id} not found`);
      }
      return this.mapToResponseDto(client);
    } catch (error) {
      this.logger.error(`Error fetching client by ID ${id}: ${error.message}`);
      throw error;
    }
  }

  private mapToResponseDto(client: any): ClientResponseDto {
    return {
      id: client._id.toString(),
      name: client.name,
      first_name: client.first_name,
      last_name: client.last_name,
      my_case_client_id: client.my_case_client_id,
      is_active: client.is_active,
      email: client.email,
      created_at: client.createdAt?.toISOString() || '',
      updated_at: client.updatedAt?.toISOString() || '',
    };
  }
}
