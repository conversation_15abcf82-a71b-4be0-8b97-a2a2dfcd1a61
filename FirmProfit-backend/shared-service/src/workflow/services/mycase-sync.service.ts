import { Injectable, Logger } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { MyCaseService } from '../mycase.service';
import { LocationService } from './location.service';
import { WorkflowService } from '../workflow.service';
import { GetLocationDto, SyncLocationsResponseDto } from '../dto/location.dto';

@Injectable()
export class MyCaseSyncService {
  private readonly logger = new Logger(MyCaseSyncService.name);

  constructor(
    private readonly myCaseService: MyCaseService,
    private readonly locationService: LocationService,
    private readonly workflowService: WorkflowService,
  ) {}

  // Run weekly on day 6 (Saturday) at 2:00 AM
  @Cron('0 2 * * 6')
  async weeklyMyCaseSync(accessToken?: string): Promise<void> {
    this.logger.log('Starting weekly MyCase sync...');

    try {
      const syncResults = {
        locations: 0,
        clients: 0,
        matters: 0,
        events: 0,
        errors: [],
      };

      // If no access token provided, skip sync (will be handled by manual trigger)
      if (!accessToken) {
        this.logger.warn(
          'No access token provided for weekly sync. Skipping automated sync.',
        );
        return;
      }

      // 1. Sync Locations
      this.logger.log('Syncing locations from MyCase...');
      const locationResult = await this.syncLocationsFromMyCase(accessToken);
      syncResults.locations = locationResult.synced_count;

      // 2. Sync Clients (basic implementation)
      this.logger.log('Syncing clients from MyCase...');
      const clientResult = await this.syncClientsFromMyCase(accessToken);
      syncResults.clients = clientResult.syncedCount;

      // 3. Sync Matters (placeholder - to be implemented)
      this.logger.log('Syncing matters from MyCase...');
      const matterResult = await this.syncMattersFromMyCase(accessToken);
      syncResults.matters = matterResult.syncedCount;

      // 4. Sync Events (with previous day filter for cron jobs)
      this.logger.log('Syncing events from MyCase (previous day filter)...');
      const eventResult = await this.syncEventsFromMyCase(accessToken, true);
      syncResults.events = eventResult.syncedCount;

      this.logger.log(
        `Weekly MyCase sync completed successfully: ${JSON.stringify(syncResults)}`,
      );
    } catch (error) {
      this.logger.error('Error during weekly MyCase sync:', error);
      throw error;
    }
  }

  // Manual sync trigger for testing
  async triggerManualSync(accessToken?: string): Promise<any> {
    this.logger.log('Manual MyCase sync triggered...');

    if (!accessToken) {
      throw new Error('Access token is required for manual sync');
    }

    return await this.weeklyMyCaseSync(accessToken);
  }

  // Sync locations using existing method
  async syncLocationsFromMyCase(
    accessToken: string,
  ): Promise<SyncLocationsResponseDto> {
    try {
      this.logger.log('Starting location sync from MyCase...');

      const getLocationDto: GetLocationDto = {
        access_token: accessToken,
      };

      return await this.workflowService.syncLocationsFromMyCase(getLocationDto);
    } catch (error) {
      this.logger.error('Error syncing locations from MyCase:', error);
      throw error;
    }
  }

  // Sync clients from MyCase (basic implementation)
  async syncClientsFromMyCase(_accessToken: string): Promise<any> {
    this.logger.log('Starting clients sync from MyCase');

    try {
      const result =
        await this.workflowService.syncClientsFromMyCase(_accessToken);
      this.logger.log(`Clients sync completed: ${result.message}`);
      return result;
    } catch (error) {
      this.logger.error(`Error syncing clients from MyCase: ${error.message}`);
      throw error;
    }
  }

  // Sync matters from MyCase (placeholder implementation)
  async syncMattersFromMyCase(
    accessToken: string,
  ): Promise<{ syncedCount: number; message: string }> {
    try {
      this.logger.log('Starting matter sync from MyCase with access token');

      if (!accessToken) {
        this.logger.warn('No access token provided for matter sync');
        return {
          syncedCount: 0,
          message: 'No access token provided for matter sync',
        };
      }

      // Call the actual workflow service method
      const result =
        await this.workflowService.syncMattersFromMyCase(accessToken);

      this.logger.log(
        `Matter sync completed: ${result.synced_count} matters synced`,
      );

      return {
        syncedCount: result.synced_count,
        message: result.message,
      };
    } catch (error) {
      this.logger.error(`Error syncing matters from MyCase: ${error.message}`);
      return {
        syncedCount: 0,
        message: `Error syncing matters: ${error.message}`,
      };
    }
  }

  // Sync events from MyCase (placeholder implementation)
  async syncEventsFromMyCase(
    accessToken: string,
    usePreviousDayFilter: boolean = false,
  ): Promise<{ syncedCount: number; message: string }> {
    try {
      this.logger.log(
        `Starting event sync from MyCase with access token: ${accessToken ? 'provided' : 'not provided'}${usePreviousDayFilter ? ' (previous day filter)' : ''}`,
      );

      if (!accessToken) {
        this.logger.warn('Access token not provided for event sync');
        return {
          syncedCount: 0,
          message: 'Access token not provided',
        };
      }

      // Call the actual workflow service method with the filter parameter
      const result = await this.workflowService.syncEventsFromMyCase(
        accessToken,
        usePreviousDayFilter,
      );

      this.logger.log(
        `Event sync completed: ${result.synced_count} events synced${usePreviousDayFilter ? ' (previous day filter)' : ''}`,
      );

      return {
        syncedCount: result.synced_count,
        message: result.message,
      };
    } catch (error) {
      this.logger.error(`Error syncing events from MyCase: ${error.message}`);
      return {
        syncedCount: 0,
        message: `Error syncing events: ${error.message}`,
      };
    }
  }

  // Health check method
  async getLastSyncStatus(): Promise<any> {
    try {
      // This could be enhanced to store sync history in database
      return {
        lastSync: new Date(),
        status: 'healthy',
        nextSync: this.getNextSyncDate(),
      };
    } catch (error) {
      this.logger.error('Error getting sync status:', error);
      return {
        lastSync: null,
        status: 'error',
        error: error.message,
      };
    }
  }

  private getNextSyncDate(): Date {
    const now = new Date();
    const nextSaturday = new Date(now);
    nextSaturday.setDate(now.getDate() + (6 - now.getDay()));
    nextSaturday.setHours(2, 0, 0, 0);

    // If it's already past this Saturday's sync time, get next Saturday
    if (nextSaturday <= now) {
      nextSaturday.setDate(nextSaturday.getDate() + 7);
    }

    return nextSaturday;
  }
}
