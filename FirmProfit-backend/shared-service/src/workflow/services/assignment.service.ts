import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { User } from '../entities/user.entity';
import { TaskAssignment, TaskAssignmentDocument } from '../entities/task-assignment.entity';
import { TaskExecution } from '../entities/task-execution.entity';

export interface AssigneeOption {
    id: string;
    name: string;
    type: 'user' | 'role';
    category?: string;
    email?: string;
    description?: string;
}

export interface AssignmentRequest {
    task_execution_id: string;
    assignees: Array<{
        id: string;
        type: 'user' | 'role';
    }>;
    assigned_by: string;
    notes?: string;
}

@Injectable()
export class AssignmentService {
    private readonly logger = new Logger(AssignmentService.name);

    constructor(
        @InjectModel(User.name) private userModel: Model<User>,
        @InjectModel(TaskAssignment.name) private taskAssignmentModel: Model<TaskAssignmentDocument>,
        @InjectModel(TaskExecution.name) private taskExecutionModel: Model<TaskExecution>,
    ) { }

    /**
     * Get all available assignees (users and roles) for workflow assignment
     */
    async getAvailableAssignees(search?: string): Promise<AssigneeOption[]> {
        try {
            const assignees: AssigneeOption[] = [];

            // Get active users
            const userQuery = { is_active: true, is_deleted: false };
            if (search) {
                userQuery['$or'] = [
                    { first_name: { $regex: search, $options: 'i' } },
                    { last_name: { $regex: search, $options: 'i' } },
                    { email: { $regex: search, $options: 'i' } },
                ];
            }

            const users = await this.userModel.find(userQuery).exec();
            users.forEach(user => {
                assignees.push({
                    id: user._id.toString(),
                    name: `${user.first_name} ${user.last_name}`,
                    type: 'user',
                    email: user.email,
                });
            });

            // Get active roles (which can represent groups)
            // Since roles are stored in PostgreSQL, we'll need to fetch them via API or direct DB connection
            // For now, we'll use a predefined list of roles that represent groups
            const roleGroups = [
                { id: 1, name: 'Court Notice Paralegal', category: 'Paralegal', description: 'Paralegals assigned to handle court notice workflows' },
                { id: 2, name: 'General Paralegal', category: 'Paralegal', description: 'General paralegal group for various legal tasks' },
                { id: 3, name: 'Senior Attorney', category: 'Attorney', description: 'Senior attorneys handling complex cases' },
                { id: 4, name: 'Junior Attorney', category: 'Attorney', description: 'Junior attorneys for case preparation and research' },
                { id: 5, name: 'Intake Specialist', category: 'Intake', description: 'Specialists handling client intake and initial case assessment' },
                { id: 6, name: 'Billing Specialist', category: 'Billing', description: 'Specialists handling billing and financial matters' },
            ];

            roleGroups.forEach(role => {
                if (!search ||
                    role.name.toLowerCase().includes(search.toLowerCase()) ||
                    role.description.toLowerCase().includes(search.toLowerCase())) {
                    assignees.push({
                        id: role.id.toString(),
                        name: role.name,
                        type: 'role',
                        category: role.category,
                        description: role.description,
                    });
                }
            });

            return assignees;
        } catch (error) {
            this.logger.error('Error getting available assignees:', error);
            throw error;
        }
    }

    /**
     * Get assignees by category (Paralegal, Attorney, Intake, Billing)
     */
    async getAssigneesByCategory(category: string, search?: string): Promise<AssigneeOption[]> {
        try {
            const assignees: AssigneeOption[] = [];

            // Get users by role category
            const userQuery = { is_active: true, is_deleted: false };
            if (search) {
                userQuery['$or'] = [
                    { first_name: { $regex: search, $options: 'i' } },
                    { last_name: { $regex: search, $options: 'i' } },
                    { email: { $regex: search, $options: 'i' } },
                ];
            }

            const users = await this.userModel.find(userQuery).populate('roleId').exec();
            users.forEach(user => {
                // Check if user's role matches the category
                if (user.roleId && this.matchesCategory(user.roleId, category)) {
                    assignees.push({
                        id: user._id.toString(),
                        name: `${user.first_name} ${user.last_name}`,
                        type: 'user',
                        email: user.email,
                        category: category,
                    });
                }
            });

            // Get roles by category
            const roleGroups = [
                { id: 1, name: 'Court Notice Paralegal', category: 'Paralegal', description: 'Paralegals assigned to handle court notice workflows' },
                { id: 2, name: 'General Paralegal', category: 'Paralegal', description: 'General paralegal group for various legal tasks' },
                { id: 3, name: 'Senior Attorney', category: 'Attorney', description: 'Senior attorneys handling complex cases' },
                { id: 4, name: 'Junior Attorney', category: 'Attorney', description: 'Junior attorneys for case preparation and research' },
                { id: 5, name: 'Intake Specialist', category: 'Intake', description: 'Specialists handling client intake and initial case assessment' },
                { id: 6, name: 'Billing Specialist', category: 'Billing', description: 'Specialists handling billing and financial matters' },
            ];

            roleGroups.forEach(role => {
                if (role.category === category &&
                    (!search ||
                        role.name.toLowerCase().includes(search.toLowerCase()) ||
                        role.description.toLowerCase().includes(search.toLowerCase()))) {
                    assignees.push({
                        id: role.id.toString(),
                        name: role.name,
                        type: 'role',
                        category: role.category,
                        description: role.description,
                    });
                }
            });

            return assignees;
        } catch (error) {
            this.logger.error('Error getting assignees by category:', error);
            throw error;
        }
    }

    /**
     * Get role members for a specific role
     */
    async getRoleMembers(roleId: string): Promise<AssigneeOption[]> {
        try {
            // Find users who have this role assigned
            const users = await this.userModel.find({
                roleId: parseInt(roleId),
                is_active: true,
                is_deleted: false,
            }).exec();

            const members: AssigneeOption[] = [];
            users.forEach(user => {
                members.push({
                    id: user._id.toString(),
                    name: `${user.first_name} ${user.last_name}`,
                    type: 'user',
                    email: user.email,
                });
            });

            return members;
        } catch (error) {
            this.logger.error('Error getting role members:', error);
            throw error;
        }
    }

    /**
     * Assign users/roles to a task
     */
    async assignToTask(assignmentRequest: AssignmentRequest): Promise<void> {
        try {
            const { task_execution_id, assignees, assigned_by, notes } = assignmentRequest;

            this.logger.log(`AssignmentService.assignToTask called with: task_execution_id=${task_execution_id}, assigned_by=${assigned_by}, assignees=${JSON.stringify(assignees)}`);

            // Validate task execution exists
            const taskExecution = await this.taskExecutionModel.findById(task_execution_id).exec();
            if (!taskExecution) {
                throw new Error('Task execution not found');
            }

            // Deactivate existing assignments for this task
            await this.taskAssignmentModel.updateMany(
                { task_execution_id: new Types.ObjectId(task_execution_id), is_active: true },
                { is_active: false }
            );

            // Create new assignments
            const assignments = assignees.map(assignee => {
                this.logger.log(`Processing assignee: type=${assignee.type}, id=${assignee.id}`);

                // Validate ObjectId inputs
                if (assignee.type === 'user' && !Types.ObjectId.isValid(assignee.id)) {
                    throw new Error(`Invalid user ID format: ${assignee.id}. Must be a valid ObjectId`);
                }

                if (!Types.ObjectId.isValid(assigned_by)) {
                    throw new Error(`Invalid assigned_by format: ${assigned_by}. Must be a valid ObjectId`);
                }

                const assignment = {
                    task_execution_id: new Types.ObjectId(task_execution_id),
                    assigned_user: assignee.type === 'user' ? new Types.ObjectId(assignee.id) : null,
                    assigned_role: assignee.type === 'role' ? parseInt(assignee.id) : null,
                    assigned_by: new Types.ObjectId(assigned_by),
                    assignment_type: assignee.type,
                    notes,
                    is_active: true,
                    is_deleted: false,
                };

                this.logger.log(`Created assignment: ${JSON.stringify(assignment)}`);
                return assignment;
            });

            await this.taskAssignmentModel.insertMany(assignments);
            this.logger.log(`Assigned ${assignments.length} assignees to task ${task_execution_id}`);
        } catch (error) {
            this.logger.error('Error assigning to task:', error);
            throw error;
        }
    }

    /**
     * Get current assignments for a task
     */
    async getTaskAssignments(taskExecutionId: string): Promise<AssigneeOption[]> {
        try {
            const assignments = await this.taskAssignmentModel
                .find({
                    task_execution_id: new Types.ObjectId(taskExecutionId),
                    is_active: true,
                    is_deleted: false,
                })
                .populate('assigned_user')
                .exec();

            const assignees: AssigneeOption[] = [];

            for (const assignment of assignments) {
                if (assignment.assignment_type === 'user' && assignment.assigned_user) {
                    const user = assignment.assigned_user as any;
                    assignees.push({
                        id: user._id.toString(),
                        name: `${user.first_name} ${user.last_name}`,
                        type: 'user',
                        email: user.email,
                    });
                } else if (assignment.assignment_type === 'role' && assignment.assigned_role) {
                    // Get role information (you might want to fetch this from PostgreSQL)
                    const roleInfo = this.getRoleInfo(assignment.assigned_role);
                    assignees.push({
                        id: assignment.assigned_role.toString(),
                        name: roleInfo.name,
                        type: 'role',
                        category: roleInfo.category,
                        description: roleInfo.description,
                    });
                }
            }

            return assignees;
        } catch (error) {
            this.logger.error('Error getting task assignments:', error);
            throw error;
        }
    }

    /**
     * Remove assignment from a task
     */
    async removeAssignment(taskExecutionId: string, assigneeId: string, assignmentType: 'user' | 'role'): Promise<void> {
        try {
            const query = {
                task_execution_id: new Types.ObjectId(taskExecutionId),
                is_active: true,
                is_deleted: false,
            };

            if (assignmentType === 'user') {
                query['assigned_user'] = new Types.ObjectId(assigneeId);
            } else {
                query['assigned_role'] = parseInt(assigneeId);
            }

            await this.taskAssignmentModel.updateMany(query, { is_active: false });
            this.logger.log(`Removed ${assignmentType} assignment ${assigneeId} from task ${taskExecutionId}`);
        } catch (error) {
            this.logger.error('Error removing assignment:', error);
            throw error;
        }
    }

    /**
     * Helper method to check if a role matches a category
     */
    private matchesCategory(role: any, category: string): boolean {
        // This is a simplified mapping - you may need to adjust based on your role structure
        const roleCategoryMap = {
            'Paralegal': [1, 2], // role IDs for paralegals
            'Attorney': [3, 4], // role IDs for attorneys
            'Intake': [5], // role IDs for intake
            'Billing': [6], // role IDs for billing
        };

        const roleId = typeof role === 'object' ? role.id : role;
        return roleCategoryMap[category]?.includes(roleId) || false;
    }

    /**
     * Helper method to get role information
     */
    private getRoleInfo(roleId: number): { name: string; category: string; description: string } {
        const roleMap = {
            1: { name: 'Court Notice Paralegal', category: 'Paralegal', description: 'Paralegals assigned to handle court notice workflows' },
            2: { name: 'General Paralegal', category: 'Paralegal', description: 'General paralegal group for various legal tasks' },
            3: { name: 'Senior Attorney', category: 'Attorney', description: 'Senior attorneys handling complex cases' },
            4: { name: 'Junior Attorney', category: 'Attorney', description: 'Junior attorneys for case preparation and research' },
            5: { name: 'Intake Specialist', category: 'Intake', description: 'Specialists handling client intake and initial case assessment' },
            6: { name: 'Billing Specialist', category: 'Billing', description: 'Specialists handling billing and financial matters' },
        };

        return roleMap[roleId] || { name: 'Unknown Role', category: 'Unknown', description: 'Role information not available' };
    }
} 