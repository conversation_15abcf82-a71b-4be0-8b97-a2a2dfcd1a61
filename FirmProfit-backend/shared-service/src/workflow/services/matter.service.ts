import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, Types } from 'mongoose';
import { Client } from '../entities/client.entity';
import {
  Matter,
  MatterDocument,
  ClientReference,
  StaffReference,
  CaseStageReference,
  PracticeAreaReference,
  BillingContact,
} from '../entities/matter.entity';

export interface CreateMatterDto {
  my_case_matter_id: number;
  name: string;
  case_number?: string;
  description?: string;
  opened_date?: string;
  closed_date?: string;
  sol_date?: string;
  outstanding_balance?: number;
  clients?: ClientReference[];
  staff?: StaffReference[];
  case_stage_reference?: CaseStageReference;
  practice_area_reference?: PracticeAreaReference;
  billing_contact?: BillingContact;
  potential_case?: boolean;
  is_active?: boolean;
  firm_uuid?: string;
  client_id?: Types.ObjectId; // For backward compatibility
  ex_county_of_arrest?: string; // For backward compatibility
  my_case_created_at?: string;
  my_case_updated_at?: string;
}

export interface UpdateMatterDto {
  name?: string;
  case_number?: string;
  description?: string;
  opened_date?: string;
  closed_date?: string;
  sol_date?: string;
  outstanding_balance?: number;
  clients?: ClientReference[];
  staff?: StaffReference[];
  case_stage_reference?: CaseStageReference;
  practice_area_reference?: PracticeAreaReference;
  billing_contact?: BillingContact;
  potential_case?: boolean;
  is_active?: boolean;
  firm_uuid?: string;
  client_id?: Types.ObjectId; // For backward compatibility
  ex_county_of_arrest?: string; // For backward compatibility
  my_case_updated_at?: string;
}

export interface FindMattersOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
  firm_uuid?: string;
  is_active?: boolean;
  client_id?: number;
  staff_id?: number;
  potential_case?: boolean;
}

export interface MatterListResponse {
  matters: Matter[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface MyCaseMatterInterface {
  id: number;
  name: string;
  case_number: string;
  description?: string;
  opened_date?: string;
  closed_date?: string;
  sol_date?: string;
  practice_area?: string;
  case_stage?: string;
  status?: string;
  outstanding_balance?: number;
  billing_contact?: {
    id: number;
  };
  billing_type?: string;
  contacts?: any;
  clients?: Array<{
    id: number;
    first_name: string;
    last_name: string;
    middle_initial?: string;
    middle_name?: string;
    email?: string;
    cell_phone_number?: string;
    work_phone_number?: string;
    home_phone_number?: string;
    fax_phone_number?: string;
    contact_group?: string;
    birthdate?: string;
    created_at?: string;
    updated_at?: string;
  }>;
  companies?: Array<{
    id: number;
  }>;
  staff?: Array<{
    id: number;
    lead_lawyer?: boolean;
    originating_lawyer?: boolean;
    case_rate?: string;
  }>;
  custom_field_values?: Array<{
    custom_field: {
      id: number;
      field_type: string;
    };
    value: string;
    created_at: string;
    updated_at: string;
  }>;
  office?: {
    id: number;
    name: string;
    fax_phone?: string;
    main_phone?: string;
    address?: {
      address1?: string;
      address2?: string;
      city?: string;
      state?: string;
      zip_code?: string;
      country?: string;
    };
    updated_at?: string;
    created_at?: string;
  };
  updated_at?: string;
  created_at?: string;
}

export interface MatterResponseDto {
  id: string;
  name: string;
  client_id: string;
  is_active: boolean;
  my_case_matter_id: string;
  case_number?: string;
  ex_county_of_arrest?: string;
  created_at: string;
  updated_at: string;
}

export interface SyncMattersResponseDto {
  matters: MatterResponseDto[];
  synced_count: number;
  total_count: number;
  message: string;
}

export interface MyCaseMatterListResponse {
  data: MyCaseMatterInterface[];
  total: number;
  page: number;
  per_page: number;
}

@Injectable()
export class MatterService {
  private readonly logger = new Logger(MatterService.name);

  constructor(
    @InjectModel(Matter.name)
    private readonly matterModel: Model<MatterDocument>,
    @InjectModel(Client.name)
    private readonly clientModel: Model<Client>,
  ) {}

  /**
   * Create a new matter
   */
  async createMatter(createMatterDto: CreateMatterDto): Promise<Matter> {
    try {
      this.logger.log(
        `Creating matter with MyCase ID: ${createMatterDto.my_case_matter_id}`,
      );

      // Check if matter already exists
      const existingMatter = await this.matterModel.findOne({
        my_case_matter_id: createMatterDto.my_case_matter_id,
      });

      if (existingMatter) {
        throw new ConflictException(
          `Matter with MyCase ID ${createMatterDto.my_case_matter_id} already exists`,
        );
      }

      const matter = new this.matterModel(createMatterDto);
      const savedMatter = await matter.save();

      this.logger.log(
        `Matter created successfully with ID: ${savedMatter._id}`,
      );
      return savedMatter.toObject();
    } catch (error) {
      this.logger.error(`Error creating matter: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Find matter by MyCase ID
   */
  async findByMyCaseId(myCaseMatterId: number): Promise<Matter | null> {
    try {
      const matter = await this.matterModel.findOne({
        my_case_matter_id: myCaseMatterId,
      });

      return matter ? matter.toObject() : null;
    } catch (error) {
      this.logger.error(
        `Error finding matter by MyCase ID ${myCaseMatterId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Find matter by MongoDB ID
   */
  async findById(id: string): Promise<Matter | null> {
    try {
      const matter = await this.matterModel.findById(id);
      return matter ? matter.toObject() : null;
    } catch (error) {
      this.logger.error(
        `Error finding matter by ID ${id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Update matter by MyCase ID
   */
  async updateByMyCaseId(
    myCaseMatterId: number,
    updateMatterDto: UpdateMatterDto,
  ): Promise<Matter> {
    try {
      this.logger.log(`Updating matter with MyCase ID: ${myCaseMatterId}`);

      const updatedMatter = await this.matterModel.findOneAndUpdate(
        { my_case_matter_id: myCaseMatterId },
        { $set: updateMatterDto },
        { new: true, runValidators: false },
      );

      if (!updatedMatter) {
        throw new NotFoundException(
          `Matter with MyCase ID ${myCaseMatterId} not found`,
        );
      }

      this.logger.log(
        `Matter updated successfully with ID: ${updatedMatter._id}`,
      );
      return updatedMatter.toObject();
    } catch (error) {
      this.logger.error(`Error updating matter: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete matter by MyCase ID
   */
  async deleteByMyCaseId(myCaseMatterId: number): Promise<void> {
    try {
      this.logger.log(`Deleting matter with MyCase ID: ${myCaseMatterId}`);

      const result = await this.matterModel.findOneAndDelete({
        my_case_matter_id: myCaseMatterId,
      });

      if (!result) {
        throw new NotFoundException(
          `Matter with MyCase ID ${myCaseMatterId} not found`,
        );
      }

      this.logger.log(
        `Matter deleted successfully with MyCase ID: ${myCaseMatterId}`,
      );
    } catch (error) {
      this.logger.error(`Error deleting matter: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Soft delete matter by MyCase ID (mark as inactive)
   */
  async softDeleteByMyCaseId(myCaseMatterId: number): Promise<Matter> {
    try {
      this.logger.log(`Soft deleting matter with MyCase ID: ${myCaseMatterId}`);

      const updatedMatter = await this.matterModel.findOneAndUpdate(
        { my_case_matter_id: myCaseMatterId },
        { $set: { is_active: false } },
        { new: true, runValidators: false },
      );

      if (!updatedMatter) {
        throw new NotFoundException(
          `Matter with MyCase ID ${myCaseMatterId} not found`,
        );
      }

      this.logger.log(
        `Matter soft deleted successfully with MyCase ID: ${myCaseMatterId}`,
      );
      return updatedMatter.toObject();
    } catch (error) {
      this.logger.error(
        `Error soft deleting matter: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Find multiple matters with pagination and filtering
   */
  async findMatters(
    options: FindMattersOptions = {},
  ): Promise<MatterListResponse> {
    try {
      const {
        page = 1,
        limit = 10,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        search,
        firm_uuid,
        is_active,
        client_id,
        staff_id,
        potential_case,
      } = options;

      // Build filter query
      const filter: FilterQuery<MatterDocument> = {};

      if (search) {
        filter.$or = [
          { name: { $regex: search, $options: 'i' } },
          { case_number: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } },
        ];
      }

      if (firm_uuid) {
        filter.firm_uuid = firm_uuid;
      }

      if (is_active !== undefined) {
        filter.is_active = is_active;
      }

      if (client_id !== undefined) {
        filter['clients.id'] = client_id;
      }

      if (staff_id !== undefined) {
        filter['staff.id'] = staff_id;
      }

      if (potential_case !== undefined) {
        filter.potential_case = potential_case;
      }

      // Build sort query
      const sort: { [key: string]: 1 | -1 } = {};
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

      // Calculate pagination
      const skip = (page - 1) * limit;

      // Execute queries
      const [matters, total] = await Promise.all([
        this.matterModel.find(filter).sort(sort).skip(skip).limit(limit).lean(),
        this.matterModel.countDocuments(filter),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        matters: matters as Matter[],
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      this.logger.error(`Error finding matters: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Create or update matter (upsert operation)
   */
  async upsertMatter(
    matterData: CreateMatterDto,
  ): Promise<{ matter: Matter; created: boolean }> {
    try {
      this.logger.log(
        `Upserting matter with MyCase ID: ${matterData.my_case_matter_id}`,
      );

      console.log(
        '🚀 ~ MatterService ~ upsertMatter ~ matterData:',
        matterData,
      );

      const matter = await this.matterModel.findOneAndUpdate(
        { my_case_matter_id: matterData.my_case_matter_id },
        { $set: matterData },
        {
          new: true,
          upsert: true,
          runValidators: false,
          setDefaultsOnInsert: true,
        },
      );

      // Check if document was created or updated by comparing timestamps
      const created =
        !matter.get('updatedAt') ||
        matter.get('createdAt')?.getTime() ===
          matter.get('updatedAt')?.getTime();

      this.logger.log(
        `Matter ${created ? 'created' : 'updated'} successfully with ID: ${matter._id}`,
      );
      return { matter: matter.toObject(), created };
    } catch (error) {
      this.logger.error(
        `Error upserting matter: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Find matters by client ID
   */
  async findByClientId(clientId: number): Promise<Matter[]> {
    try {
      const matters = await this.matterModel
        .find({
          'clients.id': clientId,
          is_active: true,
        })
        .lean();

      return matters as Matter[];
    } catch (error) {
      this.logger.error(
        `Error finding matters by client ID ${clientId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Find matters by staff ID
   */
  async findByStaffId(staffId: number): Promise<Matter[]> {
    try {
      const matters = await this.matterModel
        .find({
          'staff.id': staffId,
          is_active: true,
        })
        .lean();

      return matters as Matter[];
    } catch (error) {
      this.logger.error(
        `Error finding matters by staff ID ${staffId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get matter statistics
   */
  async getMatterStats(firm_uuid?: string): Promise<any> {
    try {
      const matchStage = firm_uuid ? { firm_uuid } : {};

      const stats = await this.matterModel.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: null,
            total: { $sum: 1 },
            active: { $sum: { $cond: [{ $eq: ['$is_active', true] }, 1, 0] } },
            inactive: {
              $sum: { $cond: [{ $eq: ['$is_active', false] }, 1, 0] },
            },
            potential_cases: {
              $sum: { $cond: [{ $eq: ['$potential_case', true] }, 1, 0] },
            },
            total_outstanding_balance: { $sum: '$outstanding_balance' },
            avg_outstanding_balance: { $avg: '$outstanding_balance' },
          },
        },
      ]);

      return (
        stats[0] || {
          total: 0,
          active: 0,
          inactive: 0,
          potential_cases: 0,
          total_outstanding_balance: 0,
          avg_outstanding_balance: 0,
        }
      );
    } catch (error) {
      this.logger.error(
        `Error getting matter statistics: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Add client to matter
   */
  async addClientToMatter(
    myCaseMatterId: number,
    clientId: number,
  ): Promise<Matter> {
    try {
      this.logger.log(`Adding client ${clientId} to matter ${myCaseMatterId}`);

      const updatedMatter = await this.matterModel.findOneAndUpdate(
        { my_case_matter_id: myCaseMatterId },
        { $addToSet: { clients: { id: clientId } } },
        { new: true, runValidators: false },
      );

      if (!updatedMatter) {
        throw new NotFoundException(
          `Matter with MyCase ID ${myCaseMatterId} not found`,
        );
      }

      this.logger.log(`Client added to matter successfully`);
      return updatedMatter.toObject();
    } catch (error) {
      this.logger.error(
        `Error adding client to matter: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Remove client from matter
   */
  async removeClientFromMatter(
    myCaseMatterId: number,
    clientId: number,
  ): Promise<Matter> {
    try {
      this.logger.log(
        `Removing client ${clientId} from matter ${myCaseMatterId}`,
      );

      const updatedMatter = await this.matterModel.findOneAndUpdate(
        { my_case_matter_id: myCaseMatterId },
        { $pull: { clients: { id: clientId } } },
        { new: true, runValidators: false },
      );

      if (!updatedMatter) {
        throw new NotFoundException(
          `Matter with MyCase ID ${myCaseMatterId} not found`,
        );
      }

      this.logger.log(`Client removed from matter successfully`);
      return updatedMatter.toObject();
    } catch (error) {
      this.logger.error(
        `Error removing client from matter: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Add staff to matter
   */
  async addStaffToMatter(
    myCaseMatterId: number,
    staffData: StaffReference,
  ): Promise<Matter> {
    try {
      this.logger.log(
        `Adding staff ${staffData.id} to matter ${myCaseMatterId}`,
      );

      const updatedMatter = await this.matterModel.findOneAndUpdate(
        { my_case_matter_id: myCaseMatterId },
        { $addToSet: { staff: staffData } },
        { new: true, runValidators: false },
      );

      if (!updatedMatter) {
        throw new NotFoundException(
          `Matter with MyCase ID ${myCaseMatterId} not found`,
        );
      }

      this.logger.log(`Staff added to matter successfully`);
      return updatedMatter.toObject();
    } catch (error) {
      this.logger.error(
        `Error adding staff to matter: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Remove staff from matter
   */
  async removeStaffFromMatter(
    myCaseMatterId: number,
    staffId: number,
  ): Promise<Matter> {
    try {
      this.logger.log(
        `Removing staff ${staffId} from matter ${myCaseMatterId}`,
      );

      const updatedMatter = await this.matterModel.findOneAndUpdate(
        { my_case_matter_id: myCaseMatterId },
        { $pull: { staff: { id: staffId } } },
        { new: true, runValidators: false },
      );

      if (!updatedMatter) {
        throw new NotFoundException(
          `Matter with MyCase ID ${myCaseMatterId} not found`,
        );
      }

      this.logger.log(`Staff removed from matter successfully`);
      return updatedMatter.toObject();
    } catch (error) {
      this.logger.error(
        `Error removing staff from matter: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async saveMatterFromMyCase(
    myCaseMatter: MyCaseMatterInterface,
  ): Promise<MatterResponseDto> {
    try {
      const existingMatter = await this.matterModel.findOne({
        my_case_matter_id: myCaseMatter.id.toString(),
      });

      // Find or create client
      let clientId: string | null = null;
      if (myCaseMatter.clients && myCaseMatter.clients.length > 0) {
        const primaryClient = myCaseMatter.clients[0]; // Use first client as primary
        clientId = await this.findOrCreateClient(primaryClient);
      }

      if (existingMatter) {
        this.logger.log(
          `Matter '${myCaseMatter.name}' already exists, updating if needed`,
        );

        // Update existing matter with latest data
        existingMatter.name = myCaseMatter.name;
        existingMatter.case_number = myCaseMatter.case_number || '';
        existingMatter.is_active = myCaseMatter.status !== 'closed';
        if (clientId) {
          existingMatter.client_id = new Types.ObjectId(clientId);
        }

        await existingMatter.save();
        return this.mapToResponseDto(existingMatter);
      }

      const newMatter = new this.matterModel({
        name: myCaseMatter.name,
        client_id: clientId ? new Types.ObjectId(clientId) : null,
        is_active: myCaseMatter.status !== 'closed',
        my_case_matter_id: myCaseMatter.id.toString(),
        case_number: myCaseMatter.case_number || '',
        ex_county_of_arrest: '', // Default empty, can be updated later
      });

      const savedMatter = await newMatter.save();
      this.logger.log(`Saved new matter: ${savedMatter.name}`);

      return this.mapToResponseDto(savedMatter);
    } catch (error) {
      this.logger.error(
        `Error saving matter '${myCaseMatter.name}': ${error.message}`,
      );
      throw error;
    }
  }

  private async findOrCreateClient(myCaseClient: any): Promise<string> {
    try {
      // First, try to find existing client by MyCase ID
      const existingClient = await this.clientModel.findOne({
        my_case_client_id: myCaseClient.id.toString(),
      });

      if (existingClient) {
        this.logger.log(
          `Found existing client: ${existingClient.name} (MyCase ID: ${myCaseClient.id})`,
        );
        return existingClient._id.toString();
      }

      // If not found, create new client
      const clientName =
        `${myCaseClient.first_name || ''} ${myCaseClient.last_name || ''}`.trim();

      const newClient = new this.clientModel({
        name: clientName,
        first_name: myCaseClient.first_name || '',
        last_name: myCaseClient.last_name || '',
        my_case_client_id: myCaseClient.id.toString(),
        is_active: true,
        email: myCaseClient.email || '',
      });

      const savedClient = await newClient.save();
      this.logger.log(
        `Created new client: ${savedClient.name} (MyCase ID: ${myCaseClient.id})`,
      );

      return savedClient._id.toString();
    } catch (error) {
      this.logger.error(
        `Error finding or creating client for MyCase ID ${myCaseClient.id}: ${error.message}`,
      );
      throw error;
    }
  }

  async syncMultipleMattersFromMyCase(
    myCaseMatters: MyCaseMatterInterface[],
  ): Promise<SyncMattersResponseDto> {
    try {
      this.logger.log(
        `Starting sync of ${myCaseMatters.length} matters from MyCase`,
      );

      const syncedMatters: MatterResponseDto[] = [];

      for (const myCaseMatter of myCaseMatters) {
        try {
          const savedMatter = await this.saveMatterFromMyCase(myCaseMatter);
          syncedMatters.push(savedMatter);
        } catch (error) {
          this.logger.error(
            `Failed to sync matter '${myCaseMatter.name}': ${error.message}`,
          );
          // Continue with other matters even if one fails
        }
      }

      const result: SyncMattersResponseDto = {
        matters: syncedMatters,
        synced_count: syncedMatters.length,
        total_count: myCaseMatters.length,
        message: `Successfully synced ${syncedMatters.length} out of ${myCaseMatters.length} matters`,
      };

      this.logger.log(result.message);
      return result;
    } catch (error) {
      this.logger.error(`Error syncing matters: ${error.message}`);
      throw error;
    }
  }

  async getAllMatters(): Promise<MatterResponseDto[]> {
    try {
      const matters = await this.matterModel.find({ is_active: true });
      return matters.map((matter) => this.mapToResponseDto(matter));
    } catch (error) {
      this.logger.error(`Error fetching matters: ${error.message}`);
      throw error;
    }
  }

  async getMatterById(id: string): Promise<MatterResponseDto> {
    try {
      const matter = await this.matterModel.findById(id);
      if (!matter) {
        throw new NotFoundException(`Matter with ID ${id} not found`);
      }
      return this.mapToResponseDto(matter);
    } catch (error) {
      this.logger.error(`Error fetching matter by ID ${id}: ${error.message}`);
      throw error;
    }
  }

  private mapToResponseDto(matter: any): MatterResponseDto {
    return {
      id: matter._id.toString(),
      name: matter.name,
      client_id: matter.client_id?.toString() || '',
      is_active: matter.is_active,
      my_case_matter_id: matter.my_case_matter_id,
      case_number: matter.case_number || '',
      ex_county_of_arrest: matter.ex_county_of_arrest || '',
      created_at: matter.createdAt?.toISOString() || '',
      updated_at: matter.updatedAt?.toISOString() || '',
    };
  }
}
