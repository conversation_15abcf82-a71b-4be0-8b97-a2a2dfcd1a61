import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  LocationResponseDto,
  SyncLocationsResponseDto,
  MyCaseLocationInterface,
} from '../dto/location.dto';
import { CourtLocation } from '../entities/court.location.entity';

@Injectable()
export class LocationService {
  private readonly logger = new Logger(LocationService.name);

  constructor(
    @InjectModel(CourtLocation.name)
    private readonly courtLocationModel: Model<CourtLocation>,
  ) {}

  async saveLocationFromMyCase(
    myCaseLocation: MyCaseLocationInterface,
  ): Promise<LocationResponseDto> {
    try {
      const existingLocation = await this.courtLocationModel.findOne({
        my_case_id: myCaseLocation.id,
      });

      if (existingLocation) {
        this.logger.log(
          `Location '${myCaseLocation.name}' already exists, updating if needed`,
        );

        // Update existing location with latest data
        existingLocation.name = myCaseLocation.name;
        existingLocation.value = myCaseLocation.name;
        existingLocation.text = myCaseLocation.name;
        existingLocation.is_active = myCaseLocation.is_active ?? true;

        // Update address fields if available
        if (myCaseLocation.address) {
          existingLocation.city = myCaseLocation.address.city;
          existingLocation.country = myCaseLocation.address.country;
          existingLocation.state = myCaseLocation.address.state;
          existingLocation.address1 = myCaseLocation.address.address1;
          existingLocation.address2 = myCaseLocation.address.address2;
          existingLocation.zip_code = myCaseLocation.address.zip_code;
        }

        // Update MyCase timestamps
        if (myCaseLocation.created_at) {
          existingLocation.my_case_created_at = new Date(
            myCaseLocation.created_at,
          );
        }
        if (myCaseLocation.updated_at) {
          existingLocation.my_case_updated_at = new Date(
            myCaseLocation.updated_at,
          );
        }

        await existingLocation.save();

        return this.mapToResponseDto(existingLocation);
      }

      const newLocation = new this.courtLocationModel({
        // Required fields
        value: myCaseLocation.name,
        text: myCaseLocation.name,
        is_active: myCaseLocation.is_active ?? true,

        // MyCase specific fields
        my_case_id: myCaseLocation.id,
        name: myCaseLocation.name,

        // Address fields
        city: myCaseLocation.address?.city,
        country: myCaseLocation.address?.country,
        state: myCaseLocation.address?.state,
        address1: myCaseLocation.address?.address1,
        address2: myCaseLocation.address?.address2,
        zip_code: myCaseLocation.address?.zip_code,

        // MyCase timestamps
        my_case_created_at: myCaseLocation.created_at
          ? new Date(myCaseLocation.created_at)
          : undefined,
        my_case_updated_at: myCaseLocation.updated_at
          ? new Date(myCaseLocation.updated_at)
          : undefined,
      });

      const savedLocation = await newLocation.save();
      this.logger.log(`Saved new location: ${savedLocation.text}`);

      return this.mapToResponseDto(savedLocation);
    } catch (error) {
      this.logger.error(
        `Error saving location '${myCaseLocation.name}': ${error.message}`,
      );
      throw error;
    }
  }

  async syncMultipleLocationsFromMyCase(
    myCaseLocations: MyCaseLocationInterface[],
  ): Promise<SyncLocationsResponseDto> {
    try {
      this.logger.log(
        `Starting sync of ${myCaseLocations.length} locations from MyCase`,
      );

      const syncedLocations: LocationResponseDto[] = [];

      for (const myCaseLocation of myCaseLocations) {
        try {
          const savedLocation =
            await this.saveLocationFromMyCase(myCaseLocation);
          syncedLocations.push(savedLocation);
        } catch (error) {
          this.logger.error(
            `Failed to sync location '${myCaseLocation.name}': ${error.message}`,
          );
          // Continue with other locations even if one fails
        }
      }

      const result: SyncLocationsResponseDto = {
        locations: syncedLocations,
        synced_count: syncedLocations.length,
        total_count: myCaseLocations.length,
        message: `Successfully synced ${syncedLocations.length} out of ${myCaseLocations.length} locations`,
      };

      this.logger.log(result.message);
      return result;
    } catch (error) {
      this.logger.error(`Error syncing locations: ${error.message}`);
      throw error;
    }
  }

  async getAllLocations(): Promise<LocationResponseDto[]> {
    try {
      const locations = await this.courtLocationModel
        .find({ is_active: true })
        .sort({ name: 1 })
        .exec();

      return locations.map((location) => this.mapToResponseDto(location));
    } catch (error) {
      this.logger.error(`Error fetching locations: ${error.message}`);
      throw error;
    }
  }

  async getLocationById(id: string): Promise<LocationResponseDto> {
    try {
      const location = await this.courtLocationModel.findById(id).exec();

      if (!location) {
        throw new NotFoundException(`Location with ID '${id}' not found`);
      }

      return this.mapToResponseDto(location);
    } catch (error) {
      this.logger.error(
        `Error fetching location by ID '${id}': ${error.message}`,
      );
      throw error;
    }
  }

  private mapToResponseDto(location: CourtLocation): LocationResponseDto {
    return {
      name: location.text,
      is_active: location.is_active,
      my_case_id: location.my_case_id,
      city: location.city,
      country: location.country,
      state: location.state,
      address1: location.address1,
      address2: location.address2,
      zip_code: location.zip_code,
      createdAt: (location as any).createdAt,
      updatedAt: (location as any).updatedAt,
      my_case_created_at: location.my_case_created_at,
      my_case_updated_at: location.my_case_updated_at,
    };
  }
}
