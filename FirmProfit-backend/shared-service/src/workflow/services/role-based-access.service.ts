import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { User } from '../entities/user.entity';
import { Role } from '../entities/roles.entity';

export interface UserRoleInfo {
    userId: string;
    roleId: number;
    roleName: string;
    category: string;
}

@Injectable()
export class RoleBasedAccessService {
    private readonly logger = new Logger(RoleBasedAccessService.name);

    constructor(
        @InjectModel(User.name) private userModel: Model<User>,
        @InjectModel(Role.name) private roleModel: Model<Role>,
    ) { }

    /**
     * Get user's role information
     */
    async getUserRoleInfo(userId: string): Promise<UserRoleInfo | null> {
        try {
            const user = await this.userModel.findById(userId).populate('roleId').exec();
            if (!user || !user.roleId) {
                return null;
            }

            const role = user.roleId as any;
            const category = this.getRoleCategory(role.id);

            return {
                userId: user._id.toString(),
                roleId: role.id,
                roleName: role.name,
                category,
            };
        } catch (error) {
            this.logger.error('Error getting user role info:', error);
            return null;
        }
    }

    /**
     * Check if user has access to specific workflow/task based on their role
     */
    async canUserAccessWorkflow(userId: string, workflowType: string): Promise<boolean> {
        try {
            const userRoleInfo = await this.getUserRoleInfo(userId);
            if (!userRoleInfo) {
                return false;
            }

            // Define access rules based on workflow types and user roles
            const accessRules = {
                'court-notice': {
                    allowedRoles: ['Paralegal', 'Attorney'],
                    allowedCategories: ['Paralegal', 'Attorney'],
                },
                'billing': {
                    allowedRoles: ['Billing', 'Billing Specialist'],
                    allowedCategories: ['Billing'],
                },
                'intake': {
                    allowedRoles: ['Intake', 'Intake Specialist'],
                    allowedCategories: ['Intake'],
                },
                'general': {
                    allowedRoles: ['Paralegal', 'Attorney', 'Intake', 'Billing'],
                    allowedCategories: ['Paralegal', 'Attorney', 'Intake', 'Billing'],
                },
            };

            const rule = accessRules[workflowType] || accessRules['general'];

            // Check if user's role name is in allowed roles
            const hasRoleAccess = rule.allowedRoles.includes(userRoleInfo.roleName);

            // Check if user's role category is in allowed categories
            const hasCategoryAccess = rule.allowedCategories.includes(userRoleInfo.category);

            return hasRoleAccess || hasCategoryAccess;
        } catch (error) {
            this.logger.error('Error checking user access:', error);
            return false;
        }
    }

    /**
     * Filter workflows based on user's role
     */
    async filterWorkflowsByUserRole(userId: string, workflows: any[]): Promise<any[]> {
        try {
            const userRoleInfo = await this.getUserRoleInfo(userId);
            if (!userRoleInfo) {
                return [];
            }

            // Filter workflows based on user's role
            return workflows.filter(workflow => {
                // For court notice workflows, only show to Paralegals and Attorneys
                if (workflow.name?.toLowerCase().includes('court notice')) {
                    return ['Paralegal', 'Attorney'].includes(userRoleInfo.category);
                }

                // For billing workflows, only show to Billing roles
                if (workflow.name?.toLowerCase().includes('billing')) {
                    return userRoleInfo.category === 'Billing';
                }

                // For intake workflows, only show to Intake roles
                if (workflow.name?.toLowerCase().includes('intake')) {
                    return userRoleInfo.category === 'Intake';
                }

                // Default: show all workflows
                return true;
            });
        } catch (error) {
            this.logger.error('Error filtering workflows:', error);
            return [];
        }
    }

    /**
     * Filter tasks based on user's role
     */
    async filterTasksByUserRole(userId: string, tasks: any[]): Promise<any[]> {
        try {
            const userRoleInfo = await this.getUserRoleInfo(userId);
            if (!userRoleInfo) {
                return [];
            }

            // Filter tasks based on user's role
            return tasks.filter(task => {
                // For court notice tasks, only show to Paralegals and Attorneys
                if (task.name?.toLowerCase().includes('court notice')) {
                    return ['Paralegal', 'Attorney'].includes(userRoleInfo.category);
                }

                // For billing tasks, only show to Billing roles
                if (task.name?.toLowerCase().includes('billing')) {
                    return userRoleInfo.category === 'Billing';
                }

                // For intake tasks, only show to Intake roles
                if (task.name?.toLowerCase().includes('intake')) {
                    return userRoleInfo.category === 'Intake';
                }

                // Default: show all tasks
                return true;
            });
        } catch (error) {
            this.logger.error('Error filtering tasks:', error);
            return [];
        }
    }

    /**
     * Get available assignees based on user's role
     */
    async getAvailableAssigneesForUser(userId: string, search?: string): Promise<any[]> {
        try {
            const userRoleInfo = await this.getUserRoleInfo(userId);
            if (!userRoleInfo) {
                return [];
            }

            // Get all users with the same role category
            const users = await this.userModel.find({
                is_active: true,
                is_deleted: false,
            }).populate('roleId').exec();

            const filteredUsers = users.filter(user => {
                const userRole = user.roleId as any;
                if (!userRole) return false;

                const userCategory = this.getRoleCategory(userRole.id);
                return userCategory === userRoleInfo.category;
            });

            // Convert to assignee format
            return filteredUsers.map(user => ({
                id: user._id.toString(),
                name: `${user.first_name} ${user.last_name}`,
                type: 'user',
                email: user.email,
                category: userRoleInfo.category,
            }));
        } catch (error) {
            this.logger.error('Error getting available assignees for user:', error);
            return [];
        }
    }

    /**
     * Helper method to get role category
     */
    private getRoleCategory(roleId: number): string {
        const roleCategoryMap = {
            1: 'Paralegal', // Paralegal
            2: 'Attorney',  // Attorney
            3: 'Intake',    // Intake
            4: 'Billing',   // Billing
            5: 'Paralegal', // Court Notice Paralegal
            6: 'Paralegal', // General Paralegal
            7: 'Attorney',  // Senior Attorney
            8: 'Attorney',  // Junior Attorney
            9: 'Intake',    // Intake Specialist
            10: 'Billing',  // Billing Specialist
        };

        return roleCategoryMap[roleId] || 'Unknown';
    }

    /**
     * Check if user can assign to specific task
     */
    async canUserAssignToTask(userId: string, taskName: string): Promise<boolean> {
        try {
            const userRoleInfo = await this.getUserRoleInfo(userId);
            if (!userRoleInfo) {
                return false;
            }

            // Define assignment rules
            const assignmentRules = {
                'court notice': {
                    allowedCategories: ['Paralegal', 'Attorney'],
                },
                'billing': {
                    allowedCategories: ['Billing'],
                },
                'intake': {
                    allowedCategories: ['Intake'],
                },
            };

            // Check if task name matches any rule
            for (const [taskType, rule] of Object.entries(assignmentRules)) {
                if (taskName.toLowerCase().includes(taskType)) {
                    return rule.allowedCategories.includes(userRoleInfo.category);
                }
            }

            // Default: allow assignment
            return true;
        } catch (error) {
            this.logger.error('Error checking assignment permission:', error);
            return false;
        }
    }
} 