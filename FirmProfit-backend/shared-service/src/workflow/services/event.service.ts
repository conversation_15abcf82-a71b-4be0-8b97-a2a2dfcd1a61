import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { MycaseClientMatter } from '../entities/mycase.client.matter.entity';
import { Matter } from '../entities/matter.entity';
import { EventStatus } from '../enum/template-status.enum';

export interface MyCaseEventInterface {
  id: number;
  name: string;
  description?: string;
  start: string;
  end: string;
  all_day: boolean;
  private: boolean;
  event_type?: string;
  location?: {
    id: number;
  };
  case?: {
    id: number;
  };
  staff?: Array<{
    id: number;
  }>;
  created_at?: string;
  updated_at?: string;
}

export interface EventResponseDto {
  id: string;
  client_matter_id: string;
  event_id: string;
  my_case_event_id: string;
  eventStatus: string;
  action: string;
  event: any;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface SyncEventsResponseDto {
  events: EventResponseDto[];
  synced_count: number;
  total_count: number;
  message: string;
}

export interface MyCaseEventListResponse {
  data: MyCaseEventInterface[];
  total: number;
  page: number;
  per_page: number;
}

@Injectable()
export class EventService {
  private readonly logger = new Logger(EventService.name);

  constructor(
    @InjectModel(MycaseClientMatter.name)
    private readonly mycaseClientMatterModel: Model<MycaseClientMatter>,
    @InjectModel(Matter.name)
    private readonly matterModel: Model<Matter>,
  ) {}

  async saveEventFromMyCase(
    myCaseEvent: MyCaseEventInterface,
  ): Promise<EventResponseDto> {
    try {
      // Find existing event by MyCase event ID
      const existingEvent = await this.mycaseClientMatterModel.findOne({
        my_case_event_id: myCaseEvent.id.toString(),
      });

      // Find matter by MyCase matter ID
      let clientMatterId: string | null = null;
      if (myCaseEvent.case?.id) {
        const matter = await this.matterModel.findOne({
          my_case_matter_id: myCaseEvent.case.id.toString(),
        });
        if (matter) {
          clientMatterId = matter._id.toString();
        } else {
          this.logger.warn(
            `Matter not found for MyCase case ID: ${myCaseEvent.case.id}`,
          );
        }
      }

      // Transform MyCase event to local event format
      const localEvent = this.transformMyCaseEventToLocal(myCaseEvent);

      if (existingEvent) {
        this.logger.log(
          `Event '${myCaseEvent.name}' already exists, updating if needed`,
        );

        // Update existing event with latest data
        existingEvent.event = localEvent;
        existingEvent.eventStatus = EventStatus.SYNCED;
        if (clientMatterId) {
          existingEvent.client_matter_id = new Types.ObjectId(clientMatterId);
        }

        await existingEvent.save();
        return this.mapToResponseDto(existingEvent);
      }

      // Create new event record
      const newEvent = new this.mycaseClientMatterModel({
        client_matter_id: clientMatterId
          ? new Types.ObjectId(clientMatterId)
          : null,
        eventStatus: EventStatus.SYNCED,
        event_id: this.generateEventId(),
        action: 'New',
        event: localEvent,
        is_active: true,
        my_case_event_id: myCaseEvent.id.toString(),
      });

      const savedEvent = await newEvent.save();
      this.logger.log(`Saved new event: ${savedEvent.event.subject}`);

      return this.mapToResponseDto(savedEvent);
    } catch (error) {
      this.logger.error(
        `Error saving event '${myCaseEvent.name}': ${error.message}`,
      );
      throw error;
    }
  }

  private transformMyCaseEventToLocal(myCaseEvent: MyCaseEventInterface): any {
    // Parse start and end dates
    const startDate = new Date(myCaseEvent.start);
    const endDate = new Date(myCaseEvent.end);

    // Extract date and time components
    const formatDate = (date: Date) => {
      return date.toISOString().split('T')[0]; // YYYY-MM-DD format
    };

    const formatTime = (date: Date) => {
      return date.toTimeString().split(' ')[0].substring(0, 5); // HH:MM format
    };

    return {
      id: this.generateEventId(),
      caseNumber: myCaseEvent.case?.id?.toString() || '',
      clientName: myCaseEvent.name || '',
      description: myCaseEvent.description || '',
      date: formatDate(startDate),
      startTime: myCaseEvent.all_day ? '00:00' : formatTime(startDate),
      endTime: myCaseEvent.all_day ? '23:59' : formatTime(endDate),
      isCompleted: false,
      subject: myCaseEvent.name || '',
      courtNoticeType: myCaseEvent.event_type || '',
      courtNoticeActions: '',
      appointmentAction: 'New',
      eventStatus: 'Synced',
      charge: '',
      county: '',
      courtLocation: '',
      optionalAttendees: '',
      requiredAttendees: '',
      clientAttendance: '',
      meetingLocation: '',
      phoneDetails: '',
      meetingAddress: '',
      meetingLink: '',
      appointmentToReschedule: '',
      court_notice_date: '',
      startDate: formatDate(startDate),
      endDate: formatDate(endDate),
      isCancel: false,
      allDay: myCaseEvent.all_day,
      files: [],
      my_case_event_id: myCaseEvent.id.toString(),
    };
  }

  private generateEventId(): string {
    return `evt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  async syncMultipleEventsFromMyCase(
    myCaseEvents: MyCaseEventInterface[],
  ): Promise<SyncEventsResponseDto> {
    try {
      this.logger.log(
        `Starting sync of ${myCaseEvents.length} events from MyCase`,
      );

      const syncedEvents: EventResponseDto[] = [];

      for (const myCaseEvent of myCaseEvents) {
        try {
          const savedEvent = await this.saveEventFromMyCase(myCaseEvent);
          syncedEvents.push(savedEvent);
        } catch (error) {
          this.logger.error(
            `Failed to sync event '${myCaseEvent.name}': ${error.message}`,
          );
          // Continue with other events even if one fails
        }
      }

      const result: SyncEventsResponseDto = {
        events: syncedEvents,
        synced_count: syncedEvents.length,
        total_count: myCaseEvents.length,
        message: `Successfully synced ${syncedEvents.length} out of ${myCaseEvents.length} events`,
      };

      this.logger.log(result.message);
      return result;
    } catch (error) {
      this.logger.error(`Error syncing events: ${error.message}`);
      throw error;
    }
  }

  async getAllEvents(): Promise<EventResponseDto[]> {
    try {
      const events = await this.mycaseClientMatterModel.find({
        is_active: true,
      });
      return events.map((event) => this.mapToResponseDto(event));
    } catch (error) {
      this.logger.error(`Error fetching events: ${error.message}`);
      throw error;
    }
  }

  async getEventById(id: string): Promise<EventResponseDto> {
    try {
      const event = await this.mycaseClientMatterModel.findById(id);
      if (!event) {
        throw new NotFoundException(`Event with ID ${id} not found`);
      }
      return this.mapToResponseDto(event);
    } catch (error) {
      this.logger.error(`Error fetching event by ID ${id}: ${error.message}`);
      throw error;
    }
  }

  async getEventByMyCaseId(myCaseEventId: string): Promise<EventResponseDto> {
    try {
      const event = await this.mycaseClientMatterModel.findOne({
        my_case_event_id: myCaseEventId,
      });
      if (!event) {
        throw new NotFoundException(
          `Event with MyCase ID ${myCaseEventId} not found`,
        );
      }
      return this.mapToResponseDto(event);
    } catch (error) {
      this.logger.error(
        `Error fetching event by MyCase ID ${myCaseEventId}: ${error.message}`,
      );
      throw error;
    }
  }

  private mapToResponseDto(event: any): EventResponseDto {
    return {
      id: event._id.toString(),
      client_matter_id: event.client_matter_id?.toString() || '',
      event_id: event.event_id || '',
      my_case_event_id: event.my_case_event_id || '',
      eventStatus: event.eventStatus || 'Pending',
      action: event.action || 'Create',
      event: event.event || {},
      is_active: event.is_active || true,
      created_at: event.createdAt?.toISOString() || '',
      updated_at: event.updatedAt?.toISOString() || '',
    };
  }
}
