import { Injectable, Logger } from '@nestjs/common';
import { MyCaseService } from '../mycase.service';
import {
  ClientService,
  CreateClientDto,
  UpdateClientDto,
} from './client.service';
import {
  MatterService,
  CreateMatterDto,
  UpdateMatterDto,
} from './matter.service';
import {
  MyCaseEventWebhookPayload,
  parseResourceBody,
} from '../dto/mycase-webhook.dto';
import { Types } from 'mongoose';

export interface WebhookData {
  firm_uuid: string;
  action: string;
  resource: string;
  resource_body: string | object;
  timestamp: string;
}

@Injectable()
export class WebhookService {
  private readonly logger = new Logger(WebhookService.name);

  constructor(
    private readonly myCaseService: MyCaseService,
    private readonly clientService: ClientService,
    private readonly matterService: MatterService,
  ) {}

  /**
   * Main webhook handler that routes to appropriate resource handlers
   */
  async handleWebhook(data: WebhookData, accessToken?: string): Promise<void> {
    try {
      this.logger.log(`Processing webhook: ${data.resource} - ${data.action}`);

      // Validate required fields
      this.validateWebhookData(data);

      // Parse resource body
      const resourceBody =
        typeof data.resource_body === 'string'
          ? parseResourceBody(data.resource_body)
          : data.resource_body;

      // Route to appropriate handler based on resource type
      switch (data.resource.toLowerCase()) {
        case 'event':
          await this.handleEventWebhook(
            resourceBody as MyCaseEventWebhookPayload,
            data.action,
            accessToken,
          );
          break;

        case 'case':
          await this.handleCaseWebhook(
            resourceBody,
            data.action,
            data.firm_uuid,
            accessToken,
          );
          break;

        case 'client':
          await this.handleClientWebhook(
            resourceBody,
            data.action,
            data.firm_uuid,
            accessToken,
          );
          break;

        case 'location':
          await this.handleLocationWebhook(
            resourceBody,
            data.action,
            accessToken,
          );
          break;

        default:
          this.logger.warn(`Unsupported resource type: ${data.resource}`);
          break;
      }

      this.logger.log(
        `Successfully processed webhook: ${data.resource} - ${data.action}`,
      );
    } catch (error) {
      this.logger.error(
        `Error processing webhook: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Validate webhook data structure
   */
  private validateWebhookData(data: WebhookData): void {
    const requiredFields = ['firm_uuid', 'action', 'resource', 'timestamp'];
    const missingFields = requiredFields.filter((field) => !data[field]);

    if (missingFields.length > 0) {
      throw new Error(
        `Missing required webhook fields: ${missingFields.join(', ')}`,
      );
    }

    if (!data.resource_body) {
      throw new Error('Missing resource_body in webhook data');
    }
  }

  /**
   * Handle Event resource webhooks
   */
  private async handleEventWebhook(
    eventPayload: MyCaseEventWebhookPayload,
    action: string,
    accessToken?: string,
  ): Promise<void> {
    if (!eventPayload?.id) {
      throw new Error('Event payload missing required id field');
    }

    await this.myCaseService.handleWebhookEvent(
      eventPayload,
      action,
      accessToken,
    );
  }

  /**
   * Handle Case resource webhooks with full CRUD operations
   */
  private async handleCaseWebhook(
    casePayload: any,
    action: string,
    firmUuid: string,
    accessToken?: string,
  ): Promise<void> {
    try {
      this.logger.log(
        `Handling case webhook: ${action} for case ID: ${casePayload?.id}`,
      );

      if (!casePayload?.id) {
        throw new Error('Case payload missing required id field');
      }

      const myCaseMatterId = casePayload.id;

      switch (action.toLowerCase()) {
        case 'created':
          await this.handleCaseCreated(casePayload, firmUuid);
          break;

        case 'updated':
          await this.handleCaseUpdated(casePayload, firmUuid);
          break;

        case 'deleted':
          await this.handleCaseDeleted(myCaseMatterId, firmUuid);
          break;

        default:
          this.logger.warn(`Unsupported case action: ${action}`);
          break;
      }
    } catch (error) {
      this.logger.error(
        `Error handling case webhook: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Handle case creation
   */
  private async handleCaseCreated(
    casePayload: any,
    firmUuid: string,
  ): Promise<void> {
    try {
      this.logger.log(`Creating case from webhook: ${casePayload.id}`);

      // Check if there are multiple clients
      const clients = casePayload.clients || [];

      if (clients.length === 0) {
        // No clients - create matter without client_id
        await this.createMatterForClient(casePayload, firmUuid, null);
      } else if (clients.length === 1) {
        // Single client - use individual client_id approach
        const clientId = await this.findOrCreateClientByMyCaseId(
          clients[0].id,
          firmUuid,
        );
        await this.createMatterForClient(casePayload, firmUuid, clientId);
      } else {
        // Multiple clients - create separate matter for each client
        this.logger.log(
          `Creating ${clients.length} separate matters for multiple clients`,
        );

        const matterPromises = clients.map(async (client: any) => {
          const clientId = await this.findOrCreateClientByMyCaseId(
            client.id,
            firmUuid,
          );
          return this.createMatterForClient(casePayload, firmUuid, clientId);
        });

        await Promise.all(matterPromises);
        this.logger.log(
          `Successfully created ${clients.length} matters for multiple clients`,
        );
      }
    } catch (error) {
      this.logger.error(`Error creating case: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Create a matter record for a specific client
   */
  private async createMatterForClient(
    casePayload: any,
    firmUuid: string,
    clientId: string | null,
  ): Promise<void> {
    try {
      // Map webhook payload to matter DTO
      const createMatterDto: CreateMatterDto = {
        my_case_matter_id: casePayload.id,
        name: casePayload.name,
        case_number: casePayload.case_number,
        description: casePayload.description,
        opened_date: casePayload.opened_date,
        closed_date: casePayload.closed_date,
        sol_date: casePayload.sol_date,
        outstanding_balance: casePayload.outstanding_balance || 0,
        staff: casePayload.staff || [],
        case_stage_reference: casePayload.case_stage_reference,
        practice_area_reference: casePayload.practice_area_reference,
        billing_contact: casePayload.billing_contact,
        potential_case: casePayload.potential_case || false,
        is_active: true,
        firm_uuid: firmUuid,
        my_case_created_at: casePayload.created_at,
        my_case_updated_at: casePayload.updated_at,
        // Use client_id for individual client assignment instead of clients array
        client_id: clientId ? new Types.ObjectId(clientId) : undefined,
        // Keep clients array empty for individual client approach
        clients: [],
      };

      // Use upsert to handle potential duplicates
      const { matter, created } =
        await this.matterService.upsertMatter(createMatterDto);

      const clientInfo = clientId
        ? ` for client ID: ${clientId}`
        : ' without client';
      this.logger.log(
        `Matter ${created ? 'created' : 'updated'} successfully: ${matter.name} (MyCase ID: ${matter.my_case_matter_id})${clientInfo}`,
      );
    } catch (error) {
      this.logger.error(
        `Error creating matter for client: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Find or create client by MyCase client ID and return the ObjectId
   */
  private async findOrCreateClientByMyCaseId(
    myCaseClientId: number,
    firmUuid: string,
  ): Promise<string | null> {
    try {
      // First, try to find existing client by MyCase ID
      const existingClient =
        await this.clientService.findByMyCaseId(myCaseClientId);

      if (existingClient) {
        this.logger.log(
          `Found existing client: ${existingClient.name} (MyCase ID: ${myCaseClientId})`,
        );
        return (existingClient as any)._id.toString();
      }

      this.logger.log(
        `Client not found, will be created when client webhook is received for MyCase ID: ${myCaseClientId}`,
      );

      // Return null for now - the client will be created when the client webhook is received
      // In a real scenario, you might want to fetch the client details from MyCase API here
      return null;
    } catch (error) {
      this.logger.error(
        `Error finding client by MyCase ID ${myCaseClientId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Handle case updates
   */
  private async handleCaseUpdated(
    casePayload: any,
    firmUuid: string,
  ): Promise<void> {
    try {
      this.logger.log(`Updating case from webhook: ${casePayload.id}`);

      // Check if there are multiple clients
      const clients = casePayload.clients || [];

      if (clients.length === 0) {
        // No clients - update matter without client_id
        await this.updateMatterForClient(casePayload, firmUuid, null);
      } else if (clients.length === 1) {
        // Single client - use individual client_id approach
        const clientId = await this.findOrCreateClientByMyCaseId(
          clients[0].id,
          firmUuid,
        );
        await this.updateMatterForClient(casePayload, firmUuid, clientId);
      } else {
        // Multiple clients - update separate matter for each client
        this.logger.log(
          `Updating ${clients.length} separate matters for multiple clients`,
        );

        const updatePromises = clients.map(async (client: any) => {
          const clientId = await this.findOrCreateClientByMyCaseId(
            client.id,
            firmUuid,
          );
          return this.updateMatterForClient(casePayload, firmUuid, clientId);
        });

        await Promise.all(updatePromises);
        this.logger.log(
          `Successfully updated ${clients.length} matters for multiple clients`,
        );
      }
    } catch (error) {
      this.logger.error(`Error updating case: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update a matter record for a specific client
   */
  private async updateMatterForClient(
    casePayload: any,
    firmUuid: string,
    clientId: string | null,
  ): Promise<void> {
    try {
      // Map webhook payload to update DTO
      const updateMatterDto: UpdateMatterDto = {
        name: casePayload.name,
        case_number: casePayload.case_number,
        description: casePayload.description,
        opened_date: casePayload.opened_date,
        closed_date: casePayload.closed_date,
        sol_date: casePayload.sol_date,
        outstanding_balance: casePayload.outstanding_balance,
        staff: casePayload.staff,
        case_stage_reference: casePayload.case_stage_reference,
        practice_area_reference: casePayload.practice_area_reference,
        billing_contact: casePayload.billing_contact,
        potential_case: casePayload.potential_case,
        firm_uuid: firmUuid,
        my_case_updated_at: casePayload.updated_at,
        // Use client_id for individual client assignment instead of clients array
        client_id: clientId ? new Types.ObjectId(clientId) : undefined,
        // Keep clients array empty for individual client approach
        clients: [],
      };

      // Remove undefined fields to avoid overwriting with undefined values
      Object.keys(updateMatterDto).forEach((key) => {
        if (updateMatterDto[key] === undefined) {
          delete updateMatterDto[key];
        }
      });

      try {
        const updatedMatter = await this.matterService.updateByMyCaseId(
          casePayload.id,
          updateMatterDto,
        );

        const clientInfo = clientId
          ? ` for client ID: ${clientId}`
          : ' without client';
        this.logger.log(
          `Matter updated successfully: ${updatedMatter.name} (MyCase ID: ${updatedMatter.my_case_matter_id})${clientInfo}`,
        );
      } catch (error) {
        if (error.message.includes('not found')) {
          // Matter doesn't exist, create it
          this.logger.log(
            `Matter not found, creating new matter: ${casePayload.id}`,
          );
          await this.createMatterForClient(casePayload, firmUuid, clientId);
        } else {
          throw error;
        }
      }
    } catch (error) {
      this.logger.error(
        `Error updating matter for client: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Handle case deletion
   */
  private async handleCaseDeleted(
    myCaseMatterId: number,
    firmUuid: string,
  ): Promise<void> {
    try {
      this.logger.log(`Deleting case from webhook: ${myCaseMatterId}`);

      // Use soft delete to preserve data integrity
      const deletedMatter =
        await this.matterService.softDeleteByMyCaseId(myCaseMatterId);

      this.logger.log(
        `Matter soft deleted successfully: ${deletedMatter.name} (MyCase ID: ${deletedMatter.my_case_matter_id})`,
      );
    } catch (error) {
      if (error.message.includes('not found')) {
        this.logger.warn(`Matter not found for deletion: ${myCaseMatterId}`);
      } else {
        this.logger.error(`Error deleting case: ${error.message}`, error.stack);
        throw error;
      }
    }
  }

  /**
   * Handle Client resource webhooks with full CRUD operations
   */
  private async handleClientWebhook(
    clientPayload: any,
    action: string,
    firmUuid: string,
    accessToken?: string,
  ): Promise<void> {
    try {
      this.logger.log(
        `Handling client webhook: ${action} for client ID: ${clientPayload?.id}`,
      );

      if (!clientPayload?.id) {
        throw new Error('Client payload missing required id field');
      }

      const myCaseClientId = clientPayload.id;

      switch (action.toLowerCase()) {
        case 'created':
          await this.handleClientCreated(clientPayload, firmUuid);
          break;

        case 'updated':
          await this.handleClientUpdated(clientPayload, firmUuid);
          break;

        case 'deleted':
          await this.handleClientDeleted(myCaseClientId, firmUuid);
          break;

        default:
          this.logger.warn(`Unsupported client action: ${action}`);
          break;
      }
    } catch (error) {
      this.logger.error(
        `Error handling client webhook: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Handle client creation
   */
  private async handleClientCreated(
    clientPayload: any,
    firmUuid: string,
  ): Promise<void> {
    try {
      this.logger.log(`Creating client from webhook: ${clientPayload.id}`);

      // Map webhook payload to client DTO
      const createClientDto: CreateClientDto = {
        my_case_client_id: clientPayload.id,
        first_name: clientPayload.first_name,
        middle_initial: clientPayload.middle_initial,
        last_name: clientPayload.last_name,
        email: clientPayload.email,
        cell_phone_number: clientPayload.cell_phone_number,
        work_phone_number: clientPayload.work_phone_number,
        home_phone_number: clientPayload.home_phone_number,
        address: clientPayload.address,
        notes: clientPayload.notes,
        birthdate: clientPayload.birthdate,
        archived: clientPayload.archived || false,
        people_group: clientPayload.people_group,
        is_active: true,
        firm_uuid: firmUuid,
        my_case_created_at: clientPayload.created_at,
        my_case_updated_at: clientPayload.updated_at,
      };

      // Use upsert to handle potential duplicates
      const { client, created } =
        await this.clientService.upsertClient(createClientDto);

      this.logger.log(
        `Client ${created ? 'created' : 'updated'} successfully: ${client.name} (MyCase ID: ${client.my_case_client_id})`,
      );
    } catch (error) {
      this.logger.error(`Error creating client: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Handle client updates
   */
  private async handleClientUpdated(
    clientPayload: any,
    firmUuid: string,
  ): Promise<void> {
    try {
      this.logger.log(`Updating client from webhook: ${clientPayload.id}`);

      // Map webhook payload to update DTO
      const updateClientDto: UpdateClientDto = {
        first_name: clientPayload.first_name,
        middle_initial: clientPayload.middle_initial,
        last_name: clientPayload.last_name,
        email: clientPayload.email,
        cell_phone_number: clientPayload.cell_phone_number,
        work_phone_number: clientPayload.work_phone_number,
        home_phone_number: clientPayload.home_phone_number,
        address: clientPayload.address,
        notes: clientPayload.notes,
        birthdate: clientPayload.birthdate,
        archived: clientPayload.archived,
        people_group: clientPayload.people_group,
        firm_uuid: firmUuid,
        my_case_updated_at: clientPayload.updated_at,
      };

      // Remove undefined fields to avoid overwriting with undefined values
      Object.keys(updateClientDto).forEach((key) => {
        if (updateClientDto[key] === undefined) {
          delete updateClientDto[key];
        }
      });

      try {
        const updatedClient = await this.clientService.updateByMyCaseId(
          clientPayload.id,
          updateClientDto,
        );

        this.logger.log(
          `Client updated successfully: ${updatedClient.name} (MyCase ID: ${updatedClient.my_case_client_id})`,
        );
      } catch (error) {
        if (error.message.includes('not found')) {
          // Client doesn't exist, create it
          this.logger.log(
            `Client not found, creating new client: ${clientPayload.id}`,
          );
          await this.handleClientCreated(clientPayload, firmUuid);
        } else {
          throw error;
        }
      }
    } catch (error) {
      this.logger.error(`Error updating client: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Handle client deletion
   */
  private async handleClientDeleted(
    myCaseClientId: number,
    firmUuid: string,
  ): Promise<void> {
    try {
      this.logger.log(`Deleting client from webhook: ${myCaseClientId}`);

      // Use soft delete to preserve data integrity
      const deletedClient =
        await this.clientService.softDeleteByMyCaseId(myCaseClientId);

      this.logger.log(
        `Client soft deleted successfully: ${deletedClient.name} (MyCase ID: ${deletedClient.my_case_client_id})`,
      );
    } catch (error) {
      if (error.message.includes('not found')) {
        this.logger.warn(`Client not found for deletion: ${myCaseClientId}`);
      } else {
        this.logger.error(
          `Error deleting client: ${error.message}`,
          error.stack,
        );
        throw error;
      }
    }
  }

  /**
   * Handle Location resource webhooks
   */
  private async handleLocationWebhook(
    locationPayload: any,
    action: string,
    accessToken?: string,
  ): Promise<void> {
    this.logger.log(
      `Handling location webhook: ${action} for location ID: ${locationPayload?.id}`,
    );

    // TODO: Implement location webhook handling
    // This could involve updating CourtLocation records, syncing location details, etc.

    this.logger.warn('Location webhook handling not yet implemented');
  }

  /**
   * Get webhook processing statistics
   */
  async getWebhookStats(dateRange?: { from: Date; to: Date }): Promise<any> {
    // TODO: Implement webhook statistics gathering
    // This could track success/failure rates, processing times, etc.

    return {
      message: 'Webhook statistics not yet implemented',
      dateRange,
    };
  }
}
