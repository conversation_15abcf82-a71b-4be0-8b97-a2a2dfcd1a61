import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { RoundRobinState, RoundRobinStateDocument } from '../entities/round-robin-state.entity';
import { User, UserDocument } from '../entities/user.entity';
import { UserRole, UserRoleDocument } from '../entities/user-role.entity';
import { Role, RoleDocument } from '../entities/roles.entity';

export interface RoundRobinContext {
    context_type: 'role' | 'workflow' | 'global';
    context_id?: string;
    role_id?: string;
}

export interface RoundRobinAssignmentResult {
    assigned_user: {
        _id: string;
        name: string;
        email: string;
        id: string
    };
    context: RoundRobinContext;
    assignment_count: number;
    next_user_index: number;
}

@Injectable()
export class RoundRobinService {
    private readonly logger = new Logger(RoundRobinService.name);

    constructor(
        @InjectModel(RoundRobinState.name) 
        private roundRobinStateModel: Model<RoundRobinStateDocument>,
        @InjectModel(User.name) 
        private userModel: Model<UserDocument>,
        @InjectModel(UserRole.name) 
        private userRoleModel: Model<UserRoleDocument>,
        @InjectModel(Role.name) 
        private roleModel: Model<RoleDocument>,
    ) {}

    /**
     * Get the next user in round robin for a given context
     */
    async getNextRoundRobinUser(context: RoundRobinContext): Promise<RoundRobinAssignmentResult | null> {
        try {
            this.logger.log(`Getting next round robin user for context: ${JSON.stringify(context)}`);

            // Get or create round robin state for this context
            let roundRobinState = await this.getRoundRobinState(context);

            if (!roundRobinState) {
                roundRobinState = await this.initializeRoundRobinState(context);
            }

            // Refresh user pool if needed (handles dynamic user changes)
            const currentUserPool = await this.getUserPoolForContext(context);
            
            // if (this.hasUserPoolChanged(roundRobinState.user_pool, currentUserPool)) {
            //     this.logger.log('User pool has changed, refreshing round robin state');
            //     roundRobinState = await this.refreshUserPool(roundRobinState, currentUserPool);
            // }

            if (currentUserPool.length === 0) {
                this.logger.warn(`No users available for round robin in context: ${JSON.stringify(context)}`);
                return null;
            }

            // Get next user using round robin logic
            const nextUserIndex = (roundRobinState.current_index + 1) % currentUserPool.length;
            const nextUserId = currentUserPool[nextUserIndex];

            // Get user details
            const user = await this.userModel.findById(nextUserId).exec();
            if (!user) {
                this.logger.error(`User not found: ${nextUserId}`);
                // Remove invalid user and try again
                await this.removeInvalidUserFromPool(roundRobinState, nextUserId);
                return this.getNextRoundRobinUser(context);
            }

            // Update round robin state
            await this.updateRoundRobinState(roundRobinState, nextUserId, nextUserIndex);

            return {
                assigned_user: {
                    _id: user._id.toString(),
                    name: `${user.first_name} ${user.last_name}`,
                    email: user.email,
                    id: user.id
                },
                context,
                assignment_count: roundRobinState.assignment_count + 1,
                next_user_index: nextUserIndex,
            };

        } catch (error) {
            this.logger.error(`Error in getNextRoundRobinUser: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Initialize round robin state for a new context
     */
    private async initializeRoundRobinState(context: RoundRobinContext): Promise<RoundRobinStateDocument> {
        try {
            const userPool = await this.getUserPoolForContext(context);
            
            if (userPool.length === 0) {
                throw new Error(`No users available for round robin in context: ${JSON.stringify(context)}`);
            }

            // Start with the last user in the pool so next assignment gets the first user
            const initialIndex = userPool.length - 1;
            const initialUser = userPool[initialIndex];

            const roundRobinState = new this.roundRobinStateModel({
                context_type: context.context_type,
                context_id: context.context_id || null,
                last_assigned_user: initialUser,
                assignment_count: 0,
                user_pool: userPool,
                current_index: initialIndex,
                last_assignment_date: new Date(),
                is_active: true,
                is_deleted: false,
            });

            await roundRobinState.save();
            this.logger.log(`Initialized round robin state for context: ${JSON.stringify(context)}`);
            
            return roundRobinState;
        } catch (error) {
            this.logger.error(`Error initializing round robin state: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get existing round robin state for a context
     */
    private async getRoundRobinState(context: RoundRobinContext): Promise<RoundRobinStateDocument | null> {
        const query: any = {
            context_type: context.context_type,
            is_active: true,
            is_deleted: false,
        };

        if (context.context_id) {
            query.context_id = context.context_id;
        }

        return this.roundRobinStateModel.findOne(query).exec();
    }

    /**
     * Get user pool for a given context
     */
    private async getUserPoolForContext(context: RoundRobinContext): Promise<Types.ObjectId[]> {
        try {
            let userIds: Types.ObjectId[] = [];

            switch (context.context_type) {
                case 'role':
                    if (context.role_id) {
                        userIds = await this.getUsersByRoleId(context.role_id);
                    }
                    break;
                
                case 'workflow':
                    // For workflow context, you might want to get users based on workflow permissions
                    // This can be extended based on your business logic
                    if (context.context_id) {
                        userIds = await this.getUsersByWorkflowAccess(context.context_id);
                    }
                    break;
                
                case 'global':
                    // Get all active users for global round robin
                    userIds = await this.getAllActiveUsers();
                    break;
                
                default:
                    throw new Error(`Unknown context type: ${context.context_type}`);
            }

            return userIds;
        } catch (error) {
            this.logger.error(`Error getting user pool for context: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get users by role ID (supports both numeric ID and MongoDB ObjectId)
     */
    private async getUsersByRoleId(roleId: string): Promise<Types.ObjectId[]> {
        try {
            let targetRoleId;
            
            // Check if roleId is a valid MongoDB ObjectId (24 hex characters)
            if (roleId.length === 24 && /^[0-9a-fA-F]{24}$/.test(roleId)) {
                // It's a MongoDB ObjectId, use it directly for userRole lookup
                targetRoleId = roleId;
                this.logger.log(`Using MongoDB ObjectId directly for role lookup: ${roleId}`);
            } else {
                // It's a numeric ID, find the role first to get its MongoDB _id
                const numericId = parseInt(roleId);
                if (isNaN(numericId)) {
                    this.logger.warn(`Invalid role ID format: ${roleId}`);
                    return [];
                }
                
                const role = await this.roleModel.findOne({ id: numericId }).exec();
                if (!role) {
                    this.logger.warn(`Role not found for numeric ID: ${roleId}`);
                    return [];
                }
                targetRoleId = role._id.toString();
                this.logger.log(`Found role ${role.name} with MongoDB _id: ${targetRoleId}`);
            }

            // Find users associated with this role using the MongoDB _id
            const userRoles = await this.userRoleModel
                .find({ roleId: targetRoleId, status: true })
                .populate('userId')
                .exec();

            const users = userRoles
                .map(userRole => userRole.userId as any)
                .filter(user => user && user.is_active && !user.is_deleted && user.my_case_staff_id)
                .map(user => user._id);

            this.logger.log(`Found ${users.length} users for role ID ${roleId}`);
            return users;
        } catch (error) {
            this.logger.error(`Error getting users for role ID ${roleId}: ${error.message}`);
            return [];
        }
    }

    /**
     * Get users by workflow access (can be extended based on requirements)
     */
    private async getUsersByWorkflowAccess(workflowId: string): Promise<Types.ObjectId[]> {
        // This is a placeholder - implement based on your workflow permission logic
        // For now, return all active users
        return this.getAllActiveUsers();
    }

    /**
     * Get all active users
     */
    private async getAllActiveUsers(): Promise<Types.ObjectId[]> {
        const users = await this.userModel
            .find({ 
                is_active: true, 
                is_deleted: false,
                my_case_staff_id: { $exists: true, $ne: null } // Only users with staff ID
            })
            .select('_id')
            .exec();

        return users.map(user => user._id);
    }

    /**
     * Check if user pool has changed
     */
    private hasUserPoolChanged(currentPool: Types.ObjectId[], newPool: Types.ObjectId[]): boolean {
        if (currentPool.length !== newPool.length) {
            return true;
        }

        const currentSet = new Set(currentPool.map(id => id.toString()));
        const newSet = new Set(newPool.map(id => id.toString()));

        for (const id of newSet) {
            if (!currentSet.has(id)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Refresh user pool for round robin state
     */
    private async refreshUserPool(
        roundRobinState: RoundRobinStateDocument, 
        newUserPool: Types.ObjectId[]
    ): Promise<RoundRobinStateDocument> {
        // Find current user's new index in the updated pool
        const currentUserId = roundRobinState.last_assigned_user.toString();
        let newCurrentIndex = newUserPool.findIndex(id => id.toString() === currentUserId);
        
        // If current user is no longer in the pool, start from beginning
        if (newCurrentIndex === -1) {
            newCurrentIndex = newUserPool.length > 0 ? 0 : -1;
        }

        roundRobinState.user_pool = newUserPool;
        roundRobinState.current_index = newCurrentIndex;
        
        await roundRobinState.save();
        return roundRobinState;
    }

    /**
     * Update round robin state after assignment
     */
    private async updateRoundRobinState(
        roundRobinState: RoundRobinStateDocument,
        assignedUserId: Types.ObjectId,
        newIndex: number
    ): Promise<void> {
        roundRobinState.last_assigned_user = assignedUserId;
        roundRobinState.current_index = newIndex;
        roundRobinState.assignment_count += 1;
        roundRobinState.last_assignment_date = new Date();
        
        await roundRobinState.save();
        
        this.logger.log(`Updated round robin state: user ${assignedUserId}, index ${newIndex}, count ${roundRobinState.assignment_count}`);
    }

    /**
     * Remove invalid user from pool
     */
    private async removeInvalidUserFromPool(
        roundRobinState: RoundRobinStateDocument,
        invalidUserId: Types.ObjectId
    ): Promise<void> {
        roundRobinState.user_pool = roundRobinState.user_pool.filter(
            id => id.toString() !== invalidUserId.toString()
        );
        
        // Adjust current index if necessary
        if (roundRobinState.current_index >= roundRobinState.user_pool.length) {
            roundRobinState.current_index = 0;
        }
        
        await roundRobinState.save();
        this.logger.log(`Removed invalid user ${invalidUserId} from round robin pool`);
    }

    /**
     * Reset round robin state for a context (useful for administrative purposes)
     */
    async resetRoundRobinState(context: RoundRobinContext): Promise<void> {
        try {
            const roundRobinState = await this.getRoundRobinState(context);
            
            if (roundRobinState) {
                await this.roundRobinStateModel.deleteOne({ _id: roundRobinState._id });
                this.logger.log(`Reset round robin state for context: ${JSON.stringify(context)}`);
            }
        } catch (error) {
            this.logger.error(`Error resetting round robin state: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get round robin statistics for a context
     */
    async getRoundRobinStats(context: RoundRobinContext): Promise<any> {
        try {
            const roundRobinState = await this.getRoundRobinState(context);
            
            if (!roundRobinState) {
                return {
                    context,
                    initialized: false,
                    total_assignments: 0,
                    user_pool_size: 0,
                };
            }

            const userPool = await this.getUserPoolForContext(context);
            
            return {
                context,
                initialized: true,
                total_assignments: roundRobinState.assignment_count,
                user_pool_size: userPool.length,
                current_index: roundRobinState.current_index,
                last_assignment_date: roundRobinState.last_assignment_date,
                last_assigned_user: roundRobinState.last_assigned_user,
            };
        } catch (error) {
            this.logger.error(`Error getting round robin stats: ${error.message}`);
            throw error;
        }
    }
} 