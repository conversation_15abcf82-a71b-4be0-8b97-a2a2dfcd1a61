import { <PERSON>, Logger } from '@nestjs/common';
import { WorkflowService } from './workflow.service';
import { AssignmentService } from './services/assignment.service';
import { GrpcMethod } from '@nestjs/microservices';
import {
  GetWorkflowDto,
  WorkflowRenderDto,
  UpdateWorkflowStatusDto,
  UpdateWorkflowEndDateDto,
  GetPresignedUrlDto,
  CleanupFilesDto,
  ArchiveWorkFlowDto,
  GetEventListDto,
  UserListDto,
} from './dto/workflow.dto';
import { GetLocationDto } from './dto/location.dto';
import {
  AssignmentRequestDto,
  GetAssigneesDto,
  RemoveAssignmentDto,
  GetRoleMembersDto,
} from './dto/assignment.dto';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';
import { MyCaseSyncService } from './services/mycase-sync.service';
import { RoundRobinService, RoundRobinContext } from './services/round-robin.service';

@Controller()
export class WorkflowController {
  private readonly logger = new Logger(WorkflowController.name);

  constructor(
    private readonly workflowService: WorkflowService,
    private readonly assignmentService: AssignmentService,
    private readonly myCaseSyncService: MyCaseSyncService,
    private readonly roundRobinService: RoundRobinService,
  ) { }

  @GrpcMethod('WorkflowSharedService', 'MyWorkflows')
  async myWorkflows(data: GetWorkflowDto) {
    this.logger.log(
      `Received my_workflow request with data: ${JSON.stringify(data)}`,
    );
    try {
      const result = await this.workflowService.myWorkFlow(data);
      // this.logger.log(
      //   `Successfully processed workflow: ${JSON.stringify(result)}`,
      // );
      return result;
    } catch (error) {
      this.logger.error(`Error in myWorkFLow controller: ${error.message}`);
      throw error;
    }
  }

  @GrpcMethod('WorkflowSharedService', 'WorkFlowRender')
  async workFlowRender(data: WorkflowRenderDto) {
    this.logger.log(
      `Received my_workflow request with data: ${JSON.stringify(data)}`,
    );
    try {
      const result = await this.workflowService.workFlowRender(data);
      return {
        data: JSON.stringify(result),
      };
    } catch (error) {
      throw error;
    }
  }

  @GrpcMethod('WorkflowSharedService', 'handleOAuthCallbackApi')
  async handleOAuthCallback() {
    try {
      const result = await this.workflowService.handleOAuthCallback();
      // this.logger.log(
      //   `Successfully processed workflow: ${JSON.stringify(result)}`,
      // );
      return result;
    } catch (error) {
      this.logger.error(`Error in myWorkFLow controller: ${error.message}`);
      throw error;
    }
  }

  @GrpcMethod('WorkflowSharedService', 'UpdateWorkflow')
  async updateWorkflow(data: any) {
    try {
      const result = await this.workflowService.updateWorkflow(data);
      // this.logger.log(
      //   `Successfully updated workflow: ${JSON.stringify(result)}`,
      // );
      return result;
    } catch (error) {
      this.logger.error(`Error in updateWorkflow controller: ${error.message}`);
      throw error;
    }
  }

  @GrpcMethod('WorkflowSharedService', 'ListCourtNotice')
  async getCourtNoticeList(data: GetWorkflowDto) {
    this.logger.log(
      `Received court notice list request with data: ${JSON.stringify(data)}`,
    );
    try {
      const result = await this.workflowService.getCourtNoticeList(data);
      return {
        total: result.total,
        page: result.page,
        limit: result.limit,
        fields: result.fields || [],
      };
    } catch (error) {
      this.logger.error(
        `Error in getCourtNoticeList controller: ${error.message}`,
      );
      throw error;
    }
  }
  @GrpcMethod('WorkflowSharedService', 'UpdateWorkflowStatus')
  async updateWorkflowStatus(data: UpdateWorkflowStatusDto) {
    try {
      const result = await this.workflowService.updateWorkflowStatus(data);
      // this.logger.log(
      //   `Successfully updated workflow: ${JSON.stringify(result)}`,
      // );
      return result;
    } catch (error) {
      this.logger.error(`Error in updateWorkflow controller: ${error.message}`);
      throw error;
    }
  }
  @GrpcMethod('WorkflowSharedService', 'ListCounty')
  async getCountyList() {
    try {
      return await this.workflowService.getCountyList();
    } catch (error) {
      this.logger.error(`Error in getCountyList controller: ${error.message}`);
      throw error;
    }
  }

  @GrpcMethod('WorkflowSharedService', 'ListCourtLocation')
  async getCourtLocationList() {
    try {
      return await this.workflowService.getCourtLocationList();
    } catch (error) {
      this.logger.error(
        `Error in getCourtLocationList controller: ${error.message}`,
      );
      throw error;
    }
  }

  @GrpcMethod('WorkflowSharedService', 'ListUsers')
  async getUserList(data: UserListDto) {
    try {
      return await this.workflowService.getUserList(data);
    } catch (error) {
      this.logger.error(`Error in getUserList controller: ${error.message}`);
      throw error;
    }
  }

  @GrpcMethod('WorkflowSharedService', 'listCourtNoticeType')
  async getCourtNoticeType() {
    try {
      return await this.workflowService.getCourtNoticeType();
    } catch (error) {
      this.logger.error(
        `Error in getCourtNoticeType controller: ${error.message}`,
      );
      throw error;
    }
  }

  @GrpcMethod('WorkflowSharedService', 'ListMatter')
  async getMatterList() {
    try {
      return await this.workflowService.getMatterList();
    } catch (error) {
      this.logger.error(`Error in getMatterList controller: ${error.message}`);
      throw error;
    }
  }

  @GrpcMethod('WorkflowSharedService', 'SearchMatter')
  async searchMatterList(data: { search: string }) {
    this.logger.log(
      `Received searchMatterList request with search term: ${data.search}`,
    );
    try {
      return await this.workflowService.searchMatterList(data.search);
    } catch (error) {
      this.logger.error(
        `Error in searchMatterList controller: ${error.message}`,
      );
      throw error;
    }
  }

  @GrpcMethod('WorkflowSharedService', 'GetNextTaskId')
  async getNextTaskId(data: { task_id: string; work_flow_id: string }) {
    this.logger.log(
      `Received getNextTaskId request with data: ${JSON.stringify(data)}`,
    );
    try {
      const result = await this.workflowService.getNextTaskId(
        data.task_id,
        data.work_flow_id,
      );
      this.logger.log(
        `Successfully got next task ID: ${JSON.stringify(result)}`,
      );
      return result;
    } catch (error) {
      this.logger.error(`Error in getNextTaskId controller: ${error.message}`);
      throw error;
    }
  }

  @GrpcMethod('WorkflowSharedService', 'CreateDemoWorkflow')
  async createDemoWorkflow(data: any) {
    this.logger.log(
      `Received createDemoWorkflow request with data: ${JSON.stringify(data)}`,
    );
    try {
      const result = await this.workflowService.createDemoWorkflow(data);
      this.logger.log(
        `Successfully created demo workflow: ${JSON.stringify(result)}`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Error in createDemoWorkflow controller: ${error.message}`,
      );
      throw error;
    }
  }

  @GrpcMethod('WorkflowSharedService', 'UpdateWorkflowEndDate')
  async updateWorkflowEndDate(data: UpdateWorkflowEndDateDto) {
    this.logger.log(
      `Received updateWorkflowEndDate request with data: ${JSON.stringify(data)}`,
    );
    try {
      const result = await this.workflowService.updateWorkflowEndDate(data);
      this.logger.log(
        `Successfully updated workflow end date: ${JSON.stringify(result)}`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Error in updateWorkflowEndDate controller: ${error.message}`,
      );
      throw error;
    }
  }

  @GrpcMethod('WorkflowSharedService', 'GetPresignedUrl')
  async getPresignedUrl(data: GetPresignedUrlDto) {
    this.logger.log(`Received getPresignedUrl request with key: ${data.key}`);
    try {
      const result = await this.workflowService.getPresignedUrl(data);
      this.logger.log(
        `Successfully generated presigned URL: ${JSON.stringify(result)}`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Error in getPresignedUrl controller: ${error.message}`,
      );
      throw error;
    }
  }

  @GrpcMethod('WorkflowSharedService', 'ArchiveWorkFlow')
  async archiveWorkFlow(data: ArchiveWorkFlowDto) {
    return this.workflowService.archiveWorkFlow(data);
  }

  @GrpcMethod('WorkflowSharedService', 'GetAppointmentEvents')
  async GetAppointmentEvents(data: GetEventListDto) {
    const result = await this.workflowService.GetAppointmentEvents(data);
    return {
      data: JSON.stringify(result),
    };
  }

  @GrpcMethod('WorkflowSharedService', 'GetEventById')
  async getEventById(data: { event_id: string }) {
    try {
      this.logger.log(`Received request to get event by ID: ${data.event_id}`);
      const result = await this.workflowService.getEventById(data);
      return result;
    } catch (error) {
      this.logger.error(`Error in getEventById: ${error.message}`);
      throw new RpcException({
        code: status.INTERNAL,
        message: `Failed to get event: ${error.message}`,
      });
    }
  }

  @GrpcMethod('WorkflowSharedService', 'CheckClientAvailability')
  async checkClientAvailability(data: {
    client_id: string;
    tenant_id?: string;
    access_token?: string;
  }) {
    try {
      this.logger.log(
        `Received request to check client availability for client_id: ${data.client_id}`,
      );
      const result = await this.workflowService.checkClientAvailability(data);
      return result;
    } catch (error) {
      this.logger.error(`Error in checkClientAvailability: ${error.message}`);
      throw new RpcException({
        code: status.INTERNAL,
        message: `Failed to check client availability: ${error.message}`,
      });
    }
  }

  @GrpcMethod('WorkflowSharedService', 'SyncLocationsFromMyCase')
  async syncLocationsFromMyCase(data: GetLocationDto) {
    this.logger.log(
      `Received syncLocationsFromMyCase request with data: ${JSON.stringify(data)}`,
    );
    try {
      const result = await this.workflowService.syncLocationsFromMyCase(data);
      this.logger.log(`Successfully synced locations: ${result.message}`);
      return result;
    } catch (error) {
      this.logger.error(
        `Error in syncLocationsFromMyCase controller: ${error.message}`,
      );
      throw error;
    }
  }

  @GrpcMethod('WorkflowSharedService', 'GetLocationList')
  async getLocationList() {
    this.logger.log('Received getLocationList request');
    try {
      const locations = await this.workflowService.getLocationList();
      this.logger.log(`Successfully fetched ${locations.length} locations`);
      return { locations };
    } catch (error) {
      this.logger.error(
        `Error in getLocationList controller: ${error.message}`,
      );
      throw error;
    }
  }

  @GrpcMethod('WorkflowSharedService', 'GetLocationById')
  async getLocationById(data: { id: string }) {
    this.logger.log(`Received getLocationById request for ID: ${data.id}`);
    try {
      const location = await this.workflowService.getLocationById(data.id);
      this.logger.log(`Successfully fetched location: ${location.name}`);
      return { location };
    } catch (error) {
      this.logger.error(
        `Error in getLocationById controller: ${error.message}`,
      );
      throw error;
    }
  }

  @GrpcMethod('WorkflowSharedService', 'CleanupUploadedFiles')
  async cleanupUploadedFiles(data: CleanupFilesDto) {
    this.logger.log(
      `Received cleanup request for ${data.fileKeys.length} files`,
    );
    try {
      const result = await this.workflowService.cleanupUploadedFiles(data);
      this.logger.log(
        `Cleanup completed: ${result.deletedCount} deleted, ${result.failedCount} failed`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Error in cleanupUploadedFiles controller: ${error.message}`,
      );
      throw error;
    }
  }

  @GrpcMethod('WorkflowSharedService', 'SaveMyCaseWebhookData')
  async saveMyCaseWebhookData(data: any) {
    this.logger.log(
      `Received SaveMyCaseWebhookData request with data: ${JSON.stringify(data)}`,
    );
    try {
      return await this.workflowService.saveMyCaseWebhookData(data);
    } catch (error) {
      this.logger.error(
        `Error in saveMyCaseWebhookData controller: ${error.message}`,
      );
      throw error;
    }
  }

  @GrpcMethod('WorkflowSharedService', 'UpdateWorkflowRetryInfo')
  async updateWorkflowRetryInfo(data: any) {
    this.logger.log(
      `Received updateWorkflowRetryInfo request with data: ${JSON.stringify(data)}`,
    );
    try {
      const result = await this.workflowService.updateWorkflowRetryInfo(data);
      this.logger.log(
        `Successfully updated workflow retry info: ${JSON.stringify(result)}`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Error in updateWorkflowRetryInfo controller: ${error.message}`,
      );
      throw error;
    }
  }

  @GrpcMethod('WorkflowSharedService', 'TriggerManualMyCaseSync')
  async triggerManualMyCaseSync(data: { access_token?: string }) {
    this.logger.log('Received manual MyCase sync trigger request');
    try {
      const result = await this.myCaseSyncService.triggerManualSync(
        data.access_token,
      );
      this.logger.log('Manual MyCase sync completed successfully');
      return {
        success: true,
        message: 'Manual MyCase sync completed successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error(
        `Error in triggerManualMyCaseSync controller: ${error.message}`,
      );
      return {
        success: false,
        message: `Manual MyCase sync failed: ${error.message}`,
        error: error.message,
      };
    }
  }

  @GrpcMethod('WorkflowSharedService', 'GetMyCaseSyncStatus')
  async getMyCaseSyncStatus() {
    this.logger.log('Received MyCase sync status request');
    try {
      const result = await this.myCaseSyncService.getLastSyncStatus();
      this.logger.log('Successfully retrieved MyCase sync status');
      return {
        success: true,
        message: 'MyCase sync status retrieved successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error(
        `Error in getMyCaseSyncStatus controller: ${error.message}`,
      );
      return {
        success: false,
        message: `Failed to retrieve MyCase sync status: ${error.message}`,
        error: error.message,
      };
    }
  }

  @GrpcMethod('WorkflowSharedService', 'TestSuccess')
  async testSuccess(data: any) {
    this.logger.log('Received TestSuccess request');
    this.logger.log(`Request data: ${JSON.stringify(data)}`);

    try {
      // Call the service method to handle the logic and store data
      const result = await this.workflowService.handleTestSuccess(data);
      return result;
    } catch (error) {
      this.logger.error(`Error in testSuccess controller: ${error.message}`);
      return {
        success: false,
        message: `Test failed: ${error.message}`,
        received_params_json: data.params_json || '{}',
      };
    }
  }
  @GrpcMethod('WorkflowSharedService', 'GetAvailableAssignees')
  async getAvailableAssignees(data: GetAssigneesDto) {
    this.logger.log(`Received getAvailableAssignees request: ${JSON.stringify(data)}`);
    try {
      const assignees = await this.assignmentService.getAvailableAssignees(data.search);
      return { assignees };
    } catch (error) {
      this.logger.error(`Error in getAvailableAssignees: ${error.message}`);
      throw new RpcException({
        code: status.INTERNAL,
        message: `Failed to get available assignees: ${error.message}`,
      });
    }
  }

  @GrpcMethod('WorkflowSharedService', 'GetAssigneesByCategory')
  async getAssigneesByCategory(data: GetAssigneesDto) {
    this.logger.log(`Received getAssigneesByCategory request: ${JSON.stringify(data)}`);
    try {
      if (!data.category) {
        throw new Error('Category is required');
      }
      const assignees = await this.assignmentService.getAssigneesByCategory(data.category, data.search);
      return { assignees };
    } catch (error) {
      this.logger.error(`Error in getAssigneesByCategory: ${error.message}`);
      throw new RpcException({
        code: status.INTERNAL,
        message: `Failed to get assignees by category: ${error.message}`,
      });
    }
  }

  @GrpcMethod('WorkflowSharedService', 'GetRoleMembers')
  async getRoleMembers(data: GetRoleMembersDto) {
    this.logger.log(`Received getRoleMembers request: ${JSON.stringify(data)}`);
    try {
      const members = await this.assignmentService.getRoleMembers(data.role_id);
      return { members };
    } catch (error) {
      this.logger.error(`Error in getRoleMembers: ${error.message}`);
      throw new RpcException({
        code: status.INTERNAL,
        message: `Failed to get role members: ${error.message}`,
      });
    }
  }

  @GrpcMethod('WorkflowSharedService', 'AssignUserToTask')
  async assignUserToTask(data: any) {
    this.logger.log(`Received assignUserToTask request: ${JSON.stringify(data)}`);
    try {
      const result = await this.workflowService.assignUserToTask(data);
      return result;
    } catch (error) {
      this.logger.error(`Error in assignUserToTask: ${error.message}`);
      throw new RpcException({
        code: status.INTERNAL,
        message: `Failed to assign user to task: ${error.message}`,
      });
    }
  }

  @GrpcMethod('WorkflowSharedService', 'RemoveAssignment')
  async removeAssignment(data: RemoveAssignmentDto) {
    this.logger.log(`Received removeAssignment request: ${JSON.stringify(data)}`);
    try {
      await this.assignmentService.removeAssignment(
        data.task_execution_id,
        data.assignee_id,
        data.assignment_type,
      );
      return { success: true, message: 'Assignment removed successfully' };
    } catch (error) {
      this.logger.error(`Error in removeAssignment: ${error.message}`);
      throw new RpcException({
        code: status.INTERNAL,
        message: `Failed to remove assignment: ${error.message}`,
      });
    }
  }

  @GrpcMethod('WorkflowSharedService', 'RemoveUserFromTask')
  async removeUserFromTask(data: any) {
    this.logger.log(`Received removeUserFromTask request: ${JSON.stringify(data)}`);
    try {
      const result = await this.workflowService.removeUserFromTask(data);
      return result;
    } catch (error) {
      this.logger.error(`Error in removeUserFromTask: ${error.message}`);
      throw new RpcException({
        code: status.INTERNAL,
        message: `Failed to remove user from task: ${error.message}`,
      });
    }
  }

  @GrpcMethod('WorkflowSharedService', 'GetAllUsersAndRoles')
  async getAllUsersAndRoles(data: { search?: string }) {
    this.logger.log(`Received getAllUsersAndRoles request: ${JSON.stringify(data)}`);
    try {
      const result = await this.workflowService.getAllUsersAndRoles(data);
      return result;
    } catch (error) {
      this.logger.error(`Error in getAllUsersAndRoles: ${error.message}`);
      throw new RpcException({
        code: status.INTERNAL,
        message: `Failed to get users and roles: ${error.message}`,
      });
    }
  }

  @GrpcMethod('WorkflowSharedService', 'CheckUserTaskAssignment')
  async checkUserTaskAssignment(data: any) {
    this.logger.log(`Received checkUserTaskAssignment request: ${JSON.stringify(data)}`);
    try {
      const result = await this.workflowService.checkUserTaskAssignment(data);
      this.logger.log(`Successfully checked user task assignment: ${JSON.stringify(result)}`);
      return result;
    } catch (error) {
      this.logger.error(`Error in checkUserTaskAssignment: ${error.message}`);
      throw error;
    }
  }

  @GrpcMethod('WorkflowSharedService', 'GetTaskAssignedUsers')
  async getTaskAssignedUsers(data: { task_id: string; workflow_execution_id: string }) {
    this.logger.log(`Received getTaskAssignedUsers request: ${JSON.stringify(data)}`);
    try {
      const result = await this.workflowService.getTaskAssignedUsers(data);
      this.logger.log(`Successfully got task assigned users: ${JSON.stringify(result)}`);
      return result;
    } catch (error) {
      this.logger.error(`Error in getTaskAssignedUsers: ${error.message}`);
      throw new RpcException({
        code: status.INTERNAL,
        message: `Failed to get task assigned users: ${error.message}`,
      });
    }
  }

  /**
   * Get round robin statistics for a specific context
   */
  @GrpcMethod('WorkflowSharedService', 'GetRoundRobinStats')
  async getRoundRobinStats(data: { context_type: 'role' | 'workflow' | 'global'; context_id?: string; role_id?: string }) {
    this.logger.log(`Received getRoundRobinStats request: ${JSON.stringify(data)}`);
    try {
      const context: RoundRobinContext = {
        context_type: data.context_type,
        context_id: data.context_id,
        role_id: data.role_id
      };

      const result = await this.roundRobinService.getRoundRobinStats(context);
      this.logger.log(`Successfully got round robin stats: ${JSON.stringify(result)}`);
      return result;
    } catch (error) {
      this.logger.error(`Error in getRoundRobinStats: ${error.message}`);
      throw new RpcException({
        code: status.INTERNAL,
        message: `Failed to get round robin stats: ${error.message}`,
      });
    }
  }

  /**
   * Reset round robin state for a specific context
   */
  @GrpcMethod('WorkflowSharedService', 'ResetRoundRobinState')
  async resetRoundRobinState(data: { context_type: 'role' | 'workflow' | 'global'; context_id?: string; role_id?: string }) {
    this.logger.log(`Received resetRoundRobinState request: ${JSON.stringify(data)}`);
    try {
      const context: RoundRobinContext = {
        context_type: data.context_type,
        context_id: data.context_id,
        role_id: data.role_id
      };

      await this.roundRobinService.resetRoundRobinState(context);
      this.logger.log(`Successfully reset round robin state for context: ${JSON.stringify(context)}`);

      return {
        success: true,
        message: 'Round robin state reset successfully',
        context
      };
    } catch (error) {
      this.logger.error(`Error in resetRoundRobinState: ${error.message}`);
      throw new RpcException({
        code: status.INTERNAL,
        message: `Failed to reset round robin state: ${error.message}`,
      });
    }
  }

  /**
   * Assign task using round robin
   */
  @GrpcMethod('WorkflowSharedService', 'AssignRoundRobinToTask')
  async assignRoundRobinToTask(data: {
    task_execution_id: string;
    role_id: string;
    assigned_by: string;
    workflow_execution_id?: string;
    notes?: string
  }) {
    this.logger.log(`Received assignRoundRobinToTask request: ${JSON.stringify(data)}`);
    try {
      const result = await this.workflowService.assignRoundRobinToTask(data);
      this.logger.log(`Successfully assigned round robin to task: ${JSON.stringify(result)}`);
      return result;
    } catch (error) {
      this.logger.error(`Error in assignRoundRobinToTask: ${error.message}`);
      throw new RpcException({
        code: status.INTERNAL,
        message: `Failed to assign round robin to task: ${error.message}`,
      });
    }
  }
}
