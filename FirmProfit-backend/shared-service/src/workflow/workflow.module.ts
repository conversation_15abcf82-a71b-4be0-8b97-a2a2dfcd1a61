import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ScheduleModule } from '@nestjs/schedule';
import { WorkflowController } from './workflow.controller';
import { WorkflowService } from './workflow.service';
import { MyCaseService } from './mycase.service';
import { LocationService } from './services/location.service';
import { MyCaseSyncService } from './services/mycase-sync.service';
import { WebhookService } from './services/webhook.service';
import { ClientService } from './services/client.service';
import { MatterService } from './services/matter.service';
import { AssignmentService } from './services/assignment.service';
import { RoundRobinService } from './services/round-robin.service';
import { MongooseModule } from '@nestjs/mongoose';
import { Template, TemplateSchema } from './entities/template.entity';
// Import shared entities
import {
  Category,
  CategorySchema,
  Client,
  ClientSchema,
  Location,
  LocationSchema,
} from '@shared/database';
// Import local entities that are not compatible with shared
import { User, UserSchema } from './entities/user.entity';
import {
  FormComponent,
  FormComponentSchema,
} from './entities/form-component.entity';
import {
  DerivedComponent,
  DerivedComponentSchema,
} from './entities/derived-component.entity';
import { Trigger, TriggerSchema } from './entities/trigger.entity';
import { Task, TaskSchema } from './entities/task.entity';
import {
  WorkflowExecution,
  WorkflowExecutionSchema,
} from './entities/workflow-execution.entity';
import { View, ViewSchema } from './entities/view.entity';
import {
  TaskExecution,
  TaskExecutionSchema,
} from './entities/task-execution.entity';
import { County, CountySchema } from './entities/counties.entity';
import { Attendees, AttendeesSchema } from './entities/attendees';
import {
  CourtLocation,
  CourtLocationSchema,
} from './entities/court.location.entity';
import { UserMetaData, UserMetaDataSchema } from './entities/user-meta.entity';
import {
  CourtNoticeType,
  CourtNoticeTypeSchema,
} from './entities/court.notice.type.entity';
import { Matter, MatterSchema } from './entities/matter.entity';
import {
  CourtNoticeAction,
  CourtNoticeActionSchema,
} from './entities/court-notice-action';
import { S3Service } from '../common/s3/s3.service';
import {
  MycaseClientMatter,
  MycaseClientMatterSchema,
} from './entities/mycase.client.matter.entity';
import {
  MyCaseHistory,
  MyCaseHistorySchema,
} from './entities/my-case-history.entity';
import {
  EventActionHistory,
  EventActionHistorySchema,
} from './entities/event-action-history';
import { EventService } from './services/event.service';
import {
  WebhookResponse,
  WebhookResponseSchema,
} from './entities/webhook.response.entity';
import { TaskAssignment, TaskAssignmentSchema } from './entities/task-assignment.entity';
import { Role, RoleSchema } from './entities/roles.entity';
import { UserRole, UserRoleSchema } from './entities/user-role.entity';
import { RoundRobinState, RoundRobinStateSchema } from './entities/round-robin-state.entity';
import { ConfigGroup, ConfigGroupSchema } from './entities/config-group.entity';

@Module({
  imports: [
    HttpModule,
    ScheduleModule.forRoot(),
    MongooseModule.forFeature([
      { name: Template.name, schema: TemplateSchema },
      { name: Category.name, schema: CategorySchema },
      { name: FormComponent.name, schema: FormComponentSchema },
      { name: DerivedComponent.name, schema: DerivedComponentSchema },
      { name: Trigger.name, schema: TriggerSchema },
      { name: Task.name, schema: TaskSchema },
      { name: User.name, schema: UserSchema },
      { name: WorkflowExecution.name, schema: WorkflowExecutionSchema },
      { name: TaskExecution.name, schema: TaskExecutionSchema },
      { name: View.name, schema: ViewSchema },
      { name: County.name, schema: CountySchema },
      { name: Attendees.name, schema: AttendeesSchema },
      { name: CourtLocation.name, schema: CourtLocationSchema },
      { name: UserMetaData.name, schema: UserMetaDataSchema },
      { name: CourtNoticeType.name, schema: CourtNoticeTypeSchema },
      { name: Matter.name, schema: MatterSchema },
      { name: Client.name, schema: ClientSchema },
      { name: CourtNoticeAction.name, schema: CourtNoticeActionSchema },
      { name: MycaseClientMatter.name, schema: MycaseClientMatterSchema },
      { name: MyCaseHistory.name, schema: MyCaseHistorySchema },
      { name: EventActionHistory.name, schema: EventActionHistorySchema },
      { name: WebhookResponse.name, schema: WebhookResponseSchema },
      { name: Location.name, schema: LocationSchema },
      { name: TaskAssignment.name, schema: TaskAssignmentSchema },
      { name: Role.name, schema: RoleSchema },
      { name: UserRole.name, schema: UserRoleSchema },
      { name: RoundRobinState.name, schema: RoundRobinStateSchema },
      { name: ConfigGroup.name, schema: ConfigGroupSchema },
    ]),
  ],
  controllers: [WorkflowController],
  providers: [
    WorkflowService,
    MyCaseService,
    LocationService,
    ClientService,
    MatterService,
    WebhookService,
    EventService,
    MyCaseSyncService,
    S3Service,
    AssignmentService,
    RoundRobinService,
  ],
  exports: [
    WorkflowService,
    MyCaseService,
    LocationService,
    ClientService,
    MatterService,
    EventService,
    MyCaseSyncService,
    WebhookService,
    AssignmentService,
    RoundRobinService,
  ],
})
export class WorkflowModule { }
