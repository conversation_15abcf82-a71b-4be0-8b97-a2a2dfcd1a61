import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { HttpService } from '@nestjs/axios';
import { MyCaseService } from './mycase.service';
import { S3Service } from '../common/s3/s3.service';
import { MycaseClientMatter } from './entities/mycase.client.matter.entity';
import { TaskExecution } from './entities/task-execution.entity';
import { MyCaseHistory } from './entities/my-case-history.entity';
import { User } from './entities/user.entity';
import { Matter } from './entities/matter.entity';
import { CourtLocation } from './entities/court.location.entity';
import { WorkflowExecution } from './entities/workflow-execution.entity';
import { EventSub } from './entities/derived.field.entity';
import { of } from 'rxjs';
import { Types } from 'mongoose';

describe('MyCaseService - Duplicate Prevention', () => {
  let service: MyCaseService;

  const mockModels = {
    MycaseClientMatter: {
      updateOne: jest.fn(),
      findOneAndUpdate: jest.fn(),
      deleteMany: jest.fn(),
    },
    TaskExecution: {
      findOneAndUpdate: jest.fn(),
      findOne: jest.fn(),
      updateOne: jest.fn(),
    },
    MyCaseHistory: {
      create: jest.fn(),
      find: jest.fn(),
    },
    User: {
      findOne: jest.fn(),
    },
    Matter: {
      findOne: jest.fn(),
    },
    CourtLocation: {
      findOne: jest.fn(),
    },
    WorkflowExecution: {
      findOne: jest.fn(),
    },
  };

  const mockHttpService = {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
  };

  // Fixed S3Service mock to properly handle getObject method
  const mockS3Service = {
    downloadFileFromS3: jest.fn(),
    getObject: jest.fn().mockResolvedValue({
      Body: {
        transformToByteArray: jest
          .fn()
          .mockResolvedValue(Buffer.from('test file content')),
      },
      ContentLength: 1024,
      ContentType: 'application/pdf',
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MyCaseService,
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
        {
          provide: S3Service,
          useValue: mockS3Service,
        },
        {
          provide: getModelToken(MycaseClientMatter.name),
          useValue: mockModels.MycaseClientMatter,
        },
        {
          provide: getModelToken(TaskExecution.name),
          useValue: mockModels.TaskExecution,
        },
        {
          provide: getModelToken(MyCaseHistory.name),
          useValue: mockModels.MyCaseHistory,
        },
        {
          provide: getModelToken(User.name),
          useValue: mockModels.User,
        },
        {
          provide: getModelToken(Matter.name),
          useValue: mockModels.Matter,
        },
        {
          provide: getModelToken(CourtLocation.name),
          useValue: mockModels.CourtLocation,
        },
        {
          provide: getModelToken(WorkflowExecution.name),
          useValue: mockModels.WorkflowExecution,
        },
      ],
    }).compile();

    service = module.get<MyCaseService>(MyCaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Duplicate Prevention Tests', () => {
    const validObjectId = new Types.ObjectId();

    const mockEvent: EventSub = {
      id: 'test-event-id',
      description: 'Test Event',
      date: '2024-01-01',
      startTime: '10:00',
      endTime: '11:00',
      files: [
        {
          id: 1,
          name: 'test-document.pdf',
          key: 's3-key-123',
          uniqueId: 'unique-123',
          mycaseDocumentId: null,
          lastUploadedKey: null,
          fileSize: 1024,
          uploadedAt: null,
        },
      ],
    } as EventSub;

    const mockExistingDocuments = [
      {
        id: 456,
        filename: 'test-document.pdf',
        path: 'workflow_uploads/test-document.pdf',
        size: 1024,
        created_at: '2024-01-01T10:00:00Z',
      },
    ];

    describe('processEventFilesIntelligently', () => {
      it('should skip upload if document already exists and has not changed', async () => {
        // Mock existing documents API call with correct response format
        mockHttpService.get.mockReturnValue(
          of({
            data: { documents: mockExistingDocuments },
          }),
        );

        const result = await service['processEventFilesIntelligently'](
          mockEvent,
          'test-case-id',
          'test-token',
        );

        expect(result).toHaveLength(1);
        expect(result[0].result.success).toBe(true);
        expect(result[0].result.error).toContain('skipped upload');
        expect(mockHttpService.post).not.toHaveBeenCalled(); // Should not create new document
      });

      it('should update existing document if content has changed', async () => {
        const eventWithChangedFile = {
          ...mockEvent,
          files: [
            {
              ...mockEvent.files[0],
              key: 's3-key-456', // Different key indicates changed content
              mycaseDocumentId: '456',
            },
          ],
        };

        // Mock existing documents API call
        mockHttpService.get.mockReturnValue(
          of({
            data: { documents: mockExistingDocuments },
          }),
        );

        // Mock document deletion success
        mockHttpService.delete.mockReturnValue(
          of({
            data: { success: true },
          }),
        );

        // Mock document creation success
        mockHttpService.post.mockReturnValue(
          of({
            data: {
              id: 789,
              filename: 'test-document.pdf',
              path: 'workflow_uploads/test-document.pdf',
            },
          }),
        );

        const result = await service['processEventFilesIntelligently'](
          eventWithChangedFile,
          'test-case-id',
          'test-token',
        );

        expect(result).toHaveLength(1);
        expect(result[0].result.success).toBe(true);
        expect(result[0].result.fileId).toBe('789');
        expect(mockHttpService.delete).toHaveBeenCalledWith(
          expect.stringContaining('/documents/456'),
          expect.any(Object),
        );
        expect(mockHttpService.post).toHaveBeenCalledWith(
          expect.stringContaining('/documents'),
          expect.any(Object),
          expect.any(Object),
        );
      });

      it('should create new document if none exists', async () => {
        // Mock empty existing documents response
        mockHttpService.get.mockReturnValue(
          of({
            data: { documents: [] },
          }),
        );

        // Mock document creation success
        mockHttpService.post.mockReturnValue(
          of({
            data: {
              id: 123,
              filename: 'test-document.pdf',
              path: 'workflow_uploads/test-document.pdf',
            },
          }),
        );

        const result = await service['processEventFilesIntelligently'](
          mockEvent,
          'test-case-id',
          'test-token',
        );

        expect(result).toHaveLength(1);
        expect(result[0].result.success).toBe(true);
        expect(result[0].result.fileId).toBe('123');
        expect(mockHttpService.post).toHaveBeenCalledWith(
          expect.stringContaining('/documents'),
          expect.any(Object),
          expect.any(Object),
        );
      });

      it('should handle file download errors gracefully', async () => {
        // Mock S3 service to throw error
        mockS3Service.getObject.mockRejectedValue(
          new Error('S3 download failed'),
        );

        // Mock empty existing documents response
        mockHttpService.get.mockReturnValue(
          of({
            data: { documents: [] },
          }),
        );

        const result = await service['processEventFilesIntelligently'](
          mockEvent,
          'test-case-id',
          'test-token',
        );

        expect(result).toHaveLength(1);
        expect(result[0].result.success).toBe(false);
        expect(result[0].result.error).toContain('S3 download failed');
      });

      it('should handle MyCase API errors gracefully', async () => {
        // Mock existing documents API call to throw error
        mockHttpService.get.mockReturnValue(
          of({
            data: { documents: [] },
          }),
        );

        // Mock document creation to fail
        mockHttpService.post.mockReturnValue(
          of({
            data: { error: 'MyCase API error' },
          }),
        );

        const result = await service['processEventFilesIntelligently'](
          mockEvent,
          'test-case-id',
          'test-token',
        );

        expect(result).toHaveLength(1);
        expect(result[0].result.success).toBe(false);
        expect(result[0].result.error).toContain('Failed to create document');
      });
    });

    describe('updateEventFilesInTaskExecution', () => {
      it('should update task execution with document metadata', async () => {
        const updatedEvent = {
          ...mockEvent,
          files: [
            {
              ...mockEvent.files[0],
              mycaseDocumentId: '123',
              lastUploadedKey: 's3-key-123',
              uploadedAt: new Date(),
            },
          ],
        };

        // Mock successful database update using updateOne
        mockModels.TaskExecution.updateOne.mockResolvedValue({
          acknowledged: true,
          modifiedCount: 1,
        });

        await service['updateEventFilesInTaskExecution'](
          validObjectId.toString(),
          'test-event-id',
          updatedEvent,
        );

        expect(mockModels.TaskExecution.updateOne).toHaveBeenCalledWith(
          { workflow_execution_id: expect.any(Types.ObjectId) },
          expect.objectContaining({
            $set: expect.objectContaining({
              'derived_field.$[].client.$[].matter.$[].event.$[eventElem].files':
                updatedEvent.files,
            }),
          }),
          expect.objectContaining({
            arrayFilters: [{ 'eventElem.id': 'test-event-id' }],
          }),
        );
      });

      it('should handle database update errors gracefully', async () => {
        const updatedEvent = {
          ...mockEvent,
          files: [
            {
              ...mockEvent.files[0],
              mycaseDocumentId: '123',
              lastUploadedKey: 's3-key-123',
              uploadedAt: new Date(),
            },
          ],
        };

        // Mock database update to fail
        mockModels.TaskExecution.updateOne.mockRejectedValue(
          new Error('Database update failed'),
        );

        // Should not throw error, just log it
        await expect(
          service['updateEventFilesInTaskExecution'](
            validObjectId.toString(),
            'test-event-id',
            updatedEvent,
          ),
        ).resolves.not.toThrow();
      });
    });

    describe('Integration Tests', () => {
      it('should create event with duplicate prevention', async () => {
        const mockUser = {
          _id: validObjectId,
          email: '<EMAIL>',
          mycaseApiKey: 'test-api-key',
        };

        const mockMatter = {
          _id: validObjectId,
          mycaseId: 'test-case-id',
        };

        const mockWorkflowExecution = {
          _id: validObjectId,
          matterId: validObjectId,
          userId: validObjectId,
        };

        // Mock database queries
        mockModels.User.findOne.mockResolvedValue(mockUser);
        mockModels.Matter.findOne.mockResolvedValue(mockMatter);
        mockModels.WorkflowExecution.findOne.mockResolvedValue(
          mockWorkflowExecution,
        );

        // Mock existing documents API call
        mockHttpService.get.mockReturnValue(
          of({
            data: { documents: [] },
          }),
        );

        // Mock event creation success
        mockHttpService.post
          .mockReturnValueOnce(
            of({
              data: {
                id: 789,
                filename: 'test-document.pdf',
                path: 'workflow_uploads/test-document.pdf',
              },
            }),
          )
          .mockReturnValueOnce(
            of({
              data: {
                id: 'event-123',
                description: 'Test Event',
                date: '2024-01-01',
              },
            }),
          );

        // Mock task execution update
        mockModels.TaskExecution.updateOne.mockResolvedValue({
          acknowledged: true,
          modifiedCount: 1,
        });

        // Mock history creation
        mockModels.MyCaseHistory.create.mockResolvedValue({
          _id: validObjectId,
          action: 'CREATE_EVENT',
          success: true,
        });

        const result = await service.createEventInMyCase(
          mockEvent,
          'location-id',
          'test-case-id',
          validObjectId.toString(),
          'test-token',
        );

        expect(result.success).toBe(true);
        expect(result.myCaseEventId).toBe('event-123');
        expect(mockHttpService.post).toHaveBeenCalledTimes(2); // Once for document, once for event
      });

      it('should update event with duplicate prevention', async () => {
        const mockUser = {
          _id: validObjectId,
          email: '<EMAIL>',
          mycaseApiKey: 'test-api-key',
        };

        const mockMatter = {
          _id: validObjectId,
          mycaseId: 'test-case-id',
        };

        const mockWorkflowExecution = {
          _id: validObjectId,
          matterId: validObjectId,
          userId: validObjectId,
        };

        // Mock database queries
        mockModels.User.findOne.mockResolvedValue(mockUser);
        mockModels.Matter.findOne.mockResolvedValue(mockMatter);
        mockModels.WorkflowExecution.findOne.mockResolvedValue(
          mockWorkflowExecution,
        );

        // Mock existing documents API call
        mockHttpService.get.mockReturnValue(
          of({
            data: { documents: mockExistingDocuments },
          }),
        );

        // Mock event update success
        mockHttpService.put.mockReturnValue(
          of({
            data: {
              id: 'event-123',
              description: 'Updated Test Event',
              date: '2024-01-01',
            },
          }),
        );

        // Mock task execution update
        mockModels.TaskExecution.updateOne.mockResolvedValue({
          acknowledged: true,
          modifiedCount: 1,
        });

        // Mock history creation
        mockModels.MyCaseHistory.create.mockResolvedValue({
          _id: validObjectId,
          action: 'UPDATE_EVENT',
          success: true,
        });

        const result = await service.updateEventInMyCaseWithDocumentUpdate(
          mockEvent,
          'event-123',
          'location-id',
          'test-case-id',
          validObjectId.toString(),
          'test-token',
        );

        expect(result.success).toBe(true);
        expect(mockHttpService.put).toHaveBeenCalledWith(
          expect.stringContaining('/events/event-123'),
          expect.any(Object),
          expect.any(Object),
        );
      });
    });
  });
});
