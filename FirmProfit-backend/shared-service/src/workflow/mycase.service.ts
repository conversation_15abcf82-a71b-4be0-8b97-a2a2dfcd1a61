import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { MycaseClientMatter } from './entities/mycase.client.matter.entity';
import { TaskExecution } from './entities/task-execution.entity';
import { EventSub } from './entities/derived.field.entity';
import { MyCaseHistory } from './entities/my-case-history.entity';
import { User } from './entities/user.entity';
import { Matter } from './entities/matter.entity';
import { CourtLocation } from './entities/court.location.entity';
import { WorkflowExecution } from './entities/workflow-execution.entity';
import * as moment from 'moment-timezone';
import { S3Service } from '../common/s3/s3.service';
import {
  MyCaseLocationInterface,
  MyCaseLocationListResponse,
} from './dto/location.dto';
import {
  MyCaseClientInterface,
  MyCaseClientListResponse,
} from './services/client.service';
import {
  MyCaseMatterInterface,
  MyCaseMatterListResponse,
} from './services/matter.service';
import {
  MyCaseEventInterface,
  MyCaseEventListResponse,
} from './services/event.service';
import {
  MyCaseEventWebhookPayload,
  MyCaseCase,
  MyCaseCaseResponse,
} from './dto/mycase-webhook.dto';
import { EventStatus } from './enum/template-status.enum';

export interface MyCaseEventPayload {
  name: string;
  description: string;
  start: string;
  end: string;
  all_day: boolean;
  private: boolean;
  location?: {
    id: number;
  };
  case: {
    id: number;
  };
  staff?: Array<{
    id: number;
    required: boolean;
  }>;
}

export interface MyCaseFileUploadResult {
  success: boolean;
  fileId?: string;
  error?: string;
}

export interface MyCaseDocumentDeleteResult {
  success: boolean;
  documentId?: string;
  error?: string;
}

export interface MyCaseDocumentUpdatePayload {
  path: string;
  filename: string;
  description?: string;
  assigned_date?: string;
  staff?: Array<{
    id: number;
  }>;
}

export interface MyCaseDocumentUpdateResult {
  success: boolean;
  documentId?: string;
  updatedData?: any;
  error?: string;
}
export interface WebhookEventContext {
  matter?: Matter;
  workflowExecution?: WorkflowExecution;
  courtLocation?: CourtLocation;
  caseDetails?: MyCaseCase;
}

@Injectable()
export class MyCaseService {
  private readonly logger = new Logger(MyCaseService.name);
  private readonly MYCASE_API_BASE_URL: string;

  constructor(
    @InjectModel(MycaseClientMatter.name)
    private readonly mycaseClientMatterModel: Model<MycaseClientMatter>,
    @InjectModel(TaskExecution.name)
    private readonly taskExecutionModel: Model<TaskExecution>,
    @InjectModel(MyCaseHistory.name)
    private readonly myCaseHistoryModel: Model<MyCaseHistory>,
    @InjectModel(User.name)
    private readonly userModel: Model<User>,
    @InjectModel(Matter.name)
    private readonly matterModel: Model<Matter>,
    @InjectModel(CourtLocation.name)
    private readonly courtLocationModel: Model<CourtLocation>,
    @InjectModel(WorkflowExecution.name)
    private readonly workflowExecutionModel: Model<WorkflowExecution>,
    private readonly httpService: HttpService,
    private readonly s3Service: S3Service,
  ) {
    this.MYCASE_API_BASE_URL = process.env.MYCASE_API_BASE_URL;
  }

  private getHeaders(accessToken?: string, contentType = 'application/json') {
    return {
      'Content-Type': contentType,
      Authorization: `Bearer ${accessToken}`,
    };
  }

  private async parseAllAttendees(
    requiredAttendees: string,
    optionalAttendees: string,
  ): Promise<Array<{ id: number; required: boolean }>> {
    const staffMembers: Array<{ id: number; required: boolean }> = [];
    // Track staff IDs with their required status - prioritize required=true
    const processedStaffMap = new Map<number, boolean>(); // staffId -> required status

    // Helper function to process attendees
    const processAttendeeList = async (
      attendeeString: string,
      isRequired: boolean,
    ) => {
      if (!attendeeString || attendeeString.trim() === '') {
        return;
      }

      const attendeeNames = attendeeString
        .split(',')
        .map((name) => name.trim());

      console.log(
        `🚀 ~ MyCaseService ~ ${isRequired ? 'required' : 'optional'}AttendeeNames:`,
        attendeeNames,
      );

      for (const fullName of attendeeNames) {
        if (!fullName) continue;

        const nameParts = fullName.split(' ').map((part) => part.trim());
        if (nameParts.length < 2) {
          this.logger.warn(
            `Invalid name format: ${fullName}. Expected format: "first_name last_name"`,
          );
          continue;
        }

        const firstName = nameParts[0];
        const lastName = nameParts.slice(1).join(' '); // Handle cases where last name has multiple parts

        try {
          const user = await this.userModel
            .findOne({
              first_name: { $regex: new RegExp(`^${firstName}$`, 'i') },
              last_name: { $regex: new RegExp(`^${lastName}$`, 'i') },
            })
            .exec();

          console.log(
            `🚀 ~ MyCaseService ~ ${isRequired ? 'required' : 'optional'} - user:`,
            user,
          );

          if (user && user.my_case_staff_id) {
            const staffId = parseInt(user.my_case_staff_id);
            const existingRequiredStatus = processedStaffMap.get(staffId);

            if (existingRequiredStatus === undefined) {
              // Staff ID not processed yet, add it
              processedStaffMap.set(staffId, isRequired);
              this.logger.log(
                `Added ${isRequired ? 'required' : 'optional'} user: ${firstName} ${lastName} with MyCase staff ID: ${user.my_case_staff_id}`,
              );
            } else if (
              existingRequiredStatus === false &&
              isRequired === true
            ) {
              // Update from optional to required (prioritize required=true)
              processedStaffMap.set(staffId, true);
              this.logger.log(
                `Updated user ${firstName} ${lastName} from optional to required`,
              );
            } else {
              // Either same status or trying to downgrade from required to optional - skip
              this.logger.log(
                `User ${firstName} ${lastName} already processed with same or higher priority, skipping`,
              );
            }
          } else {
            this.logger.warn(
              `User not found or missing MyCase staff ID: ${firstName} ${lastName}`,
            );
          }
        } catch (error) {
          this.logger.error(
            `Error looking up user ${firstName} ${lastName}: ${error.message}`,
          );
        }
      }
    };

    // Process required attendees first
    await processAttendeeList(requiredAttendees, true);

    // Process optional attendees
    await processAttendeeList(optionalAttendees, false);

    // Convert map to array
    for (const [staffId, isRequired] of processedStaffMap.entries()) {
      staffMembers.push({
        id: staffId,
        required: isRequired,
      });
    }

    return staffMembers;
  }

  private async transformEventToMyCasePayload(
    event: any,
    myCaseMatterId: string,
    my_case_location_id: number,
  ): Promise<MyCaseEventPayload> {
    console.log('🚀 ~ MyCaseService ~ event:', event);

    // Extract the actual data from Mongoose subdocument
    const eventData = event._doc || event;

    console.log('🚀 ~ MyCaseService ~ eventData:', eventData);

    const staffMembers = await this.parseAllAttendees(
      eventData.requiredAttendees || '',
      eventData.optionalAttendees || '',
    );

    // Add default staff only when no staff members found (length === 0)
    const finalStaffMembers =
      staffMembers.length === 0
        ? [
          {
            id: 61054170,
            required: true,
          },
        ]
        : staffMembers;

    let start: moment.Moment;
    let end: moment.Moment;

    if (eventData.allDay) {
      console.log('===============if call=============');
      // For all-day events, use only the date and set to start of day
      // Use IST timezone and convert to UTC
      const startDate =
        eventData.startDate || new Date().toISOString().split('T')[0];
      const endDate =
        eventData.endDate || new Date().toISOString().split('T')[0];
      start = eventData.startTime
      end = eventData.endTime
    } else {
      console.log('===============else call=============', eventData.startDate);

      // For non-all-day events, combine date with time as IST and convert to UTC
      const startDate =
        eventData.startDate || new Date().toISOString().split('T')[0];
      const endDate =
        eventData.endDate || new Date().toISOString().split('T')[0];
      const startTime = eventData.startTime || '09:00';
      const endTime = eventData.endTime || '10:00';

      // Create datetime strings in YYYY-MM-DD HH:mm format
      const startDateTime = `${startDate} ${startTime}`;
      const endDateTime = `${endDate} ${endTime}`;

      // Parse the combined date-time strings as IST timezone and convert to UTC
      // This ensures IST times are properly converted to UTC for MyCase
      start = eventData.startTime
      end = eventData.startTime

      // Log for debugging
      this.logger.log(
        `Original startDate: ${startDate}, startTime: ${startTime} (IST)`,
      );
      this.logger.log(`Original endDate: ${endDate}, endTime: ${endTime} (IST)`);
      this.logger.log(`Combined startDateTime: ${startDateTime} (IST)`);
      this.logger.log(`Combined endDateTime: ${endDateTime} (IST)`);
      this.logger.log(
        `Converted start moment (UTC): ${start.format('YYYY-MM-DD HH:mm')}`,
      );
      this.logger.log(`Converted end moment (UTC): ${end.format('YYYY-MM-DD HH:mm')}`);
    }

    // Ensure myCaseMatterId is a valid number
    const caseId =
      myCaseMatterId && myCaseMatterId !== 'null' ? +myCaseMatterId : null;
    if (!caseId) {
      this.logger.warn(`Invalid myCaseMatterId: ${myCaseMatterId}, using null`);
    }

    const payload = {
      name: eventData.subject || eventData.description || 'Event',
      description: eventData.description || '',
      start: start.toISOString(),
      end: end.toISOString(),
      all_day: !!eventData.allDay,
      private: false,
      location: {
        id: my_case_location_id,
      },
      case: {
        id: caseId,
      },
      staff: finalStaffMembers,
    };

    this.logger.log(`Final payload start (converted to UTC): ${payload.start}`);
    this.logger.log(`Final payload end (converted to UTC): ${payload.end}`);
    this.logger.log(`Final payload name (subject): ${payload.name}`);
    this.logger.log(`Event subject received: ${eventData.subject}`);
    this.logger.log(
      `Final staff members: ${JSON.stringify(finalStaffMembers)}`,
    );

    return payload;
  }

  private async createHistoryEntry(
    eventId: string,
    operation: 'CREATE' | 'UPDATE' | 'DELETE',
    success: boolean,
    response: any,
    error?: any,
  ): Promise<void> {
    console.log('🚀 ~ MyCaseService ~ eventId:', eventId);
    try {
      // Ensure eventId is provided
      if (!eventId) {
        this.logger.error('Cannot create history entry: eventId is required');
        return;
      }

      const historyData = {
        event_id: eventId,
        response: {
          operation,
          success,
          timestamp: new Date().toISOString(),
          ...(success ? { data: response } : { error }),
        },
        is_active: true,
      };

      await this.myCaseHistoryModel.create(historyData);
      this.logger.log(
        `Created history entry for event ${eventId} - ${operation} - ${success ? 'SUCCESS' : 'FAILED'}`,
      );
    } catch (error) {
      this.logger.error(`Failed to create history entry: ${error.message}`);
    }
  }

  /**
   * Download file from S3 using the file key
   */
  private async downloadFileFromS3(fileKey: string): Promise<Buffer> {
    try {
      this.logger.log(`Downloading file from S3: ${fileKey}`);

      const response = await this.s3Service.getObject(
        process.env.AWS_BUCKET_NAME_s3,
        fileKey,
      );

      if (!response.Body) {
        throw new Error('No file content received from S3');
      }

      // Convert stream to buffer
      const chunks: Buffer[] = [];
      const stream = response.Body as NodeJS.ReadableStream;

      return new Promise((resolve, reject) => {
        stream.on('data', (chunk) => chunks.push(chunk));
        stream.on('end', () => resolve(Buffer.concat(chunks)));
        stream.on('error', reject);
      });
    } catch (error) {
      this.logger.error(`Error downloading file from S3: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create document record in MyCase and get presigned URL
   */
  private async createDocumentInMyCase(
    fileName: string,
    caseId: string,
    description: string,
    accessToken?: string,
  ): Promise<{
    success: boolean;
    documentId?: number;
    putUrl?: string;
    putHeaders?: any;
    error?: string;
  }> {
    try {
      this.logger.log(
        `Creating document record in MyCase: ${fileName} for case ${caseId}`,
      );

      const payload = {
        path: `workflow_uploads/${fileName}`,
        filename: fileName,
        description: description,
        assigned_date: new Date().toISOString().split('T')[0],
      };

      this.logger.log(`Document creation payload: ${JSON.stringify(payload)}`);

      const response = await firstValueFrom(
        this.httpService.post(
          `${this.MYCASE_API_BASE_URL}/cases/${caseId}/documents`,
          payload,
          {
            headers: this.getHeaders(accessToken),
          },
        ),
      );

      this.logger.log(`Full MyCase response: ${JSON.stringify(response.data)}`);

      const { id, put_url, put_headers } = response.data;

      this.logger.log(`Document record created in MyCase with ID: ${id}`);
      this.logger.log(`Presigned URL: ${put_url}`);
      this.logger.log(
        `Using put_headers from MyCase: ${JSON.stringify(put_headers)}`,
      );

      // Additional validation
      if (!put_url) {
        throw new Error('No put_url received from MyCase');
      }
      if (!put_headers) {
        this.logger.warn('No put_headers received from MyCase');
      }

      return {
        success: true,
        documentId: id,
        putUrl: put_url,
        putHeaders: put_headers,
      };
    } catch (error) {
      this.logger.error(
        `Error creating document record in MyCase: ${error.message}`,
      );
      if (error.response) {
        this.logger.error(
          `MyCase API response: ${JSON.stringify(error.response.data)}`,
        );
      }
      return { success: false, error: error.message };
    }
  }

  /**
   * Upload file to MyCase using presigned URL
   */
  private async uploadFileToPresignedUrl(
    fileBuffer: Buffer,
    fileName: string,
    mimeType: string,
    putUrl: string,
    putHeaders: any,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      this.logger.log(`Uploading file to MyCase presigned URL: ${fileName}`);
      this.logger.log(`File size: ${fileBuffer.length} bytes`);
      this.logger.log(`Detected MIME type: ${mimeType}`);
      this.logger.log(`Presigned URL: ${putUrl}`);
      this.logger.log(
        `Using put_headers from MyCase: ${JSON.stringify(putHeaders)}`,
      );
      this.logger.log(`Put headers type: ${typeof putHeaders}`);
      this.logger.log(`Put headers keys: ${Object.keys(putHeaders || {})}`);

      // Ensure putHeaders is an object and not null/undefined
      let headersToUse = putHeaders || {};
      this.logger.log(`Final headers to use: ${JSON.stringify(headersToUse)}`);

      // Handle case where headers might be a string or need special processing
      if (typeof putHeaders === 'string') {
        try {
          headersToUse = JSON.parse(putHeaders);
          this.logger.log(
            `Parsed headers from string: ${JSON.stringify(headersToUse)}`,
          );
        } catch (e) {
          this.logger.error(`Failed to parse headers string: ${e.message}`);
          headersToUse = {};
        }
      }

      // If no headers provided by MyCase, try with minimal headers
      if (!putHeaders || Object.keys(headersToUse).length === 0) {
        this.logger.warn(
          'No headers provided by MyCase, trying without any headers first',
        );
        headersToUse = {}; // Try with no headers first
      }

      // Based on AWS signature error, we need to add the content-type header
      // that AWS expects based on the signature calculation
      if (headersToUse['x-amz-acl']) {
        // MyCase documentation specifically states: "Please make sure to set Content-Type: application/octet-stream"
        // Let's follow their documentation exactly
        headersToUse['Content-Type'] = 'application/octet-stream';
        this.logger.log(
          'Added Content-Type: application/octet-stream as per MyCase documentation',
        );
      }

      this.logger.log(
        `Actually using headers: ${JSON.stringify(headersToUse)}`,
      );

      // Use ONLY the headers provided by MyCase - do not add or modify any headers
      // as presigned URLs require exact signature matching
      await firstValueFrom(
        this.httpService.put(putUrl, fileBuffer, {
          headers: headersToUse,
        }),
      );

      this.logger.log(`File successfully uploaded to MyCase: ${fileName}`);
      return { success: true };
    } catch (error) {
      this.logger.error(
        `Error uploading file to presigned URL: ${error.message}`,
      );

      if (error.response) {
        this.logger.error(`HTTP Status: ${error.response.status}`);
        this.logger.error(
          `Response headers: ${JSON.stringify(error.response.headers)}`,
        );
        this.logger.error(
          `Response data: ${JSON.stringify(error.response.data)}`,
        );

        // Check for specific AWS S3 signature errors
        if (error.response.status === 403) {
          this.logger.error(
            '403 Forbidden - This is likely a signature mismatch error',
          );
          this.logger.error(
            'Verify that we are using the exact headers provided by MyCase',
          );
          this.logger.error(
            'Headers used for upload:',
            JSON.stringify(putHeaders),
          );
        }
      }

      return { success: false, error: error.message };
    }
  }

  /**
   * Upload file to MyCase using the two-step process
   */
  private async uploadFileToMyCase(
    fileBuffer: Buffer,
    fileName: string,
    mimeType: string,
    caseId: string,
    accessToken?: string,
  ): Promise<MyCaseFileUploadResult> {
    try {
      this.logger.log(
        `Starting two-step upload process for: ${fileName} to case ${caseId}`,
      );

      // Step 1: Create document record and get presigned URL
      const documentResult = await this.createDocumentInMyCase(
        fileName,
        caseId,
        `Uploaded from workflow: ${fileName}`,
        accessToken,
      );

      if (!documentResult.success) {
        return {
          success: false,
          error: `Failed to create document record: ${documentResult.error}`,
        };
      }

      // Step 2: Upload file using presigned URL
      const uploadResult = await this.uploadFileToPresignedUrl(
        fileBuffer,
        fileName,
        mimeType,
        documentResult.putUrl,
        documentResult.putHeaders,
      );

      if (!uploadResult.success) {
        return {
          success: false,
          error: `Failed to upload file: ${uploadResult.error}`,
        };
      }

      this.logger.log(
        `Successfully completed two-step upload for ${fileName} with document ID: ${documentResult.documentId}`,
      );

      return {
        success: true,
        fileId: documentResult.documentId.toString(),
      };
    } catch (error) {
      this.logger.error(
        `Error in two-step upload process for ${fileName}: ${error.message}`,
      );
      return { success: false, error: error.message };
    }
  }

  /**
   * Process and upload files from event to MyCase
   */
  private async processEventFiles(
    event: EventSub,
    caseId: string,
    accessToken?: string,
  ): Promise<Array<{ fileName: string; result: MyCaseFileUploadResult }>> {
    const fileUploadResults: Array<{
      fileName: string;
      result: MyCaseFileUploadResult;
    }> = [];

    if (!event.files || event.files.length === 0) {
      this.logger.log('No files to upload for this event');
      return fileUploadResults;
    }

    this.logger.log(
      `Processing ${event.files.length} files for event ${event.id}`,
    );

    for (const file of event.files) {
      try {
        // Check if file has S3 key
        if (!file.key) {
          this.logger.warn(`File ${file.name} has no S3 key, skipping upload`);
          fileUploadResults.push({
            fileName: file.name,
            result: { success: false, error: 'No S3 key provided' },
          });
          continue;
        }

        // Download file from S3
        const fileBuffer = await this.downloadFileFromS3(file.key);

        // Determine MIME type (default to application/pdf for most legal documents)
        const mimeType = this.getMimeType(file.name);

        // Upload to MyCase
        const uploadResult = await this.uploadFileToMyCase(
          fileBuffer,
          file.name,
          mimeType,
          caseId,
          accessToken,
        );

        fileUploadResults.push({
          fileName: file.name,
          result: uploadResult,
        });

        if (uploadResult.success) {
          this.logger.log(
            `Successfully uploaded ${file.name} to MyCase with ID: ${uploadResult.fileId}`,
          );
        } else {
          this.logger.error(
            `Failed to upload ${file.name} to MyCase: ${uploadResult.error}`,
          );
        }
      } catch (error) {
        this.logger.error(
          `Error processing file ${file.name}: ${error.message}`,
        );
        fileUploadResults.push({
          fileName: file.name,
          result: { success: false, error: error.message },
        });
      }
    }

    return fileUploadResults;
  }

  /**
   * Get MIME type based on file extension
   */
  private getMimeType(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();

    const mimeTypes = {
      pdf: 'application/pdf',
      doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      txt: 'text/plain',
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      zip: 'application/zip',
      rar: 'application/x-rar-compressed',
    };

    return mimeTypes[extension] || 'application/octet-stream';
  }

  async createEventInMyCase(
    event: EventSub,
    my_case_location_id: any,
    myCaseMatterId: string,
    workflowExecutionId: string,
    accessToken?: string,
  ): Promise<{ success: boolean; myCaseEventId?: string; error?: string }> {
    console.log('🚀 ~ MyCaseService***$$ ~ event:', event);
    try {
      this.logger.log(`Creating event in MyCase for event ID: ${event.id}`);

      // Validate access token
      if (!accessToken) {
        const error = 'Access token is required for MyCase API calls';
        this.logger.error(error);
        await this.createHistoryEntry(event.id, 'CREATE', false, null, {
          message: error,
          statusCode: 401,
        });
        return { success: false, error };
      }

      const payload = await this.transformEventToMyCasePayload(
        event,
        myCaseMatterId,
        my_case_location_id,
      );
      this.logger.log(`MyCase API payload: ${JSON.stringify(payload)}`);

      // Process and upload files if available
      let fileUploadResults: Array<{
        fileName: string;
        result: MyCaseFileUploadResult;
      }> = [];

      if (event.files && event.files.length > 0) {
        this.logger.log(
          `Processing ${event.files.length} files for upload to MyCase using intelligent document handling`,
        );
        fileUploadResults = await this.processEventFilesIntelligently(
          event,
          myCaseMatterId,
          accessToken,
        );
      }

      let myCaseEventId: string = '';

      if (event.courtNoticeActions != 'Save court notice only') {
        const response = await firstValueFrom(
          this.httpService.post(`${this.MYCASE_API_BASE_URL}/events`, payload, {
            headers: this.getHeaders(accessToken),
          }),
        );

        myCaseEventId = response.data.id;
        this.logger.log(`Event created in MyCase with ID: ${myCaseEventId}`);

        // Update the MycaseClientMatter record with MyCase event ID
        await this.mycaseClientMatterModel.updateOne(
          {
            event_id: event.id,
            workflow_execution_id: new Types.ObjectId(workflowExecutionId),
          },
          {
            $set: {
              my_case_event_id: myCaseEventId,
            },
          },
        );

        // Update the event in TaskExecution with MyCase event ID
        await this.updateEventInTaskExecution(workflowExecutionId, event.id, {
          my_case_event_id: myCaseEventId,
        });

        this.logger.log(
          `Updated local records with MyCase event ID: ${myCaseEventId}`,
        );

        // Create success history entry with file upload results
        await this.createHistoryEntry(event.id, 'CREATE', true, {
          myCaseEventId,
          payload,
          apiResponse: response.data,
          fileUploadResults,
        });
      }

      // Update the event files in TaskExecution to persist MyCase document IDs
      await this.updateEventFilesInTaskExecution(
        workflowExecutionId,
        event.id,
        event,
      );

      // Log file upload summary
      if (fileUploadResults.length > 0) {
        const successfulUploads = fileUploadResults.filter(
          (result) => result.result.success,
        ).length;
        const failedUploads = fileUploadResults.length - successfulUploads;

        this.logger.log(
          `File upload summary: ${successfulUploads} successful, ${failedUploads} failed`,
        );
      }

      return { success: true, myCaseEventId };
    } catch (error) {
      this.logger.error(`Error creating event in MyCase: ${error.message}`);
      if (error.response) {
        this.logger.error(
          `MyCase API response: ${JSON.stringify(error.response.data)}`,
        );
      }

      // Create failure history entry
      await this.createHistoryEntry(event.id, 'CREATE', false, null, {
        message: error.message,
        apiResponse: error.response?.data,
        statusCode: error.response?.status,
      });

      return { success: false, error: error.message };
    }
  }

  /**
   * Delete document from MyCase
   */
  private async deleteDocumentFromMyCase(
    documentId: string,
    caseId: string,
    accessToken?: string,
  ): Promise<MyCaseDocumentDeleteResult> {
    try {
      this.logger.log(
        `Deleting document from MyCase: Document ID ${documentId} in case ${caseId}`,
      );

      // Log the access token (first 10 characters for security)
      this.logger.log(
        `Using access token: ${accessToken ? accessToken.substring(0, 10) + '...' : 'NOT PROVIDED'}`,
      );

      // Log the full URL being called
      const deleteUrl = `${this.MYCASE_API_BASE_URL}/documents/${documentId}`;
      this.logger.log(`DELETE URL: ${deleteUrl}`);

      // Log the headers being sent
      const headers = this.getHeaders(accessToken);
      this.logger.log(`Request headers: ${JSON.stringify(headers)}`);

      await firstValueFrom(
        this.httpService.delete(deleteUrl, {
          headers: headers,
        }),
      );

      this.logger.log(
        `Document deleted successfully from MyCase: ${documentId}`,
      );

      return {
        success: true,
        documentId: documentId,
      };
    } catch (error) {
      this.logger.error(
        `Error deleting document from MyCase: ${error.message}`,
      );

      // Enhanced error logging
      if (error.response) {
        this.logger.error(`HTTP Status Code: ${error.response.status}`);
        this.logger.error(`HTTP Status Text: ${error.response.statusText}`);
        this.logger.error(
          `Response Headers: ${JSON.stringify(error.response.headers)}`,
        );
        this.logger.error(
          `MyCase API Response Data: ${JSON.stringify(error.response.data)}`,
        );

        // Check for specific permission errors
        if (error.response.status === 403) {
          this.logger.error(
            '403 Forbidden - This indicates insufficient permissions to delete documents',
          );
          this.logger.error(
            'Possible causes: 1) Access token lacks delete permissions 2) User role restrictions 3) Document ownership issues',
          );
        } else if (error.response.status === 401) {
          this.logger.error(
            '401 Unauthorized - Access token may be invalid or expired',
          );
        } else if (error.response.status === 404) {
          this.logger.error(
            '404 Not Found - Document may not exist or user cannot access it',
          );
        }
      } else if (error.request) {
        this.logger.error('No response received from MyCase API');
        this.logger.error(`Request details: ${JSON.stringify(error.request)}`);
      } else {
        this.logger.error(`Request setup error: ${error.message}`);
      }

      return { success: false, error: error.message };
    }
  }

  /**
   * Update document in MyCase
   */
  private async updateDocumentInMyCase(
    documentId: string,
    updatePayload: MyCaseDocumentUpdatePayload,
    accessToken?: string,
  ): Promise<MyCaseDocumentUpdateResult> {
    try {
      this.logger.log(`Updating document in MyCase: Document ID ${documentId}`);

      // Log the access token (first 10 characters for security)
      this.logger.log(
        `Using access token: ${accessToken ? accessToken.substring(0, 10) + '...' : 'NOT PROVIDED'}`,
      );

      // Log the full URL being called
      const updateUrl = `${this.MYCASE_API_BASE_URL}/documents/${documentId}`;
      this.logger.log(`PUT URL: ${updateUrl}`);

      // Log the payload being sent
      this.logger.log(`Update payload: ${JSON.stringify(updatePayload)}`);

      // Log the headers being sent
      const headers = this.getHeaders(accessToken);
      this.logger.log(`Request headers: ${JSON.stringify(headers)}`);

      const response = await firstValueFrom(
        this.httpService.put(updateUrl, updatePayload, {
          headers: headers,
        }),
      );

      this.logger.log(`Document updated successfully in MyCase: ${documentId}`);
      this.logger.log(`MyCase response: ${JSON.stringify(response.data)}`);

      return {
        success: true,
        documentId: documentId,
        updatedData: response.data,
      };
    } catch (error) {
      this.logger.error(`Error updating document in MyCase: ${error.message}`);

      // Enhanced error logging
      if (error.response) {
        this.logger.error(`HTTP Status Code: ${error.response.status}`);
        this.logger.error(`HTTP Status Text: ${error.response.statusText}`);
        this.logger.error(
          `Response Headers: ${JSON.stringify(error.response.headers)}`,
        );
        this.logger.error(
          `MyCase API Response Data: ${JSON.stringify(error.response.data)}`,
        );

        // Check for specific errors
        if (error.response.status === 403) {
          this.logger.error(
            '403 Forbidden - This indicates insufficient permissions to update documents',
          );
          this.logger.error(
            'Possible causes: 1) Access token lacks update permissions 2) User role restrictions 3) Document ownership issues',
          );
        } else if (error.response.status === 401) {
          this.logger.error(
            '401 Unauthorized - Access token may be invalid or expired',
          );
        } else if (error.response.status === 404) {
          this.logger.error(
            '404 Not Found - Document may not exist or user cannot access it',
          );
        } else if (error.response.status === 400) {
          this.logger.error(
            '400 Bad Request - Invalid payload or missing required fields',
          );
        }
      } else if (error.request) {
        this.logger.error('No response received from MyCase API');
        this.logger.error(`Request details: ${JSON.stringify(error.request)}`);
      } else {
        this.logger.error(`Request setup error: ${error.message}`);
      }

      return { success: false, error: error.message };
    }
  }

  /**
   * Get previous file upload results from history to identify documents to delete
   */
  private async getPreviousFileUploadResults(
    eventId: string,
  ): Promise<Array<{ fileName: string; result: MyCaseFileUploadResult }>> {
    try {
      const historyEntries = await this.myCaseHistoryModel
        .find({
          event_id: eventId,
          'response.success': true,
          'response.operation': { $in: ['CREATE', 'UPDATE'] },
        })
        .sort({ createdAt: -1 })
        .limit(1)
        .exec();

      if (historyEntries.length === 0) {
        this.logger.log(
          `No previous file upload history found for event ${eventId}`,
        );
        return [];
      }

      const latestHistory = historyEntries[0];
      const fileUploadResults =
        latestHistory.response?.data?.fileUploadResults || [];

      this.logger.log(
        `Found ${fileUploadResults.length} previous file upload results for event ${eventId}`,
      );

      return fileUploadResults.filter(
        (result) =>
          result.result && result.result.success && result.result.fileId,
      );
    } catch (error) {
      this.logger.error(
        `Error getting previous file upload results: ${error.message}`,
      );
      return [];
    }
  }

  /**
   * Delete previous documents from MyCase
   */
  private async deletePreviousDocuments(
    eventId: string,
    caseId: string,
    accessToken?: string,
  ): Promise<Array<{ fileName: string; result: MyCaseDocumentDeleteResult }>> {
    const deletionResults: Array<{
      fileName: string;
      result: MyCaseDocumentDeleteResult;
    }> = [];

    try {
      // Get previous file upload results
      const previousFiles = await this.getPreviousFileUploadResults(eventId);

      if (previousFiles.length === 0) {
        this.logger.log(`No previous documents to delete for event ${eventId}`);
        return deletionResults;
      }

      this.logger.log(
        `Deleting ${previousFiles.length} previous documents for event ${eventId}`,
      );

      // Delete each document
      for (const fileResult of previousFiles) {
        if (fileResult.result.fileId) {
          const deleteResult = await this.deleteDocumentFromMyCase(
            fileResult.result.fileId,
            caseId,
            accessToken,
          );

          deletionResults.push({
            fileName: fileResult.fileName,
            result: deleteResult,
          });

          if (deleteResult.success) {
            this.logger.log(
              `Successfully deleted document ${fileResult.fileName} (ID: ${fileResult.result.fileId})`,
            );
          } else {
            this.logger.error(
              `Failed to delete document ${fileResult.fileName}: ${deleteResult.error}`,
            );
          }
        } else {
          this.logger.warn(
            `No document ID found for file ${fileResult.fileName}, skipping deletion`,
          );
        }
      }

      return deletionResults;
    } catch (error) {
      this.logger.error(`Error deleting previous documents: ${error.message}`);
      return deletionResults;
    }
  }

  /**
   * Delete all documents associated with an event from MyCase
   */
  private async deleteAllEventDocuments(
    eventId: string,
    caseId: string,
    accessToken?: string,
  ): Promise<Array<{ fileName: string; result: MyCaseDocumentDeleteResult }>> {
    this.logger.log(
      `Deleting all documents for event ${eventId} from MyCase case ${caseId}`,
    );

    const deletionResults = await this.deletePreviousDocuments(
      eventId,
      caseId,
      accessToken,
    );

    if (deletionResults.length > 0) {
      const successfulDeletions = deletionResults.filter(
        (result) => result.result.success,
      ).length;
      const failedDeletions = deletionResults.length - successfulDeletions;

      this.logger.log(
        `Event deletion document cleanup: ${successfulDeletions} successful, ${failedDeletions} failed`,
      );
    } else {
      this.logger.log(`No documents found to delete for event ${eventId}`);
    }

    return deletionResults;
  }

  /**
   * Update previous documents from MyCase with new names/metadata
   */
  private async updatePreviousDocuments(
    eventId: string,
    caseId: string,
    newFiles: Array<{ name: string; key?: string }>,
    accessToken?: string,
  ): Promise<Array<{ fileName: string; result: MyCaseDocumentUpdateResult }>> {
    const updateResults: Array<{
      fileName: string;
      result: MyCaseDocumentUpdateResult;
    }> = [];

    try {
      // Get previous file upload results
      const previousFiles = await this.getPreviousFileUploadResults(eventId);

      if (previousFiles.length === 0) {
        this.logger.log(`No previous documents to update for event ${eventId}`);
        return updateResults;
      }

      this.logger.log(
        `Updating ${previousFiles.length} previous documents for event ${eventId}`,
      );

      // Update each document with new file information
      for (let i = 0; i < previousFiles.length; i++) {
        const previousFile = previousFiles[i];
        const newFile = newFiles[i]; // Map to corresponding new file

        if (previousFile.result.fileId && newFile) {
          // Create update payload
          const updatePayload: MyCaseDocumentUpdatePayload = {
            path: `workflow_uploads/${newFile.name}`,
            filename: newFile.name,
            description: `Updated from workflow: ${newFile.name}`,
            assigned_date: new Date().toISOString().split('T')[0],
          };

          const updateResult = await this.updateDocumentInMyCase(
            previousFile.result.fileId,
            updatePayload,
            accessToken,
          );

          updateResults.push({
            fileName: `${previousFile.fileName} -> ${newFile.name}`,
            result: updateResult,
          });

          if (updateResult.success) {
            this.logger.log(
              `Successfully updated document ${previousFile.fileName} to ${newFile.name} (ID: ${previousFile.result.fileId})`,
            );
          } else {
            this.logger.error(
              `Failed to update document ${previousFile.fileName}: ${updateResult.error}`,
            );
          }
        } else if (previousFile.result.fileId && !newFile) {
          // If no corresponding new file, mark as archived
          const updatePayload: MyCaseDocumentUpdatePayload = {
            path: `workflow_uploads/archived/${previousFile.fileName}`,
            filename: `archived_${previousFile.fileName}`,
            description: `Archived from workflow update: ${previousFile.fileName}`,
            assigned_date: new Date().toISOString().split('T')[0],
          };

          const updateResult = await this.updateDocumentInMyCase(
            previousFile.result.fileId,
            updatePayload,
            accessToken,
          );

          updateResults.push({
            fileName: `${previousFile.fileName} -> ARCHIVED`,
            result: updateResult,
          });

          this.logger.log(
            `Archived document ${previousFile.fileName} (ID: ${previousFile.result.fileId})`,
          );
        } else {
          this.logger.warn(
            `No document ID found for file ${previousFile.fileName}, skipping update`,
          );
        }
      }

      return updateResults;
    } catch (error) {
      this.logger.error(`Error updating previous documents: ${error.message}`);
      return updateResults;
    }
  }

  async updateEventInMyCase(
    event: EventSub,
    myCaseEventId: string,
    my_case_location_id: any,
    myCaseMatterId: string,
    workflowExecutionId: string,
    accessToken?: string,
  ): Promise<{ success: boolean; error?: string }> {
    console.log('🚀 UPDATE PAYLOAD~ MyCaseService ~ event:', event);
    const mainEvent = event;
    try {
      this.logger.log(
        `Updating event in MyCase with ID: ${myCaseEventId}, myCaseMatterId: ${myCaseMatterId}`,
      );

      // Validate access token
      if (!accessToken) {
        const error = 'Access token is required for MyCase API calls';
        this.logger.error(error);
        await this.createHistoryEntry(event.id, 'UPDATE', false, null, {
          message: error,
          statusCode: 401,
        });
        return { success: false, error };
      }

      const payload = await this.transformEventToMyCasePayload(
        event,
        myCaseMatterId.toString(),
        my_case_location_id,
      );

      this.logger.log(`MyCase API update payload: ${JSON.stringify(payload)}`);

      const response = await firstValueFrom(
        this.httpService.put(
          `${this.MYCASE_API_BASE_URL}/events/${myCaseEventId}`,
          payload,
          { headers: this.getHeaders(accessToken) },
        ),
      );
      console.log('🚀 ~ MyCaseService ~ response:', response);

      // Handle document updates: delete old documents and upload new ones
      let documentDeletionResults: Array<{
        fileName: string;
        result: MyCaseDocumentDeleteResult;
      }> = [];
      let fileUploadResults: Array<{
        fileName: string;
        result: MyCaseFileUploadResult;
      }> = [];

      // Check if there are files to process
      if (event.files && event.files.length > 0) {
        this.logger.log(
          `Processing document updates for event ${event.id}: deleting old documents and uploading new ones`,
        );

        // Step 1: Delete previous documents
        documentDeletionResults = await this.deletePreviousDocuments(
          event.id,
          myCaseMatterId,
          accessToken,
        );

        // Step 2: Upload new documents
        this.logger.log(
          `Uploading ${event.files.length} new files for updated event`,
        );
        fileUploadResults = await this.processEventFiles(
          event,
          myCaseMatterId,
          accessToken,
        );
      } else {
        // If no files in the updated event, just delete previous documents
        this.logger.log(
          `No files in updated event, deleting previous documents only`,
        );
        documentDeletionResults = await this.deletePreviousDocuments(
          event.id,
          myCaseMatterId,
          accessToken,
        );
      }

      // Update the local MycaseClientMatter record
      await this.mycaseClientMatterModel.updateOne(
        {
          my_case_event_id: myCaseEventId,
          workflow_execution_id: new Types.ObjectId(workflowExecutionId),
        },
        {
          $set: {
            event: event,
          },
        },
      );

      // Create success history entry
      await this.createHistoryEntry(mainEvent.id, 'UPDATE', true, {
        myCaseEventId,
        payload,
        apiResponse: response.data,
        documentDeletionResults,
        fileUploadResults,
      });

      // Log document operation summary
      if (documentDeletionResults.length > 0) {
        const successfulDeletions = documentDeletionResults.filter(
          (result) => result.result.success,
        ).length;
        const failedDeletions =
          documentDeletionResults.length - successfulDeletions;

        this.logger.log(
          `Document deletion summary: ${successfulDeletions} successful, ${failedDeletions} failed`,
        );
      }

      if (fileUploadResults.length > 0) {
        const successfulUploads = fileUploadResults.filter(
          (result) => result.result.success,
        ).length;
        const failedUploads = fileUploadResults.length - successfulUploads;

        this.logger.log(
          `File upload summary during update: ${successfulUploads} successful, ${failedUploads} failed`,
        );
      }

      return { success: true };
    } catch (error) {
      this.logger.error(`Error updating event in MyCase: ${error.message}`);
      if (error.response) {
        this.logger.error(
          `MyCase API response: ${JSON.stringify(error.response.data)}`,
        );

        // Handle 401 Unauthorized specifically
        if (error.response.status === 401) {
          this.logger.error(
            'MyCase API returned 401 Unauthorized - Access token may be expired or invalid',
          );
        }
      }

      // Create failure history entry
      await this.createHistoryEntry(event.id, 'UPDATE', false, null, {
        message: error.message,
        myCaseEventId,
        apiResponse: error.response?.data,
        statusCode: error.response?.status,
      });

      return { success: false, error: error.message };
    }
  }

  /**
   * Update event in MyCase with document update option (instead of deletion)
   */
  async updateEventInMyCaseWithDocumentUpdate(
    event: EventSub,
    myCaseEventId: string,
    my_case_location_id: any,
    myCaseMatterId: string,
    workflowExecutionId: string,
    accessToken?: string,
    prevParentTaskExecutionEvents?: any[],
  ): Promise<{ success: boolean; error?: string }> {
    console.log('🚀 ~ MyCaseServiceHAVE ~ event:', event);
    try {
      this.logger.log(`Updating event in MyCase with ID: ${myCaseEventId}`);
      this.logger.log(
        `Using intelligent document handling for file processing`,
      );

      const payload = await this.transformEventToMyCasePayload(
        event,
        myCaseMatterId.toString(),
        my_case_location_id,
      );

      this.logger.log(`MyCase API update payload: ${JSON.stringify(payload)}`);

      // Handle document updates using intelligent processing
      let fileUploadResults: Array<{
        fileName: string;
        result: MyCaseFileUploadResult;
      }> = [];

      // Check if there are files to process
      if (event.files && event.files.length > 0) {
        this.logger.log(
          `Processing document updates for event ${event.id} using intelligent document handling`,
        );

        // Use the new intelligent file processing method
        fileUploadResults = await this.processEventFilesIntelligently(
          event,
          myCaseMatterId,
          accessToken,
          prevParentTaskExecutionEvents,
        );
      } else {
        this.logger.log(`No files in updated event for event ${event.id}`);
      }

      if (event.courtNoticeActions != 'Save court notice only') {
        const response = await firstValueFrom(
          this.httpService.put(
            `${this.MYCASE_API_BASE_URL}/events/${myCaseEventId}`,
            payload,
            { headers: this.getHeaders(accessToken) },
          ),
        );

        this.logger.log(`Event updated in MyCase successfully`);

        // Update the local MycaseClientMatter record
        const results = await this.mycaseClientMatterModel.updateOne(
          {
            my_case_event_id: myCaseEventId,
            event_id: event?.id,
          },
          {
            $set: {
              event: event,
            },
          },
        );

        this.logger.log(
          `Updated local MycaseClientMatter record: ${JSON.stringify(results)}`,
        );

        // Update the local MycaseClientMatter record with workflow execution ID
        const result = await this.mycaseClientMatterModel.updateOne(
          {
            my_case_event_id: myCaseEventId,
            workflow_execution_id: new Types.ObjectId(workflowExecutionId),
          },
          {
            $set: {
              event: event,
            },
          },
        );

        this.logger.log(
          `Updated local MycaseClientMatter record with workflow: ${JSON.stringify(result)}`,
        );

        // Create success history entry with operation results
        await this.createHistoryEntry(event.id, 'UPDATE', true, {
          myCaseEventId,
          payload,
          apiResponse: response.data,
          fileUploadResults,
          documentStrategy: 'INTELLIGENT_UPDATE',
        });
      }

      // Log file operation summary
      if (fileUploadResults.length > 0) {
        const successfulOperations = fileUploadResults.filter(
          (result) => result.result.success,
        ).length;
        const failedOperations =
          fileUploadResults.length - successfulOperations;

        this.logger.log(
          `Intelligent document processing summary: ${successfulOperations} successful, ${failedOperations} failed`,
        );

        // Log details of each file operation
        fileUploadResults.forEach((result) => {
          if (result.result.success) {
            this.logger.log(
              `✓ ${result.fileName}: ${result.result.fileId ? 'Updated/Created' : 'Processed'} successfully`,
            );
          } else {
            this.logger.error(
              `✗ ${result.fileName}: Failed - ${result.result.error}`,
            );
          }
        });
      }

      // Update the event files in TaskExecution to persist MyCase document IDs
      await this.updateEventFilesInTaskExecution(
        workflowExecutionId,
        event.id,
        event,
      );

      return { success: true };
    } catch (error) {
      this.logger.error(`Error updating event in MyCase: ${error.message}`);
      if (error.response) {
        this.logger.error(
          `MyCase API response: ${JSON.stringify(error.response.data)}`,
        );
      }

      // Create failure history entry
      await this.createHistoryEntry(event.id, 'UPDATE', false, null, {
        message: error.message,
        myCaseEventId,
        apiResponse: error.response?.data,
        statusCode: error.response?.status,
        documentStrategy: 'INTELLIGENT_UPDATE',
      });

      return { success: false, error: error.message };
    }
  }

  async deleteEventInMyCase(
    myCaseEventId: string,
    workflowExecutionId: string,
    eventId: string,
    myCaseMatterId?: string,
    accessToken?: string,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      this.logger.log(`Deleting event in MyCase with ID: ${myCaseEventId}`);

      // Delete all documents associated with this event before deleting the event
      let documentDeletionResults: Array<{
        fileName: string;
        result: MyCaseDocumentDeleteResult;
      }> = [];

      if (myCaseMatterId) {
        documentDeletionResults = await this.deleteAllEventDocuments(
          eventId,
          myCaseMatterId,
          accessToken,
        );
      } else {
        this.logger.warn(
          `No case ID provided for event ${eventId}, skipping document deletion`,
        );
      }

      // Delete the event from MyCase
      const response = await firstValueFrom(
        this.httpService.delete(
          `${this.MYCASE_API_BASE_URL}/events/${myCaseEventId}`,
          { headers: this.getHeaders(accessToken) },
        ),
      );

      this.logger.log(`Event deleted from MyCase successfully`);

      // Delete the corresponding record from MycaseClientMatter
      await this.mycaseClientMatterModel.deleteMany({
        my_case_event_id: myCaseEventId,
        workflow_execution_id: new Types.ObjectId(workflowExecutionId),
      });

      // Remove my_case_event_id from TaskExecution
      await this.updateEventInTaskExecution(
        workflowExecutionId,
        eventId,
        {
          $unset: {
            'derived_field.$[].client.$[].matter.$[].event.$[eventElem].my_case_event_id':
              '',
          },
        },
        { 'eventElem.id': eventId },
      );

      this.logger.log(`Cleaned up local records`);

      // Create success history entry with document deletion results
      await this.createHistoryEntry(eventId, 'DELETE', true, {
        myCaseEventId,
        apiResponse: response.data,
        documentDeletionResults,
      });

      return { success: true };
    } catch (error) {
      this.logger.error(`Error deleting event in MyCase: ${error.message}`);
      if (error.response) {
        this.logger.error(
          `MyCase API response: ${JSON.stringify(error.response.data)}`,
        );
      }

      // Create failure history entry
      await this.createHistoryEntry(eventId, 'DELETE', false, null, {
        message: error.message,
        myCaseEventId,
        apiResponse: error.response?.data,
        statusCode: error.response?.status,
      });

      return { success: false, error: error.message };
    }
  }

  private async updateEventInTaskExecution(
    workflowExecutionId: string,
    eventId: string,
    updateData: any,
    arrayFilters?: any,
  ): Promise<void> {
    try {
      // For updating my_case_event_id in the nested event structure
      if (updateData.my_case_event_id) {
        const updateResult = await this.taskExecutionModel.updateOne(
          {
            workflow_execution_id: new Types.ObjectId(workflowExecutionId),
            'derived_field.client.matter.event.id': eventId,
          },
          {
            $set: {
              'derived_field.$[].client.$[].matter.$[].event.$[eventElem].my_case_event_id':
                updateData.my_case_event_id,
            },
          },
          {
            arrayFilters: [{ 'eventElem.id': eventId }],
          },
        );

        this.logger.log(
          `Updated TaskExecution for event ID: ${eventId}, matched: ${updateResult.matchedCount}, modified: ${updateResult.modifiedCount}`,
        );
      } else {
        // For other types of updates (like unset operations)
        const updateOptions: any = {};
        if (arrayFilters) {
          updateOptions.arrayFilters = [arrayFilters];
        }

        await this.taskExecutionModel.updateOne(
          {
            workflow_execution_id: new Types.ObjectId(workflowExecutionId),
            'derived_field.client.matter.event.id': eventId,
          },
          updateData,
          updateOptions,
        );
        this.logger.log(`Updated TaskExecution for event ID: ${eventId}`);
      }
    } catch (error) {
      this.logger.error(`Error updating TaskExecution: ${error.message}`);
      throw error;
    }
  }

  async getMyCaseEventId(
    workflowExecutionId: string,
    eventId: string,
  ): Promise<string | null> {
    try {
      const record = await this.mycaseClientMatterModel.findOne({
        workflow_execution_id: new Types.ObjectId(workflowExecutionId),
        event_id: eventId,
      });

      return record?.my_case_event_id || null;
    } catch (error) {
      this.logger.error(`Error getting MyCase event ID: ${error.message}`);
      return null;
    }
  }

  async getMyCaseMatterId(clientMatterId: string): Promise<number | null> {
    try {
      // This should be implemented based on your matter-to-mycase mapping logic
      // For now, returning a default value - you should implement proper mapping
      // You might need to add a mycase_matter_id field to your Matter entity

      // Placeholder implementation - replace with actual logic
      return parseInt(clientMatterId) || 0;
    } catch (error) {
      this.logger.error(`Error getting MyCase matter ID: ${error.message}`);
      return null;
    }
  }

  // Additional method to get history for an event
  async getEventHistory(eventId: string): Promise<MyCaseHistory[]> {
    try {
      return await this.myCaseHistoryModel
        .find({ event_id: eventId })
        .sort({ createdAt: -1 })
        .exec();
    } catch (error) {
      this.logger.error(`Error getting event history: ${error.message}`);
      return [];
    }
  }

  // Method to get recent history with pagination
  async getRecentHistory(
    page: number = 1,
    limit: number = 50,
  ): Promise<{ data: MyCaseHistory[]; total: number }> {
    try {
      const skip = (page - 1) * limit;
      const [data, total] = await Promise.all([
        this.myCaseHistoryModel
          .find({ is_active: true })
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .exec(),
        this.myCaseHistoryModel.countDocuments({ is_active: true }),
      ]);

      return { data, total };
    } catch (error) {
      this.logger.error(`Error getting recent history: ${error.message}`);
      return { data: [], total: 0 };
    }
  }

  // New methods for location management
  async getLocationsFromMyCase(
    accessToken?: string,
    page: number = 1,
    perPage: number = 50,
    updatedAfter?: string,
  ): Promise<MyCaseLocationListResponse> {
    try {
      this.logger.log(
        `Fetching locations from MyCase API - Page: ${page}, Per Page: ${perPage}${updatedAfter ? `, Updated After: ${updatedAfter}` : ''}`,
      );

      const params = new URLSearchParams({
        page: page.toString(),
        per_page: perPage.toString(),
      });

      // Add updated_after filter if provided
      if (updatedAfter) {
        params.append('filter[updated_after]', updatedAfter);
      }

      const response = await firstValueFrom(
        this.httpService.get(
          `${this.MYCASE_API_BASE_URL}/locations?${params.toString()}`,
          {
            headers: this.getHeaders(accessToken),
          },
        ),
      );

      this.logger.log(
        `Successfully fetched ${response.data?.length || 0} locations from MyCase`,
      );

      return {
        data: response.data || [],
        total: response.data.total || 0,
        page: response.data.page || page,
        per_page: response.data.per_page || perPage,
      };
    } catch (error) {
      this.logger.error(
        `Error fetching locations from MyCase: ${error.message}`,
      );
      if (error.response) {
        this.logger.error(
          `MyCase API response: ${JSON.stringify(error.response.data)}`,
        );
      }
      throw error;
    }
  }

  async getAllLocationsFromMyCase(
    accessToken?: string,
    updatedAfter?: string,
  ): Promise<MyCaseLocationInterface[]> {
    try {
      this.logger.log(
        `Fetching all locations from MyCase API${updatedAfter ? ` updated after: ${updatedAfter}` : ''}`,
      );

      let allLocations: MyCaseLocationInterface[] = [];
      let currentPage = 1;
      let hasMorePages = true;
      const perPage = 100; // Maximum per page to reduce API calls

      while (hasMorePages) {
        const response = await this.getLocationsFromMyCase(
          accessToken,
          currentPage,
          perPage,
          updatedAfter,
        );

        allLocations = allLocations.concat(response.data);

        // Check if there are more pages
        const totalPages = Math.ceil(response.total / perPage);
        hasMorePages = currentPage < totalPages;
        currentPage++;
      }

      this.logger.log(
        `Successfully fetched ${allLocations.length} total locations from MyCase${updatedAfter ? ` updated after: ${updatedAfter}` : ''}`,
      );
      return allLocations;
    } catch (error) {
      this.logger.error(
        `Error fetching all locations from MyCase: ${error.message}`,
      );
      throw error;
    }
  }

  async getLocationFromMyCaseByCase(
    caseId: string,
    accessToken?: string,
  ): Promise<MyCaseLocationInterface | null> {
    try {
      this.logger.log(
        `Fetching case details to get location for case ID: ${caseId}`,
      );

      const response = await firstValueFrom(
        this.httpService.get(`${this.MYCASE_API_BASE_URL}/cases/${caseId}`, {
          headers: this.getHeaders(accessToken),
        }),
      );

      const caseData = response.data;

      if (!caseData.location) {
        this.logger.log(`No location found for case ID: ${caseId}`);
        return null;
      }

      this.logger.log(`Found location for case ID: ${caseId}`);
      return caseData.location;
    } catch (error) {
      this.logger.error(
        `Error fetching case location for case ID ${caseId}: ${error.message}`,
      );
      if (error.response) {
        this.logger.error(
          `MyCase API response: ${JSON.stringify(error.response.data)}`,
        );
      }
      throw error;
    }
  }

  /**
   * Check if a client exists in MyCase by client ID
   * @param clientId MyCase client ID
   * @param accessToken MyCase access token
   * @returns Client data if found, null if not found
   */
  async getClientFromMyCase(
    clientId: string,
    accessToken?: string,
  ): Promise<any | null> {
    console.log('🚀 ~ MyCaseService ~ accessToken:', accessToken);
    try {
      this.logger.log(`Fetching client details for client ID: ${clientId}`);

      if (!accessToken) {
        this.logger.error('Access token is required for MyCase API calls');
        throw new Error('Access token is required for MyCase API calls');
      }

      const response = await firstValueFrom(
        this.httpService.get(
          `${this.MYCASE_API_BASE_URL}/clients/${clientId}`,
          {
            headers: this.getHeaders(accessToken),
          },
        ),
      );

      const clientData = response.data;
      this.logger.log(`Successfully fetched client data for ID: ${clientId}`);

      return clientData;
    } catch (error) {
      this.logger.error(
        `Error fetching client for client ID ${clientId}: ${error.message}`,
      );

      if (error.response) {
        this.logger.error(
          `MyCase API response: ${JSON.stringify(error.response.data)}`,
        );

        // If client not found (404), return null instead of throwing
        if (error.response.status === 404) {
          this.logger.log(`Client with ID ${clientId} not found in MyCase`);
          return null;
        }

        // If unauthorized (401), throw authentication error
        if (error.response.status === 401) {
          throw new Error(
            'Authentication failed: Invalid or expired access token',
          );
        }
      }

      throw error;
    }
  }

  // New methods for client management
  async getClientsFromMyCase(
    accessToken?: string,
    page: number = 1,
    perPage: number = 50,
    updatedAfter?: string,
  ): Promise<MyCaseClientListResponse> {
    try {
      this.logger.log(
        `Fetching clients from MyCase API - Page: ${page}, Per Page: ${perPage}${updatedAfter ? `, Updated After: ${updatedAfter}` : ''}`,
      );

      const params = new URLSearchParams({
        page: page.toString(),
        per_page: perPage.toString(),
      });

      // Add updated_after filter if provided
      if (updatedAfter) {
        params.append('filter[updated_after]', updatedAfter);
      }

      const response = await firstValueFrom(
        this.httpService.get(
          `${this.MYCASE_API_BASE_URL}/clients?${params.toString()}`,
          {
            headers: this.getHeaders(accessToken),
          },
        ),
      );

      this.logger.log(
        `Successfully fetched ${response.data?.length || 0} clients from MyCase`,
      );

      return {
        data: response.data || [],
        total: response.data.total || 0,
        page: response.data.page || page,
        per_page: response.data.per_page || perPage,
      };
    } catch (error) {
      this.logger.error(`Error fetching clients from MyCase: ${error.message}`);
      if (error.response) {
        this.logger.error(
          `MyCase API response: ${JSON.stringify(error.response.data)}`,
        );
      }
      throw error;
    }
  }

  async getAllClientsFromMyCase(
    accessToken?: string,
    updatedAfter?: string,
  ): Promise<MyCaseClientInterface[]> {
    try {
      this.logger.log(
        `Fetching all clients from MyCase API${updatedAfter ? ` updated after: ${updatedAfter}` : ''}`,
      );

      let allClients: MyCaseClientInterface[] = [];
      let currentPage = 1;
      let hasMorePages = true;
      const perPage = 100; // Maximum per page to reduce API calls

      while (hasMorePages) {
        const response = await this.getClientsFromMyCase(
          accessToken,
          currentPage,
          perPage,
          updatedAfter,
        );

        allClients = allClients.concat(response.data);

        // Check if there are more pages
        const totalPages = Math.ceil(response.total / perPage);
        hasMorePages = currentPage < totalPages;
        currentPage++;
      }

      this.logger.log(
        `Successfully fetched ${allClients.length} total clients from MyCase${updatedAfter ? ` updated after: ${updatedAfter}` : ''}`,
      );
      return allClients;
    } catch (error) {
      this.logger.error(
        `Error fetching all clients from MyCase: ${error.message}`,
      );
      throw error;
    }
  }

  async getClientFromMyCaseById(
    clientId: string,
    accessToken?: string,
  ): Promise<MyCaseClientInterface | null> {
    try {
      this.logger.log(`Fetching client details for client ID: ${clientId}`);

      const response = await firstValueFrom(
        this.httpService.get(
          `${this.MYCASE_API_BASE_URL}/clients/${clientId}`,
          {
            headers: this.getHeaders(accessToken),
          },
        ),
      );

      const clientData = response.data;

      if (!clientData) {
        this.logger.log(`No client found for client ID: ${clientId}`);
        return null;
      }

      this.logger.log(`Found client for client ID: ${clientId}`);
      return clientData;
    } catch (error) {
      this.logger.error(
        `Error fetching client for client ID ${clientId}: ${error.message}`,
      );
      if (error.response) {
        this.logger.error(
          `MyCase API response: ${JSON.stringify(error.response.data)}`,
        );
      }
      throw error;
    }
  }

  // New methods for matter management
  async getMattersFromMyCase(
    accessToken?: string,
    page: number = 1,
    perPage: number = 50,
    updatedAfter?: string,
  ): Promise<MyCaseMatterListResponse> {
    try {
      this.logger.log(
        `Fetching matters from MyCase API - Page: ${page}, Per Page: ${perPage}${updatedAfter ? `, Updated After: ${updatedAfter}` : ''}`,
      );

      const params = new URLSearchParams({
        page: page.toString(),
        per_page: perPage.toString(),
      });

      // Add updated_after filter if provided
      if (updatedAfter) {
        params.append('filter[updated_after]', updatedAfter);
      }

      const response = await firstValueFrom(
        this.httpService.get(
          `${this.MYCASE_API_BASE_URL}/cases?${params.toString()}`,
          {
            headers: this.getHeaders(accessToken),
          },
        ),
      );

      this.logger.log(
        `Successfully fetched ${response.data?.length || 0} matters from MyCase`,
      );

      return {
        data: response.data || [],
        total: response.data.total || 0,
        page: response.data.page || page,
        per_page: response.data.per_page || perPage,
      };
    } catch (error) {
      this.logger.error(`Error fetching matters from MyCase: ${error.message}`);
      if (error.response) {
        this.logger.error(
          `MyCase API response: ${JSON.stringify(error.response.data)}`,
        );
      }
      throw error;
    }
  }

  async getAllMattersFromMyCase(
    accessToken?: string,
    updatedAfter?: string,
  ): Promise<MyCaseMatterInterface[]> {
    try {
      this.logger.log(
        `Fetching all matters from MyCase API${updatedAfter ? ` updated after: ${updatedAfter}` : ''}`,
      );

      let allMatters: MyCaseMatterInterface[] = [];
      let currentPage = 1;
      let hasMorePages = true;
      const perPage = 100; // Maximum per page to reduce API calls

      while (hasMorePages) {
        const response = await this.getMattersFromMyCase(
          accessToken,
          currentPage,
          perPage,
          updatedAfter,
        );

        allMatters = allMatters.concat(response.data);

        // Check if there are more pages
        const totalPages = Math.ceil(response.total / perPage);
        hasMorePages = currentPage < totalPages;
        currentPage++;
      }

      this.logger.log(
        `Successfully fetched ${allMatters.length} total matters from MyCase${updatedAfter ? ` updated after: ${updatedAfter}` : ''}`,
      );
      return allMatters;
    } catch (error) {
      this.logger.error(
        `Error fetching all matters from MyCase: ${error.message}`,
      );
      throw error;
    }
  }

  async getMatterFromMyCaseById(
    matterId: string,
    accessToken?: string,
  ): Promise<MyCaseMatterInterface | null> {
    try {
      this.logger.log(`Fetching matter details for matter ID: ${matterId}`);

      const response = await firstValueFrom(
        this.httpService.get(`${this.MYCASE_API_BASE_URL}/cases/${matterId}`, {
          headers: this.getHeaders(accessToken),
        }),
      );

      const matterData = response.data;

      if (!matterData) {
        this.logger.log(`No matter found for matter ID: ${matterId}`);
        return null;
      }

      this.logger.log(`Found matter for matter ID: ${matterId}`);
      return matterData;
    } catch (error) {
      this.logger.error(
        `Error fetching matter for matter ID ${matterId}: ${error.message}`,
      );
      if (error.response) {
        this.logger.error(
          `MyCase API response: ${JSON.stringify(error.response.data)}`,
        );
      }
      throw error;
    }
  }

  // New methods for event management
  async getEventsFromMyCase(
    accessToken?: string,
    page: number = 1,
    perPage: number = 50,
    updatedAfter?: string,
  ): Promise<MyCaseEventListResponse> {
    try {
      this.logger.log(
        `Fetching events from MyCase API - Page: ${page}, Per Page: ${perPage}${updatedAfter ? `, Updated After: ${updatedAfter}` : ''}`,
      );

      const params = new URLSearchParams({
        page: page.toString(),
        per_page: perPage.toString(),
      });

      // Add updated_after filter if provided
      if (updatedAfter) {
        params.append('filter[updated_after]', updatedAfter);
      }

      const response = await firstValueFrom(
        this.httpService.get(
          `${this.MYCASE_API_BASE_URL}/events?${params.toString()}`,
          {
            headers: this.getHeaders(accessToken),
          },
        ),
      );

      this.logger.log(
        `Successfully fetched ${response.data?.length || 0} events from MyCase`,
      );

      return {
        data: response.data || [],
        total: response.data.total || 0,
        page: response.data.page || page,
        per_page: response.data.per_page || perPage,
      };
    } catch (error) {
      this.logger.error(`Error fetching events from MyCase: ${error.message}`);
      if (error.response) {
        this.logger.error(
          `MyCase API response: ${JSON.stringify(error.response.data)}`,
        );
      }
      throw error;
    }
  }

  async getAllEventsFromMyCase(
    accessToken?: string,
    updatedAfter?: string,
  ): Promise<MyCaseEventInterface[]> {
    try {
      this.logger.log(
        `Fetching all events from MyCase API${updatedAfter ? ` updated after: ${updatedAfter}` : ''}`,
      );

      let allEvents: MyCaseEventInterface[] = [];
      let currentPage = 1;
      let hasMorePages = true;
      const perPage = 100; // Maximum per page to reduce API calls

      while (hasMorePages) {
        const response = await this.getEventsFromMyCase(
          accessToken,
          currentPage,
          perPage,
          updatedAfter,
        );

        allEvents = allEvents.concat(response.data);

        // Check if there are more pages
        const totalPages = Math.ceil(response.total / perPage);
        hasMorePages = currentPage < totalPages;
        currentPage++;
      }

      this.logger.log(
        `Successfully fetched ${allEvents.length} total events from MyCase${updatedAfter ? ` updated after: ${updatedAfter}` : ''}`,
      );
      return allEvents;
    } catch (error) {
      this.logger.error(
        `Error fetching all events from MyCase: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Get events from previous day for cron job sync
   * Uses updated_after filter to only fetch events created/updated since yesterday
   */
  async getEventsFromPreviousDay(
    accessToken?: string,
  ): Promise<MyCaseEventInterface[]> {
    try {
      // Calculate previous day's date in ISO format
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      yesterday.setHours(0, 0, 0, 0); // Set to start of day

      const updatedAfter = yesterday.toISOString();

      this.logger.log(
        `Fetching events from MyCase updated after: ${updatedAfter}`,
      );

      const events = await this.getAllEventsFromMyCase(
        accessToken,
        updatedAfter,
      );

      this.logger.log(
        `Found ${events.length} events updated since ${updatedAfter}`,
      );

      return events;
    } catch (error) {
      this.logger.error(
        `Error fetching events from previous day: ${error.message}`,
      );
      throw error;
    }
  }

  async getEventFromMyCaseById(
    eventId: string,
    accessToken?: string,
  ): Promise<MyCaseEventInterface | null> {
    try {
      this.logger.log(`Fetching event details for event ID: ${eventId}`);

      const response = await firstValueFrom(
        this.httpService.get(`${this.MYCASE_API_BASE_URL}/events/${eventId}`, {
          headers: this.getHeaders(accessToken),
        }),
      );

      const eventData = response.data;

      if (!eventData) {
        this.logger.log(`No event found for event ID: ${eventId}`);
        return null;
      }

      this.logger.log(`Found event for event ID: ${eventId}`);
      return eventData;
    } catch (error) {
      this.logger.error(
        `Error fetching event for event ID ${eventId}: ${error.message}`,
      );
      if (error.response) {
        this.logger.error(
          `MyCase API response: ${JSON.stringify(error.response.data)}`,
        );
      }
      throw error;
    }
  }

  /**
   * Fetch case details from MyCase API
   */
  async fetchCaseDetails(
    caseId: number,
    accessToken?: string,
  ): Promise<MyCaseCase | null> {
    try {
      this.logger.log(`Fetching case details for case ID: ${caseId}`);

      // Fetch single case by ID
      const response = await firstValueFrom(
        this.httpService.get<MyCaseCase>(
          `${this.MYCASE_API_BASE_URL}/cases/${caseId}`,
          {
            headers: this.getHeaders(accessToken),
          },
        ),
      );

      const caseDetails = response.data;

      if (!caseDetails || !caseDetails.id) {
        this.logger.warn(`Case not found with ID: ${caseId}`);
        return null;
      }

      this.logger.log(`Successfully fetched case details for ID: ${caseId}`);
      return caseDetails;
    } catch (error) {
      this.logger.error(
        `Error fetching case details for ID ${caseId}: ${error.message}`,
      );
      if (error.response) {
        this.logger.error(
          `MyCase API response: ${JSON.stringify(error.response.data)}`,
        );
      }

      // If single case fetch fails, try fetching from cases list
      try {
        this.logger.log(`Trying to fetch case ${caseId} from cases list`);
        const response = await firstValueFrom(
          this.httpService.get<MyCaseCaseResponse>(
            `${this.MYCASE_API_BASE_URL}/cases`,
            {
              headers: this.getHeaders(accessToken),
            },
          ),
        );

        const cases = response.data;
        const caseDetails = cases.find((c) => c.id === caseId);

        if (!caseDetails) {
          this.logger.warn(`Case not found with ID: ${caseId} in cases list`);
          return null;
        }

        this.logger.log(
          `Successfully fetched case details for ID: ${caseId} from cases list`,
        );
        return caseDetails;
      } catch (listError) {
        this.logger.error(
          `Error fetching case from cases list: ${listError.message}`,
        );
        throw error; // Throw original error
      }
    }
  }

  /**
   * Build context for webhook event processing
   */
  async buildWebhookEventContext(
    eventPayload: MyCaseEventWebhookPayload,
    accessToken: string,
  ): Promise<WebhookEventContext> {
    const context: WebhookEventContext = {};

    try {
      // 1. Always fetch case details from MyCase API if case ID is provided
      if (eventPayload.case?.id) {
        this.logger.log(
          `Fetching case details for case ID: ${eventPayload.case.id}`,
        );

        try {
          const caseDetails = await this.fetchCaseDetails(
            eventPayload.case.id,
            accessToken,
          );

          console.log(
            '🚀 ~ MyCaseService ~ buildWebhookEventContext ~ caseDetails:',
            caseDetails,
          );

          if (caseDetails) {
            context.caseDetails = caseDetails;
            this.logger.log(
              `Successfully fetched case details for case ID: ${eventPayload.case.id}`,
            );
          }
        } catch (error) {
          this.logger.warn(
            `Failed to fetch case details for case ID ${eventPayload.case.id}: ${error.message}`,
          );
          // Continue processing without case details
        }

        // 2. Try to find local Matter by my_case_matter_id
        const matter = await this.matterModel.findOne({
          my_case_matter_id: String(eventPayload.case.id),
        });

        if (matter) {
          context.matter = matter;
          this.logger.log(
            `Found local matter for case ID: ${eventPayload.case.id}`,
          );

          // Try to find a workflow_execution for this matter
          const workflow = await this.workflowExecutionModel.findOne({
            assigns: matter.client_id,
            is_active: true,
          });

          if (workflow) {
            context.workflowExecution = workflow;
            this.logger.log(`Found workflow execution for matter`);
          }
        } else {
          this.logger.warn(
            `No local matter found for case ID: ${eventPayload.case.id}`,
          );
        }
      }

      // 3. Find CourtLocation by my_case_id if location ID is provided
      if (eventPayload.location?.id) {
        const location = await this.courtLocationModel.findOne({
          my_case_id: eventPayload.location.id,
        });

        if (location) {
          context.courtLocation = location;
          this.logger.log(
            `Found court location for location ID: ${eventPayload.location.id}`,
          );
        } else {
          this.logger.warn(
            `No court location found for location ID: ${eventPayload.location.id}`,
          );
        }
      }

      console.log(
        '🚀 ~ MyCaseService ~ buildWebhookEventContext ~~~~~~~~~~~~ context:',
        context,
      );

      return context;
    } catch (error) {
      this.logger.error(
        `Error building webhook event context: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Map MyCase event payload to EventSub
   */
  mapEventPayloadToEventSub(
    eventPayload: MyCaseEventWebhookPayload,
    context: WebhookEventContext,
    action: string,
  ): EventSub {
    // Extract date and time from start/end ISO strings
    const startDate = eventPayload.start
      ? eventPayload.start.split('T')[0]
      : '';
    const endDate = eventPayload.end ? eventPayload.end.split('T')[0] : '';
    const startTime = eventPayload.start
      ? eventPayload.start.split('T')[1]?.substring(0, 5)
      : '';
    const endTime = eventPayload.end
      ? eventPayload.end.split('T')[1]?.substring(0, 5)
      : '';

    // Format date for display (MM/DD/YYYY format)
    const formatDateForDisplay = (dateString: string): string => {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US');
    };

    // Build client name with proper formatting
    let clientName = '';
    let caseNumber = '';
    let county = '';
    let charge = '';

    if (context.caseDetails) {
      const caseDetails = context.caseDetails;
      caseNumber = caseDetails.case_number || '';

      // Build client name from case details
      if (caseDetails.clients && caseDetails.clients.length > 0) {
        const primaryClient = caseDetails.clients[0];
        const firstName = primaryClient.first_name || '';
        const lastName = primaryClient.last_name || '';
        clientName = `${firstName} ${lastName}`.trim();

        // Add case name and practice area if available
        if (caseDetails.name) {
          clientName = `${clientName} | ${clientName} (${caseDetails.name})`;
        }
      }

      // Extract county from office address
      if (caseDetails.office?.address) {
        county =
          caseDetails.office.address.city ||
          caseDetails.office.address.state ||
          '';
      }

      // Extract charge from practice area or description
      charge = caseDetails.practice_area || '';
    }

    // Get court location name
    const courtLocationName =
      context.courtLocation?.name || context.courtLocation?.text || '';

    const eventSub: any = {
      id: String(eventPayload.id),
      caseNumber: caseNumber,
      clientName: clientName,
      description: eventPayload.description || '',
      date: formatDateForDisplay(eventPayload.start),
      startTime: startTime,
      endTime: endTime,
      isCompleted: false,
      subject: eventPayload.name || eventPayload.description || '',
      courtNoticeType: '', // This would need to be derived from event name or description
      courtNoticeActions: 'Calendar event and save court notice',
      appointmentAction: this.mapActionToAppointmentAction(action),
      eventStatus: this.mapActionToEventStatus('sync'),
      charge: charge,
      county: county,
      courtLocation: courtLocationName,
      optionalAttendees: '', // These would come from staff if mapped
      requiredAttendees: '', // These would come from staff if mapped
      clientAttendance: eventPayload.private
        ? 'Appearance required'
        : 'Appearance not required',
      meetingLocation: '', // Could be derived from location details
      phoneDetails: '',
      meetingAddress: '',
      meetingLink: '',
      appointmentToReschedule: '',
      court_notice_date: '',
      startDate: startDate,
      endDate: endDate,
      allDay: eventPayload.all_day || false,
      files: [],
      my_case_event_id: String(eventPayload.id),
    };

    // If we have case details, enhance the description
    if (context.caseDetails && clientName && caseNumber) {
      const enhancedDescription = `${eventPayload.name}; ${clientName} (${charge}-${county}) ${caseNumber} ${eventSub.clientAttendance}`;
      eventSub.description = enhancedDescription;
      eventSub.subject = enhancedDescription;
    }

    return eventSub;
  }

  /**
   * Map webhook action to EventStatus
   */
  private mapActionToEventStatus(action: string): string {
    switch (action) {
      case 'created':
        return 'New';
      case 'updated':
        return 'Pending';
      case 'deleted':
        return 'Cancel';
      case 'sync':
        return 'Synced';
      default:
        return 'New';
    }
  }

  /**
   * Map webhook action to appointment action
   */
  private mapActionToAppointmentAction(action: string): string {
    switch (action) {
      case 'created':
        return 'New';
      case 'updated':
        return 'Update';
      case 'deleted':
        return 'Cancel';
      default:
        return 'New';
    }
  }

  /**
   * Build client name from case details
   */
  private buildClientName(caseDetails: MyCaseCase): string {
    if (!caseDetails.clients || caseDetails.clients.length === 0) {
      return '';
    }

    const primaryClient = caseDetails.clients[0];
    return `${primaryClient.first_name} ${primaryClient.last_name}`.trim();
  }

  /**
   * Extract county from office address
   */
  private extractCountyFromOffice(office: any): string {
    if (!office?.address) {
      return '';
    }

    // You can implement more sophisticated county extraction logic here
    return office.address.city || '';
  }

  /**
   * Handle webhook event with proper error handling and logging
   */
  async handleWebhookEvent(
    eventPayload: MyCaseEventWebhookPayload,
    action: string,
    accessToken?: string,
  ): Promise<void> {
    try {
      this.logger.log(
        `Processing webhook event: ${action} for event ID: ${eventPayload.id}`,
      );

      // Build context for processing
      const context = await this.buildWebhookEventContext(
        eventPayload,
        accessToken,
      );

      // Map event payload to EventSub
      const eventSub = this.mapEventPayloadToEventSub(
        eventPayload,
        context,
        action,
      );

      // Process based on action
      switch (action) {
        case 'created':
          await this.handleEventCreated(eventPayload, eventSub, context);
          break;
        case 'updated':
          await this.handleEventUpdated(eventPayload, eventSub, context);
          break;
        case 'deleted':
          await this.handleEventDeleted(eventPayload, eventSub, context);
          break;
        default:
          this.logger.warn(`Unknown action: ${action}`);
          return;
      }

      this.logger.log(
        `Successfully processed webhook event: ${action} for event ID: ${eventPayload.id}`,
      );
    } catch (error) {
      this.logger.error(
        `Error handling webhook event: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Handle event created
   */
  private async handleEventCreated(
    eventPayload: MyCaseEventWebhookPayload,
    eventSub: EventSub,
    context: WebhookEventContext,
  ): Promise<void> {
    const eventId = String(eventPayload.id);

    // Check if event exists with this my_case_event_id
    const existingEvent = await this.mycaseClientMatterModel.findOne({
      my_case_event_id: eventId,
    });

    if (existingEvent) {
      // Try to find by event_id as fallback
      return;
    }

    const updateData: any = {
      event_id: String(eventPayload.id),
      my_case_event_id: String(eventPayload.id),
      event: {
        id: eventSub.id,
        caseNumber: eventSub.caseNumber || '',
        clientName: eventSub.clientName || '',
        description: eventSub.description || '',
        date: eventSub.date || '',
        startTime: eventSub.startTime || '',
        endTime: eventSub.endTime || '',
        isCompleted: eventSub.isCompleted || false,
        subject: eventSub.subject || '',
        courtNoticeType: eventSub.courtNoticeType || '',
        courtNoticeActions:
          eventSub.courtNoticeActions || 'Calendar event and save court notice',
        appointmentAction: eventSub.appointmentAction || 'New',
        eventStatus: eventSub.eventStatus || 'New',
        charge: eventSub.charge || '',
        county: eventSub.county || '',
        courtLocation: eventSub.courtLocation || '',
        optionalAttendees: eventSub.optionalAttendees || '',
        requiredAttendees: eventSub.requiredAttendees || '',
        clientAttendance:
          eventSub.clientAttendance || 'Appearance not required',
        meetingLocation: eventSub.meetingLocation || '',
        phoneDetails: eventSub.phoneDetails || '',
        meetingAddress: eventSub.meetingAddress || '',
        meetingLink: eventSub.meetingLink || '',
        appointmentToReschedule: eventSub.appointmentToReschedule || '',
        court_notice_date: eventSub.court_notice_date || '',
        startDate: eventSub.startDate || '',
        endDate: eventSub.endDate || '',
        allDay: eventSub.allDay || false,
        files: eventSub.files || [],
        my_case_event_id: eventSub.my_case_event_id || String(eventPayload.id),
      },
      eventStatus: EventStatus.NEW,
      is_active: true,
    };

    if (context.matter) {
      updateData.client_matter_id = (context.matter as any)._id;
    }

    if (context.workflowExecution) {
      updateData.workflow_execution_id = (context.workflowExecution as any)._id;
    }

    try {
      // Use findOneAndUpdate with upsert to ensure data is properly saved
      const result = await this.mycaseClientMatterModel.findOneAndUpdate(
        { event_id: String(eventPayload.id) },
        { $set: updateData },
        {
          upsert: true,
          new: true,
          runValidators: false, // Disable validators to allow all fields
          strict: false, // Allow fields not in schema
        },
      );

      this.logger.log(
        `Created/Updated MycaseClientMatter for event_id ${eventPayload.id}`,
      );
      console.log('🚀 ~ MyCaseService ~ handleEventCreated ~ result:', result);
    } catch (error) {
      this.logger.error(`Error saving MycaseClientMatter: ${error.message}`);
      throw error;
    }
  }

  /**
   * Handle event updated - Only updates fields present in the payload
   */
  private async handleEventUpdated(
    eventPayload: MyCaseEventWebhookPayload,
    eventSub: EventSub,
    context: WebhookEventContext,
  ): Promise<void> {
    // Build selective update fields using helper method
    const eventUpdateFields = this.buildSelectiveEventUpdateFields(
      eventPayload,
      context,
    );

    // Always update these fields for tracking
    // eventUpdateFields['eventStatus'] = EventStatus.SYNCED;
    eventUpdateFields['is_active'] = true;

    // Add context-related IDs if available
    if (context.matter) {
      eventUpdateFields['client_matter_id'] = (context.matter as any)._id;
    }

    if (context.workflowExecution) {
      eventUpdateFields['workflow_execution_id'] = (
        context.workflowExecution as any
      )._id;
    }

    try {
      // Use $set to only update the specified fields
      const result = await this.mycaseClientMatterModel.findOneAndUpdate(
        { my_case_event_id: String(eventPayload.id) },
        { $set: eventUpdateFields },
        {
          new: true,
          runValidators: false,
          strict: false,
        },
      );

      if (!result) {
        this.logger.warn(
          `No existing record found for event_id ${eventPayload.id}, creating new one`,
        );
        await this.handleEventCreated(eventPayload, eventSub, context);
      } else {
        this.logger.log(
          `Updated MycaseClientMatter for event_id ${eventPayload.id} with fields: ${Object.keys(eventUpdateFields).join(', ')}`,
        );
        console.log(
          '🚀 ~ MyCaseService ~ handleEventUpdated ~ result:',
          result,
        );
      }
    } catch (error) {
      this.logger.error(`Error updating MycaseClientMatter: ${error.message}`);
      throw error;
    }
  }

  /**
   * Handle event deleted
   */
  private async handleEventDeleted(
    eventPayload: MyCaseEventWebhookPayload,
    eventSub: EventSub,
    context: WebhookEventContext,
  ): Promise<void> {
    const updateData: any = {
      event: {
        id: eventSub.id,
        caseNumber: eventSub.caseNumber || '',
        clientName: eventSub.clientName || '',
        description: eventSub.description || '',
        date: eventSub.date || '',
        startTime: eventSub.startTime || '',
        endTime: eventSub.endTime || '',
        isCompleted: eventSub.isCompleted || false,
        subject: eventSub.subject || '',
        courtNoticeType: eventSub.courtNoticeType || '',
        courtNoticeActions:
          eventSub.courtNoticeActions || 'Calendar event and save court notice',
        appointmentAction: 'Cancel',
        eventStatus: 'Cancel',
        charge: eventSub.charge || '',
        county: eventSub.county || '',
        courtLocation: eventSub.courtLocation || '',
        optionalAttendees: eventSub.optionalAttendees || '',
        requiredAttendees: eventSub.requiredAttendees || '',
        clientAttendance:
          eventSub.clientAttendance || 'Appearance not required',
        meetingLocation: eventSub.meetingLocation || '',
        phoneDetails: eventSub.phoneDetails || '',
        meetingAddress: eventSub.meetingAddress || '',
        meetingLink: eventSub.meetingLink || '',
        appointmentToReschedule: eventSub.appointmentToReschedule || '',
        court_notice_date: eventSub.court_notice_date || '',
        startDate: eventSub.startDate || '',
        endDate: eventSub.endDate || '',
        allDay: eventSub.allDay || false,
        files: eventSub.files || [],
        my_case_event_id: eventSub.my_case_event_id || String(eventPayload.id),
      },
      eventStatus: EventStatus.CANCEL,
      is_active: false,
    };

    // Add client_matter_id if available
    if (context.matter) {
      updateData.client_matter_id = (context.matter as any)._id;
    }

    // Add workflow_execution_id if available
    if (context.workflowExecution) {
      updateData.workflow_execution_id = (context.workflowExecution as any)._id;
    }

    try {
      const result = await this.mycaseClientMatterModel.findOneAndUpdate(
        {
          $or: [
            { event_id: String(eventPayload.id) },
            { my_case_event_id: String(eventPayload.id) },
          ],
        },
        { $set: updateData },
        {
          new: true,
          runValidators: false,
          strict: false,
        },
      );

      if (result) {
        this.logger.log(
          `Marked MycaseClientMatter as inactive for event_id ${eventPayload.id}`,
        );
        console.log(
          '🚀 ~ MyCaseService ~ handleEventDeleted ~ result:',
          result,
        );
      } else {
        this.logger.warn(
          `No record found to delete for event_id ${eventPayload.id}`,
        );
      }
    } catch (error) {
      this.logger.error(`Error deleting MycaseClientMatter: ${error.message}`);
      throw error;
    }
  }

  /**
   * Helper method to selectively map webhook payload fields to database fields
   * Only maps fields that are present in the payload
   */
  private buildSelectiveEventUpdateFields(
    eventPayload: MyCaseEventWebhookPayload,
    context: WebhookEventContext,
  ): Record<string, any> {
    const eventUpdateFields: Record<string, any> = {};

    // Core event fields mapping
    const fieldMappings = [
      {
        payload: 'my_case_event_id',
        dbField: 'event.my_case_event_id',
        transform: String,
      },
      { payload: 'name', dbField: 'event.subject' },
      { payload: 'description', dbField: 'event.description' },
      { payload: 'all_day', dbField: 'event.allDay' },
    ];

    // Apply basic field mappings
    fieldMappings.forEach((mapping) => {
      const value = eventPayload[mapping.payload];
      if (value !== undefined) {
        eventUpdateFields[mapping.dbField] = mapping.transform
          ? mapping.transform(value)
          : value;
      }
    });

    // Handle date/time fields
    if (eventPayload.start !== undefined) {
      const startDate = eventPayload.start.split('T')[0];
      const startTime = eventPayload.start.split('T')[1]?.substring(0, 5);
      eventUpdateFields['event.startDate'] = startDate;
      eventUpdateFields['event.startTime'] = startTime;
      eventUpdateFields['event.date'] = new Date(
        eventPayload.start,
      ).toLocaleDateString('en-US');
    }

    if (eventPayload.end !== undefined) {
      const endDate = eventPayload.end.split('T')[0];
      const endTime = eventPayload.end.split('T')[1]?.substring(0, 5);
      eventUpdateFields['event.endDate'] = endDate;
      eventUpdateFields['event.endTime'] = endTime;
    }

    // Handle private field transformation
    if (eventPayload.private !== undefined) {
      eventUpdateFields['event.clientAttendance'] = eventPayload.private
        ? 'Appearance required'
        : 'Appearance not required';
    }

    // Handle location updates
    if (eventPayload.location?.id !== undefined && context.courtLocation) {
      eventUpdateFields['event.courtLocation'] =
        context.courtLocation.name || context.courtLocation.text || '';
    }

    // Handle case-related updates
    if (eventPayload.case?.id !== undefined && context.caseDetails) {
      const caseDetails = context.caseDetails;

      if (caseDetails.case_number) {
        eventUpdateFields['event.caseNumber'] = caseDetails.case_number;
      }

      if (caseDetails.clients && caseDetails.clients.length > 0) {
        const primaryClient = caseDetails.clients[0];
        const clientName =
          `${primaryClient.first_name || ''} ${primaryClient.last_name || ''}`.trim();
        eventUpdateFields['event.clientName'] = clientName;
      }

      if (caseDetails.practice_area) {
        eventUpdateFields['event.charge'] = caseDetails.practice_area;
      }

      if (caseDetails.office?.address?.city) {
        eventUpdateFields['event.county'] = caseDetails.office.address.city;
      }

      // Build enhanced description if we have enough data
      if (
        eventPayload.name &&
        eventUpdateFields['event.clientName'] &&
        eventUpdateFields['event.caseNumber']
      ) {
        const enhancedDescription = `${eventPayload.name}; ${eventUpdateFields['event.clientName']} (${eventUpdateFields['event.charge'] || ''}-${eventUpdateFields['event.county'] || ''}) ${eventUpdateFields['event.caseNumber']} ${eventUpdateFields['event.clientAttendance'] || 'Appearance not required'}`;
        eventUpdateFields['event.description'] = enhancedDescription;
        eventUpdateFields['event.subject'] = enhancedDescription;
      }
    }

    // Add timestamp if present in payload
    if (eventPayload.updated_at) {
      eventUpdateFields['updatedAt'] = new Date(eventPayload.updated_at);
    }

    return eventUpdateFields;
  }

  /**
   * Get matters from previous day for cron job sync
   * Uses updated_after filter to only fetch matters created/updated since yesterday
   */
  async getMattersFromPreviousDay(
    accessToken?: string,
  ): Promise<MyCaseMatterInterface[]> {
    try {
      // Calculate previous day's date in ISO format
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      yesterday.setHours(0, 0, 0, 0); // Set to start of day

      const updatedAfter = yesterday.toISOString();

      this.logger.log(
        `Fetching matters from MyCase updated after: ${updatedAfter}`,
      );

      const matters = await this.getAllMattersFromMyCase(
        accessToken,
        updatedAfter,
      );

      this.logger.log(
        `Found ${matters.length} matters updated since ${updatedAfter}`,
      );

      return matters;
    } catch (error) {
      this.logger.error(
        `Error fetching matters from previous day: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Get clients from previous day for cron job sync
   * Uses updated_after filter to only fetch clients created/updated since yesterday
   */
  async getClientsFromPreviousDay(
    accessToken?: string,
  ): Promise<MyCaseClientInterface[]> {
    try {
      // Calculate previous day's date in ISO format
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      yesterday.setHours(0, 0, 0, 0); // Set to start of day

      const updatedAfter = yesterday.toISOString();

      this.logger.log(
        `Fetching clients from MyCase updated after: ${updatedAfter}`,
      );

      const clients = await this.getAllClientsFromMyCase(
        accessToken,
        updatedAfter,
      );

      this.logger.log(
        `Found ${clients.length} clients updated since ${updatedAfter}`,
      );

      return clients;
    } catch (error) {
      this.logger.error(
        `Error fetching clients from previous day: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Get locations from previous day for cron job sync
   * Uses updated_after filter to only fetch locations created/updated since yesterday
   */
  async getLocationsFromPreviousDay(
    accessToken?: string,
  ): Promise<MyCaseLocationInterface[]> {
    try {
      // Calculate previous day's date in ISO format
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      yesterday.setHours(0, 0, 0, 0); // Set to start of day

      const updatedAfter = yesterday.toISOString();

      this.logger.log(
        `Fetching locations from MyCase updated after: ${updatedAfter}`,
      );

      const locations = await this.getAllLocationsFromMyCase(
        accessToken,
        updatedAfter,
      );

      this.logger.log(
        `Found ${locations.length} locations updated since ${updatedAfter}`,
      );

      return locations;
    } catch (error) {
      this.logger.error(
        `Error fetching locations from previous day: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Process and upload/update files from event to MyCase with intelligent document handling
   * This method checks for existing MyCase document IDs and updates them instead of creating new ones
   */
  private async processEventFilesIntelligently(
    event: EventSub,
    caseId: string,
    accessToken?: string,
    prevParentTaskExecutionEvents?: any[]
  ): Promise<Array<{ fileName: string; result: MyCaseFileUploadResult }>> {
    const fileUploadResults: Array<{
      fileName: string;
      result: MyCaseFileUploadResult;
    }> = [];

    if (!event.files || event.files.length === 0) {
      this.logger.log('No files to upload for this event');
      return fileUploadResults;
    }

    this.logger.log(
      `Processing ${event.files.length} files for event ${event.id} with intelligent document handling`,
    );

    // Use files from prevParentTaskExecutionEvents if available, otherwise use current event files
    let filesToProcess = event.files;
    if (
      prevParentTaskExecutionEvents &&
      prevParentTaskExecutionEvents.length > 0
    ) {
      // Find the matching event in prevParentTaskExecutionEvents based on event ID
      const prevEvent = prevParentTaskExecutionEvents.find(
        (prevEvt) => prevEvt.id === event.id,
      );
      if (prevEvent && prevEvent.files && prevEvent.files.length > 0) {
        this.logger.log(
          `Found previous event with files for event ${event.id}, comparing by uniqueId`,
        );

        // Compare files by uniqueId and copy mycaseDocumentId if exists
        filesToProcess.forEach((currentFile) => {
          if (currentFile.uniqueId) {
            const matchingPrevFile = prevEvent.files.find(
              (prevFile) => prevFile.uniqueId === currentFile.uniqueId,
            );

            if (matchingPrevFile && matchingPrevFile.mycaseDocumentId) {
              this.logger.log(
                `Copying mycaseDocumentId ${matchingPrevFile.mycaseDocumentId} from previous file ${matchingPrevFile.name} to current file ${currentFile.name}`,
              );
              currentFile.mycaseDocumentId = matchingPrevFile.mycaseDocumentId;
            }
          }
        });
      }
    }

    for (const file of filesToProcess) {
      try {
        // Check if file has S3 key
        if (!file.key) {
          this.logger.warn(`File ${file.name} has no S3 key, skipping upload`);
          fileUploadResults.push({
            fileName: file.name,
            result: { success: false, error: 'No S3 key provided' },
          });
          continue;
        }

        // Check if file already has a MyCase document ID
        if (file.mycaseDocumentId) {
          this.logger.log(
            `File ${file.name} has existing MyCase document ID: ${file.mycaseDocumentId}, updating instead of creating new`,
          );

          // Create update payload
          const updatePayload: MyCaseDocumentUpdatePayload = {
            path: `workflow_uploads/${file.name}`,
            filename: file.name,
            description: `Updated from workflow: ${file.name}`,
            assigned_date: new Date().toISOString().split('T')[0],
          };

          // Update existing document
          const updateResult = await this.updateDocumentInMyCase(
            file.mycaseDocumentId,
            updatePayload,
            accessToken,
          );

          if (updateResult.success) {
            // If update successful, we still need to upload the new file content
            // For MyCase, updating metadata doesn't update file content
            // So we need to delete and recreate the document
            this.logger.log(
              `Document metadata updated, now replacing file content for ${file.name}`,
            );
          }
        } else {
          // File doesn't have MyCase document ID, create new document
          this.logger.log(
            `File ${file.name} has no MyCase document ID, creating new document`,
          );

          // Download file from S3
          const fileBuffer = await this.downloadFileFromS3(file.key);

          // Determine MIME type
          const mimeType = this.getMimeType(file.name);

          // Upload to MyCase
          const uploadResult = await this.uploadFileToMyCase(
            fileBuffer,
            file.name,
            mimeType,
            caseId,
            accessToken,
          );

          if (uploadResult.success) {
            // Store the new document ID in the file object
            file.mycaseDocumentId = uploadResult.fileId;
            this.logger.log(
              `Successfully uploaded ${file.name} to MyCase with new ID: ${uploadResult.fileId}`,
            );
          }

          fileUploadResults.push({
            fileName: file.name,
            result: uploadResult,
          });
        }
      } catch (error) {
        this.logger.error(
          `Error processing file ${file.name}: ${error.message}`,
        );
        fileUploadResults.push({
          fileName: file.name,
          result: { success: false, error: error.message },
        });
      }
    }

    return fileUploadResults;
  }

  /**
   * Update event data in TaskExecution to persist MyCase document IDs
   */
  private async updateEventFilesInTaskExecution(
    workflowExecutionId: string,
    eventId: string,
    updatedEvent: EventSub,
  ): Promise<void> {
    try {
      this.logger.log(
        `Updating event files in TaskExecution for event ID: ${eventId}`,
      );

      const updateResult = await this.taskExecutionModel.updateOne(
        {
          workflow_execution_id: new Types.ObjectId(workflowExecutionId),
          'derived_field.client.matter.event.id': eventId,
        },
        {
          $set: {
            'derived_field.$[].client.$[].matter.$[].event.$[eventElem]':
              updatedEvent,
          },
        },
        {
          arrayFilters: [{ 'eventElem.id': eventId }],
        },
      );

      this.logger.log(
        `Updated TaskExecution for event ID: ${eventId}, matched: ${updateResult.matchedCount}, modified: ${updateResult.modifiedCount}`,
      );
    } catch (error) {
      this.logger.error(
        `Error updating event files in TaskExecution: ${error.message}`,
      );
      throw error;
    }
  }
}
