import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  GetEventListDto,
  GetWorkflowDto,
  UpdateWorkflowDto,
  WorkflowRenderDto,
} from './dto/workflow.dto';
import { Model, Types } from 'mongoose';
import { WorkflowExecution } from './entities/workflow-execution.entity';
import { TaskExecution } from './entities/task-execution.entity';
import { ErrorExceptions } from 'src/exceptions/error.exception';
import { FormComponent } from './entities/form-component.entity';
import { Template } from './entities/template.entity';
import { Task } from './entities/task.entity';
import {
  EventStatus,
  Form_type,
  Task_type,
  TASK_VISIBLE_STATUS,
  Template_status,
  Work_flow_execution_status,
  EventAction,
} from './enum/template-status.enum';
import { DerivedComponent } from './entities/derived-component.entity';
import { County } from './entities/counties.entity';
import { Attendees } from './entities/attendees';
import { CourtLocation } from './entities/court.location.entity';
import {
  appointmentActions,
  clientAttendanceOptions,
  meetingLocations,
} from './constant/constant';
import { UserMetaData } from './entities/user-meta.entity';
import { EventSub } from './entities/derived.field.entity';
import { CourtNoticeType } from './entities/court.notice.type.entity';
import { Matter } from './entities/matter.entity';
import { Client } from '@shared/database';
import { RpcException } from '@nestjs/microservices';
import { getCurrentDateInTimezone, getRelativeTimeString, getRelativeTimeStringWithTimezone, getRelativeTimeTimezoneShifted, formatDateInTimezone } from 'src/common/common';
import mongoose from 'mongoose';
import * as moment from 'moment';
import { User } from './entities/user.entity';
import { CourtNoticeAction } from './entities/court-notice-action';
import { S3Service } from '../common/s3/s3.service';
import { MycaseClientMatter } from './entities/mycase.client.matter.entity';
import { MyCaseService } from './mycase.service';
import { EventActionHistory } from './entities/event-action-history';
import { LocationService } from './services/location.service';
import { Location } from '@shared/database';
import {
  GetLocationDto,
  SyncLocationsResponseDto,
  LocationResponseDto,
} from './dto/location.dto';
import {
  ClientService,
  SyncClientsResponseDto,
} from './services/client.service';
import {
  MatterService,
  SyncMattersResponseDto,
} from './services/matter.service';
import { EventService, SyncEventsResponseDto } from './services/event.service';
import { MyCaseHistory } from './entities/my-case-history.entity';
import { WebhookResponse } from './entities/webhook.response.entity';
import { WebhookService } from './services/webhook.service';
import { MyCaseEventInterface } from './services/event.service';
import { AssignmentService } from './services/assignment.service';
import {
  RoundRobinService,
  RoundRobinContext,
} from './services/round-robin.service';
import { Role } from './entities/roles.entity';
import { UserRole } from './entities/user-role.entity';
import { ConfigGroup } from './entities/config-group.entity';
import { last } from 'rxjs';

@Injectable()
export class WorkflowService {
  private readonly logger = new Logger(WorkflowService.name);

  constructor(
    @InjectModel(WorkflowExecution.name)
    private readonly workflowExecutionModel: Model<WorkflowExecution>,
    @InjectModel(TaskExecution.name)
    private readonly taskExecutionModel: Model<TaskExecution>,
    @InjectModel(FormComponent.name)
    private readonly formComponentModel: Model<FormComponent>,
    @InjectModel(Template.name)
    private readonly templateModel: Model<Template>,
    @InjectModel(Task.name)
    private readonly taskModel: Model<Task>,
    @InjectModel(DerivedComponent.name)
    private readonly derivedComponent: Model<DerivedComponent>,
    @InjectModel(County.name)
    private readonly countyModel: Model<County>,
    @InjectModel(Attendees.name)
    private readonly attendeesModel: Model<Attendees>,
    @InjectModel(CourtLocation.name)
    private readonly courtLocationModel: Model<CourtLocation>,
    @InjectModel(UserMetaData.name)
    private readonly userMetaDataModel: Model<UserMetaData>,
    @InjectModel(CourtNoticeType.name)
    private readonly CourtNoticeTypeModel: Model<CourtNoticeType>,
    @InjectModel(Matter.name)
    private readonly matterModel: Model<Matter>,
    @InjectModel(Client.name)
    private readonly clientModel: Model<Client>,
    @InjectModel(User.name)
    private readonly userModel: Model<User>,
    @InjectModel(CourtNoticeAction.name)
    private readonly courtNoticeActionModel: Model<CourtNoticeAction>,
    @InjectModel(MycaseClientMatter.name)
    private readonly mycaseClientMatterModel: Model<MycaseClientMatter>,
    @InjectModel(EventActionHistory.name)
    private readonly eventActionHistoryModel: Model<EventActionHistory>,
    @InjectModel(MyCaseHistory.name)
    private readonly myCaseHistoryModel: Model<MyCaseHistory>,
    @InjectModel(WebhookResponse.name)
    private readonly webhookResponseModel: Model<WebhookResponse>,
    @InjectModel(Location.name)
    private readonly locationModel: Model<Location>,
    @InjectModel(Role.name)
    private readonly roleModel: Model<Role>,
    @InjectModel(UserRole.name)
    private readonly userRoleModel: Model<UserRole>,
    @InjectModel(ConfigGroup.name)
    private readonly configGroupModel: Model<ConfigGroup>,
    private readonly s3Service: S3Service,
    private readonly myCaseService: MyCaseService,
    private readonly locationService: LocationService,
    private readonly clientService: ClientService,
    private readonly matterService: MatterService,
    private readonly eventService: EventService,
    private readonly webhookService: WebhookService,
    private readonly assignmentService: AssignmentService,
    private readonly roundRobinService: RoundRobinService,
  ) {}

  async myWorkFlow(getWorkflowDto: GetWorkflowDto) {
    const { page = 1, limit = 10 } = getWorkflowDto;

    const pageNumber = Number(page);
    const limitNumber = Number(limit);
    const skip = (pageNumber - 1) * limitNumber;

    const pipeline: any[] = [
      {
        $match: {
          is_deleted: false,
          is_active: true,
        },
      },
      {
        $lookup: {
          from: 'template',
          localField: 'template_id',
          foreignField: '_id',
          as: 'template',
        },
      },
      {
        $unwind: {
          path: '$template',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'task_execution',
          let: {
            wfId: '$_id',
            taskIdsFromTemplate: '$template.task_id',
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$task_status', 'ON_TRACK'] },
                    { $in: ['$task_id', '$$taskIdsFromTemplate'] },
                  ],
                },
              },
            },
          ],
          as: 'completedTasks',
        },
      },
      {
        $lookup: {
          from: 'client',
          localField: 'run_by',
          foreignField: '_id',
          as: 'runByDetails',
        },
      },
      {
        $lookup: {
          from: 'task_execution',
          let: { templateId: '$template._id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$template_id', '$$templateId'] },
                    { $eq: ['$is_deleted', false] },
                    { $eq: ['$is_active', true] },
                  ],
                },
              },
            },
            { $sort: { sequence: 1 } }, // ✅ Sort ascending to get lowest sequence
            { $limit: 1 },
            {
              $project: {
                _id: 0,
                task_id: 1,
              },
            },
          ],
          as: 'latestTaskExecution',
        },
      },
      {
        $addFields: {
          latest_task_id: {
            $cond: [
              { $gt: [{ $size: '$latestTaskExecution' }, 0] },
              {
                $toString: {
                  $arrayElemAt: ['$latestTaskExecution.task_id', 0],
                },
              },
              null,
            ],
          },
        },
      },
      {
        $addFields: {
          assigns: {
            $cond: {
              if: {
                $and: [
                  { $isArray: '$assigns' },
                  { $gt: [{ $size: '$assigns' }, 0] },
                ],
              },
              then: {
                $map: {
                  input: '$assigns',
                  as: 'id',
                  in: {
                    $cond: [
                      { $eq: [{ $type: '$$id' }, 'objectId'] },
                      '$$id',
                      { $toObjectId: '$$id' },
                    ],
                  },
                },
              },
              else: [],
            },
          },
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'assigns',
          foreignField: '_id',
          as: 'assignsDetail',
        },
      },
      {
        $addFields: {
          status: {
            $switch: {
              branches: [
                {
                  case: {
                    $and: [
                      { $ne: ['$end_date', null] },
                      {
                        $eq: [
                          {
                            $dateToString: {
                              format: '%Y-%m-%d',
                              date: { $toDate: '$end_date' },
                            },
                          },
                          {
                            $dateToString: {
                              format: '%Y-%m-%d',
                              date: new Date(),
                            },
                          },
                        ],
                      },
                    ],
                  },
                  then: Work_flow_execution_status.DUE_SOON,
                },
                {
                  case: {
                    $and: [
                      { $ne: ['$end_date', null] },
                      { $lt: [{ $toDate: '$end_date' }, new Date()] },
                    ],
                  },
                  then: Work_flow_execution_status.OVERDUE,
                },
              ],
              default: Work_flow_execution_status.ON_TRACK,
            },
          },
        },
      },
      {
        $project: {
          id: { $toString: '$_id' },
          start_date: {
            $cond: [
              { $eq: ['$start_date', null] },
              null,
              {
                $dateToString: {
                  format: '%Y-%m-%dT%H:%M:%S.%LZ',
                  date: { $toDate: '$start_date' },
                },
              },
            ],
          },
          end_date: {
            $cond: [
              { $eq: ['$end_date', null] },
              null,
              {
                $dateToString: {
                  format: '%Y-%m-%dT%H:%M:%S.%LZ',
                  date: { $toDate: '$end_date' },
                },
              },
            ],
          },
          work_flow_name: {
            $cond: [
              { $eq: [{ $size: '$run_by' }, 0] },
              'New court Notice',
              {
                $reduce: {
                  input: {
                    $map: {
                      input: '$runByDetails',
                      as: 'user',
                      in: '$$user.name',
                    },
                  },
                  initialValue: '',
                  in: {
                    $cond: [
                      { $eq: ['$$value', ''] },
                      '$$this',
                      { $concat: ['$$value', ', ', '$$this'] },
                    ],
                  },
                },
              },
            ],
          },
          task_complete: { $size: '$completedTasks' },
          status: '$status',
          updatedAt: 1, // Include updatedAt for post-processing
          assign: {
            $map: {
              input: '$assignsDetail',
              as: 'user',
              in: {
                id: { $toString: '$$user._id' },
                name: '$$user.email',
              },
            },
          },
          notes: '$notes',
          latest_task_id: 1,
        },
      },
      {
        $sort: { updatedAt: -1 }, // Sort by updatedAt instead of last_activity
      },
      {
        $facet: {
          data: [{ $skip: skip }, { $limit: limitNumber }],
          totalCount: [{ $count: 'count' }],
        },
      },
    ];

    const result = await this.workflowExecutionModel.aggregate(pipeline).exec();

    const workflows = result[0]?.data || [];
    const total = result[0]?.totalCount[0]?.count || 0;

    // Process the workflows to add relative time
    const processedWorkflows = workflows.map((workflow) => {
      return {
        ...workflow,
        last_activity: getRelativeTimeString(workflow.updatedAt),
      };
    });

    return {
      total,
      page: pageNumber,
      limit: limitNumber,
      workflows: processedWorkflows,
    };
  }

  async workFlowRender(dto: WorkflowRenderDto) {
    const { work_flow_id } = dto;
    // Validate/convert the passed ids
    if (!Types.ObjectId.isValid(work_flow_id)) {
      throw new NotFoundException('Invalid workflow execution id');
    }
    let workflowExecutionId = new Types.ObjectId(work_flow_id);

    // First, get the primary workflow execution data
    const workflowExecCheck = await this.workflowExecutionModel
      .findById(workflowExecutionId)
      .populate('template_id')
      .exec();

    if (!workflowExecCheck) {
      return { workflows: [] };
    }
    if (workflowExecCheck.parent_id) {
      workflowExecutionId = workflowExecCheck.parent_id;
    }

    const workflowExec = await this.workflowExecutionModel
      .findById(workflowExecutionId)
      .populate('template_id')
      .exec();

    // Check if this is a child workflow by looking for parent_id
    if (workflowExec.parent_id) {
      // This is a child workflow, so we need to get the parent workflow
      const parentWorkflowExec = await this.workflowExecutionModel
        .findById(workflowExec.parent_id)
        .populate('template_id')
        .exec();

      if (!parentWorkflowExec) {
        // If parent is not found, fall back to regular behavior
        return this.renderSingleWorkflow(workflowExec);
      }

      // Get the template to access tasks for parent workflow
      const parentTemplate = await this.templateModel
        .findById(parentWorkflowExec.template_id)
        .populate('task_id')
        .exec();

      if (!parentTemplate) {
        // If parent template not found, fall back to regular behavior
        return this.renderSingleWorkflow(workflowExec);
      }

      // Process parent workflow
      const parentWorkflow = await this.processWorkflow(
        parentWorkflowExec,
        parentTemplate,
        this.formatDateString.bind(this),
      );

      // Get template for child workflow (the one requested)
      const childTemplate = await this.templateModel
        .findById(workflowExec.template_id)
        .populate('task_id')
        .exec();

      if (!childTemplate) {
        // If child template not found, return just the parent
        return { workflows: [parentWorkflow] };
      }

      // Process child workflow (the one requested)
      const childWorkflow = await this.processWorkflow(
        workflowExec,
        childTemplate,
        this.formatDateString.bind(this),
      );

      // Create result structure with parent workflow and child workflow
      const result = {
        workflows: [parentWorkflow],
        child_workflow_1: [childWorkflow],
      };

      // Get options and other data as before
      const county = await this.countyModel.find({});
      const attendees = await this.attendeesModel.find({ type: 1 });
      const optionalAttendees = await this.attendeesModel.find({ type: 0 });
      const courtLocation = await this.courtLocationModel.find({});
      const courtNoticeTypes = await this.CourtNoticeTypeModel.find({});
      const email_info = await this.userMetaDataModel.find({
        workflow_execution_id: workflowExecutionId,
      });
      const courtNoticeActions = await this.courtNoticeActionModel.find({});

      // Get available assignees using the assignment service
      const availableAssignees =
        await this.assignmentService.getAvailableAssignees();

      // Transform assignees to match the expected format
      const assignInfo = availableAssignees.map((assignee) => ({
        _id: assignee.id,
        fullName: assignee.name,
        type: assignee.type,
        category: assignee.category,
        email: assignee.email,
        description: assignee.description,
      }));

      const staticOptions = {
        courtNoticeTypes: courtNoticeTypes,
        appointmentActions: appointmentActions,
        counties: county,
        courtLocations: courtLocation,
        attendees: attendees,
        optionalAttendees: optionalAttendees,
        clientAttendanceOptions: clientAttendanceOptions,
        meetingLocations: meetingLocations,
        assignInfo: assignInfo,
        courtNoticeActions,
      };

      // Get client event counts
      const { clientEventCounts, eventCountsPerTask } =
        await this.getClientEventCounts(
          parentWorkflowExec,
          [workflowExec], // Include only this child workflow
        );

      // Format notice summary
      const notice_summary = Object.entries(clientEventCounts)
        .map(([client_name, count]) => ({
          client_name,
          count: Number(count),
        }))
        .sort((a, b) => b.count - a.count);

      const formattedNoticeSummary = {};
      notice_summary.forEach((item: any) => {
        formattedNoticeSummary[item.client_name] = item.count;
      });

      return {
        ...result,
        options: staticOptions,
        email_info: email_info[0],
        notice_summary: eventCountsPerTask,
        client_not_found: !!parentWorkflowExec.client_not_found,
        advisor_message: parentWorkflowExec.client_not_found
          ? 'Client name is not there in database'
          : 'The following court notice workflow below was processed by AI. Please review.',
        advisor_color: parentWorkflowExec.client_not_found ? 'red' : 'green',
        complete_button_disabled: !!parentWorkflowExec.client_not_found,
        user_group_id: parentTemplate.templateGroupId?.toString(),
      };
    } else {
      // This is a parent workflow, use existing logic
      // Find child workflows that have this workflow as parent
      const directChildWorkflows = await this.workflowExecutionModel
        .find({ parent_id: workflowExecutionId })
        .populate('template_id')
        .exec();

      // Get the template to access tasks
      const template = await this.templateModel
        .findById(workflowExec.template_id)
        .populate('task_id')
        .exec();

      if (!template) {
        return { workflows: [] };
      }

      // Process main workflow
      const mainWorkflow = await this.processWorkflow(
        workflowExec,
        template,
        this.formatDateString.bind(this),
      );

      // Create result structure with the main workflow
      const result = {
        workflows: [mainWorkflow],
      };

      // Process direct child workflows based on parent_id relationship
      if (directChildWorkflows && directChildWorkflows.length > 0) {
        // Process each child workflow with its own index
        let childIndex = 0;
        for (const childWorkflow of directChildWorkflows) {
          const childTemplate = await this.templateModel
            .findById(childWorkflow.template_id)
            .populate('task_id')
            .exec();

          if (childTemplate) {
            const processedChildWorkflow = await this.processWorkflow(
              childWorkflow,
              childTemplate,
              this.formatDateString.bind(this),
            );
            // Add child workflow with index
            childIndex++;
            result[`child_workflow_${childIndex}`] = [processedChildWorkflow];
          }
        }
      }

      const county = await this.countyModel.find({});
      const attendees = await this.attendeesModel.find({ type: 1 });
      const optionalAttendees = await this.attendeesModel.find({ type: 0 });
      const courtLocation = await this.courtLocationModel.find({});
      const courtNoticeTypes = await this.CourtNoticeTypeModel.find({});
      const email_info = await this.userMetaDataModel.find({
        workflow_execution_id: workflowExecutionId,
      });
      const courtNoticeActions = await this.courtNoticeActionModel.find();
      const userInfo = await this.userModel.aggregate([
        {
          $project: {
            _id: 1,
            fullName: { $concat: ['$first_name', ' ', '$last_name'] },
          },
        },
      ]);
      const staticOptions = {
        courtNoticeTypes: courtNoticeTypes,
        appointmentActions: appointmentActions,
        counties: county,
        courtLocations: courtLocation,
        attendees: attendees,
        optionalAttendees: optionalAttendees,
        clientAttendanceOptions: clientAttendanceOptions,
        meetingLocations: meetingLocations,
        assignInfo: userInfo,
        courtNoticeActions,
      };

      // Define interface for client event count object
      interface ClientEventCount {
        client_name: string;
        count: number;
      }

      // Convert clientEventCounts to an array of objects and sort by count in descending order
      const { clientEventCounts, eventCountsPerTask } =
        await this.getClientEventCounts(workflowExec, directChildWorkflows);
      const notice_summary: ClientEventCount[] = Object.entries(
        clientEventCounts,
      )
        .map(([client_name, count]) => ({
          client_name,
          count: Number(count), // Ensure the count is a number
        }))
        .sort((a, b) => b.count - a.count);

      // Convert sorted array back to object format as requested: { "Jeffery Price": 9, "SA": 1 }
      const formattedNoticeSummary: { [key: string]: number } = {};
      notice_summary.forEach((item: ClientEventCount) => {
        formattedNoticeSummary[item.client_name] = item.count;
      });

      return {
        ...result,
        options: staticOptions,
        email_info: email_info[0],
        notice_summary: eventCountsPerTask,
        client_not_found: !!workflowExec.client_not_found,
        advisor_message: workflowExec.client_not_found
          ? 'Client name is not there in database'
          : 'The following court notice workflow below was processed by AI. Please review.',
        advisor_color: workflowExec.client_not_found ? 'red' : 'green',
        complete_button_disabled: !!workflowExec.client_not_found,
        user_group_id:
          template.templateGroupId?.toString() || '6877420fd4928f6a37ba1b95',
      };
    }
  }

  // Helper method to render a single workflow
  private async renderSingleWorkflow(workflowExec) {
    // Get the template to access tasks
    const template = await this.templateModel
      .findById(workflowExec.template_id)
      .populate('task_id')
      .exec();

    if (!template) {
      return { workflows: [] };
    }

    // Process workflow
    const workflow = await this.processWorkflow(
      workflowExec,
      template,
      this.formatDateString.bind(this),
    );

    // Create result structure
    const result = {
      workflows: [workflow],
    };

    const workflowExecutionId = workflowExec._id;

    const county = await this.countyModel.find({});
    const attendees = await this.attendeesModel.find({ type: 1 });
    const optionalAttendees = await this.attendeesModel.find({ type: 0 });
    const courtLocation = await this.courtLocationModel.find({});
    const courtNoticeTypes = await this.CourtNoticeTypeModel.find({});
    const email_info = await this.userMetaDataModel.find({
      workflow_execution_id: workflowExecutionId,
    });
    const courtNoticeAction = await this.courtNoticeActionModel.find({});
    const userInfo = await this.userModel.aggregate([
      {
        $project: {
          _id: 1,
          fullName: { $concat: ['$first_name', ' ', '$last_name'] },
        },
      },
    ]);
    const staticOptions = {
      courtNoticeTypes: courtNoticeTypes,
      appointmentActions: appointmentActions,
      counties: county,
      courtLocations: courtLocation,
      attendees: attendees,
      optionalAttendees: optionalAttendees,
      clientAttendanceOptions: clientAttendanceOptions,
      meetingLocations: meetingLocations,
      assignInfo: userInfo,
      courtNoticeAction,
    };

    return {
      ...result,
      options: staticOptions,
      email_info: email_info[0],
      notice_summary: {},
    };
  }

  // Helper method to format date consistently
  private formatDateString(dateString: string): string {
    if (!dateString) return '';
    const date = new Date(dateString);

    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const year = String(date.getFullYear()).slice(-2);

    let hours = date.getHours();
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';

    hours = hours % 12;
    hours = hours ? hours : 12; // Convert 0 to 12
    const formattedHours = String(hours).padStart(2, '0');

    return `${month}/${day}/${year} ${formattedHours}:${minutes} ${ampm}`;
  }
  // Helper method to process a workflow and its tasks
  private async processWorkflow(workflowExec, template, formatDateFn) {
    // Create workflow object
    const workflowObj = {
      id: template._id,
      name: await this.getRunByNames(workflowExec._id),
      description: template.description,
      workflow_status: workflowExec.workflow_status,
      is_archive: workflowExec.is_archive,
      archive_at: getRelativeTimeString(workflowExec.archive_at),
      archive_by: workflowExec.archive_by,
      end_date: formatDateFn(workflowExec.end_date),
      tasks: [],
      workflow_execution_id: workflowExec._id, // Add workflow execution ID for better tracing
    };

    // Get all tasks for this template
    const tasks = await this.taskModel
      .find({
        _id: { $in: template.task_id },
      })
      .exec();

    tasks.sort((a, b) => {
      if (a.default_task === b.default_task) return 0;
      return a.default_task ? -1 : 1;
    });

    // Create object to track client event counts for notice_summary
    const clientEventCounts = {};

    // For each task, get its execution data
    for (const task of tasks) {
      // Get task execution for THIS workflow specifically (added workflow_execution_id to the query)
      const taskExecution = await this.taskExecutionModel
        .findOne({
          workflow_execution_id: workflowExec._id,
          task_id: task._id,
          is_deleted: false,
        })
        .exec();

      // Skip this task if we don't have a task execution for this specific workflow
      if (!taskExecution) {
        continue;
      }

      // Create task object
      const taskObj = {
        _id: task._id,
        id: task._id,
        is_reviewed: taskExecution?.is_reviewed,
        task_visible_status: taskExecution?.task_visible_status,
        name: task.name,
        description: task.description,
        icon: task.icon,
        end_date: formatDateFn(taskExecution.end_date),
        default_task: task?.default_task,
        selected_task: task?.selected_task,
        condition_task: task?.condition_task,
        work_flow_id: workflowExec._id,
        group_id: taskExecution?.user_group_id,
        status: taskExecution ? taskExecution.task_status : null,
        formFields: [],
      };

      // Store regular form fields and derived fields separately
      const regularFormFields = [];
      const derivedFields = [];

      // Process form fields if task execution exists
      if (
        taskExecution &&
        taskExecution.formField &&
        taskExecution.formField.length > 0
      ) {
        // Get all form component IDs to fetch in one query
        const componentIds = [];
        for (const formField of taskExecution.formField) {
          for (const field of formField.fields) {
            componentIds.push(field.form_component_id);
          }
        }

        // Get all form components in one query
        const formComponents = await this.formComponentModel
          .find({
            _id: { $in: componentIds },
          })
          .exec();

        const componentsMap = new Map();
        formComponents.forEach((comp) => {
          componentsMap.set(comp._id.toString(), comp);
        });

        // Process form fields in their original order
        for (const formField of taskExecution.formField) {
          const formFieldObj = {
            id: formField.id,
            type: formField.type,
            label: formField.label,
            form_field_id: formField.form_field_id,
            api_end_point: formField.api_end_point,
            status: taskExecution.task_status,
            sequence: formField.sequence,
            required: formField.required,
            dynamic_fields: formField?.dynamic_fields || false,
            dynamic_selected_task: formField?.dynamic_selected_task || false,
            condition: this.processCondition(formField.condition),
            fields: [],
          };

          // Process fields in their original order
          if (formField.fields && formField.fields.length > 0) {
            for (const field of formField.fields) {
              if (field.form_component_id) {
                const componentId = field?.form_component_id.toString();
                const component = componentsMap.get(componentId);
                if (component) {
                  formFieldObj.fields.push({
                    _id: field.form_component_id,
                    id: field.form_component_id,
                    value: field.value || [],
                    name: component.name,
                    label: component.label,
                    type: component.type,
                    placeholder: component.placeholder || '',
                    api_end_point: component.api_end_point || '',
                    form_field_id: component.form_field_id,
                    // fo
                    required:
                      component.mandatory_field !== undefined
                        ? component.mandatory_field
                        : component.required || false,
                    validation: component.validation || {},
                    options: component.options || [],
                  });
                }
              }
            }
          }

          regularFormFields.push(formFieldObj);
        }

        // Add derived fields from task execution
        if (
          taskExecution.derived_field &&
          taskExecution.derived_field.length > 0
        ) {
          for (const derivedField of taskExecution.derived_field) {
            // Process event data to group by matter._id if this is a court notice type
            if (derivedField.type === 'courtNotice' && derivedField.client) {
              // Create a new structure for events grouped by matter._id
              const eventsByMatterId = {};

              // Iterate through clients and their matters to collect events
              if (Array.isArray(derivedField.client)) {
                derivedField.client.forEach((client) => {
                  if (client.matter && Array.isArray(client.matter)) {
                    client.matter.forEach((matter) => {
                      if (matter.event && Array.isArray(matter.event)) {
                        // Count events for each client for notice_summary
                        const clientName =
                          client.client_name || 'Unknown Client';
                        if (!clientEventCounts[clientName]) {
                          clientEventCounts[clientName] = 0;
                        }
                        clientEventCounts[clientName] += matter.event.length;

                        // Initialize array for this matter if not exists
                        // Use client name + matter id as a unique key to ensure proper mapping
                        const matterKey = `${client.client_name}:${matter._id}`;
                        if (!eventsByMatterId[matterKey]) {
                          eventsByMatterId[matterKey] = {
                            event_items: matter.event,
                            client_name: client.client_name,
                            matter_name: matter.name,
                            matter_id: matter._id,
                          };
                        } else {
                          // Add all events for this matter to the array
                          eventsByMatterId[matterKey].event_items = [
                            ...eventsByMatterId[matterKey].event_items,
                            ...matter.event,
                          ];
                        }
                      }
                    });
                  }
                });
              }

              // Here's the transformation for the courtNotice field:
              // First, find the court notice field or create it
              const courtNoticeField = {
                id: derivedField.id,
                _id: derivedField.id,
                type: derivedField.type,
                label: derivedField.label,
                sequence: derivedField.sequence || 999,
                matter_list_api: derivedField?.matter_list_api,
                condition: this.processCondition(derivedField.condition),
                value: [],
                fields: [],
              };

              // Create the transformedData structure
              const transformedData = {
                clients: [],
                events: {},
              };
              const value = [];
              value.push({ value: transformedData });

              // Process clients data
              if (Array.isArray(derivedField.client)) {
                derivedField.client.forEach((client, clientIndex) => {
                  const clientEntry = {
                    id: `client-${clientIndex + 1}`,
                    name: client.client_name,
                    matters: [],
                  };

                  // Process matters for each client
                  if (client.matter && Array.isArray(client.matter)) {
                    client.matter.forEach((matter, matterIndex) => {
                      const matterEntry = {
                        id: `matter-${clientIndex + 1}-${matterIndex + 1}`,
                        name: `${client.client_name} (${matter.name})`,
                        caseDescription: `${matter.name}`,
                        ex_county_of_arrest: `${matter.ex_county_of_arrest}`,
                        case_number: `${matter.case_number}`,
                        matter_id: client.client_matter_id
                          ? client.client_matter_id.toString()
                          : matter._id,
                      };

                      clientEntry.matters.push(matterEntry);

                      // Prepare events structure for this matter
                      transformedData.events[matterEntry.id] = [];

                      // Add events for this matter if available
                      // Use the same composite key as when we built the eventsByMatterId
                      const matterKey = `${client.client_name}:${matter._id}`;
                      if (
                        eventsByMatterId[matterKey] &&
                        eventsByMatterId[matterKey].event_items
                      ) {
                        eventsByMatterId[matterKey].event_items.forEach(
                          (event) => {
                            const eventEntry = {
                              id: event.id,
                              _id: event._id,
                              caseNumber: event.caseNumber,
                              clientName:
                                event.clientName ||
                                `${client.client_name} | ${client.client_name} (${matter.name}) SA`,
                              description: event.description,
                              subject: event.subject,
                              courtNoticeType: event.courtNoticeType,
                              courtNoticeActions:
                                event.courtNoticeActions || 'status_conference',
                              appointmentAction: event.appointmentAction
                                ? event.appointmentAction
                                : EventStatus.NEW,
                              eventStatus: event.eventStatus,
                              appointmentToReschedule:
                                event.appointmentToReschedule,
                              court_notice_date: event.court_notice_date,
                              charge: event.charge,
                              county: event.county,
                              courtLocation: event.courtLocation,
                              startDate: event.startDate,
                              endDate: event.endDate,
                              date: event.date,
                              startTime: event.startTime,
                              endTime: event.endTime,
                              allDay: event.allDay || false,
                              requiredAttendees: event.requiredAttendees,
                              optionalAttendees: event.optionalAttendees || '',
                              clientAttendance: event.clientAttendance,
                              meetingLocation: event.meetingLocation,
                              phoneDetails: event.phoneDetails,
                              meetingAddress: event.meetingAddress,
                              meetingLink: event.meetingLink,
                              isCompleted: event.isCompleted,
                              files: event.files,
                              my_case_event_id: event?.my_case_event_id,
                              isAddSecondary: event?.isAddSecondary,
                              client_matter_id: client.client_matter_id
                                ? client.client_matter_id.toString()
                                : matter._id,
                            };

                            transformedData.events[matterEntry.id].push(
                              eventEntry,
                            );
                          },
                        );
                      }
                    });
                  }

                  transformedData.clients.push(clientEntry);
                });
              }

              // Add the transformed data to the court notice field
              if (!courtNoticeField.fields) {
                courtNoticeField.fields = [];
              }
              courtNoticeField.fields.push({
                id: '',
                type: derivedField.type,
                label: derivedField.label,
                sequence: derivedField.sequence,
                matter_list_api:
                  '${process.env.NEXT_PUBLIC_BASE_URL}/workflow/matter-list}',

                value: value,
              });

              derivedFields.push(courtNoticeField);
            } else {
              // For non-courtNotice types, push as regular object
              derivedFields.push({
                ...derivedField,
                sequence: derivedField.sequence || 999,
                condition: this.processCondition(derivedField.condition),
              });
            }
          }
        }
      }
      // If task has derived fields but not added from taskExecution
      else if (task.derived_field && task.derived_field.length > 0) {
        // Process task's derived fields if no task execution exists
        for (const derivedField of task.derived_field) {
          // Process event data to group by matter._id if this is a court notice type
          if (derivedField.type === 'courtNotice' && derivedField.client) {
            // Create a new structure for events grouped by matter._id
            const eventsByMatterId = {};

            // Iterate through clients and their matters to collect events
            if (Array.isArray(derivedField.client)) {
              derivedField.client.forEach((client) => {
                if (client.matter && Array.isArray(client.matter)) {
                  client.matter.forEach((matter) => {
                    if (matter.event && Array.isArray(matter.event)) {
                      // Count events for each client for notice_summary
                      const clientName = client.client_name || 'Unknown Client';
                      if (!clientEventCounts[clientName]) {
                        clientEventCounts[clientName] = 0;
                      }
                      clientEventCounts[clientName] += matter.event.length;

                      // Initialize array for this matter if not exists
                      // Use client name + matter id as a unique key to ensure proper mapping
                      const matterKey = `${client.client_name}:${matter._id}`;
                      if (!eventsByMatterId[matterKey]) {
                        eventsByMatterId[matterKey] = {
                          event_items: matter.event,
                          client_name: client.client_name,
                          matter_name: matter.name,
                          matter_id: matter._id,
                        };
                      } else {
                        // Add all events for this matter to the array
                        eventsByMatterId[matterKey].event_items = [
                          ...eventsByMatterId[matterKey].event_items,
                          ...matter.event,
                        ];
                      }
                    }
                  });
                }
              });
            }

            // Here's the transformation for the courtNotice field from task:
            // First, create the court notice field
            const courtNoticeField = {
              id: derivedField.id,
              _id: derivedField.id,
              type: derivedField.type,
              label: derivedField.label,
              sequence: derivedField.sequence || 999,
              condition: this.processCondition(derivedField.condition),
              value: [],
              fields: [],
            };
            const fields = [];

            // Create the transformedData structure
            const transformedData = {
              clients: [],
              events: {},
            };

            // Add the transformed data to the court notice field
            courtNoticeField.value.push({
              id: derivedField.id,
              _id: derivedField.id,
              type: derivedField.type,
              label: derivedField.label,
              sequence: derivedField.sequence || 999,
              value: transformedData,
            });

            // Process clients data
            if (Array.isArray(derivedField.client)) {
              derivedField.client.forEach((client, clientIndex) => {
                const clientEntry = {
                  id: `client-${clientIndex + 1}`,
                  name: client.client_name,
                  matters: [],
                };

                // Process matters for each client
                if (client.matter && Array.isArray(client.matter)) {
                  client.matter.forEach((matter, matterIndex) => {
                    const matterEntry = {
                      id: `matter-${clientIndex + 1}-${matterIndex + 1}`,
                      name: `${client.client_name} (${matter.name}) SA`,
                      caseDescription: `${matter.name}`,
                      ex_county_of_arrest: `${matter.ex_county_of_arrest}`,
                      case_number: `${matter.case_number}`,
                      matter_id: client.client_matter_id
                        ? client.client_matter_id.toString()
                        : matter._id,
                    };

                    clientEntry.matters.push(matterEntry);

                    // Prepare events structure for this matter
                    transformedData.events[matterEntry.id] = [];

                    // Add events for this matter if available
                    // Use the same composite key as when we built the eventsByMatterId
                    const matterKey = `${client.client_name}:${matter._id}`;
                    if (
                      eventsByMatterId[matterKey] &&
                      eventsByMatterId[matterKey].event_items
                    ) {
                      eventsByMatterId[matterKey].event_items.forEach(
                        (event) => {
                          const eventEntry = {
                            id: event.id,
                            _id: event._id,
                            caseNumber: event.caseNumber,
                            clientName:
                              event.clientName ||
                              `${client.client_name} | ${client.client_name} (${matter.name}) SA`,
                            description: event.description,
                            subject: event.subject,
                            appointmentToReschedule:
                              event.appointmentToReschedule,
                            courtNoticeType: event.courtNoticeType,
                            courtNoticeActions:
                              event.courtNoticeActions || 'status_conference',
                            appointmentAction: event.appointmentAction
                              ? event.appointmentAction
                              : EventStatus.NEW,
                            eventStatus: event.eventStatus,
                            charge: event.charge,
                            county: event.county,
                            courtLocation: event.courtLocation,
                            meetingLink: event.meetingLink,
                            startDate: event.startDate,
                            endDate: event.endDate,
                            date: event.date,
                            startTime: event.startTime,
                            endTime: event.endTime,
                            allDay: event.allDay || false,
                            requiredAttendees: event.requiredAttendees,
                            optionalAttendees: event.optionalAttendees || '',
                            clientAttendance: event.clientAttendance,
                            meetingLocation: event.meetingLocation,

                            isCompleted: event.isCompleted,
                            files: event.files,
                          };

                          transformedData.events[matterEntry.id].push(
                            eventEntry,
                          );
                        },
                      );
                    }
                  });
                }

                transformedData.clients.push(clientEntry);
              });
            }

            derivedFields.push(courtNoticeField);
            fields.push(courtNoticeField);
          } else {
            derivedFields.push({
              ...derivedField,
              sequence: derivedField.sequence || 999,
              condition: this.processCondition(derivedField.condition),
            });
          }
        }
      }

      // Combine regular form fields and derived fields
      const allFields = [...regularFormFields, ...derivedFields];

      // Sort all fields by sequence
      allFields.sort((a, b) => {
        const seqA = a.sequence || 999;
        const seqB = b.sequence || 999;
        return seqA - seqB;
      });

      // Add all fields to task object (with transformations applied)
      taskObj.formFields = allFields;

      workflowObj.tasks.push(taskObj);
    }

    return workflowObj;
  }

  private async getRunByNames(workflowExecId) {
    try {
      const pipeline: any[] = [
        {
          $match: {
            _id: new Types.ObjectId(workflowExecId),
          },
        },
        {
          $lookup: {
            from: 'client',
            localField: 'run_by',
            foreignField: '_id',
            as: 'runByDetails',
          },
        },
        {
          $project: {
            id: { $toString: '$_id' },

            work_flow_name: {
              $cond: [
                { $eq: [{ $size: '$run_by' }, 0] },
                'New court Notice',
                {
                  $reduce: {
                    input: {
                      $map: {
                        input: '$runByDetails',
                        as: 'user',
                        in: '$$user.name',
                      },
                    },
                    initialValue: '',
                    in: {
                      $cond: [
                        { $eq: ['$$value', ''] },
                        '$$this',
                        { $concat: ['$$value', ', ', '$$this'] },
                      ],
                    },
                  },
                },
              ],
            },
          },
        },
      ];

      const result = await this.workflowExecutionModel
        .aggregate(pipeline)
        .exec();
      return result[0]?.work_flow_name || 'New court Notice';
    } catch (error) {
      throw error;
    }
  }

  // Helper method to collect all client event counts from main and child workflows
  private async getClientEventCounts(mainWorkflow, childWorkflows) {
    const clientEventCounts = {};
    const eventCountsPerTask = {}; // { taskId: { clientName: count } }

    // Function to process a workflow and collect event counts
    const processWorkflowForCounts = async (workflow) => {
      const template = await this.templateModel
        .findById(workflow.template_id)
        .populate('task_id')
        .exec();

      if (!template) return;

      // Get all tasks for this template
      const tasks = await this.taskModel
        .find({
          _id: { $in: template.task_id },
        })
        .exec();

      // For each task, get its execution data
      for (const task of tasks) {
        const taskExecution = await this.taskExecutionModel
          .findOne({
            workflow_execution_id: workflow._id,
            task_id: task._id,
          })
          .exec();

        if (
          taskExecution &&
          taskExecution.derived_field &&
          taskExecution.derived_field.length > 0
        ) {
          for (const derivedField of taskExecution.derived_field) {
            if (
              derivedField.type === 'courtNotice' &&
              Array.isArray(derivedField.client)
            ) {
              for (const client of derivedField.client) {
                // Extract just the client name part (before the pipe) for notice summary
                const fullClientName = client.client_name || 'Unknown Client';
                const clientName = fullClientName.includes('|')
                  ? fullClientName.split('|')[0].trim()
                  : fullClientName;

                if (Array.isArray(client.matter)) {
                  for (const matter of client.matter) {
                    if (Array.isArray(matter.event)) {
                      // Initialize nested structure
                      if (!eventCountsPerTask[task._id.toString()]) {
                        eventCountsPerTask[task._id.toString()] = {};
                      }
                      if (
                        !eventCountsPerTask[task._id.toString()][clientName]
                      ) {
                        eventCountsPerTask[task._id.toString()][clientName] = 0;
                      }

                      // Add event count
                      eventCountsPerTask[task._id.toString()][clientName] +=
                        matter.event.length;
                    }
                  }
                }
              }
            }
          }
        }
      }
    };

    // Process main workflow
    await processWorkflowForCounts(mainWorkflow);

    // Process child workflows
    if (childWorkflows && childWorkflows.length > 0) {
      for (const childWorkflow of childWorkflows) {
        await processWorkflowForCounts(childWorkflow);
      }
    }

    return { clientEventCounts, eventCountsPerTask };
  }
  // Helper method to extract county from courtLocation
  private extractCounty(courtLocation: string): string {
    if (!courtLocation) return '';

    // Simple check based on the location string
    if (courtLocation.toLowerCase().includes('lubbock')) return 'lubbock';
    if (courtLocation.toLowerCase().includes('dallas')) return 'dallas';
    if (courtLocation.toLowerCase().includes('houston')) return 'houston';

    // Default to empty string if can't determine
    return '';
  }

  // Helper method to determine client attendance
  private determineClientAttendance(event): string {
    if (!event) return 'appearance_req'; // Default

    // If virtual keyword is present in the meeting location
    if (
      event.meetingLocation &&
      (event.meetingLocation === 'virtual_meeting_link' ||
        event.meetingLocation.toLowerCase().includes('virtual'))
    ) {
      return 'virtual';
    }

    // For in-person meetings
    if (
      event.meetingLocation &&
      (event.meetingLocation === 'in_person' ||
        event.meetingLocation.toLowerCase().includes('person'))
    ) {
      return 'appearance_req';
    }

    // If specifically marked as not required
    if (event.clientAttendance && event.clientAttendance === 'not_required') {
      return 'not_required';
    }

    // Default to appearance required
    return 'appearance_req';
  }

  private processCondition(condition) {
    if (!condition) return null;

    // Handle complex AND/OR condition
    if (condition.type === 'AND' || condition.type === 'OR') {
      const conditionObj = {
        type: condition.type,
        conditions: [],
      };

      // Process conditions array if it exists
      if (Array.isArray(condition.conditions)) {
        conditionObj.conditions = condition.conditions.map((cond) => {
          return {
            field: cond.field,
            value:
              typeof cond.value === 'boolean'
                ? cond.value.toString()
                : cond.value,
            bool_value:
              typeof cond.value === 'boolean' ? cond.value : undefined,
          };
        });
      }

      return conditionObj;
    }
    // Handle simple field/value condition
    else if (condition.field) {
      const condValue = condition.value;
      return {
        field: condition.field,
        value:
          typeof condValue === 'boolean' ? condValue.toString() : condValue,
        bool_value: typeof condValue === 'boolean' ? condValue : undefined,
      };
    }
    // Handle unexpected/empty conditions
    else if (
      typeof condition === 'object' &&
      Object.keys(condition).length === 0
    ) {
      return null;
    }

    return condition;
  }

  async handleOAuthCallback() {
    this.logger.log('Handling OAuth callback');
    try {
      // Your OAuth callback handling logic here
      return {
        result: `https://${process.env.GRPC_URL}/mycase/oauth/callback`,
      };
    } catch (error) {
      this.logger.error(`Error handling OAuth callback: ${error.message}`);
      throw error;
    }
  }

  async updateWorkflow(updateWorkflowDto: UpdateWorkflowDto) {
    const { workflow_id, task_id, forms, is_completed, event } =
      updateWorkflowDto;

    try {
      const taskExecution = await this.taskExecutionModel.findOne({
        workflow_execution_id: workflow_id,
        task_id,
      });

      await this.updateWorkflowExecutionStatusByExecutionId(workflow_id);

      if (!taskExecution) {
        throw ErrorExceptions.TASK_NOT_FOUND();
      }

      // Debug log the initial state
      this.logger.log(
        `Initial formField state: ${JSON.stringify(taskExecution.formField)}`,
      );

      let dynamicComponentId: string | null = null;
      let dynamicValue: string | null = null;

      const deleteFiles =
        updateWorkflowDto?.event?.updateData?.deleteFiles || [];

      await Promise.all(
        deleteFiles.map(async (file) => {
          if (file?.key) {
            await this.s3Service.deleteFile(file.key + file.name);
          }
        }),
      );

      // Store renamed file mappings for database updates
      const renamedFileMap = new Map<
        string,
        { newKey: string; newName: string }
      >();

      if (updateWorkflowDto?.event && updateWorkflowDto.event.updateData) {
        // Loop through each file object and rename files in S3
        await Promise.all(
          updateWorkflowDto?.event?.updateData?.renameFiles.map(
            async (fileObj) => {
              if (fileObj?.key && fileObj?.new_fileName) {
                console.log(
                  fileObj,
                  `Renaming S3 key: `,
                  'event/court-notice/' +
                    fileObj.new_fileName.split('.pdf')[0] +
                    fileObj.uniqueId +
                    '.pdf',
                );

                const newKey =
                  `event/court-notice/` +
                  fileObj.new_fileName.split('.pdf')[0] +
                  ' ' +
                  fileObj.uniqueId +
                  '.pdf';

                await this.s3Service.renameFile(fileObj.key, newKey);

                // Store the old key -> new key and new name mapping for database updates
                renamedFileMap.set(fileObj.key, {
                  newKey: newKey,
                  newName: fileObj.new_fileName,
                });

                this.logger.log(
                  `Successfully renamed S3 file from ${fileObj.key} to ${newKey} with new name ${fileObj.new_fileName}`,
                );
              } else {
                console.warn(
                  'Missing required properties (key or new_fileName) for file:',
                  fileObj,
                );
              }
            },
          ),
        );

        // Update database entries with new S3 keys and names after successful rename
        if (renamedFileMap.size > 0) {
          await this.updateDatabaseWithRenamedFiles(
            taskExecution,
            renamedFileMap,
            workflow_id,
          );
        }
      }

      // Process form updates
      if (forms && forms.length > 0) {
        for (const incomingForm of forms) {
          const { form_component_id, value } = incomingForm;

          for (const section of taskExecution.formField || []) {
            if (
              section.condition &&
              'type' in section.condition &&
              (!('conditions' in section.condition) ||
                (section.condition as any).conditions.length === 0)
            ) {
              (section.condition as any).conditions = [
                { field: 'default', value: true },
              ];
            }

            // Initialize fields array if it doesn't exist
            if (!section.fields) {
              section.fields = [];
            }

            for (const field of section.fields) {
              if (field.form_component_id.toString() === form_component_id) {
                // Debug log before update
                this.logger.log(
                  `Updating field value. Current value: ${JSON.stringify(field.value)}`,
                );

                // Initialize empty array if value is undefined or null
                if (!field.value) {
                  field.value = [];
                }

                try {
                  // Ensure value is always an array of properly structured objects
                  field.value = Array.isArray(value)
                    ? value.map((v) => ({
                        id: v.id ?? null,
                        value:
                          v.value !== undefined && v.value !== null
                            ? String(v.value)
                            : null,
                      }))
                    : [];

                  // Debug log after update
                  this.logger.log(
                    `Updated field value: ${JSON.stringify(field.value)}`,
                  );
                } catch (err) {
                  this.logger.error(
                    `Error updating field value: ${err.message}`,
                  );
                  throw err;
                }

                if (section.dynamic_selected_task === true) {
                  dynamicComponentId = form_component_id;
                  dynamicValue = value?.[0]?.value || '';
                }
              }
            }
          }
        }
      }

      // Ensure all form fields have proper value structure
      if (taskExecution.formField) {
        for (const section of taskExecution.formField) {
          if (section.fields) {
            for (const field of section.fields) {
              if (!Array.isArray(field.value)) {
                field.value = [];
              }
              // Ensure each value in the array is a proper object
              field.value = field.value.map((val: any) => {
                if (typeof val === 'string' || !val) {
                  return { id: null, value: val || null };
                }
                return {
                  id: val.id ?? null,
                  value:
                    val.value !== undefined && val.value !== null
                      ? String(val.value)
                      : null,
                };
              });
            }
          }
        }
      }

      // Debug log the final state before saving
      this.logger.log(
        `Final formField state before save: ${JSON.stringify(taskExecution.formField)}`,
      );

      // Process court notice event updates if provided
      if (event) {
        const {
          derivedFieldId,
          clientIdentifier,
          clientMatterId,
          matterId,
          eventId,
          createEvent,
          deleteEvent,
          updateData,
          newClient,
        } = event;
        this.logger.log(`Processing event update: ${JSON.stringify(event)}`);

        try {
          // Find the derived field with the specified ID
          const derivedField = taskExecution.derived_field.find(
            (df) => df.id === derivedFieldId,
          );
          console.log(
            '🚀 ~ WorkflowService ~ updateWorkflow ~ derivedField:',
            derivedField,
          );

          if (!derivedField) {
            this.logger.error(`Derived field not found: ${derivedFieldId}`);
            return {
              success: false,
              error: `Derived field not found: ${derivedFieldId}`,
            };
          }

          if (derivedField.type !== 'courtNotice') {
            this.logger.error(
              `Derived field is not a courtNotice type: ${derivedField.type}`,
            );
            return {
              success: false,
              error: `Derived field is not a courtNotice type: ${derivedField.type}`,
            };
          }

          // Handle reschedule/cancel logic for appointmentToReschedule events
          if (
            updateData?.appointmentAction === EventAction.RESCHEDULE ||
            updateData?.appointmentAction === EventAction.CANCLE
          ) {
            const appointmentToReschedule = updateData.appointmentToReschedule;

            if (appointmentToReschedule) {
              this.logger.log(
                `Logging ${updateData.appointmentAction} action for appointment: ${appointmentToReschedule}`,
              );

              const mycaseRecord = await this.mycaseClientMatterModel.findOne({
                event_id: appointmentToReschedule,
              });

              if (mycaseRecord) {
                await this.mycaseClientMatterModel.updateMany(
                  { event_id: appointmentToReschedule },
                  {
                    $set: {
                      eventStatus: EventStatus.PENDING,
                      action:
                        updateData?.appointmentAction == EventAction.CANCLE
                          ? EventAction.CANCLE
                          : EventAction.RESET,
                    },
                    $addToSet: { update_event_id: workflow_id },
                  },
                );

                const existingHistory =
                  await this.eventActionHistoryModel.findOne({
                    event_id: appointmentToReschedule,
                    action:
                      updateData?.appointmentAction == EventAction.CANCLE
                        ? EventAction.CANCLE
                        : EventAction.RESET,
                    workflow_execution_id: workflow_id,
                  });

                if (!existingHistory) {
                  // const eventActionHistoryRecord =
                  //   await this.eventActionHistoryModel.create({
                  //     client_matter_id: mycaseRecord.client_matter_id,
                  //     eventStatus: EventStatus.PENDING,
                  //     event_id: appointmentToReschedule,
                  //     my_case_event_id: mycaseRecord.my_case_event_id,
                  //     workflow_execution_id: workflow_id,
                  //     action:
                  //       updateData?.appointmentAction === EventAction.RESCHEDULE
                  //         ? EventAction.RESET
                  //         : updateData?.appointmentAction,
                  //   });
                  // this.logger.log(
                  //   `Created new event history record for ${appointmentToReschedule} with ID: ${eventActionHistoryRecord._id}`,
                  // );
                  // for (const client of derivedField.client) {
                  //   for (const matter of client.matter) {
                  //     for (const event of matter.event) {
                  //       if (event.id === appointmentToReschedule) {
                  //         event.eventActionHistoryModelId =
                  //           eventActionHistoryRecord._id.toString();
                  //         break;
                  //       }
                  //     }
                  //   }
                  // }
                } else {
                  this.logger.log(
                    `Using existing event history record for ${appointmentToReschedule} with ID: ${existingHistory._id}`,
                  );
                }
              } else {
                this.logger.warn(
                  `Event to ${updateData.appointmentAction} not found in MycaseClientMatter: ${appointmentToReschedule}`,
                );

                if (updateData.appointmentAction === 'Cancel') {
                  return {
                    success: false,
                    error: `Event to cancel not found: ${appointmentToReschedule}`,
                  };
                }
              }
            }
          }

          if (updateData?.appointmentAction == '') {
            updateData.appointmentAction = 'New';
          }

          // Handle matter deletion if deleteMatter is true and clientMatterId is provided
          if (
            updateWorkflowDto.deleteMatter === true &&
            updateWorkflowDto.clientMatterId
          ) {
            const { clientMatterId } = updateWorkflowDto;
            this.logger.log(
              `Attempting to delete matter with client_matter_id: ${clientMatterId}`,
            );

            // Find the client with the specified client_matter_id
            const clientIndex = derivedField.client.findIndex(
              (c) =>
                c.client_matter_id &&
                c.client_matter_id.toString() === clientMatterId,
            );

            if (clientIndex === -1) {
              this.logger.error(
                `Client not found with client_matter_id: ${clientMatterId}`,
              );
              return {
                success: false,
                error: `Client not found with client_matter_id: ${clientMatterId}`,
              };
            }

            try {
              // First find all events associated with this matter
              const matterEvents = await this.mycaseClientMatterModel.find({
                client_matter_id: new Types.ObjectId(clientMatterId),
                workflow_execution_id: new Types.ObjectId(workflow_id),
              });

              this.logger.log(
                `Found ${matterEvents.length} events associated with matter ${clientMatterId}`,
              );

              // Collect all eventActionHistoryModelIds from events
              const eventActionHistoryIds = matterEvents
                .filter(
                  (record) =>
                    record.event && record.event.eventActionHistoryModelId,
                )
                .map(
                  (record) =>
                    new Types.ObjectId(record.event.eventActionHistoryModelId),
                );

              if (eventActionHistoryIds.length > 0) {
                // Delete all associated event history records
                const deleteResult =
                  await this.eventActionHistoryModel.deleteMany({
                    _id: { $in: eventActionHistoryIds },
                  });

                this.logger.log(
                  `Deleted ${deleteResult.deletedCount} event history records for matter ${clientMatterId}`,
                );
              }

              // Remove the client from the array
              derivedField.client.splice(clientIndex, 1);
              this.logger.log(
                `Client with client_matter_id ${clientMatterId} deleted successfully`,
              );

              // Delete all associated records from mycaseClientMatterModel
              const deleteResult =
                await this.mycaseClientMatterModel.deleteMany({
                  client_matter_id: new Types.ObjectId(clientMatterId),
                  workflow_execution_id: new Types.ObjectId(workflow_id),
                });

              this.logger.log(
                `Deleted ${deleteResult.deletedCount} records from mycaseClientMatterModel for matter ${clientMatterId}`,
              );

              // Save changes to database
              await taskExecution.save();
              return { success: true };
            } catch (error) {
              this.logger.error(
                `Error while deleting matter and associated records: ${error.message}`,
              );
              throw error;
            }
          }

          let clientIndex = -1;

          // Check if we have a client with the same client_matter_id
          if (clientMatterId) {
            clientIndex = derivedField.client.findIndex(
              (c) =>
                c.client_matter_id &&
                c.client_matter_id.toString() === clientMatterId,
            );
            this.logger.log(
              `Looking for client with client_matter_id: ${clientMatterId}, found at index: ${clientIndex}`,
            );
            this.logger.log(
              `Current client array length: ${derivedField.client.length}`,
            );
          }

          const matterDetails = await this.matterModel.findById({
            _id: clientMatterId,
          });
          console.log('🚀 ~ updateWorkflow ~ matterDetails:', matterDetails);

          if (!matterDetails) {
            this.logger.error(`Matter not found with ID: ${clientMatterId}`);
            return {
              success: false,
              error: `Matter not found with ID: ${clientMatterId}`,
            };
          }

          await this.workflowExecutionModel.updateOne(
            { _id: workflow_id },
            {
              $addToSet: {
                run_by: { $each: [matterDetails.client_id] },
              },
            },
          );

          // Handle client creation or identification
          if (clientIndex === -1) {
            // No matching client found, create new client if newClient is true OR if client array is empty
            if (newClient === true || derivedField.client.length === 0) {
              this.logger.log(
                `Creating new client: ${clientIdentifier} with matter ID: ${clientMatterId} (newClient: ${newClient}, client array empty: ${derivedField.client.length === 0})`,
              );

              // Create a new client object with the provided client_matter_id
              const newClientObj = {
                client_name: clientIdentifier,
                client_matter_id: clientMatterId
                  ? new Types.ObjectId(clientMatterId)
                  : undefined,
                matter: [],
              };

              derivedField.client.push(newClientObj);
              clientIndex = derivedField.client.length - 1;
              this.logger.log(`New client created at index ${clientIndex}`);
            } else {
              // No matching client and not instructed to create new client
              this.logger.error(
                `Client not found with client_matter_id: ${clientMatterId}`,
              );
              this.logger.log(
                `Available clients: ${derivedField.client
                  .map((c) => ({
                    name: c.client_name,
                    id: c.client_matter_id?.toString(),
                  }))
                  .join(', ')}`,
              );
              return {
                success: false,
                error: `Client not found with client_matter_id: ${clientMatterId}`,
              };
            }
          } else {
            this.logger.log(
              `Using existing client with client_matter_id: ${clientMatterId} at index: ${clientIndex}`,
            );
          }

          // Find the matter - using either exact ID match or the matter-1-1 format
          this.logger.log(`Looking for matterId: ${matterId}`);

          let matterIndex = -1;
          let realMatterId = matterId;

          // First try direct ID match (mtr-001 format)
          matterIndex = derivedField.client[clientIndex].matter.findIndex(
            (m) => m._id === matterId,
          );

          // If not found, try to match by matter_id which is the actual MongoDB _id
          if (matterIndex === -1) {
            this.logger.log(`Trying to find matter by actual MongoDB _id`);
            matterIndex = derivedField.client[clientIndex].matter.findIndex(
              (m) => m.matter_id === matterId,
            );
            if (matterIndex !== -1) {
              this.logger.log(`Found matter by matter_id: ${matterId}`);
              realMatterId =
                derivedField.client[clientIndex].matter[matterIndex]._id;
            }
          }

          // If not found, try to match by matter name
          if (matterIndex === -1) {
            this.logger.log(`Trying to find matter by name: ${matterId}`);
            matterIndex = derivedField.client[clientIndex].matter.findIndex(
              (m) => m.name === matterId,
            );
            if (matterIndex !== -1) {
              this.logger.log(`Found matter by name: ${matterId}`);
              realMatterId =
                derivedField.client[clientIndex].matter[matterIndex]._id;
            }
          }

          // If not found and it's in matter-1-1 format, try to find by position
          if (matterIndex === -1 && matterId.startsWith('matter-')) {
            this.logger.log(
              `Matter ID in matter-X-Y format, trying to parse: ${matterId}`,
            );
            const matterParts = matterId.split('-');

            if (matterParts.length === 3) {
              const clientPosition = parseInt(matterParts[1]) - 1; // Convert to 0-based index
              const matterPosition = parseInt(matterParts[2]) - 1; // Convert to 0-based index

              // Make sure the client index matches
              if (
                clientPosition === clientIndex &&
                matterPosition >= 0 &&
                matterPosition < derivedField.client[clientIndex].matter.length
              ) {
                // Found the matter by position
                matterIndex = matterPosition;
                // Get the actual matter ID for later use
                realMatterId =
                  derivedField.client[clientIndex].matter[matterIndex]._id;
                this.logger.log(
                  `Found matter by position: clientIdx=${clientPosition}, matterIdx=${matterPosition}, realMatterId=${realMatterId}`,
                );
              }
            }
          }

          const processedFiles =
            updateData.files && Array.isArray(updateData.files)
              ? updateData.files.map((file, index) => {
                if (typeof file === 'string') {
                  // Handle string case (fallback)
                  return {
                    id: index,
                    name: file,
                    key: file,
                  };
                }

                // Handle object case (like the sample you gave)
                return {
                  id: index,
                  name: file.name || `file-${index}`,
                  key: file.key || '',
                  uniqueId: file.uniqueId,
                };
              })
              : [];

          // If matter not found, create it (especially for new clients)
          if (matterIndex === -1) {
            // Create new matter
            const newMatter = {
              _id: matterId,
              name: matterDetails.name || 'New Matter',
              event: [],
              attorney: 'Lisa Spencer',
              matter_id: matterId, // Set matter_id to match _id for new matters
              ex_county_of_arrest: matterDetails?.ex_county_of_arrest,
              case_number: matterDetails?.case_number,
              my_case_matter_id: matterDetails?.my_case_matter_id.toString(),
            };

            derivedField.client[clientIndex].matter.push(newMatter);
            matterIndex = derivedField.client[clientIndex].matter.length - 1;
            this.logger.log(
              `New matter created with ID ${matterId} at index ${matterIndex}`,
            );
          }

          const matter = derivedField.client[clientIndex].matter[matterIndex];
          this.logger.log(
            `Using matter: ${JSON.stringify({ _id: matter._id, name: matter.name })}`,
          );

          // Initialize events array if not exists
          if (!matter.event) {
            matter.event = [];
          }
          const events = matter.event;

          // Add debugging information
          this.logger.log(
            `Matter details: ${JSON.stringify({
              _id: matter._id,
              name: matter.name,
              eventCount: events.length,
              events: events.map((e) => ({
                id: e.id,
                description: e.description,
              })),
            })}`,
          );

          // If deleteEvent is true and eventId is provided, delete the event
          if (deleteEvent === true && eventId) {
            this.logger.log(`Attempting to delete event with ID: ${eventId}`);
            // Find the event index
            const eventIndex = events.findIndex((e) => e.id === eventId);
            if (eventIndex === -1) {
              this.logger.error(`Event not found with ID****: ${eventId}`);
              return { success: false, error: `Event not found: ${eventId}` };
            }

            // Get the event to be deleted
            const eventToDelete = events[eventIndex];
            const myCaseMatterId = eventToDelete.my_case_matter_id;
            const eventHistoryId = eventToDelete.event_history_id;
 
            this.logger.log(
              `Starting cascading delete for event: ${eventId}, my_case_matter_id: ${myCaseMatterId}, event_history_id: ${eventHistoryId}`,
            );
 
            // 1. Delete from mycase_client_matter table using my_case_matter_id
            if (myCaseMatterId) {
              try {
                const deletedMyCaseRecordsByMatterId =
                  await this.mycaseClientMatterModel.deleteMany({
                    _id: new Types.ObjectId(myCaseMatterId),
                  });
 
                this.logger.log(
                  `Deleted ${deletedMyCaseRecordsByMatterId.deletedCount} mycase_client_matter records by my_case_matter_id: ${myCaseMatterId}`,
                );
              } catch (error) {
                this.logger.error(
                  `Failed to delete mycase_client_matter records by my_case_matter_id: ${error.message}`,
                );
                // Continue with other deletions even if this fails
              }
            }
 
            // 2. Delete from event_action_history table using event_history_id
            if (eventHistoryId) {
              try {
                const deletedEventHistoryRecords =
                  await this.eventActionHistoryModel.deleteMany({
                    _id: new Types.ObjectId(eventHistoryId),
                  });
 
                this.logger.log(
                  `Deleted ${deletedEventHistoryRecords.deletedCount} event_action_history records by event_history_id: ${eventHistoryId}`,
                );
              } catch (error) {
                this.logger.error(
                  `Failed to delete event_action_history records by event_history_id: ${error.message}`,
                );
                // Continue with other deletions even if this fails
              }
            }

            // If event has eventActionHistoryModelId, delete that record first
            if (eventToDelete.eventActionHistoryModelId) {
              this.logger.log(
                `Deleting EventActionHistory record: ${eventToDelete.eventActionHistoryModelId}`,
              );
              try {
                await this.eventActionHistoryModel.deleteOne({
                  _id: new Types.ObjectId(
                    eventToDelete.eventActionHistoryModelId,
                  ),
                });
                this.logger.log(
                  `Successfully deleted EventActionHistory record`,
                );
              } catch (error) {
                this.logger.error(
                  `Failed to delete EventActionHistory record: ${error.message}`,
                );
                // Continue with other deletions even if this fails
              }
            }

            // Delete all related mycase_client_matter records
            try {
              const deletedMyCaseRecords =
                await this.mycaseClientMatterModel.deleteMany({
                  event_id: eventId,
                });

              this.logger.log(
                `Deleted ${deletedMyCaseRecords.deletedCount} mycase_client_matter records`,
              );
            } catch (error) {
              this.logger.error(
                `Failed to delete mycase_client_matter records: ${error.message}`,
              );
              // Continue with event deletion even if this fails
            }

            // Remove the event from the array
            events.splice(eventIndex, 1);
            this.logger.log(`Event deleted successfully from taskExecution`);

            // Save changes to database
            await taskExecution.save();
            return { success: true };
          }

          // If eventId is not provided or createEvent is true, create a new event
          if (!eventId || createEvent === true) {
            let newEvent: any;

            if (
              updateData.appointmentAction === 'Cancel' &&
              updateData.appointmentToReschedule
            ) {
              // For cancellation, find the existing event to be cancelled
              const existingEvent = await this.mycaseClientMatterModel.findOne({
                event_id: updateData.appointmentToReschedule,
              });
              console.log(
                '🚀 ~ updateWorkflow ~ existingEvent:',
                existingEvent,
              );

              if (existingEvent && existingEvent.event) {
                // Use the existing event data as base
                newEvent = {
                  id: updateData.id, // Use the new event ID from updateData
                  caseNumber: existingEvent.event.caseNumber || '',
                  clientName:
                    existingEvent.event.clientName ||
                    `${clientIdentifier} | ${clientIdentifier} (${matter.name}) SA`,
                  description: existingEvent.event.description || '',
                  subject: updateData?.subject,
                  courtNoticeType: event?.updateData?.courtNoticeType || '',
                  courtNoticeActions:
                    existingEvent.event.courtNoticeActions ||
                    'status_conference',
                  appointmentAction: EventStatus.CANCEL,
                  isCancel: true,
                  charge: existingEvent.event.charge || '',
                  county: existingEvent.event.county || '',
                  courtLocation: existingEvent.event.courtLocation || '',
                  startDate:
                    existingEvent.event.startDate ||
                    new Date().toISOString().split('T')[0],
                  endDate:
                    existingEvent.event.endDate ||
                    new Date().toISOString().split('T')[0],
                  date:
                    existingEvent.event.date ||
                    new Date().toLocaleDateString('en-US'),
                  startTime: existingEvent.event.startTime || '09:00',
                  endTime: existingEvent.event.endTime || '10:00',
                  allDay: existingEvent.event.allDay || false,
                  requiredAttendees:
                    existingEvent.event.requiredAttendees || '',
                  optionalAttendees:
                    existingEvent.event.optionalAttendees || '',
                  clientAttendance: existingEvent.event.clientAttendance || '',
                  meetingLink: existingEvent.event.meetingLink || '',
                  meetingLocation: existingEvent.event.meetingLocation || '',
                  phoneDetails: existingEvent.event.phoneDetails || '',
                  meetingAddress: existingEvent.event.meetingAddress || '',
                  court_notice_date: existingEvent.event?.court_notice_date,
                  isCompleted:
                    existingEvent.event.isCompleted !== undefined
                      ? existingEvent.event.isCompleted
                      : false,
                  files: [],
                  appointmentToReschedule: updateData.appointmentToReschedule,
                };

                this.logger.log(
                  `Creating cancellation event based on existing event: ${JSON.stringify(newEvent)}`,
                );
              } else {
                // Fallback to regular event creation if existing event not found
                newEvent = {
                  id: updateData.id,
                  caseNumber: updateData.caseNumber || '',
                  clientName:
                    updateData.clientName ||
                    `${clientIdentifier} | ${clientIdentifier} (${matter.name}) SA`,
                  description: updateData.description || '',
                  subject: updateData?.subject,
                  courtNoticeType: updateData.courtNoticeType || '',
                  courtNoticeActions:
                    updateData.courtNoticeActions || 'status_conference',
                  appointmentAction: EventStatus.CANCEL,
                  isCancel: true,
                  charge: updateData.charge || '',
                  county: updateData.county || '',
                  courtLocation: updateData.courtLocation || '',
                  startDate:
                    updateData.startDate ||
                    new Date().toISOString().split('T')[0],
                  endDate:
                    updateData.endDate ||
                    new Date().toISOString().split('T')[0],
                  date:
                    updateData.date || new Date().toLocaleDateString('en-US'),
                  startTime: updateData.startTime || '09:00',
                  endTime: updateData.endTime || '10:00',
                  allDay: updateData.allDay || false,
                  requiredAttendees: updateData.requiredAttendees || '',
                  optionalAttendees: updateData.optionalAttendees || '',
                  clientAttendance: updateData.clientAttendance || '',
                  meetingLink: updateData.meetingLink || '',
                  meetingLocation: updateData.meetingLocation || '',
                  phoneDetails: updateData.phoneDetails || '',
                  meetingAddress: updateData.meetingAddress || '',
                  court_notice_date: updateData?.court_notice_date,
                  appointmentToReschedule: updateData.appointmentToReschedule,
                  isCompleted: true, // Cancelled events are marked as completed
                  files: processedFiles,
                };
              }
            } else if (
              updateData.appointmentAction === 'Reschedule' &&
              updateData.appointmentToReschedule
            ) {
              // For reschedule, ensure appointmentToReschedule is set on the new event
              newEvent = {
                id: updateData.id,
                caseNumber: updateData.caseNumber || '',
                clientName:
                  updateData.clientName ||
                  `${clientIdentifier} | ${clientIdentifier} (${matter.name}) SA`,
                description: updateData.description || '',
                subject: updateData?.subject,
                courtNoticeType: updateData.courtNoticeType || '',
                courtNoticeActions:
                  updateData.courtNoticeActions || 'status_conference',
                appointmentAction: updateData.appointmentAction,
                charge: updateData.charge || '',
                county: updateData.county || '',
                courtLocation: updateData.courtLocation || '',
                startDate:
                  updateData.startDate ||
                  new Date().toISOString().split('T')[0],
                endDate:
                  updateData.endDate || new Date().toISOString().split('T')[0],
                date: updateData.date || new Date().toLocaleDateString('en-US'),
                startTime: updateData.startTime || '09:00',
                endTime: updateData.endTime || '10:00',
                allDay: updateData.allDay || false,
                requiredAttendees: updateData.requiredAttendees || '',
                optionalAttendees: updateData.optionalAttendees || '',
                clientAttendance: updateData.clientAttendance || '',
                meetingLink: updateData.meetingLink || '',
                meetingLocation: updateData.meetingLocation || '',
                phoneDetails: updateData.phoneDetails || '',
                meetingAddress: updateData.meetingAddress || '',
                court_notice_date: updateData?.court_notice_date,
                appointmentToReschedule: updateData.appointmentToReschedule, // <-- always set for reschedule
                isCompleted:
                  updateData.isCompleted !== undefined
                    ? updateData.isCompleted
                    : false,
                files: processedFiles,
              };
            } else {
              // Regular event creation for non-cancellation cases
              newEvent = {
                id: updateData.id,
                caseNumber: updateData.caseNumber || '',
                clientName:
                  updateData.clientName ||
                  `${clientIdentifier} | ${clientIdentifier} (${matter.name}) SA`,
                description: updateData.description || '',
                subject: updateData?.subject,
                courtNoticeType: updateData.courtNoticeType || '',
                courtNoticeActions:
                  updateData.courtNoticeActions || 'status_conference',
                appointmentAction: updateData.appointmentAction,
                charge: updateData.charge || '',
                county: updateData.county || '',
                courtLocation: updateData.courtLocation || '',
                startDate:
                  updateData.startDate ||
                  new Date().toISOString().split('T')[0],
                endDate:
                  updateData.endDate || new Date().toISOString().split('T')[0],
                date: updateData.date || new Date().toLocaleDateString('en-US'),
                startTime: updateData.startTime || '09:00',
                endTime: updateData.endTime || '10:00',
                allDay: updateData.allDay || false,
                requiredAttendees: updateData.requiredAttendees || '',
                optionalAttendees: updateData.optionalAttendees || '',
                clientAttendance: updateData.clientAttendance || '',
                meetingLink: updateData.meetingLink || '',
                meetingLocation: updateData.meetingLocation || '',
                phoneDetails: updateData.phoneDetails || '',
                meetingAddress: updateData.meetingAddress || '',
                court_notice_date: updateData?.court_notice_date,
                appointmentToReschedule:
                  updateData.appointmentToReschedule || undefined, // add if present
                isCompleted:
                  updateData.isCompleted !== undefined
                    ? updateData.isCompleted
                    : false,
                files: processedFiles,
              };
            }

            this.logger.log(`New event data: ${JSON.stringify(newEvent)}`);

            // Handle different appointmentAction scenarios
            let mycaseClientMatterId: string | null = null;
            let eventHistoryId: string | null = null;

            // 1. For appointmentAction = 'New': Create entry in mycase_client_matter
            if (updateData.appointmentAction === EventAction.NEW || updateData.appointmentAction === 'New') {
              this.logger.log(`Creating mycase_client_matter entry for New appointment`);
              
              const mycaseRecord = await this.mycaseClientMatterModel.create({
                client_matter_id: new Types.ObjectId(clientMatterId),
                event_id: updateData.id,
                workflow_execution_id: new Types.ObjectId(workflow_id),
                eventStatus: EventStatus.NEW,
                event: newEvent,
                is_active: true,
              });

              mycaseClientMatterId = mycaseRecord._id.toString();
              eventHistoryId = ''; // Empty string for New appointments
              
              this.logger.log(`Created mycase_client_matter record with ID: ${mycaseClientMatterId}`);
            }
            
            // 2. For appointmentAction = 'Reschedule': Create entries in both tables
            else if (updateData.appointmentAction === EventAction.RESCHEDULE || updateData.appointmentAction === 'Reschedule') {
              this.logger.log(`Creating entries for Reschedule appointment`);
              
              // Create mycase_client_matter entry
              const mycaseRecord = await this.mycaseClientMatterModel.create({
                client_matter_id: new Types.ObjectId(clientMatterId),
                event_id: updateData.id,
                workflow_execution_id: new Types.ObjectId(workflow_id),
                eventStatus: EventStatus.NEW,
                event: newEvent,
                is_active: true,
              });

              mycaseClientMatterId = mycaseRecord._id.toString();
              
              // Create event_action_history entry
              const eventActionHistoryRecord = await this.eventActionHistoryModel.create({
                client_matter_id: new Types.ObjectId(clientMatterId),
                eventStatus: EventStatus.PENDING,
                event_id: updateData.appointmentToReschedule || updateData.id,
                my_case_event_id: newEvent.my_case_event_id,
                workflow_execution_id: new Types.ObjectId(workflow_id),
                action: EventAction.RESET,
              });

              eventHistoryId = eventActionHistoryRecord._id.toString();
              
              this.logger.log(`Created mycase_client_matter record with ID: ${mycaseClientMatterId}`);
              this.logger.log(`Created event_action_history record with ID: ${eventHistoryId}`);
            }
            
            // 3. For appointmentAction = 'Cancel': Create entry only in event_action_history
            else if (updateData.appointmentAction === EventAction.CANCLE || updateData.appointmentAction === 'Cancel') {
              this.logger.log(`Creating event_action_history entry for Cancel appointment`);
              
              // Create event_action_history entry only
              const eventActionHistoryRecord = await this.eventActionHistoryModel.create({
                client_matter_id: new Types.ObjectId(clientMatterId),
                eventStatus: EventStatus.PENDING,
                event_id: updateData.appointmentToReschedule || updateData.id,
                my_case_event_id: newEvent.my_case_event_id,
                workflow_execution_id: new Types.ObjectId(workflow_id),
                action: EventAction.CANCEL_DIS,
              });

              mycaseClientMatterId = null;
              eventHistoryId = eventActionHistoryRecord._id.toString();
              
              this.logger.log(`Created event_action_history record with ID: ${eventHistoryId}`);
            }
            
            // For any other cases (backward compatibility)
            else {
              this.logger.log(`Creating mycase_client_matter entry for other appointment action: ${updateData.appointmentAction}`);
              
              const mycaseRecord = await this.mycaseClientMatterModel.create({
                client_matter_id: new Types.ObjectId(clientMatterId),
                event_id: updateData.id,
                workflow_execution_id: new Types.ObjectId(workflow_id),
                eventStatus: EventStatus.NEW,
                event: newEvent,
                is_active: true,
              });

              mycaseClientMatterId = mycaseRecord._id.toString();
              eventHistoryId = '';
            }

            // Store the IDs in the event object
            if (mycaseClientMatterId) {
              newEvent.my_case_matter_id = mycaseClientMatterId;
            }
            if (eventHistoryId !== null) {
              newEvent.event_history_id = eventHistoryId;
            }

            // Maintain backward compatibility with eventActionHistoryModelId
            if (eventHistoryId && eventHistoryId !== '') {
              newEvent.eventActionHistoryModelId = eventHistoryId;
            }

            // Add the new event to the matter's events array in taskExecution
            events.push(newEvent);
            await taskExecution.save();

            this.logger.log(`Event created successfully with my_case_matter_id: ${mycaseClientMatterId}, event_history_id: ${eventHistoryId}`);
            return { success: true, eventId: updateData.id };
          }

          // Handle "Save court notice only" case - cleanup records if needed
          if (updateData.courtNoticeActions === 'Save court notice only') {
            this.logger.log(`Processing "Save court notice only" action for event: ${eventId || 'new event'}`);
            
            // Find the event to process (either existing or newly created)
            let targetEvent = null;
            
            if (eventId) {
              // Look for existing event
              targetEvent = events.find((e) => e.id === eventId);
            } else if (events.length > 0) {
              // Use the last event if no eventId (newly created event)
              targetEvent = events[events.length - 1];
            }
            
            if (targetEvent) {
              // Handle mycaseClientMatterId cleanup
              if (targetEvent.my_case_matter_id && targetEvent.my_case_matter_id !== '') {
                this.logger.log(`Deleting mycaseClientMatter record with ID: ${targetEvent.my_case_matter_id}`);
                
                try {
                  const deleteResult = await this.mycaseClientMatterModel.deleteOne({
                    _id: new Types.ObjectId(targetEvent.my_case_matter_id),
                  });
                  
                  this.logger.log(`Deleted ${deleteResult.deletedCount} mycaseClientMatter record(s)`);
                  
                  // Clear the field in the event
                  targetEvent.my_case_matter_id = '';
                  this.logger.log(`Cleared my_case_matter_id from event`);
                } catch (error) {
                  this.logger.error(`Failed to delete mycaseClientMatter record: ${error.message}`);
                }
              }
              
              // Handle eventHistoryId cleanup
              if (targetEvent.event_history_id && targetEvent.event_history_id !== '') {
                this.logger.log(`Deleting eventActionHistory record with ID: ${targetEvent.event_history_id}`);
                
                try {
                  const deleteResult = await this.eventActionHistoryModel.deleteOne({
                    _id: new Types.ObjectId(targetEvent.event_history_id),
                  });
                  
                  this.logger.log(`Deleted ${deleteResult.deletedCount} eventActionHistory record(s)`);
                  
                  // Clear the field in the event
                  targetEvent.event_history_id = '';
                  // Also clear eventActionHistoryModelId for backward compatibility
                  if (targetEvent.eventActionHistoryModelId) {
                    targetEvent.eventActionHistoryModelId = '';
                  }
                  this.logger.log(`Cleared event_history_id from event`);
                } catch (error) {
                  this.logger.error(`Failed to delete eventActionHistory record: ${error.message}`);
                }
              }
              
              // Save changes and return success
              await taskExecution.save();
              this.logger.log(`Successfully processed "Save court notice only" action`);
              return { success: true, message: 'Court notice saved successfully with cleanup completed' };
            } else {
              this.logger.warn(`No event found to process for "Save court notice only" action`);
            }
          }

          // Otherwise, update an existing event
          this.logger.log(
            `Events in matter: ${JSON.stringify(events.map((e) => ({ id: e.id, desc: e.description })))}`,
          );

          console.log('============Hello========', eventId);

          // Use direct event ID matching
          let eventIndex = events.findIndex((e) => e.id === eventId);

          if (eventIndex === -1) {
            this.logger.error(`Event not found with ID2345: ${eventId}`);

            // Check if event exists in any matter within the same client
            this.logger.log(
              `Checking if event exists in any matter within the same client: ${eventId}`,
            );
            let foundEvent = null;
            let foundMatterIndex = -1;

            for (
              let m = 0;
              m < derivedField.client[clientIndex].matter.length;
              m++
            ) {
              const matterEvents =
                derivedField.client[clientIndex].matter[m].event || [];
              const foundIndex = matterEvents.findIndex(
                (e) => e.id === eventId,
              );
              if (foundIndex !== -1) {
                foundEvent = matterEvents[foundIndex];
                foundMatterIndex = m;
                break;
              }
            }

            if (foundEvent && foundMatterIndex !== -1) {
              // Move the event from the found matter to the current matter
              const sourceMatter =
                derivedField.client[clientIndex].matter[foundMatterIndex];
              const eventToMove = sourceMatter.event.find(
                (e) => e.id === eventId,
              );

              if (eventToMove) {
                // Remove from source matter
                sourceMatter.event = sourceMatter.event.filter(
                  (e) => e.id !== eventId,
                );

                // Add to current matter
                events.push(eventToMove);
                eventIndex = events.length - 1;

                this.logger.log(
                  `Event moved from matter ${foundMatterIndex} to current matter`,
                );
              }
            } else {
              // Check if event exists in any workflow execution
              this.logger.log(
                `Checking if event exists in any workflow execution: ${eventId}`,
              );
              const allMycaseEvents = await this.mycaseClientMatterModel.find({
                event_id: eventId,
              });

              if (allMycaseEvents.length > 0) {
                this.logger.log(
                  `Found event in other workflow executions, copying to current taskExecution: ${eventId}`,
                );

                // Use the first found event
                const eventToCopy = allMycaseEvents[0].event;
                events.push(eventToCopy);
                await taskExecution.save();

                // Now find the event index again
                eventIndex = events.findIndex((e) => e.id === eventId);
              } else {
                this.logger.error(
                  `Event not found in any workflow execution: ${eventId}`,
                );
                return { success: false, error: `Event not found: ${eventId}` };
              }
            }
          }

          const eventToUpdate = events[eventIndex];
          this.logger.log(
            `Found event to update: ${JSON.stringify({
              id: eventToUpdate.id,
              description: eventToUpdate.description,
              isCompleted: eventToUpdate.isCompleted,
            })}`,
          );

          // Transform files array to match expected schema before updating
          const processedUpdateData: any = { ...updateData };

          if (updateData.files && Array.isArray(updateData.files)) {
            processedUpdateData.files = updateData.files.map((file, i) => {
              if (typeof file === 'string') {
                return file;
              }

              if (
                processedUpdateData?.renameFiles &&
                Array.isArray(processedUpdateData?.renameFiles) &&
                processedUpdateData?.renameFiles.length > 0
              ) {
                const path = file.key.replace(
                  processedUpdateData.renameFiles[i].old_fileName.replace(
                    '.pdf',
                    '',
                  ),
                  processedUpdateData?.renameFiles[i].new_fileName.replace(
                    '.pdf',
                    '',
                  ),
                );

                file.key = path;
              }

              return {
                name: file.name,
                key: file.key,
                uniqueId: file.uniqueId,
              };
            });
          }

          // Update the event with provided data
          this.logger.log(
            `Updating with data: ${JSON.stringify(processedUpdateData)}`,
          );

          Object.assign(eventToUpdate, processedUpdateData);

          // Handle different appointmentAction scenarios for existing event updates
          if (updateData.appointmentAction) {
            this.logger.log(
              `Processing appointmentAction: ${updateData.appointmentAction} for existing event: ${eventId}`,
            );

            // 1. For appointmentAction = 'New': Check and create/update mycase_client_matter
            if (
              updateData.appointmentAction === EventAction.NEW ||
              updateData.appointmentAction === 'New'
            ) {
              this.logger.log(
                `Processing New appointment action for existing event`,
              );

              // Check if my_case_matter_id exists in event stub
              if (!eventToUpdate.my_case_matter_id) {
                this.logger.log(
                  `No my_case_matter_id found, creating mycase_client_matter record`,
                );

                // Create new mycase_client_matter record
                const mycaseRecord = await this.mycaseClientMatterModel.create({
                  client_matter_id: new Types.ObjectId(clientMatterId),
                  event_id: updateData.id || eventId,
                  workflow_execution_id: new Types.ObjectId(workflow_id),
                  eventStatus: EventStatus.NEW,
                  event: eventToUpdate,
                  is_active: true,
                });

                // Update the event stub with the new ID
                eventToUpdate.my_case_matter_id = mycaseRecord._id.toString();
                this.logger.log(
                  `Created and updated my_case_matter_id: ${mycaseRecord._id.toString()}`,
                );
              } else {
                this.logger.log(
                  `my_case_matter_id already exists: ${eventToUpdate.my_case_matter_id}, updating existing record`,
                );

                // Update existing mycase_client_matter record
                await this.mycaseClientMatterModel.updateOne(
                  { _id: new Types.ObjectId(eventToUpdate.my_case_matter_id) },
                  {
                    $set: {
                      event: eventToUpdate,
                      client_matter_id: new Types.ObjectId(clientMatterId),
                      workflow_execution_id: new Types.ObjectId(workflow_id),
                    },
                  },
                );
              }
            }

            // 2. For appointmentAction = 'Reschedule': Handle both mycase_client_matter and event_action_history
            else if (
              updateData.appointmentAction === EventAction.RESCHEDULE ||
              updateData.appointmentAction === 'Reschedule'
            ) {
              this.logger.log(
                `Processing Reschedule appointment action for existing event`,
              );

              // Handle mycase_client_matter
              if (!eventToUpdate.my_case_matter_id) {
                this.logger.log(
                  `No my_case_matter_id found, creating mycase_client_matter record for reschedule`,
                );

                const mycaseRecord = await this.mycaseClientMatterModel.create({
                  client_matter_id: new Types.ObjectId(clientMatterId),
                  event_id: updateData.id || eventId,
                  workflow_execution_id: new Types.ObjectId(workflow_id),
                  eventStatus: EventStatus.NEW,
                  event: eventToUpdate,
                  is_active: true,
                });

                eventToUpdate.my_case_matter_id = mycaseRecord._id.toString();
                this.logger.log(
                  `Created my_case_matter_id for reschedule: ${mycaseRecord._id.toString()}`,
                );
              } else {
                this.logger.log(
                  `Updating existing mycase_client_matter record for reschedule`,
                );

                await this.mycaseClientMatterModel.updateOne(
                  { _id: new Types.ObjectId(eventToUpdate.my_case_matter_id) },
                  {
                    $set: {
                      event: eventToUpdate,
                      client_matter_id: new Types.ObjectId(clientMatterId),
                      workflow_execution_id: new Types.ObjectId(workflow_id),
                    },
                  },
                );
              }

              // Handle event_action_history
              if (!eventToUpdate.event_history_id || eventToUpdate.event_history_id === '') {
                this.logger.log(
                  `No event_history_id found, creating event_action_history record for reschedule`,
                );

                const eventActionHistoryRecord =
                  await this.eventActionHistoryModel.create({
                    client_matter_id: new Types.ObjectId(clientMatterId),
                    eventStatus: EventStatus.PENDING,
                    event_id: updateData.appointmentToReschedule || eventId,
                    my_case_event_id: eventToUpdate.my_case_event_id,
                    workflow_execution_id: new Types.ObjectId(workflow_id),
                    action: EventAction.RESET,
                  });

                eventToUpdate.event_history_id =
                  eventActionHistoryRecord._id.toString();
                eventToUpdate.eventActionHistoryModelId =
                  eventActionHistoryRecord._id.toString();
                this.logger.log(
                  `Created event_history_id for reschedule: ${eventActionHistoryRecord._id.toString()}`,
                );
              } else {
                this.logger.log(
                  `Updating existing event_action_history record for reschedule`,
                );

                await this.eventActionHistoryModel.updateOne(
                  { _id: new Types.ObjectId(eventToUpdate.event_history_id) },
                  {
                    $set: {
                      event_id: updateData.appointmentToReschedule || eventId,
                      action: EventAction.RESET,
                      eventStatus: EventStatus.PENDING,
                      client_matter_id: new Types.ObjectId(clientMatterId),
                      workflow_execution_id: new Types.ObjectId(workflow_id),
                    },
                  },
                );
              }
            }

            // 3. For appointmentAction = 'Cancel': Delete mycase_client_matter and handle event_action_history
            else if (
              updateData.appointmentAction === EventAction.CANCLE ||
              updateData.appointmentAction === 'Cancel'
            ) {
              this.logger.log(
                `Processing Cancel appointment action for existing event`,
              );

              // Delete mycase_client_matter record if exists
              if (eventToUpdate.my_case_matter_id) {
                this.logger.log(
                  `Deleting mycase_client_matter record: ${eventToUpdate.my_case_matter_id}`,
                );

                await this.mycaseClientMatterModel.deleteOne({
                  _id: new Types.ObjectId(eventToUpdate.my_case_matter_id),
                });

                // Clear the field in event stub
                eventToUpdate.my_case_matter_id = '';
                this.logger.log(`Cleared my_case_matter_id from event stub`);
              }

              // Handle event_action_history
              if (!eventToUpdate.event_history_id || eventToUpdate.event_history_id === '') {
                this.logger.log(
                  `Creating event_action_history record for cancel`,
                );

                const eventActionHistoryRecord =
                  await this.eventActionHistoryModel.create({
                    client_matter_id: new Types.ObjectId(clientMatterId),
                    eventStatus: EventStatus.PENDING,
                    event_id: updateData.appointmentToReschedule || eventId,
                    my_case_event_id: eventToUpdate.my_case_event_id,
                    workflow_execution_id: new Types.ObjectId(workflow_id),
                    action: EventAction.CANCEL_DIS,
                  });

                eventToUpdate.event_history_id =
                  eventActionHistoryRecord._id.toString();
                eventToUpdate.eventActionHistoryModelId =
                  eventActionHistoryRecord._id.toString();
                this.logger.log(
                  `Created event_history_id for cancel: ${eventActionHistoryRecord._id.toString()}`,
                );
              } else {
                this.logger.log(
                  `Updating existing event_action_history record for cancel`,
                );

                await this.eventActionHistoryModel.updateOne(
                  { _id: new Types.ObjectId(eventToUpdate.event_history_id) },
                  {
                    $set: {
                      event_id: updateData.appointmentToReschedule || eventId,
                      action: EventAction.CANCEL_DIS,
                      eventStatus: EventStatus.PENDING,
                      client_matter_id: new Types.ObjectId(clientMatterId),
                      workflow_execution_id: new Types.ObjectId(workflow_id),
                    },
                  },
                );
              }
            }
          }

          await this.mycaseClientMatterModel.updateOne(
            {
              event_id: eventId,
              workflow_execution_id: new Types.ObjectId(workflow_id),
            },
            {
              $set: {
                event: eventToUpdate, // Update the single event object
              },
            },
          );

          this.logger.log(
            `MycaseClientMatter entry updated successfully for event_id: ${eventId}`,
          );

          // Save changes to database
          await taskExecution.save();

          return { success: true };
        } catch (error) {
          this.logger.error(`Error updating event: ${error.message}`);
          return { success: false, error: error.message };
        }
      }

      await taskExecution.save();

      if (is_completed === true) {
        await this.workflowExecutionModel.updateOne(
          { _id: workflow_id },
          { status: Work_flow_execution_status.COMPLETED },
        );
      }

      if (dynamicComponentId !== null) {
        const workflowExecution = await this.workflowExecutionModel.findOne({
          _id: workflow_id,
        });
        if (!workflowExecution)
          throw ErrorExceptions.WORK_FLOW_EXECUTION_NOT_FOUND();

        const template = await this.templateModel.findOne({
          _id: workflowExecution.template_id,
        });
        if (!template) throw ErrorExceptions.TEMPLATE_NOT_FOUND();

        const tasks = await this.taskModel.find({
          _id: { $in: template.task_id },
        });

        // Always unselect all tasks
        await this.taskModel.updateMany(
          { _id: { $in: template.task_id }, selected_task: true },
          { selected_task: false },
        );

        // Only proceed to select task if dynamicValue is non-empty
        if (dynamicValue && dynamicValue.trim() !== '') {
          const matchingTask = tasks.find(
            (task) =>
              task.condition_task &&
              task.condition_task.value === dynamicValue &&
              task.condition_task.id === dynamicComponentId,
          );

          if (matchingTask) {
            await this.taskModel.updateOne(
              { _id: matchingTask._id },
              { selected_task: true },
            );
          }
        }
      }

      return { success: true };
    } catch (error) {
      throw error;
    }
  }

  async getCourtNoticeList(getWorkflowDto: GetWorkflowDto) {
    try {
      const { page = 1, limit = 10, type, userId } = getWorkflowDto;
      const pageNumber = Number(page);
      const limitNumber = Number(limit);
      const skip = (pageNumber - 1) * limitNumber;

      const userTimezone = await this.userModel.findOne({
        id: userId,
      }).select('timezone');

      const matchStage: any = {
        is_deleted: false,
        is_active: true,
      };

      if (type === 'follow_up') {
        matchStage['workflow_status'] = { $ne: Work_flow_execution_status.COMPLETED };
        matchStage['is_archive'] = false;
        matchStage['parent_id'] = { $exists: true, $ne: null };
      } else if (type === 'new_court_notice') {
        matchStage['status'] = Work_flow_execution_status.PENDING;
        matchStage['is_archive'] = false;
      } else if (type === 'completed') {
        matchStage['workflow_status'] = Work_flow_execution_status.COMPLETED;
        matchStage['is_archive'] = false;
      } else if (type === 'archive') {
        matchStage['is_archive'] = true;
      }

      const workflowExecutions = await this.workflowExecutionModel.aggregate([
        { $match: matchStage },

        // template lookup
        {
          $lookup: {
            from: 'template',
            localField: 'template_id',
            foreignField: '_id',
            as: 'template',
          },
        },
        { $unwind: { path: '$template', preserveNullAndEmptyArrays: true } },

        // task_execution lookup
        {
          $lookup: {
            from: 'task_execution',
            let: { workflow_execution_id: '$_id' },
            pipeline: [
              {
                $match: {
                  $expr: { $eq: ['$workflow_execution_id', '$$workflow_execution_id'] },
                },
              },
              { $sort: { sequence: 1 } },
            ],
            as: 'taskExecutions',
          },
        },

        // derived fields: totalTasks, completedTasks, last_task_id, isChild
        {
          $addFields: {
            totalTasks: { $size: '$taskExecutions' },
            completedTasks: {
              $size: {
                $filter: {
                  input: '$taskExecutions',
                  as: 'task',
                  cond: { $eq: ['$$task.task_visible_status', TASK_VISIBLE_STATUS.REVIEWED] },
                },
              },
            },
            last_task_id: {
              $cond: {
                if: { $ifNull: ['$last_task_id', false] },
                then: '$last_task_id',
                else: { $arrayElemAt: ['$taskExecutions.task_id', 0] },
              },
            },
            isChild: {
              $cond: [{ $ifNull: ['$parent_id', false] }, false, true],
            },
          },
        },

        // unwind taskExecutions to apply further match/filter logic
        { $unwind: { path: '$taskExecutions', preserveNullAndEmptyArrays: true } },
        { $unwind: { path: '$taskExecutions.derived_field', preserveNullAndEmptyArrays: true } },

        // Keep only courtNotice derived_field (or when derived_field doesn't exist)
        {
          $match: {
            $or: [
              { 'taskExecutions.derived_field.type': 'courtNotice' },
              { 'taskExecutions.derived_field': { $exists: false } },
            ],
          },
        },

        // Group back per workflow execution and collect fields
        {
          $group: {
            _id: '$_id',
            execution_id: { $first: { $toString: '$_id' } },
            last_task_id: { $first: '$last_task_id' },
            isChild: { $first: '$isChild' },
            totalTasks: { $first: '$totalTasks' },
            completedTasks: { $first: '$completedTasks' },
            template_name: { $first: '$template.name' },
            start_date: { $first: '$start_date' },
            end_date: { $first: '$end_date' },
            last_activity: { $first: '$last_activity' },
            status: { $first: '$status' },
            assigns: { $first: '$assigns' },
            assign_by: { $first: '$assign_by' },
            run_by: { $first: '$run_by' },
            notes: { $first: '$notes' },
            updatedAt: { $first: '$updatedAt' },

            // matter push (preserve original logic)
            matter: {
              $push: {
                $cond: [
                  {
                    $and: [
                      { $isArray: '$taskExecutions.derived_field.client' },
                      { $gt: [{ $size: '$taskExecutions.derived_field.client' }, 0] },
                    ],
                  },
                  {
                    $let: {
                      vars: {
                        firstClient: {
                          $arrayElemAt: ['$taskExecutions.derived_field.client', 0],
                        },
                      },
                      in: {
                        $cond: [
                          {
                            $and: [
                              { $isArray: '$$firstClient.matter' },
                              { $gt: [{ $size: '$$firstClient.matter' }, 0] },
                            ],
                          },
                          {
                            $let: {
                              vars: {
                                firstMatter: { $arrayElemAt: ['$$firstClient.matter', 0] },
                              },
                              in: '$$firstMatter.name',
                            },
                          },
                          '',
                        ],
                      },
                    },
                  },
                  '',
                ],
              },
            },

            // attorney push
            attorney: {
              $push: {
                $cond: [
                  {
                    $and: [
                      { $isArray: '$taskExecutions.derived_field.client' },
                      { $gt: [{ $size: '$taskExecutions.derived_field.client' }, 0] },
                    ],
                  },
                  {
                    $let: {
                      vars: {
                        firstClient: {
                          $arrayElemAt: ['$taskExecutions.derived_field.client', 0],
                        },
                      },
                      in: {
                        $cond: [
                          {
                            $and: [
                              { $isArray: '$$firstClient.matter' },
                              { $gt: [{ $size: '$$firstClient.matter' }, 0] },
                            ],
                          },
                          {
                            $let: {
                              vars: {
                                firstMatter: { $arrayElemAt: ['$$firstClient.matter', 0] },
                              },
                              in: '$$firstMatter.attorney',
                            },
                          },
                          '',
                        ],
                      },
                    },
                  },
                  '',
                ],
              },
            },

            // ex_county_of_arrest push
            ex_county_of_arrest: {
              $push: {
                $cond: [
                  {
                    $and: [
                      { $isArray: '$taskExecutions.derived_field.client' },
                      { $gt: [{ $size: '$taskExecutions.derived_field.client' }, 0] },
                    ],
                  },
                  {
                    $let: {
                      vars: {
                        firstClient: {
                          $arrayElemAt: ['$taskExecutions.derived_field.client', 0],
                        },
                      },
                      in: {
                        $cond: [
                          {
                            $and: [
                              { $isArray: '$$firstClient.matter' },
                              { $gt: [{ $size: '$$firstClient.matter' }, 0] },
                            ],
                          },
                          {
                            $let: {
                              vars: {
                                firstMatter: { $arrayElemAt: ['$$firstClient.matter', 0] },
                              },
                              in: '$$firstMatter.ex_county_of_arrest',
                            },
                          },
                          '',
                        ],
                      },
                    },
                  },
                  '',
                ],
              },
            },

            // case_number push
            case_number: {
              $push: {
                $cond: [
                  {
                    $and: [
                      { $isArray: '$taskExecutions.derived_field.client' },
                      { $gt: [{ $size: '$taskExecutions.derived_field.client' }, 0] },
                    ],
                  },
                  {
                    $let: {
                      vars: {
                        firstClient: {
                          $arrayElemAt: ['$taskExecutions.derived_field.client', 0],
                        },
                      },
                      in: {
                        $cond: [
                          {
                            $and: [
                              { $isArray: '$$firstClient.matter' },
                              { $gt: [{ $size: '$$firstClient.matter' }, 0] },
                            ],
                          },
                          {
                            $let: {
                              vars: {
                                firstMatter: { $arrayElemAt: ['$$firstClient.matter', 0] },
                              },
                              in: '$$firstMatter.case_number',
                            },
                          },
                          '',
                        ],
                      },
                    },
                  },
                  '',
                ],
              },
            },
          },
        },

        // Lookup users and roles using the original assigns array
        {
          $lookup: {
            from: 'users',
            localField: 'assigns',
            foreignField: '_id',
            as: 'userAssignments',
          },
        },
        {
          $lookup: {
            from: 'roles',
            localField: 'assigns',
            foreignField: '_id',
            as: 'roleAssignments',
          },
        },

        // Build combined allAssignments array with _id and displayName
        {
          $addFields: {
            allAssignments: {
              $concatArrays: [
                {
                  $map: {
                    input: '$userAssignments',
                    as: 'u',
                    in: {
                      _id: '$$u._id',
                      displayName: {
                        $concat: [
                          { $ifNull: ['$$u.first_name', ''] },
                          ' ',
                          { $ifNull: ['$$u.last_name', ''] },
                        ],
                      },
                    },
                  },
                },
                {
                  $map: {
                    input: '$roleAssignments',
                    as: 'r',
                    in: {
                      _id: '$$r._id',
                      displayName: { $ifNull: ['$$r.name', ''] },
                    },
                  },
                },
              ],
            },
          },
        },

        // Preserve order by mapping over assigns and picking the displayName from allAssignments
        {
          $addFields: {
            assignedUsers: {
              $map: {
                input: { $ifNull: ['$assigns', []] },
                as: 'assignedId',
                in: {
                  $let: {
                    vars: {
                      match: {
                        $arrayElemAt: [
                          {
                            $filter: {
                              input: '$allAssignments',
                              as: 'a',
                              cond: { $eq: ['$$a._id', '$$assignedId'] },
                            },
                          },
                          0,
                        ],
                      },
                    },
                    in: { $ifNull: ['$$match.displayName', ''] },
                  },
                },
              },
            },
          },
        },

        // Convert assignedUsers array to a comma-separated string
        {
          $addFields: {
            assigned_users: {
              $reduce: {
                input: {
                  $filter: {
                    input: '$assignedUsers',
                    as: 'item',
                    cond: { $ne: ['$$item', ''] },
                  },
                },
                initialValue: '',
                in: {
                  $cond: [
                    { $eq: ['$$value', ''] },
                    '$$this',
                    { $concat: ['$$value', ', ', '$$this'] },
                  ],
                },
              },
            },
          },
        },

        // run_by lookup (client collection)
        {
          $lookup: {
            from: 'client',
            localField: 'run_by',
            foreignField: '_id',
            as: 'runByUser',
          },
        },

        // assign_by lookup (to get assign_by email)
        {
          $lookup: {
            from: 'users',
            localField: 'assign_by',
            foreignField: '_id',
            as: 'assignByUser',
          },
        },
        { $unwind: { path: '$assignByUser', preserveNullAndEmptyArrays: true } },

        // Sorting / Pagination
        { $sort: { updatedAt: -1 } },
        { $skip: skip },
        { $limit: limitNumber },

        // Final projection: preserve your original reductions and formatting
        {
          $project: {
            _id: 1,
            execution_id: { $toString: '$_id' },
            template_name: 1,
            start_date: 1,
            end_date: 1,
            last_activity: 1,
            status: 1,
            updatedAt: 1,
            assigned_users: 1,
            last_task_id: { $ifNull: [{ $toString: '$last_task_id' }, ''] },
            isChild: 1,
            totalTasks: 1,
            completedTasks: 1,
            assigns: 1,
            assign_by: '$assignByUser.email',
            matter: {
              $reduce: {
                input: { $filter: { input: '$matter', as: 'item', cond: { $ne: ['$$item', ''] } } },
                initialValue: '',
                in: {
                  $cond: [
                    { $eq: ['$$value', ''] },
                    '$$this',
                    { $concat: ['$$value', ', ', '$$this'] },
                  ],
                },
              },
            },
            attorney: {
              $reduce: {
                input: { $filter: { input: '$attorney', as: 'item', cond: { $ne: ['$$item', ''] } } },
                initialValue: '',
                in: {
                  $cond: [
                    { $eq: ['$$value', ''] },
                    '$$this',
                    { $concat: ['$$value', ', ', '$$this'] },
                  ],
                },
              },
            },
            ex_county_of_arrest: {
              $reduce: {
                input: { $filter: { input: '$ex_county_of_arrest', as: 'item', cond: { $ne: ['$$item', ''] } } },
                initialValue: '',
                in: {
                  $cond: [
                    { $eq: ['$$value', ''] },
                    '$$this',
                    { $concat: ['$$value', ', ', '$$this'] },
                  ],
                },
              },
            },
            case_number: {
              $reduce: {
                input: { $filter: { input: '$case_number', as: 'item', cond: { $ne: ['$$item', ''] } } },
                initialValue: '',
                in: {
                  $cond: [
                    { $eq: ['$$value', ''] },
                    '$$this',
                    { $concat: ['$$value', ', ', '$$this'] },
                  ],
                },
              },
            },
            run_by: {
              $cond: [
                { $isArray: '$runByUser' },
                {
                  $reduce: {
                    input: '$runByUser',
                    initialValue: '',
                    in: {
                      $cond: [
                        { $eq: ['$$value', ''] },
                        '$$this.name',
                        { $concat: ['$$this.name', ', ', '$$value'] },
                      ],
                    },
                  },
                },
                '',
              ],
            },
            notes: 1,
          },
        },
      ]);

      const total = await this.workflowExecutionModel.countDocuments(matchStage);

      const mappedFields = workflowExecutions.map((workflow) => {
        const today = new Date();
        const endDate = new Date(workflow.end_date);
        const formatOnlyDate = (d: Date) => new Date(d.getFullYear(), d.getMonth(), d.getDate());

        const formattedToday = formatOnlyDate(today);
        const formattedEndDate = formatOnlyDate(endDate);
        let displayStatus = Work_flow_execution_status.ON_TRACK;

        if (formattedToday.getTime() > formattedEndDate.getTime()) {
          displayStatus = Work_flow_execution_status.OVERDUE;
        } else if (formattedToday.getTime() === formattedEndDate.getTime()) {
          displayStatus = Work_flow_execution_status.DUE_SOON;
        }

        const currentDateTime = today.getTime();
        const endDateTime = endDate.getTime();
        if (currentDateTime > endDateTime) {
          displayStatus = Work_flow_execution_status.OVERDUE;
        } else if (formattedToday.getTime() === formattedEndDate.getTime()) {
          const timeUntilEnd = endDateTime - currentDateTime;
          const hoursUntilEnd = timeUntilEnd / (1000 * 60 * 60);
          if (hoursUntilEnd > 6) {
            displayStatus = Work_flow_execution_status.ON_TRACK;
          } else {
            displayStatus = Work_flow_execution_status.DUE_SOON;
          }
        }

        if (type === 'completed') {
          displayStatus = Work_flow_execution_status.COMPLETED;
        }
        if (type === 'archive') {
          displayStatus = Work_flow_execution_status.ARCHIVED;
        }

        const completedTasksString = `${workflow.completedTasks || 0}/${workflow.totalTasks || 0}`;

        const TimeZonedStartDate = formatDateInTimezone(workflow.start_date, userTimezone.timezone, 'MM/DD/YYYY');
        const TimeZonedEndDate = formatDateInTimezone(workflow.end_date, userTimezone.timezone, 'MM/DD/YYYY');

        return {
          _id: workflow._id?.toString(),
          id: workflow._id?.toString(),
          template_name: workflow.template_name,
          start_date: TimeZonedStartDate,
          end_date: TimeZonedEndDate,
          last_activity: getRelativeTimeString(workflow.updatedAt),
          // last_activity: getRelativeTimeStringWithTimezone(workflow.updatedAt, userTimezone.timezone),
          // last_activity: getRelativeTimeTimezoneShifted(workflow.updatedAt, "Asia/Kolkata", userTimezone.timezone),
          status: displayStatus,
          completed_tasks: completedTasksString,
          assigned_users: workflow.assigned_users || '',
          work_flow_runner: workflow.run_by || 'New Court Notice',
          assign_by: workflow.assign_by,
          matter: workflow.matter || '',
          attorney: workflow.attorney || '',
          ex_county_of_arrest: workflow.ex_county_of_arrest || '',
          case_number: workflow.case_number || '',
          notes: workflow.notes,
          last_task_id: workflow.last_task_id || '',
          isChild: workflow.isChild ? 'New Court Notice' : 'Court Notice Follow Up',
        };
      });

      return {
        total,
        page: pageNumber,
        limit: limitNumber,
        fields: mappedFields,
      };
    } catch (error) {
      this.logger.error(`Error in getCourtNoticeList service: ${error.message}`);
      throw error;
    }
  }


  private formatDate(date: Date | string): string {
    if (!date) return '';
    const d = new Date(date);
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const year = d.getFullYear();
    return `${month}/${day}/${year}`;
  }

  async updateWorkflowStatus(updateWorkflowDto) {
    try {
      // Step 1: Find the workflow execution record
      const workflowExecution = await this.workflowExecutionModel.findOne({
        _id: updateWorkflowDto.workflow_id,
        is_deleted: false,
        is_active: true,
      });

      if (!workflowExecution) {
        throw ErrorExceptions.WORK_FLOW_EXECUTION_NOT_FOUND();
      }

      await this.updateWorkflowExecutionStatusByExecutionId(
        updateWorkflowDto.workflow_id,
      );

      // Step 2: Get the template_id from the found record
      const templateId = workflowExecution.template_id;

      // Step 3: Find the task execution with the same template_id and workflow_execution_id
      const taskExecution = await this.taskExecutionModel
        .findOne({
          template_id: templateId,
          workflow_execution_id: updateWorkflowDto.workflow_id,
          is_deleted: false,
          is_active: true,
        })
        .sort({ sequence: -1 }) // Get the latest task execution
        .exec();

      if (!taskExecution) {
        throw new Error('Task execution not found');
      }

      await this.taskExecutionModel.updateMany(
        {
          task_id: taskExecution.task_id,
          workflow_execution_id: updateWorkflowDto.workflow_id,
        },
        {
          is_reviewed: true,
          task_visible_status: TASK_VISIBLE_STATUS.REVIEWED,
        },
      );
      if (!updateWorkflowDto.isChild) {
        // Step 4: Process derived fields to create separate tasks for each client
        const clientTasks = [];
        const allTaskIds = [];

        const valueAssign = taskExecution.formField.find(
          (field) => field.id === 'assign-section',
        );

        if (
          taskExecution.derived_field &&
          taskExecution.derived_field.length > 0
        ) {
          // Find the court notice derived field
          const courtNoticeField = taskExecution.derived_field.find(
            (field) => field.type === 'courtNotice',
          );

          if (
            courtNoticeField &&
            courtNoticeField.client &&
            courtNoticeField.client.length > 0
          ) {
            // Step 1: Create form components once (to be shared)
            const componentIds = await this.createFormComponents();

            // Step 2: Create tasks for each client
            for (const client of courtNoticeField.client) {
              const task = await this.createClientTask(
                client,
                templateId,
                workflowExecution,
                componentIds,
              );
              clientTasks.push(task);
              allTaskIds.push(task.task._id);
            }

            // Step 3: Now create a single template for all tasks
            const lastTemplate = await this.templateModel
              .findOne({})
              .sort({ template_id: -1 })
              .lean();

            const newTemplateId = lastTemplate?.template_id
              ? lastTemplate.template_id + 1
              : 1;

            // Fetch templateGroupId from configGroup collection
            const configGroup = await this.configGroupModel.findOne({ _id: new Types.ObjectId('6895a45628a84ede80d42fa3') });

            console.log(
              '🚀 ~ WorkflowService ~ updateWorkflowStatus dhyey ~ configGroup:',
              configGroup,
            );
            let templateGroupId = new Types.ObjectId('6877420fd4928f6a37ba1b95'); // default fallback
            if (configGroup) {
              // Use followupgroupid for court notice followup templates
              templateGroupId = configGroup.followupgroupid;
            }

            // Create a single template containing all task IDs
            const template = new this.templateModel({
              name: `Court Notice Followup - Multiple Clients`,
              description: 'court notice followup',
              template_id: newTemplateId,
              template_workflow_id: 'court-notice-workflow',
              category_id: new Types.ObjectId('6616fd1f60d4a4f8f8a7c1a3'), // Using default category
              task_id: allTaskIds, // Add all task IDs to the template
              parent_id: templateId,
              version: 1,
              status: Template_status.ON_TRACK,
              is_active: true,
              is_deleted: false,
              templateGroupId: templateGroupId,
            });

            const savedTemplate = await template.save();
            console.log(
              '🚀 ~ WorkflowService ~ updateWorkflowStatus ~ savedTemplate:',
              savedTemplate,
            );

            // Step 4: Create workflow executions for each client task
            for (let i = 0; i < clientTasks.length; i++) {
              const client = courtNoticeField.client[i];
              console.log(
                '🚀 ~ WorkflowService ~ updateWorkflowStatus ~ client:',
                client,
              );
              const task = clientTasks[i];

              // Get client ID
              let clientId;
              if (client.client_matter_id) {
                // We'll ignore the returned document, we just need the client ID
                const matterDetails = await this.matterModel
                  .findOne({ _id: client.client_matter_id })
                  .exec();
                console.log(
                  '🚀 ~ WorkflowService ~ updateWorkflowStatus ~ matterDetails:',
                  matterDetails,
                );
                clientId = matterDetails.client_id;
              } else {
                // If client_matter_id is not available, look up client by name
                const clientDoc = await this.clientModel
                  .findOne({ name: client.client_name })
                  .exec();
                clientId = clientDoc ? clientDoc._id : null;
              }

              const workflowExecutionObj = new this.workflowExecutionModel({
                template_id: savedTemplate._id, // Use the same template ID for all executions
                execution_id: Math.floor(Math.random() * 1000000),
                // start_date: new Date(),
                // end_date: new Date(new Date().getTime() + 24 * 60 * 60 * 1000),
                status: Work_flow_execution_status.ON_TRACK,
                last_activity: '1 day',
                assigns: [],
                assign_by: workflowExecution.assign_by,
                run_by: [clientId],
                parent_id: new Types.ObjectId(updateWorkflowDto.workflow_id),
                notes: '',
                is_manually_run: true,
                is_active: true,
                is_deleted: false,
              });

              const savedWorkflowExecution = await workflowExecutionObj.save();
              task.workflowExecution = savedWorkflowExecution;

              // Get the last task execution sequence number
              const lastTaskExecution = await this.taskExecutionModel
                .findOne({})
                .sort({ sequence: -1 })
                .lean();

              const newSequence = lastTaskExecution?.sequence
                ? lastTaskExecution.sequence + 1
                : 1;

              // Create task execution entry for this task
              function transformFormFieldForTaskExecution(formFieldInput) {
                if (!formFieldInput || !Array.isArray(formFieldInput)) {
                  return [];
                }

                return formFieldInput.map((field) => {
                  // Create the base structure that matches TaskExecution.formField schema
                  const transformedField = {
                    id: field.id,
                    type: field.type,
                    label: field.label,
                    required: field.required || false,
                    dynamic_fields: field.dynamic_fields || false,
                    form_field_id: field.form_field_id,
                    sequence: field.sequence,
                    dynamic_selected_task: field.dynamic_selected_task || false,
                    new_field_add: field.new_field_add || false,
                    condition: field.condition || undefined,
                    api_end_point: field.api_end_point,
                    fields: [], // Will be populated below
                  };

                  // Transform the fields property to match expected structure
                  if (field.fields && Array.isArray(field.fields)) {
                    transformedField.fields = field.fields.map(
                      (fieldItem, index) => {
                        console.log(
                          '🚀 ~ WorkflowService ~ updateWorkflowStatus ~ fieldItem:',
                          field.id,
                        );

                        // If field.id is 'assign-section', set value to clientMatterId
                        if (field.id == 'assign-section-01') {
                          return {
                            form_component_id: fieldItem,
                            value: [
                              {
                                id: '',
                                value:
                                  client.client_matter_id?.toString() || '',
                              },
                            ],
                          };
                        }

                        if (field.id == 'assign-section') {
                          // Handle multiselect dropdown - propagate all selected users
                          const originalAssignValues =
                            valueAssign?.fields[0]?.value || [];

                          // If it's a single user assignment (backward compatibility)
                          if (originalAssignValues.length === 1) {
                            return {
                              form_component_id: fieldItem,
                              value: [
                                {
                                  id: originalAssignValues[0]?.id || '',
                                  value: originalAssignValues[0]?.value || '',
                                },
                              ],
                            };
                          }

                          // If it's multiple users from multiselect dropdown
                          if (originalAssignValues.length > 1) {
                            return {
                              form_component_id: fieldItem,
                              value: originalAssignValues.map((user) => ({
                                id: user?.id || '',
                                value: user?.value || '',
                              })),
                            };
                          }

                          // Fallback for empty assignment
                          return {
                            form_component_id: fieldItem,
                            value: [
                              {
                                id: '',
                                value: '',
                              },
                            ],
                          };
                        }

                        // If field.id is 'confirm-sms-section', set predefined SMS text for first field only
                        if (field.id === 'confirm-sms-section') {
                          // First field (text area) gets the SMS text
                          if (index === 0) {
                            return {
                              form_component_id: fieldItem,
                              value: [
                                {
                                  id: '',
                                  value: `Dear ${client.client_name}, you have a scheduled court hearing on 07/04/2025 at 10:00 am your appearance requirement is TBD. In addition, you have another scheduled court hearing on 10/04/2025 at 9:00 am and your appearance is required for this event. If anything is to change of this, I'll be sure to let you know. Please let me know that you have received this message. Thank you!!`,
                                },
                              ],
                            };
                          }
                          // Second field (checkbox) gets empty value
                          else {
                            return {
                              form_component_id: fieldItem,
                              value: [
                                {
                                  id: '',
                                  value: '',
                                },
                              ],
                            };
                          }
                        }

                        // If the field is already in the expected format (with form_component_id and value properties)
                        if (
                          typeof fieldItem === 'object' &&
                          fieldItem?.form_component_id
                        ) {
                          return fieldItem;
                        }

                        // If the field is an ObjectId, transform it to expected format
                        return {
                          form_component_id: fieldItem, // This is the ObjectId
                          value: [
                            {
                              id: '', // Default empty id
                              value: '', // Default empty value
                            },
                          ],
                        };
                      },
                    );
                  }

                  return transformedField;
                });
              }

              // Function to transform derived field and set all event eventStatus to NEW
              function transformDerivedFieldForTaskExecution(
                derivedFieldInput,
              ) {
                if (!derivedFieldInput || !Array.isArray(derivedFieldInput)) {
                  return [];
                }

                return derivedFieldInput.map((derivedField) => {
                  // Create a deep copy of the derived field
                  const transformedDerivedField = {
                    ...derivedField,
                    client:
                      derivedField.client?.map((client) => ({
                        ...client,
                        matter:
                          client.matter?.map((matter) => ({
                            ...matter,
                            event:
                              matter.event?.map((event) => ({
                                ...event,
                                eventStatus: EventStatus.NEW,
                                isAddSecondary: true, // Set eventStatus to EventStatus.NEW for all events
                                // appointmentAction: EventStatus.NEW, // Set eventStatus to EventStatus.NEW for all events
                              })) || [],
                          })) || [],
                      })) || [],
                  };

                  return transformedDerivedField;
                });
              }

              // Example usage in your code:
              const taskExecutionData = {
                workflow_execution_id: savedWorkflowExecution._id,
                task_id: task.task._id,
                start_date: new Date(),
                end_date: new Date(new Date().getTime() + 24 * 60 * 60 * 1000),
                task_status: 'DRAFT',
                last_activity: '1 day',
                assigns: [], // Fixed: Changed back to array to match schema
                assign_by: workflowExecution.assign_by,
                notes: '',
                template_id: savedTemplate._id,
                sequence: newSequence,
                task_visible_status: TASK_VISIBLE_STATUS.IN_REVIEW,
                formField: transformFormFieldForTaskExecution(
                  task.task.formField,
                ),
                derived_field: transformDerivedFieldForTaskExecution(
                  task.task.derived_field || [],
                ),
                is_active: true,
                is_deleted: false,
                user_group_id: templateGroupId,
              };

              const taskExecution = new this.taskExecutionModel(
                taskExecutionData,
              );
              await taskExecution.save();

              console.log(
                `Created task execution with ID: ${taskExecution._id} for task: ${task.task._id}`,
              );

              // Automatically assign multiple users from secondary court notice to follow-up tasks
              if (
                valueAssign &&
                valueAssign.fields &&
                valueAssign.fields[0] &&
                valueAssign.fields[0].value
              ) {
                const originalAssignValues = valueAssign.fields[0].value;

                // Only proceed if there are multiple users assigned
                if (originalAssignValues.length > 1) {
                  try {
                    await this.assignMultipleUsersToFollowUpTasks(
                      taskExecution._id.toString(),
                      originalAssignValues,
                      workflowExecution.assign_by?.toString() || '',
                    );
                    console.log(
                      `Successfully assigned ${originalAssignValues.length} users to follow-up task ${taskExecution._id}`,
                    );
                  } catch (assignmentError) {
                    console.error(
                      `Error assigning users to follow-up task: ${assignmentError.message}`,
                    );
                    // Don't throw error here to avoid breaking the workflow creation
                  }
                }
              }
            }
          }
        }
      }

      let conditionObj = {};

      if (updateWorkflowDto.isChild) {
        conditionObj = { status: Work_flow_execution_status.ON_TRACK };
      } else {
        conditionObj = { status: Work_flow_execution_status.COMPLETED };
      }

      // Step 5: Update the corresponding template record
      const result = await this.workflowExecutionModel.updateOne(
        { _id: taskExecution?.workflow_execution_id },
        { $set: conditionObj },
      );

      await this.workflowExecutionModel.updateOne(
        { _id: taskExecution?.workflow_execution_id },
        { $set: { workflow_status: Work_flow_execution_status.COMPLETED } },
      );

      return {
        success: true,
        modifiedCount: result.modifiedCount,
        // clientTasks: clientTasks,
      };
    } catch (error) {
      this.logger.error(`Failed to update workflow status: ${error.message}`);
      throw error;
    }
  }

  // Helper method to create form components and return their IDs
  private async createFormComponents() {
    // Create the assign form component
    const selectedClientMatterComponent = new this.formComponentModel({
      name: '',
      label: 'Client/Matter',
      type: Form_type.SELECT,
      placeholder: '',
      form_field_id: 'case_priority_child',
      mandatory_field: true,
      skip_by_ai: false,
      is_active: true,
      api_end_point: "workflow/user-list?search=''&client_id=''",
      options: [
        {
          value: '6814be8b1646c7e96702ed3d',
          text: 'Amanda Burke | Amanda Burke | DWI BAC',
          client_id: '6814be8b1646c7e96702ed3d',
          is_active: true,
          client_name: 'Amanda Burke',
          id: 'Amanda Burke | DWI BAC',
        },
        {
          value: '68150007fb71128b4e2aa486',
          text: 'Amanda Jones | Amanda Jones (DWI 2nd - Lubbock) SA',
          client_id: '6814c1291646c7e96702ed44',
          is_active: true,
          client_name: 'Amanda Jones',
          id: 'Amanda Jones (DWI 2nd - Lubbock) SA',
        },
        {
          value: '68150001fb71128b4e2aa480',
          text: 'Joe Smith | Joe Smith (DWI 1st - Travis) SA',
          client_id: '6814be971646c7e96702ed3f',
          is_active: true,
          client_name: 'Joe Smith',
          id: 'Joe Smith (DWI 1st - Travis) SA',
        },
        {
          value: '68150003fb71128b4e2aa482',
          text: 'Lucas Butler | Lucas Butler | Theft under $750',
          client_id: '6814c1251646c7e96702ed40',
          is_active: true,
          client_name: 'Lucas Butler',
          id: 'Lucas Butler | Theft under $750',
        },
        {
          value: '68150002fb71128b4e2aa481',
          text: 'Jane Danovan | Jane Danovan (Assault - Family Violence - Harris) SA',
          client_id: '6814c1241646c7e96702ed3f',
          is_active: true,
          client_name: 'Jane Danovan',
          id: 'Jane Danovan (Assault - Family Violence - Harris) SA',
        },
        {
          value: '6815000afc71128b4e2aa489',
          text: 'Grace Meyer | Grace Meyer | Pre-Trial Hearing',
          client_id: '6814c12b1646c7e96702ed46',
          is_active: true,
          client_name: 'Grace Meyer',
          id: 'Grace Meyer | Pre-Trial Hearing',
        },
        {
          value: '68150004fb71128b4e2aa483',
          text: 'Joe Clark | Joe Clark (Felony Drug Possession - Dallas) SA',
          client_id: '6814c1261646c7e96702ed41',
          is_active: true,
          client_name: 'Joe Clark',
          id: 'Joe Clark (Felony Drug Possession - Dallas) SA',
        },
        {
          value: '68150005fb71128b4e2aa484',
          text: 'Mike Miler | Mike Miler | Burglary',
          client_id: '6814c1271646c7e96702ed42',
          is_active: true,
          client_name: 'Mike Miler',
          id: 'Mike Miler | Burglary',
        },
        {
          value: '68150006fb71128b4e2aa485',
          text: 'Mark Jones | Mark Jones (Probation Violation - El Paso) SA',
          client_id: '6814c1281646c7e96702ed43',
          is_active: true,
          client_name: 'Mark Jones',
          id: 'Mark Jones (Probation Violation - El Paso) SA',
        },
        {
          value: '68150008fb71128b4e2aa487',
          text: 'Jeffery Price | Jeffery Price (DWI 2nd - Lubbock) SA',
          client_id: '6814c12a1646c7e96702ed45',
          is_active: true,
          client_name: 'Jeffery Price',
          id: 'Jeffery Price (DWI 2nd - Lubbock) SA',
        },
      ],
    });

    await selectedClientMatterComponent.save();
    // Create the assign form component
    // Round robin assignment will be done later when creating the form field
    const assignComponent = new this.formComponentModel({
      name: '',
      label: 'Assign this workflow to',
      type: Form_type.SELECT,
      placeholder: '',
      form_field_id: 'assign_workflow_child',
      required: true,
      mandatory_field: true,
      skip_by_ai: false,
      value: [
        {
          id: '',
          value: 'System Default',
        },
      ],
      is_active: true,
      api_end_point: "workflow/user-list?search=''&client_id=''",
      options: [
        { value: 'Lisa Spencer', text: 'Lisa Spencer' },
        { value: 'John Doe', text: 'John Doe' },
        { value: 'Michael Johnson', text: 'Michael Johnson' },
        { value: 'Sarah Williams', text: 'Sarah Williams' },
      ],
    });
    await assignComponent.save();

    // Create the confirm checkbox form component
    const confirmComponent = new this.formComponentModel({
      name: '',
      label: 'Confirmed',
      type: 'checkbox',
      placeholder: '',
      form_field_id: 'confirm-notice-dates-child',
      mandatory_field: false,
      skip_by_ai: false,
      is_active: true,
    });
    await confirmComponent.save();

    // Create the confirm checkbox form component
    const confirmSMSComponent = new this.formComponentModel({
      name: '',
      label: 'I confirm that the SMS was sent',
      type: 'checkbox',
      placeholder: '',
      form_field_id: 'confirm-notice-dates-child',
      mandatory_field: false,
      skip_by_ai: false,
      is_active: true,
    });
    await confirmSMSComponent.save();

    // Create the radio button group form component
    const radioComponent = new this.formComponentModel({
      name: '',
      label:
        'Confirm with the client that they are aware of the court notice via Email, SMS or Phone.',
      type: 'radio_button_group',
      placeholder: '',
      form_field_id: 'confirm-client-child',
      mandatory_field: true,
      skip_by_ai: false,
      is_active: true,
      options: [
        { value: 'yes', text: 'Yes' },
        { value: 'no', text: 'No' },
      ],
    });
    await radioComponent.save();

    // Create the updatemycase button group form component
    const updateMyCaseComponent = new this.formComponentModel({
      name: '',
      label: 'Update MyCase',
      type: 'button',
      placeholder: '',
      form_field_id: 'update-mycases',
      mandatory_field: false,
      skip_by_ai: false,
      is_active: true,
      options: [],
    });
    await updateMyCaseComponent.save();

    // Create the text area form component
    const textAreaComponent = new this.formComponentModel({
      name: '',
      label: 'Enter your note here',
      type: 'text_area',
      placeholder: 'Enter notes below if any',
      form_field_id: 'client-first-name',
      mandatory_field: false,
      skip_by_ai: false,
      is_active: true,
    });
    await textAreaComponent.save();

    // Create the text area form component for SMS
    const textAreaSMSComponent = new this.formComponentModel({
      name: '',
      label: 'Send the following SMS via Kenect:',
      type: 'text_area',
      placeholder: 'Enter notes below if any',
      form_field_id: 'client-sms-name',
      mandatory_field: false,
      skip_by_ai: false,
      is_active: true,
    });
    await textAreaSMSComponent.save();

    return {
      assignComponentId: assignComponent._id,
      confirmComponentId: confirmComponent._id,
      radioComponentId: radioComponent._id,
      textAreaComponentId: textAreaComponent._id,
      textAreaSMSComponentId: textAreaSMSComponent._id,
      selectedClientMatterComponentId: selectedClientMatterComponent._id,
      confirmSMSComponentId: confirmSMSComponent._id,
      updateMyCaseComponentId: updateMyCaseComponent._id,
    };
  }

  // Helper method to create a task for a specific client
  private async createClientTask(
    client,
    templateId,
    workflowExecution,
    componentIds = null,
    userGroupId = '6877420fd4928f6a37ba1b95',
  ) {
    try {
      // Step 1: Use provided componentIds or create new ones if not provided
      const formComponentIds =
        componentIds || (await this.createFormComponents());

      const pendingState = await this.formComponentModel.findOne({
        type: 'pending_message',
      });
      const failedState = await this.formComponentModel.findOne({
        type: 'failed_message',
      });
      console.log('🚀 ~ pendingState:', pendingState);

      // Step 3: Prepare the derived field with only this client's data
      const derivedField = [
        {
          id: 'court-notice-section',
          type: 'courtNotice',
          label:
            'Please review the events below and update the missing information marked as TBD in calendar.',
          sequence: 3,
          matter_list_api: `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/matter-list`,
          condition: {},
          client: [client], // Include only the current client
        },
      ];

      const update_form_component = await this.formComponentModel.findOne({
        form_field_id: 'update-mycases',
      });

      console.log('🚀 ~ update_form_component:', update_form_component);

      // Get round robin assignment for this workflow using the specified user group
      // const roundRobinAssignment = await this.getNextRoundRobinAssignment(userGroupId);

      // Step 4: Prepare the formField structure
      const formField = [
        {
          id: 'assign-section-01',
          type: Form_type.SELECT,
          label: 'Client/Matter',
          sequence: 1,
          fields: [formComponentIds.selectedClientMatterComponentId],
        },
        {
          id: 'assign-section',
          type: Form_type.ASSIGN,
          label: 'Assign this workflow to',
          sequence: 2,
          required: true,
          fields: [formComponentIds.assignComponentId],
        },
        {
          id: 'update-section',
          type: Form_type.BUTTON,
          label:
            'If the information above is correct, submit below to update MyCase',
          dynamic_fields: true,
          sequence: 4,
          new_field_add: true,
          required: true,
          fields: [formComponentIds.updateMyCaseComponentId],
        },
        {
          id: 'success-section',
          type: 'success_message',
          label: 'Dates are successfully submitted in MyCase.',
          sequence: 5,
          new_field_add: true,
          condition: {
            field: formComponentIds.updateMyCaseComponentId,
            value: 'true',
          },
          fields: ['680777b0e1af8cdccbe00c1c'],
        },
        {
          id: 'pending-section',
          type: 'pending_message',
          label:
            'Updating MyCase taking longer than usual. This may take up to 30 seconds. Please wait.',
          sequence: 9,
          new_field_add: true,
          condition: {
            field: formComponentIds.updateMyCaseComponentId,
            value: 'pending',
          },
          fields: [pendingState._id],
        },
        {
          id: 'failed-section',
          type: 'failed_message',
          label: 'Failed to update MyCase.',
          sequence: 10,
          new_field_add: true,
          condition: {
            field: formComponentIds.updateMyCaseComponentId,
            value: 'failed',
          },
          fields: [failedState._id],
        },
        {
          id: 'confirm-dates-section',
          type: Form_type.CHECKBOX,
          label:
            'Please confirm that the events above are updated appropriately',
          dynamic_fields: false,
          required: true,
          sequence: 11,
          condition: {
            field: formComponentIds.updateMyCaseComponentId,
            value: 'failed',
          },
          fields: [formComponentIds.confirmComponentId],
        },
        {
          id: 'confirm-sms-section',
          type: Form_type.TEXT_AREA,
          dynamic_fields: false,
          label: 'Send the following SMS via Kenect:',
          condition: {
            field: formComponentIds.updateMyCaseComponentId,
            value: 'failed',
          },
          sequence: 12,
          fields: [
            formComponentIds.textAreaSMSComponentId,
            formComponentIds.confirmSMSComponentId,
          ],
        },
        {
          id: 'has-events-section',
          type: Form_type.RADIO_BUTTON_GROUP,
          dynamic_fields: false,
          label:
            'Confirm with the client that they are aware of the court notice via Email, SMS or Phone.',
          sequence: 13,
          condition: {
            field: formComponentIds.updateMyCaseComponentId,
            value: 'failed',
          },
          required: true,
          fields: [formComponentIds.radioComponentId],
        },
        {
          id: 'client-notes-section',
          type: Form_type.TEXT_AREA,
          dynamic_fields: false,
          condition: {
            field: formComponentIds.updateMyCaseComponentId,
            value: 'failed',
          },
          label: 'Enter notes below if any',
          sequence: 14,
          fields: [formComponentIds.textAreaComponentId],
        },
        {
          id: 'confirm-dates-section',
          type: Form_type.CHECKBOX,
          label:
            'Please confirm that the events above are updated appropriately',
          dynamic_fields: false,
          required: true,
          sequence: 5,
          condition: {
            field: formComponentIds.updateMyCaseComponentId,
            value: 'true',
          },
          fields: [formComponentIds.confirmComponentId],
        },
        {
          id: 'confirm-sms-section',
          type: Form_type.TEXT_AREA,
          dynamic_fields: false,
          label: 'Send the following SMS via Kenect:',
          condition: {
            field: formComponentIds.updateMyCaseComponentId,
            value: 'true',
          },
          sequence: 6,
          fields: [
            formComponentIds.textAreaSMSComponentId,
            formComponentIds.confirmSMSComponentId,
          ],
        },
        {
          id: 'has-events-section',
          type: Form_type.RADIO_BUTTON_GROUP,
          dynamic_fields: false,
          label:
            'Confirm with the client that they are aware of the court notice via Email, SMS or Phone.',
          sequence: 7,
          condition: {
            field: formComponentIds.updateMyCaseComponentId,
            value: 'true',
          },
          required: true,
          fields: [formComponentIds.radioComponentId],
        },
        {
          id: 'client-notes-section',
          type: Form_type.TEXT_AREA,
          dynamic_fields: false,
          condition: {
            field: formComponentIds.updateMyCaseComponentId,
            value: 'true',
          },
          label: 'Enter notes below if any',
          sequence: 8,
          fields: [formComponentIds.textAreaComponentId],
        },
      ];

      console.log('===========formField=============', formField);

      // Step 5: Create the task with strict object creation to match schema
      const taskData = {
        task_id: Math.floor(Math.random() * 1000000),
        name: `${client.client_name}`,
        description: '',
        icon: '',
        type: Task_type.FORM_COMPONENT,
        previous_node: 'task1',
        next_node: 'task2',
        default_task: true,
        formField: formField,
        derived_field: derivedField,
        is_active: true,
        is_deleted: false,
      };

      // Create a new task instance using the defined data
      const task = new this.taskModel(taskData);
      const savedTask = await task.save();

      // Return the task (template will be created once at the parent level)
      return { task: savedTask };
    } catch (error) {
      this.logger.error(`Failed to create client task: ${error.message}`);
      throw error;
    }
  }

  async getCountyList() {
    try {
      const response = await this.countyModel.find({});
      return { counties: response };
    } catch (error) {
      throw error;
    }
  }

  async getCourtLocationList() {
    try {
      const response = await this.courtLocationModel.find({});
      return { court_location: response };
    } catch (error) {
      throw error;
    }
  }

  async getAttendeesList(data) {
    try {
      const response = await this.attendeesModel.find({ type: data.type });
      return { attendees: response };
    } catch (error) {
      throw error;
    }
  }

  async getCourtNoticeType(): Promise<{ court_notice_type: any[] }> {
    try {
      const response = await this.CourtNoticeTypeModel.find({});
      return { court_notice_type: response };
    } catch (error) {
      throw error;
    }
  }

  async getMatterList(): Promise<{ matters: any[] }> {
    try {
      const matters = await this.matterModel.aggregate([
        { $match: { is_active: true, client_id: { $ne: null } } },
        {
          $lookup: {
            from: 'client',
            localField: 'client_id',
            foreignField: '_id',
            as: 'client',
          },
        },
        { $unwind: { path: '$client', preserveNullAndEmptyArrays: true } },
        {
          $project: {
            _id: { $toString: '$_id' },
            client_id: { $toString: '$client_id' },
            client_name: '$client.name',
            is_active: 1,
            ex_county_of_arrest: '$ex_county_of_arrest',
            case_number: '$case_number',
            my_case_matter_id: '$my_case_matter_id',
            name: {
              $concat: ['$client.name', ' | ', '$name'],
            },
            id: { $toString: '$name' },
            updatedAt: 1, // Include updated_at in projection
          },
        },
        {
          $sort: { updatedAt: -1 }, // Sort by updated_at in descending order
        },
      ]);

      return { matters };
    } catch (error) {
      this.logger.error(`Error in getMatterList service: ${error.message}`);
      throw error;
    }
  }

  /**
   * Search matters by name - can search by client name or matter name
   * @param search Search term to filter by
   * @returns Filtered list of matters
   */
  async searchMatterList(search: string) {
    try {
      // Define search pattern for case-insensitive partial matching
      const searchPattern = search ? new RegExp(search, 'i') : /.*/;

      // Find matters that match the search term in either client name or matter name
      const matters = await this.matterModel.aggregate([
        { $match: { is_active: true, client_id: { $ne: null } } },
        {
          $lookup: {
            from: 'client',
            localField: 'client_id',
            foreignField: '_id',
            as: 'client',
          },
        },
        { $unwind: { path: '$client', preserveNullAndEmptyArrays: true } },
        // Filter by either client name or matter name containing the search term
        {
          $match: {
            $or: [
              { 'client.name': { $regex: searchPattern } },
              // { name: { $regex: searchPattern } },
            ],
          },
        },
        {
          $project: {
            _id: { $toString: '$_id' },
            client_id: { $toString: '$client_id' },
            client_name: '$client.name',
            is_active: 1,
            name: {
              $concat: ['$client.name', ' | ', '$name'],
            },
            id: { $toString: '$name' },
          },
        },
      ]);

      return { matters };
    } catch (error) {
      this.logger.error(`Error in searchMatterList service: ${error.message}`);
      throw error;
    }
  }

  async getNextTaskId(
    task_id: string,
    work_flow_id: string,
  ): Promise<{ next_task_id: string | null }> {
    try {
      // Validate task_id is a valid ObjectId
      if (!Types.ObjectId.isValid(task_id)) {
        throw ErrorExceptions.INVALID_TASK_ID();
      }

      const taskObjectId = new Types.ObjectId(task_id);

      // Find the current task execution
      const currentTask = await this.taskExecutionModel.findOne({
        task_id: taskObjectId,
        workflow_execution_id: new Types.ObjectId(work_flow_id),
        is_deleted: false,
      });

      await this.taskExecutionModel.updateMany(
        {
          task_id: taskObjectId,
          workflow_execution_id: new Types.ObjectId(work_flow_id),
        },
        {
          is_reviewed: true,
          task_visible_status: TASK_VISIBLE_STATUS.REVIEWED,
        },
      );

      await this.updateWorkflowExecutionStatusByExecutionId(work_flow_id);

      if (!currentTask) {
        this.logger.error(`Task execution not found for ID: ${task_id}`);
        throw ErrorExceptions.WORK_FLOW_EXECUTION_NOT_FOUND();
      }

      this.logger.log(
        `Found current task with workflow execution ID: ${currentTask.workflow_execution_id}`,
      );

      // Find all tasks in the same workflow execution, sorted by sequence
      const allTasks = await this.taskExecutionModel
        .find({
          workflow_execution_id: new Types.ObjectId(
            currentTask.workflow_execution_id,
          ),
        })
        .sort({ sequence: 1 })
        .exec();

      this.logger.log(`Found ${allTasks.length} tasks in the workflow`);

      // Find the index of the current task
      const currentIndex = allTasks.findIndex(
        (t) => t.task_id.toString() === task_id,
      );

      if (currentIndex === -1) {
        this.logger.error(
          `Task ID ${task_id} not found in the workflow's tasks`,
        );
        return { next_task_id: null };
      }

      if (currentIndex === allTasks.length - 1) {
        this.logger.log(`Task ID ${task_id} is the last task in the workflow`);
        return { next_task_id: null };
      }

      // Get the next task
      const nextTask = allTasks[currentIndex + 1];
      const nextTaskId = nextTask.task_id.toString();

      if (nextTaskId && nextTaskId.trim() !== '') {
        await this.taskExecutionModel.updateOne(
          {
            task_id: new Types.ObjectId(nextTaskId),
            workflow_execution_id: new Types.ObjectId(work_flow_id),
            is_deleted: false,
            is_active: true,
          },
          {
            $set: {
              task_visible_status: TASK_VISIBLE_STATUS.IN_REVIEW,
              end_date: new Date(Date.now() + 1 * 60 * 1000), // current time + 1 minute
            },
          },
        );
        await this.workflowExecutionModel.updateOne(
          {
            _id: new Types.ObjectId(work_flow_id),
          },
          {
            $set: { last_task_id: new Types.ObjectId(nextTaskId) },
          },
        );
      }

      // Create an update object for the next task
      const updateFields: any = {};

      // Clone derived fields from current task to next task if current task has derived fields
      if (currentTask.derived_field && currentTask.derived_field.length > 0) {
        this.logger.log(
          `Copying derived fields from task ${task_id} to next task ${nextTaskId}`,
        );
        updateFields.derived_field = currentTask.derived_field;
        this.logger.log(`Successfully copied derived fields to next task`);
      } else {
        this.logger.log(`No derived fields found in current task to copy`);
      }

      // Handle formField array merging
      if (currentTask.formField && currentTask.formField.length > 0) {
        this.logger.log(
          `Processing formField array from task ${task_id} to next task ${nextTaskId}`,
        );

        // Get the next task's formField array to identify fields with new_field_add=true
        const nextTaskData = await this.taskExecutionModel.findOne({
          _id: nextTask._id,
        });

        if (
          nextTaskData &&
          nextTaskData.formField &&
          nextTaskData.formField.length > 0
        ) {
          // Identify form fields with new_field_add=true in next task
          const nextTaskSpecialFields = nextTaskData.formField.filter(
            (field) => field.new_field_add === true,
          );
          console.log(
            '🚀 ~ WorkflowService ~ nextTaskSpecialFields:',
            nextTaskSpecialFields,
          );

          // Get IDs of special fields to preserve
          const specialFieldIds = nextTaskSpecialFields.map(
            (field) => field.id,
          );

          // Get template information to access templateGroupId for round robin assignment
          const workflowExecution = await this.workflowExecutionModel
            .findById(currentTask.workflow_execution_id)
            .populate('template_id')
            .exec();
          
          const template = workflowExecution?.template_id as any;
          const templateGroupId = template?.templateGroupId?.toString() || '6877420fd4928f6a37ba1b95';
          
          // Create merged formField array:
          // 1. Include all fields from current task that don't have IDs matching special fields
          // 2. Include all special fields from next task
          const mergedFormFieldsPromises = [
            ...currentTask.formField.filter(
              (field) => !specialFieldIds.includes(field.id),
            ),
            ...nextTaskSpecialFields,
          ].map(async (field) => {
            // Log each field before processing
            // Handle assign-section fields with round robin assignment
            if (field.id === 'assign-section') {
              console.log('Processing assign-section field with round robin assignment');
              console.log('Using templateGroupId for round robin:', templateGroupId);
              
              // Get round robin assignment
              const roundRobinAssignment = await this.getNextRoundRobinAssignment(templateGroupId);
              
              // Create a deep copy of the field to avoid reference issues
              const updatedField = JSON.parse(JSON.stringify(field));
              
              // Update the main fields array
              updatedField.fields = updatedField.fields.map((fieldItem) => {
                console.log(
                  'Setting assign-section fieldItem with round robin assignment:',
                  roundRobinAssignment,
                );
                const updatedFieldItem = {
                  ...fieldItem,
                  value: roundRobinAssignment ? [roundRobinAssignment] : '',
                };
                return updatedFieldItem;
              });
              
              // If there's a __parentArray, update it as well to maintain consistency
              if (updatedField.__parentArray && Array.isArray(updatedField.__parentArray)) {
                updatedField.__parentArray = updatedField.__parentArray.map((parentItem) => {
                  if (parentItem.id === 'assign-section') {
                    return {
                      ...parentItem,
                      fields: parentItem.fields?.map((fieldItem) => ({
                        ...fieldItem,
                        value: roundRobinAssignment ? [roundRobinAssignment] : '',
                      })) || [],
                    };
                  }
                  return parentItem;
                });
              }
              
              return updatedField;
            }
            return field;
          });

          // Wait for all async operations to complete
          const mergedFormFields = await Promise.all(mergedFormFieldsPromises);
          
          // Additional processing to ensure all nested references are updated
          // This handles complex Mongoose subdocument structures
          const processedFormFields = mergedFormFields.map((field) => {
            if (field.id === 'assign-section') {
              // Get the round robin assignment value that was set
              const roundRobinValue = field.fields?.[0]?.value;
              
              if (roundRobinValue && Array.isArray(roundRobinValue) && roundRobinValue.length > 0) {
                // Create a complete deep copy and update all nested references
                const processedField = JSON.parse(JSON.stringify(field));
                
                // Function to recursively update assign-section values
                const updateAssignValues = (obj) => {
                  if (obj && typeof obj === 'object') {
                    if (Array.isArray(obj)) {
                      return obj.map(updateAssignValues);
                    }
                    
                    const updated = {};
                    for (const [key, value] of Object.entries(obj)) {
                      if (key === 'fields' && Array.isArray(value)) {
                        updated[key] = value.map((fieldItem) => {
                          if (fieldItem.form_component_id === '68076d77e1af8cdccbe00c16') {
                            // This is the assign field component, update its value
                            return {
                              ...fieldItem,
                              value: roundRobinValue,
                            };
                          }
                          return updateAssignValues(fieldItem);
                        });
                      } else {
                        updated[key] = updateAssignValues(value);
                      }
                    }
                    return updated;
                  }
                  return obj;
                };
                
                return updateAssignValues(processedField);
              }
            }
            return field;
          });

          console.log('🚀 ~ mergedFormFields:', JSON.stringify(processedFormFields, null, 3));

          // Log assign-section field value for debugging
          const assignSection = processedFormFields.find(
            (field) => field.id === 'assign-section',
          );
          if (
            assignSection &&
            assignSection.fields &&
            assignSection.fields[0]
          ) {
            console.log(
              '🚀 ~ assign-section value:',
              assignSection.fields[0].value,
            );
          }

          updateFields.formField = processedFormFields;
          this.logger.log(
            `Successfully merged formField arrays, preserving ${nextTaskSpecialFields.length} special fields`,
          );
        } else {
          // If next task doesn't have formField array or it's empty, simply copy from current task
          updateFields.formField = currentTask.formField;
          this.logger.log(
            `Copied all formField items from current task as next task had no formField data`,
          );
        }
      }

      // Debug log for assign-section field values  
      const assignSectionField = updateFields.formField?.find(field => field.id === 'assign-section');
      if (assignSectionField) {
        console.log('🚀 ~ assign-section field after processing:', JSON.stringify(assignSectionField, null, 2));
        if (assignSectionField.fields?.[0]?.value) {
          console.log('🚀 ~ assign-section value:', assignSectionField.fields[0].value);
        }
      }

      // Apply all updates to the next task in a single operation
      if (Object.keys(updateFields).length > 0) {
        await this.taskExecutionModel.updateOne(
          { _id: nextTask._id },
          { $set: updateFields },
        );
        this.logger.log(
          `Successfully updated next task with fields: ${Object.keys(updateFields).join(', ')}`,
        );
      }

      this.logger.log(`Next task ID is: ${nextTaskId}`);
      return { next_task_id: nextTaskId };
    } catch (error) {
      this.logger.error(`Error in getNextTaskId service: ${error.message}`);
      if (error instanceof RpcException) {
        throw error;
      }
      throw error;
    }
  }

  async updateWorkflowExecutionStatusByExecutionId(
    executionId: string,
  ): Promise<void> {
    const result = await this.workflowExecutionModel.updateOne(
      { _id: new Types.ObjectId(executionId) },
      {
        $set: {
          updatedAt: new Date(), // explicitly update updatedAt if needed
        },
      },
    );

    if (result.modifiedCount === 0) {
      throw ErrorExceptions.WORK_FLOW_EXECUTION_NOT_FOUND();
    }
  }

  /**
   * Creates a demo workflow for testing purposes
   * @param data Request data containing template_id, notes, and assigns
   * @returns Success status and workflow ID
   */
  async createDemoWorkflow(data: any) {
    try {
      const { template_id, notes, assigns, user_group_id } = data;

      // Validate template ID
      if (!Types.ObjectId.isValid(template_id)) {
        throw new RpcException('Invalid template id');
      }

      // Find the template
      const template = await this.templateModel
        .findById(new Types.ObjectId(template_id))
        .populate('task_id')
        .exec();

      if (!template) {
        throw new RpcException('Template not found');
      }

      // Use template's templateGroupId, fallback to provided user_group_id, then default
      const userGroupId = template.templateGroupId?.toString() || user_group_id;

      // Default assigns if not provided
      let assignArray = [];
      if (assigns && assigns.length > 0) {
        // Validate assigns are valid ObjectIds
        for (const assign of assigns) {
          if (!Types.ObjectId.isValid(assign)) {
            throw new RpcException('Invalid assign id');
          }
          assignArray.push(new Types.ObjectId(assign));
        }
      } else {
        // Get some clients to assign if none provided
        const clients = await this.clientModel.find().limit(2).exec();
        console.log('🚀 ~ createDemoWorkflow ~ clients:', clients);
        if (clients.length > 0) {
          assignArray = clients.map((client) => client._id);
        }
      }
      console.log(assignArray, 'assignArray');
      const pendingState = await this.formComponentModel.findOne({
        type: 'pending_message',
      });
      const failedState = await this.formComponentModel.findOne({
        type: 'failed_message',
      });

      // Get assign_by user
      const assignBy =
        assignArray.length > 0
          ? assignArray[0]
          : new Types.ObjectId('67f644975909b1b40b527650'); // Default assign_by ID from example

      // Generate a unique execution ID
      const executionId = Math.floor(Math.random() * 100000);

      const now = moment();
      const startDate = now.clone(); // important to clone!
      const endDate = now.clone().add(1, 'day');

      // Create a new workflow execution with structure matching the provided example
      const workflowExecutionObj = new this.workflowExecutionModel({
        template_id: template._id,
        execution_id: executionId,
        start_date: startDate,
        end_date: endDate,
        status: Work_flow_execution_status.PENDING,
        last_activity: '1 day',
        assigns: assignArray,
        assign_by: assignBy,
        run_by: [],
        notes: notes || 'Auto-generated execution',
        is_manually_run: true,
        is_active: true,
        is_deleted: false,
      });

      const savedWorkflowExecution = await workflowExecutionObj.save();

      // Shared variable for workflow_execution_id, start_date, end_date
      const workflowExecutionId = savedWorkflowExecution._id;

      const records = [
        {
          workflow_execution_id: workflowExecutionId,
          task_id: template.task_id[0]._id,
          start_date: startDate,
          end_date: endDate,
          task_status: 'DRAFT',
          last_activity: startDate,
          assigns: [],
          assign_by: new mongoose.Types.ObjectId('67f644975909b1b40b527650'),
          user_group_id: new mongoose.Types.ObjectId(userGroupId),
          notes: 'Task Execution Note 1',
          view_id: 1000,
          template_id: template_id,
          is_active: true,
          is_deleted: false,
          formField: [
            {
              id: 'instruction-section-1',
              type: 'instruction',
              label: 'Review the court notice and follow the steps below.',
              required: false,
              sequence: 1,
              fields: [
                {
                  form_component_id: new mongoose.Types.ObjectId(
                    '68076794e1af8cdccbe00c0e',
                  ),
                  value: [{ id: '', value: '' }],
                },
              ],
            },
            {
              id: 'assign-section',
              type: 'assign',
              label: 'Assign this workflow to',
              required: true,
              sequence: 2,
              fields: [
                {
                  form_component_id: new mongoose.Types.ObjectId(
                    '68076d77e1af8cdccbe00c16',
                  ),
                  value: [
                    (await this.getNextRoundRobinAssignment(userGroupId)) || {
                      id: '',
                      value: 'System Default',
                    },
                  ],
                },
              ],
            },
            {
              id: 'has-events-section',
              type: 'radio_button_group',
              label:
                'Do you have any events in the calendar in this court notice?',
              sequence: 3,
              dynamic_fields: true,
              required: true,
              fields: [
                {
                  form_component_id: new mongoose.Types.ObjectId(
                    '6807762ae1af8cdccbe00c18',
                  ),
                  value: [{ id: '', value: '' }],
                },
              ],
            },
            {
              id: 'confirm-dates-section',
              type: 'checkbox',
              label: 'Confirm that the date above are correct.',
              sequence: 10,
              required: true,
              new_field_add: true,
              condition: {
                field: '6807762ae1af8cdccbe00c18',
                value: 'yes',
              },
              fields: [
                {
                  form_component_id: new mongoose.Types.ObjectId(
                    '680778a9e1af8cdccbe00c1e',
                  ),
                  value: [{ id: '', value: '' }],
                },
              ],
            },
          ],
          derived_field: [
            {
              id: 'court-notice-section',
              type: 'courtNotice',
              label:
                'Review the court notice and ensure all the dates are correct.',
              sequence: 7,
              matter_list_api:
                '${process.env.NEXT_PUBLIC_BASE_URL}/workflow/matter-list}',
              condition: {
                field: '6807762ae1af8cdccbe00c18',
                value: 'yes',
              },
            },
          ],
          __v: 377,
          createdAt: startDate,
          updatedAt: startDate,
          default_task: true,
          selected_task: false,
          sequence: 1,
          isReviewed: true,
          is_reviewed: true,
          task_visible_status: 'IN_REVIEW',
        },
        {
          workflow_execution_id: workflowExecutionId,
          task_id: template.task_id[1]._id,
          start_date: startDate,
          end_date: endDate,
          task_status: 'DRAFT',
          last_activity: startDate,
          assigns: [],
          assign_by: new mongoose.Types.ObjectId('67f644975909b1b40b527650'),
          user_group_id: new mongoose.Types.ObjectId(userGroupId),
          notes: 'Task Execution Note 1',
          view_id: 1000,
          template_id: template_id,
          is_active: true,
          is_deleted: false,
          formField: [
            {
              id: 'instruction-section-1',
              type: 'instruction',
              label: 'Review the court notice and follow the steps below.',
              required: false,
              sequence: 1,
              fields: [
                {
                  form_component_id: new mongoose.Types.ObjectId(
                    '68076794e1af8cdccbe00c0e',
                  ),
                  value: [{ id: '', value: '' }],
                },
              ],
            },
            {
              id: 'assign-section',
              type: 'assign',
              label: 'Assign this workflow to',
              required: true,
              sequence: 2,
              fields: [
                {
                  form_component_id: new mongoose.Types.ObjectId(
                    '68076d77e1af8cdccbe00c16',
                  ),
                  value: [
                    {
                      id: '',
                      value: 'System Default',
                    },
                  ],
                },
              ],
            },
            {
              id: 'has-events-section',
              type: 'radio_button_group',
              label:
                'Do you have any events in the calendar in this court notice?',
              dynamic_fields: true,
              required: true,
              sequence: 3,
              fields: [
                {
                  form_component_id: new mongoose.Types.ObjectId(
                    '6807762ae1af8cdccbe00c18',
                  ),
                  value: [{ id: '', value: 'yes' }],
                },
              ],
            },
            {
              id: 'update-section',
              type: 'button',
              label:
                'If the information above is correct, submit below to update MyCase',
              dynamic_fields: true,
              sequence: 8,
              new_field_add: true,
              required: true,
              condition: {
                field: '6807762ae1af8cdccbe00c18',
                value: 'yes',
              },
              fields: [
                {
                  form_component_id: new mongoose.Types.ObjectId(
                    '680776cfe1af8cdccbe00c1a',
                  ),
                  value: [{ id: '', value: '' }],
                },
              ],
            },
            {
              id: 'pending-section',
              type: 'pending_message',
              label:
                'Updating MyCase taking longer than usual. This may take up to 30 seconds. Please wait.',
              sequence: 9,
              new_field_add: true,
              condition: {
                field: '680776cfe1af8cdccbe00c1a',
                value: 'pending',
              },
              fields: [
                {
                  form_component_id: new mongoose.Types.ObjectId(
                    pendingState._id,
                  ),
                  value: [{ id: '', value: '' }],
                },
              ],
            },
            {
              id: 'success-section',
              type: 'success_message',
              label: 'Dates are successfully submitted in MyCase.',
              sequence: 10,
              new_field_add: true,
              condition: {
                field: '680776cfe1af8cdccbe00c1a',
                value: 'true',
              },
              fields: [
                {
                  form_component_id: new mongoose.Types.ObjectId(
                    '680777b0e1af8cdccbe00c1c',
                  ),
                  value: [{ id: '', value: '' }],
                },
              ],
            },
            {
              id: 'confirm-dates-section',
              type: 'checkbox',
              label:
                'Confirm that the court notice dates above and the court notice files are entered in MyCase appropriately, if not enter manually. Click here to access MyCase.',
              sequence: 11,
              new_field_add: true,
              condition: {
                field: '680776cfe1af8cdccbe00c1a',
                value: 'true',
              },
              fields: [
                {
                  form_component_id: new mongoose.Types.ObjectId(
                    '680778a9e1af8cdccbe00c1e',
                  ),
                  value: [{ id: '', value: '' }],
                },
              ],
            },
            {
              id: 'failed-section',
              type: 'failed_message',
              label: 'Failed to update MyCase.',
              sequence: 12,
              new_field_add: true,
              condition: {
                field: '680776cfe1af8cdccbe00c1a',
                value: 'failed',
              },
              fields: [
                {
                  form_component_id: new mongoose.Types.ObjectId(
                    failedState._id,
                  ),
                  value: [{ id: '', value: '' }],
                },
              ],
            },
            {
              id: 'confirm-dates-section-failed',
              type: 'checkbox',
              label:
                'Please update MyCase calendar and upload court notice file manually. Click here to access MyCase.',
              sequence: 13,
              new_field_add: true,
              condition: {
                field: '680776cfe1af8cdccbe00c1a',
                value: 'failed',
              },
              fields: [
                {
                  form_component_id: new mongoose.Types.ObjectId(
                    '680778a9e1af8cdccbe00c1e',
                  ),
                  value: [{ id: '', value: '' }],
                },
              ],
            },
          ],
          derived_field: [
            {
              id: 'court-notice-section',
              type: 'courtNotice',
              label:
                'Please review the events below and update the missing information marked as TBD in calendar.',
              sequence: 7,
              matter_list_api:
                '${process.env.NEXT_PUBLIC_BASE_URL}/workflow/matter-list}',
              condition: {
                field: '6807762ae1af8cdccbe00c18',
                value: 'yes',
              },
            },
          ],
          __v: 229,
          createdAt: startDate,
          updatedAt: startDate,
          default_task: true,
          selected_task: false,
          sequence: 2,
          isReviewed: true,
          is_reviewed: true,
          task_visible_status: 'DRAFT',
        },
      ];

      await this.taskExecutionModel.insertMany(records);
      console.log('2 Task Execution records inserted.');

      return {
        success: true,
        workflow_id: savedWorkflowExecution._id.toString(),
        message: 'Demo workflow created successfully',
      };
    } catch (error) {
      this.logger.error(`Failed to create demo workflow: ${error.message}`);
      throw error;
    }
  }

  /**
   * Updates the end date for a workflow task
   * @param updateWorkflowEndDateDto Data containing workflow_id, task_id, and end_date
   * @returns Success status and message
   */
  async updateWorkflowEndDate(updateWorkflowEndDateDto: any) {
    try {
      const { workflow_id, task_id, end_date } = updateWorkflowEndDateDto;

      // Validate that workflow exists
      const workflowExecution = await this.workflowExecutionModel.findOne({
        _id: new Types.ObjectId(workflow_id),
        is_deleted: false,
        is_active: true,
      });

      if (!workflowExecution) {
        throw ErrorExceptions.WORK_FLOW_EXECUTION_NOT_FOUND();
      }

      // Find the task execution
      const taskExecution = await this.taskExecutionModel.findOne({
        workflow_execution_id: new Types.ObjectId(workflow_id),
        task_id: new Types.ObjectId(task_id),
        is_deleted: false,
        is_active: true,
      });

      if (!taskExecution) {
        throw ErrorExceptions.TASK_NOT_FOUND();
      }

      // Parse the date and set it to the correct format
      let parsedDate;
      try {
        // Try to parse the date from various formats
        if (end_date.includes(' ')) {
          // Format like "05/15/25 10:00 AM"
          const [datePart, timePart, periodPart] = end_date.split(' ');

          let date, time, period;
          if (
            periodPart.toUpperCase().includes('AM') ||
            periodPart.toUpperCase().includes('PM')
          ) {
            date = datePart;
            period = periodPart;
            time = timePart;
          } else {
            // Format without period
            date = datePart;
            time = timePart;
            period = '';
          }

          // Parse date (MM/DD/YY format)
          const [month, day, year] = date.split('/').map(Number);

          // Parse time (HH:MM format)
          const [hours, minutes] = time.split(':').map(Number);

          // Adjust hours for PM if needed
          let adjustedHours = hours;
          if (period && period.toUpperCase() === 'PM' && hours < 12) {
            adjustedHours = hours + 12;
          } else if (period && period.toUpperCase() === 'AM' && hours === 12) {
            adjustedHours = 0;
          }

          // Create Date object (assuming 20xx for two-digit years)
          const fullYear = year < 100 ? 2000 + year : year;
          parsedDate = new Date(
            fullYear,
            month - 1,
            day,
            adjustedHours,
            minutes,
          );
        } else {
          // Try to parse as ISO format or other standard format
          parsedDate = new Date(end_date);
        }

        if (isNaN(parsedDate.getTime())) {
          throw new Error('Invalid date format');
        }
      } catch (error) {
        this.logger.error(
          `Failed to parse date: ${end_date}, error: ${error.message}`,
        );
        throw new Error(`Invalid date format: ${end_date}`);
      }

      // Update both the workflow execution and task execution end dates
      await this.workflowExecutionModel.updateOne(
        { _id: new Types.ObjectId(workflow_id) },
        {
          $set: {
            end_date: parsedDate,
            updatedAt: new Date(),
          },
        },
      );

      await this.taskExecutionModel.updateOne(
        {
          workflow_execution_id: new Types.ObjectId(workflow_id),
          task_id: new Types.ObjectId(task_id),
        },
        {
          $set: {
            end_date: parsedDate,
            updatedAt: new Date(),
          },
        },
      );

      return {
        success: true,
        message: 'End date updated successfully',
      };
    } catch (error) {
      this.logger.error(`Failed to update workflow end date: ${error.message}`);
      throw error;
    }
  }
  /**
   * Get a presigned URL for a given key
   * @param data Object containing the key for which to generate a presigned URL
   * @returns Object containing the presigned URL and a success message
   */
  async getPresignedUrl(data: any) {
    try {
      const url = await this.s3Service.generatePresignedUrl(
        data.key,
        data.operation as 'get' | 'put',
        data.contentType,
      );
      return {
        url,
        message: 'success',
      };
    } catch (error) {
      this.logger.error(`Error generating presigned URL: ${error.message}`);
      throw error;
    }
  }

  /**
   * Cleanup uploaded files from S3 when user cancels or closes modal without saving
   * @param data Object containing array of file keys to delete
   * @returns Object containing success status and message
   */
  async cleanupUploadedFiles(data: { fileKeys: string[] }) {
    try {
      const { fileKeys } = data;

      if (!fileKeys || !Array.isArray(fileKeys) || fileKeys.length === 0) {
        return {
          success: true,
          message: 'No files to cleanup',
          deletedCount: 0,
        };
      }

      this.logger.log(`Starting cleanup of ${fileKeys.length} files from S3`);

      const deletePromises = fileKeys.map(async (fileKey) => {
        try {
          if (fileKey && fileKey.trim()) {
            await this.s3Service.deleteFile(fileKey.trim());
            this.logger.log(`Successfully deleted file: ${fileKey}`);
            return { key: fileKey, success: true };
          }
          return { key: fileKey, success: false, error: 'Empty file key' };
        } catch (error) {
          this.logger.error(
            `Failed to delete file ${fileKey}: ${error.message}`,
          );
          return { key: fileKey, success: false, error: error.message };
        }
      });

      const results = await Promise.all(deletePromises);
      const successCount = results.filter((r) => r.success).length;
      const failedCount = results.filter((r) => !r.success).length;

      this.logger.log(
        `Cleanup completed: ${successCount} successful, ${failedCount} failed`,
      );

      return {
        success: true,
        message: `Cleanup completed: ${successCount} files deleted, ${failedCount} failed`,
        deletedCount: successCount,
        failedCount: failedCount,
        results: results,
      };
    } catch (error) {
      this.logger.error(`Error during file cleanup: ${error.message}`);
      throw error;
    }
  }

  async archiveWorkFlow(data) {
    try {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const {
        work_flow_execution_id,
        mycase_access_token,
        error_message,
        work_child_flow_execution_id,
        increment_retry = false,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        retry_count: _retry_count = 0,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        last_error: _last_error,
      } = data;

      // If increment_retry is true, increment the retry count and update error info
      if (increment_retry) {
        const workflowExecution = await this.workflowExecutionModel.findById(
          work_flow_execution_id,
        );
        if (workflowExecution) {
          const now = new Date();
          const retryCount =
            (workflowExecution.mycase_archive_retry_count || 0) + 1;

          await this.workflowExecutionModel.updateOne(
            { _id: work_flow_execution_id },
            {
              $set: {
                mycase_archive_retry_count: retryCount,
                mycase_archive_last_error: error_message,
                mycase_archive_last_attempt: now,
                mycase_archive_status: 'FAILED',
              },
            },
          );

          return {
            success: false,
            mycase_archive_retry_count: retryCount,
            error: error_message,
          };
        }
      }

      if (data.type == 'archive') {
        const workflowExecution = await this.workflowExecutionModel.findOne({
          _id: work_flow_execution_id,
        });

        const archive_by = data.archive_by;
        const archive_at = data.archive_at;

        const result = await this.workflowExecutionModel.updateOne(
          { _id: work_flow_execution_id },
          {
            $set: {
              is_archive: !workflowExecution.is_archive,
              archive_by: archive_by,
              archive_at: archive_at,
            },
          },
        );
        return {
          success: result.modifiedCount > 0,
          mycase_archive_retry_count:
            workflowExecution.mycase_archive_retry_count || 0,
        };
      } else if (data.type == 'complete') {
        const archive_by = data.archive_by;
        const archive_at = data.archive_at;

        const result = await this.workflowExecutionModel.updateOne(
          { _id: work_flow_execution_id },
          {
            $set: {
              status: Work_flow_execution_status.COMPLETED,
              workflow_status: Work_flow_execution_status.COMPLETED,
              archive_by: archive_by,
              archive_at: archive_at,
            },
          },
        );

        await this.taskExecutionModel.updateOne(
          {
            workflow_execution_id: work_flow_execution_id,
            task_id: data.task_id,
          },
          {
            $set: {
              task_visible_status: TASK_VISIBLE_STATUS.IN_REVIEW,
            },
          },
        );

        const workflowExecution = await this.workflowExecutionModel.findOne({
          _id: work_flow_execution_id,
        });

        return {
          success: result.modifiedCount > 0,
          mycase_archive_retry_count:
            workflowExecution.mycase_archive_retry_count || 0,
        };
      } else if (data.type == 'reactive') {
        const taskExecutions = await this.taskExecutionModel
          .find({
            workflow_execution_id: work_flow_execution_id,
          })
          .sort({ sequence: 1 }); // 1 for ascending, -1 for descending

        const result = await this.workflowExecutionModel.updateOne(
          { _id: work_flow_execution_id },
          {
            $set: {
              status: Work_flow_execution_status.PENDING,
              workflow_status: Work_flow_execution_status.PENDING,
              last_task_id: taskExecutions[0]?.task_id,
            },
          },
        );

        const workflowExecution = await this.workflowExecutionModel.findOne({
          _id: work_flow_execution_id,
        });

        return {
          success: result.modifiedCount > 0,
          mycase_archive_retry_count:
            workflowExecution.mycase_archive_retry_count || 0,
        };
      } else if (data.type == 'updatecase') {
        console.log('🚀 ~ archiveWorkFlow ~ data:', data);
        try {
          // Find the workflow execution record
          const workflowExecution = await this.workflowExecutionModel.findById(
            work_flow_execution_id,
          );

          if (!workflowExecution) {
            throw new Error('Workflow execution not found');
          }

          // Find the last task execution for this workflow (highest sequence number)
          const lastTaskExecution = await this.taskExecutionModel

            .findOne({
              workflow_execution_id: work_flow_execution_id,
            })
            .sort({ sequence: -1 }) // Get the task with highest sequence (last task)
            .exec();

          if (!lastTaskExecution) {
            this.logger.warn(
              `No task execution found for workflow: ${work_flow_execution_id}`,
            );
            return {
              success: false,
              message: 'No task execution found for this workflow',
            };
          }

          let totalProcessedEvents = 0;
          let totalSuccessfulEvents = 0;
          let totalFailedEvents = 0;
          const eventResults = [];

          // Process only the last task execution to collect events and create them in MyCase
          if (lastTaskExecution.derived_field?.length > 0) {
            for (const derivedField of lastTaskExecution.derived_field) {
              for (const client of derivedField.client || []) {
                for (const matter of client.matter || []) {
                  if (matter.event && matter.event.length > 0) {
                    // Process ALL events - check if they need create or update in MyCase
                    for (let i = 0; i < matter.event.length; i++) {
                      const event = matter.event[i];

                      const courtLocation = await this.courtLocationModel.find({
                        value: event.courtLocation,
                      });
                      const my_case_location_id =
                        courtLocation.length > 0
                          ? courtLocation[0].my_case_id
                          : null;

                      totalProcessedEvents++;

                      try {
                        // Get MyCase matter ID from the matter details
                        const matterDetails = await this.matterModel.findById(
                          client.client_matter_id,
                        );

                        const myCaseMatterId = matterDetails?.my_case_matter_id;
                        const tenantId = matterDetails?.firm_uuid; // Get tenant ID for timezone configuration

                        if (!myCaseMatterId) {
                          this.logger.warn(
                            `No MyCase matter ID found for matter: ${matter._id}`,
                          );
                          // Update event status to SYNC_FAILED
                          matter.event[i].eventStatus = EventStatus.SYNC_FAILED;
                          totalFailedEvents++;

                          // Update task execution event status to FAILED
                          await this.taskExecutionModel.updateOne(
                            {
                              _id: lastTaskExecution._id,
                              'derived_field.client.matter.event.id': event.id,
                            },
                            {
                              $set: {
                                'derived_field.$[].client.$[].matter.$[].event.$[evt].eventStatus':
                                  EventStatus.SYNC_FAILED,
                              },
                            },
                            {
                              arrayFilters: [{ 'evt.id': event.id }],
                            },
                          );

                          eventResults.push({
                            eventId: event.id,
                            status: 'failed',
                            error: 'No MyCase matter ID found',
                          });
                          continue;
                        }

                        // Check if event already has my_case_event_id
                        const hasExistingMyCaseId =
                          event.my_case_event_id &&
                          event.my_case_event_id.trim() !== '';

                        let myCaseResult;

                        console.log('🚀 ~ workflow.service.ts:5692 ~ archiveWorkFlow dp1 ~ hasExistingMyCaseId:', hasExistingMyCaseId);
                        
                        if (hasExistingMyCaseId) {
                          // Event exists in MyCase - call update API
                          this.logger.log(
                            `Updating existing event ${event.id} in MyCase with ID: ${event.my_case_event_id}`,
                          );
                          console.log(
                            '🚀 ~ workflow.service.ts:5531 ~ archiveWorkFlow ~ event:',
                            event,
                          );

                          myCaseResult =
                            await this.myCaseService.updateEventInMyCaseWithDocumentUpdate(
                              event,
                              event.my_case_event_id,
                              my_case_location_id,
                              myCaseMatterId.toString(),
                              work_flow_execution_id,
                              mycase_access_token,
                              undefined, // prevParentTaskExecutionEvents
                              
                            );
                        } else {
                          // Event doesn't exist in MyCase - call create API
                          this.logger.log(
                            `Creating new event ${event.id} in MyCase`,
                          );

                          // Handle cancelled events differently
                          if (event.isCancel) {
                            this.logger.log(
                              `Skipping creation of cancelled event ${event.id} in MyCase`,
                            );
                            myCaseResult = { success: true, skipped: true };
                          } else {
                            myCaseResult =
                              await this.myCaseService.createEventInMyCase(
                                event,
                                my_case_location_id,
                                myCaseMatterId.toString(),
                                work_flow_execution_id,
                                mycase_access_token,
                              
                              );
                          }
                        }

                        this.logger.log(
                          '🚀 ~ archiveWorkFlow ~ myCaseResult:',
                          myCaseResult || 'No result returned',
                        );

                        // Ensure myCaseResult exists before accessing properties
                        if (!myCaseResult) {
                          throw new Error(
                            `Failed to get result for event ${event.id} operation`,
                          );
                        }

                        if (myCaseResult.success) {
                          // MyCase operation successful - update status to SYNCED
                          matter.event[i].eventStatus = EventStatus.SYNCED;

                          // Set my_case_event_id if it's a create operation or if returned from update
                          if (myCaseResult.myCaseEventId) {
                            matter.event[i].my_case_event_id =
                              myCaseResult.myCaseEventId;
                          }

                          totalSuccessfulEvents++;

                          eventResults.push({
                            eventId: event.id,
                            status: 'success',
                            operation: hasExistingMyCaseId
                              ? 'update'
                              : 'create',
                            myCaseEventId:
                              myCaseResult.myCaseEventId ||
                              event.my_case_event_id,
                          });

                          this.logger.log(
                            `Successfully ${hasExistingMyCaseId ? 'updated' : 'created'} event ${event.id} in MyCase${myCaseResult.myCaseEventId ? ` with ID: ${myCaseResult.myCaseEventId}` : ''}`,
                          );
                        } else {
                          // MyCase operation failed - update status to SYNC_FAILED
                          matter.event[i].eventStatus = EventStatus.SYNC_FAILED;
                          totalFailedEvents++;

                          // Update task execution event status to FAILED
                          await this.taskExecutionModel.updateOne(
                            {
                              _id: lastTaskExecution._id,
                              'derived_field.client.matter.event.id': event.id,
                            },
                            {
                              $set: {
                                'derived_field.$[].client.$[].matter.$[].event.$[evt].eventStatus':
                                  EventStatus.SYNC_FAILED,
                              },
                            },
                            {
                              arrayFilters: [{ 'evt.id': event.id }],
                            },
                          );

                          eventResults.push({
                            eventId: event.id,
                            status: 'failed',
                            operation: hasExistingMyCaseId
                              ? 'update'
                              : 'create',
                            error: myCaseResult.error,
                          });

                          this.logger.error(
                            `Failed to ${hasExistingMyCaseId ? 'update' : 'create'} event ${event.id} in MyCase: ${myCaseResult.error}`,
                          );
                        }
                      } catch (error) {
                        // Exception occurred - update status to SYNC_FAILED
                        matter.event[i].eventStatus = EventStatus.SYNC_FAILED;
                        totalFailedEvents++;

                        // Update task execution event status to FAILED
                        await this.taskExecutionModel.updateOne(
                          {
                            _id: lastTaskExecution._id,
                            'derived_field.client.matter.event.id': event.id,
                          },
                          {
                            $set: {
                              'derived_field.$[].client.$[].matter.$[].event.$[evt].eventStatus':
                                EventStatus.SYNC_FAILED,
                            },
                          },
                          {
                            arrayFilters: [{ 'evt.id': event.id }],
                          },
                        );

                        const hasExistingMyCaseId =
                          event.my_case_event_id &&
                          event.my_case_event_id.trim() !== '';

                        eventResults.push({
                          eventId: event.id,
                          status: 'failed',
                          operation: hasExistingMyCaseId ? 'update' : 'create',
                          error: error.message,
                        });

                        this.logger.error(
                          `Exception while ${hasExistingMyCaseId ? 'updating' : 'creating'} event ${event.id} in MyCase: ${error.message}`,
                        );
                      }
                    }
                  }
                }
              }
            }
          }

          // Save updated last task execution with new event statuses
          const taskUpdateResult = await this.taskExecutionModel.updateOne(
            { _id: lastTaskExecution._id },
            { $set: { derived_field: lastTaskExecution.derived_field } },
          );

          // Update mycaseClientMatterModel for all processed events
          const mycaseUpdatePromises = eventResults.map(async (result) => {
            try {
              const updateData = {
                eventStatus:
                  result.status === 'success'
                    ? EventStatus.SYNCED
                    : EventStatus.SYNC_FAILED,
                'event.eventStatus':
                  result.status === 'success'
                    ? EventStatus.SYNCED
                    : EventStatus.SYNC_FAILED,
              };

              // Add MyCase event ID if successful
              if (result.status === 'success' && result.myCaseEventId) {
                updateData['event.my_case_event_id'] = result.myCaseEventId;
                updateData['my_case_event_id'] = result.myCaseEventId;
              }

              const eventUpdateResult =
                await this.mycaseClientMatterModel.updateMany(
                  {
                    event_id: result.eventId,
                    workflow_execution_id: new Types.ObjectId(
                      work_flow_execution_id,
                    ),
                  },
                  { $set: updateData },
                );

              this.logger.log(
                `Updated MycaseClientMatter for event_id: ${result.eventId}, status: ${result.status}, modified: ${eventUpdateResult.modifiedCount}`,
              );

              return eventUpdateResult.modifiedCount;
            } catch (error) {
              this.logger.error(
                `Error updating mycaseClientMatterModel for event_id ${result.eventId}: ${error.message}`,
              );
              return 0;
            }
          });

          // Run MyCase updates
          const mycaseUpdateResults = await Promise.all(mycaseUpdatePromises);

          const successfulMycaseUpdates = mycaseUpdateResults.reduce(
            (sum, count) => sum + count,
            0,
          );

          // Calculate operation statistics
          const createOperations = eventResults.filter(
            (r) => r.operation === 'create',
          ).length;
          const updateOperations = eventResults.filter(
            (r) => r.operation === 'update',
          ).length;
          const successfulCreates = eventResults.filter(
            (r) => r.operation === 'create' && r.status === 'success',
          ).length;
          const successfulUpdates = eventResults.filter(
            (r) => r.operation === 'update' && r.status === 'success',
          ).length;
          const failedCreates = eventResults.filter(
            (r) => r.operation === 'create' && r.status === 'failed',
          ).length;
          const failedUpdates = eventResults.filter(
            (r) => r.operation === 'update' && r.status === 'failed',
          ).length;

          this.logger.log(
            `Processed ${totalProcessedEvents} events from last task execution: ${totalSuccessfulEvents} successful (${successfulCreates} created, ${successfulUpdates} updated), ${totalFailedEvents} failed (${failedCreates} create failures, ${failedUpdates} update failures)`,
          );

          this.logger.log(
            `Updated task execution and ${successfulMycaseUpdates} mycaseClientMatter records,${work_flow_execution_id}`,
          );
          // Step: Process EventActionHistory with PENDING status
          // const pendingEventActions = await this.mycaseClientMatterModel.find({
          //   eventStatus: EventStatus.PENDING,
          //   $or: [
          //     { workflow_execution_id: work_flow_execution_id },
          //     { update_event_id: { $in: [work_flow_execution_id.toString()] } }, // check if array contains this string
          //   ],
          // });
          const pendingEventActions = await this.eventActionHistoryModel.find({
            eventStatus: EventStatus.PENDING,
            $and: [{ workflow_execution_id: work_flow_execution_id }],
          });
          console.log(
            '🚀 ~ archiveWorkFlow ~ pendingEventActions:',
            pendingEventActions,
          );

          if (pendingEventActions.length > 0) {
            this.logger.log(
              `Found ${pendingEventActions.length} pending event actions to sync with MyCase.`,
            );

            for (const action of pendingEventActions) {
              console.log('🚀 ~ archiveWorkFlow ~ action:', action);
              try {
                const eventId = action.event_id;

                // Find the MycaseClientMatter for the event
                const mycaseRecord = await this.mycaseClientMatterModel.findOne(
                  {
                    event_id: eventId,
                  },
                );
                console.log(
                  '🚀 ~ archiveWorkFlow ~ mycaseRecord:',
                  mycaseRecord,
                );

                if (!mycaseRecord || !mycaseRecord.my_case_event_id) {
                  this.logger.warn(
                    `MyCase record not found or missing my_case_event_id for event_id: ${eventId}`,
                  );
                  continue;
                }

                const oldSubject = mycaseRecord.event?.subject || '';
                const updatedSubject = `*${action.action.toUpperCase()}* ${oldSubject}`;
                console.log(
                  '🚀 ~ archiveWorkFlow ~ updatedSubject:',
                  updatedSubject,
                );

                // Update the subject in the mycaseRecord.event object
                mycaseRecord.event.subject = updatedSubject;

                const updatedEvent: EventSub = {
                  ...mycaseRecord.event,
                  subject: updatedSubject, // Explicitly set the subject to ensure it's updated
                };

                console.log(
                  '🚀 ~ archiveWorkFlow ~ updatedEvent*****:',
                  updatedEvent,
                );

                const matterDetails = await this.matterModel.findById(
                  action?.client_matter_id,
                );

                const courtLocation = await this.courtLocationModel.find({
                  value: mycaseRecord.event.courtLocation,
                });
                const my_case_location_id =
                  courtLocation.length > 0 ? courtLocation[0].my_case_id : null;

                console.log(
                  '🚀 HERECODING~ archiveWorkFlow ~ updatedEvent*****:',
                  updatedEvent,
                );

                const updateResult =
                  await this.myCaseService.updateEventInMyCase(
                    updatedEvent,
                    mycaseRecord.my_case_event_id,
                    my_case_location_id,
                    matterDetails?.my_case_matter_id,
                    mycaseRecord.workflow_execution_id.toString(),
                    mycase_access_token,
                    
                  );

                if (updateResult.success) {
                  // Update EventActionHistory → SYNCED
                  const result = await this.eventActionHistoryModel.updateMany(
                    { event_id: action.event_id },
                    { $set: { eventStatus: EventStatus.SYNCED } },
                  );

                  this.logger.log(
                    `Updated ${result.modifiedCount} EventActionHistory records for event ${action.event_id}`,
                  );

                  // Update MycaseClientMatter → updated subject + status
                  const result2 = await this.mycaseClientMatterModel.updateOne(
                    { _id: mycaseRecord._id },
                    {
                      $set: {
                        'event.subject': updatedSubject,
                        eventStatus: EventStatus.SYNCED,
                        'event.eventStatus': EventStatus.SYNCED,
                      },
                    },
                  );
                  console.log('🚀 ~ archiveWorkFlow ~ result2:', result2);

                  this.logger.log(
                    `Event ${eventId} successfully synced and updated in MyCase with subject: ${updatedSubject}`,
                  );
                } else {
                  this.logger.error(
                    `Failed to update event ${eventId} in MyCase: ${updateResult.error}`,
                  );
                }
              } catch (error) {
                this.logger.error(
                  `Exception while processing EventActionHistory ${action._id}: ${error.message}`,
                );
              }
            }
          }

          if (totalFailedEvents > 0) {
            const now = new Date();
            const retryCount =
              (workflowExecution.mycase_archive_retry_count || 0) + 1;

            await this.workflowExecutionModel.updateOne(
              { _id: work_flow_execution_id },
              {
                $set: {
                  mycase_archive_retry_count: retryCount,
                  mycase_archive_last_error: `Failed to sync ${totalFailedEvents} events with MyCase`,
                  mycase_archive_last_attempt: now,
                  mycase_archive_status: EventStatus.SYNC_FAILED,
                },
              },
            );
          } else {
            // If all operations succeeded, update status to SUCCESS
            await this.workflowExecutionModel.updateOne(
              { _id: work_flow_execution_id },
              {
                $set: {
                  mycase_archive_status: 'SUCCESS',
                },
              },
            );
          }

          return {
            success: totalFailedEvents === 0,
            totalProcessedEvents,
            totalSuccessfulEvents,
            totalFailedEvents,
            createOperations,
            updateOperations,
            successfulCreates,
            successfulUpdates,
            failedCreates,
            failedUpdates,
            taskExecutionSequence: lastTaskExecution.sequence,
            taskExecutionUpdated: taskUpdateResult.modifiedCount > 0,
            successfulMycaseClientMatterUpdates: successfulMycaseUpdates,
            eventResults,
            mycase_archive_retry_count:
              workflowExecution.mycase_archive_retry_count || 0,
            message: `Processed ${totalProcessedEvents} events from last task execution (sequence: ${lastTaskExecution.sequence}): ${successfulCreates} created and ${successfulUpdates} updated successfully in MyCase, ${totalFailedEvents} failed (${failedCreates} create failures, ${failedUpdates} update failures). Updated task execution and ${successfulMycaseUpdates} mycaseClientMatter records.`,
          };
        } catch (error) {
          // Update workflow execution with error details
          const now = new Date();
          await this.workflowExecutionModel.updateOne(
            { _id: work_flow_execution_id },
            {
              $inc: { mycase_archive_retry_count: 1 },
              $set: {
                mycase_archive_last_error: error.message,
                mycase_archive_last_attempt: now,
                mycase_archive_status: 'FAILED',
              },
            },
          );

          return {
            success: false,
            error: error.message,
          };
        }
      }
    } catch (error) {
      const workflowExecution = await this.workflowExecutionModel.findOne({
        _id: data.work_flow_execution_id,
      });
      this.logger.error(`Failed to archive workflow: ${error.message}`);
      return {
        success: false,
        mycase_archive_retry_count:
          workflowExecution?.mycase_archive_retry_count || 0,
      };
    }
  }

  // Helper method to update workflow execution retry information
  private async updateWorkflowExecutionRetryInfo(
    workflowExecutionId: string,
    status: 'PENDING' | 'IN_PROGRESS' | 'SUCCESS' | 'FAILED',
    retryCount: number,
    errorMessage?: string,
  ) {
    try {
      const updateData = {
        mycase_archive_status: status,
        mycase_archive_retry_count: retryCount,
        mycase_archive_last_attempt: new Date(),
      };

      if (errorMessage) {
        updateData['mycase_archive_last_error'] = errorMessage;
      }

      const result = await this.workflowExecutionModel.updateOne(
        { _id: workflowExecutionId },
        { $set: updateData },
      );

      this.logger.log(
        `Updated workflow execution ${workflowExecutionId} - Status: ${status}, Retry Count: ${retryCount}, Modified: ${result.modifiedCount}`,
      );

      return result.modifiedCount > 0;
    } catch (error) {
      this.logger.error(
        `Failed to update workflow execution retry info: ${error.message}`,
      );
      return false;
    }
  }

  // Public method to update workflow execution retry information via gRPC
  async updateWorkflowRetryInfo(data: {
    workflow_id: string;
    status: 'PENDING' | 'IN_PROGRESS' | 'SUCCESS' | 'FAILED';
    retry_count: number;
    error_message?: string;
  }) {
    console.log('🚀 ~ data:', data);
    try {
      const success = await this.updateWorkflowExecutionRetryInfo(
        data.workflow_id,
        data.status,
        data.retry_count,
        data.error_message,
      );

      return {
        success,
        message: success
          ? 'Workflow retry info updated successfully'
          : 'Failed to update workflow retry info',
        workflow_id: data.workflow_id,
        status: data.status,
        retry_count: data.retry_count,
      };
    } catch (error) {
      this.logger.error(`Error updating workflow retry info: ${error.message}`);
      throw error;
    }
  }

  async GetAppointmentEvents(data: GetEventListDto) {
    try {
      // Convert string to ObjectId
      const objectId = new Types.ObjectId(data.client_matter_id);

      // Find all documents with matching client_matter_id and SYNCED status
      const documents = await this.mycaseClientMatterModel
        .find({
          client_matter_id: objectId,
          eventStatus: { $ne: EventStatus.SYNC_FAILED }, // Exclude 'SYNC_FAILED'
          my_case_event_id: { $exists: true, $ne: null }, // Ensure field exists and is not null
          action: { $ne: EventStatus.SYNC_FAILED }, // Exclude 'SYNCFILES'
        })
        .sort({ updatedAt: -1 })
        .lean()
        .exec();

      // Transform the data to the desired format
      // Since event is now a single object, we don't need flatMap
      const result = documents.map((doc) => {
        // Type assertion to handle the plain object
        const plainDoc = doc as unknown as {
          client_matter_id: Types.ObjectId;
          event_id: string;
          event: Record<string, any>; // Single object now, not array
          eventStatus: string;
        };

        // Return the event with additional metadata
        return {
          client_matter_id: plainDoc.client_matter_id,
          event_id: plainDoc.event_id,
          ...plainDoc.event, // Spread all event properties
          eventStatus: plainDoc.eventStatus,
        };
      });

      return result;
    } catch (error) {
      this.logger.error(`Failed to fetch appointment events: ${error.message}`);
      throw error;
    }
  }

  async getUserList(data) {
    try {
      const { search, client_id, user_group_id } = data;
      console.log('🚀 ~ getUserList ~ data:', data);
      let users = [];
      let searchRegex;

      // First, get users from UserRole with specific roleId
      const roleId = user_group_id;
      console.log('🚀 ~ getUserList ~ roleId:', roleId);
      const userRoles = await this.userRoleModel
        .find({ roleId: roleId, status: true })
        .select('userId');

      const userIds = userRoles.map((userRole) => userRole.userId);

      if (userIds.length === 0) {
        return { users: [] };
      }

      // Build query for users
      let userQuery: any = {
        _id: { $in: userIds },
      };

      if (search) {
        // Create a regex pattern for case-insensitive search
        searchRegex = new RegExp(search, 'i');

        // Search in the combination of first_name and last_name
        userQuery = {
          ...userQuery,
          $or: [
            { first_name: searchRegex },
            { last_name: searchRegex },
            {
              $expr: {
                $regexMatch: {
                  input: { $concat: ['$first_name', ' ', '$last_name'] },
                  regex: search,
                  options: 'i',
                },
              },
            },
          ],
        };
      }

      // ✅ If client_id exists, only include users who have my_case_staff_id
      if (client_id) {
        userQuery = {
          ...userQuery,
          my_case_staff_id: { $exists: true, $ne: null },
        };
      }

      // Get users based on search criteria
      const userResults = await this.userModel
        .find(userQuery)
        .select('_id first_name last_name is_active');

      // Transform the user response to match the expected format
      users = userResults.map((user) => ({
        _id: user._id.toString(),
        value: `${user.first_name} ${user.last_name}`,
        text: `${user.first_name} ${user.last_name}`,
        is_active: (user as any).is_active,
      }));

      // ✅ If client_id and search are provided, check for matching client name
      if (client_id && searchRegex) {
        try {
          const matter = await this.matterModel.findById(client_id);

          if (matter?.client_id) {
            const client = await this.clientModel.findById(matter.client_id);

            if (client) {
              const fullClientName = `${client.first_name} ${client.last_name}`;

              if (searchRegex.test(fullClientName)) {
                const clientEntry = {
                  _id: client._id.toString(),
                  value: fullClientName,
                  text: fullClientName,
                  is_active: client.is_active,
                };

                const alreadyExists = users.find(
                  (user) => user._id === clientEntry._id,
                );

                if (!alreadyExists) {
                  users.unshift(clientEntry); // Add client at top
                }
              }
            }
          }
        } catch (error) {
          console.error(
            'Error fetching matter or client (search check):',
            error,
          );
        }
      }

      // ✅ If client_id exists, always prepend the client (without search check)
      if (client_id) {
        try {
          const matter = await this.matterModel.findById(client_id);

          if (matter?.client_id) {
            const client = await this.clientModel.findById(matter.client_id);

            if (client) {
              const fullClientName = `${client.first_name} ${client.last_name} (Client)`;

              if (fullClientName) {
                const clientEntry = {
                  _id: client._id.toString(),
                  value: fullClientName,
                  text: fullClientName,
                  is_active: client.is_active,
                };

                const alreadyExists = users.find(
                  (user) => user._id === clientEntry._id,
                );

                if (!alreadyExists) {
                  users.unshift(clientEntry); // Add client at top
                }
              }
            }
          }
        } catch (error) {
          console.error('Error fetching matter or client (default):', error);
        }
      }

      // Auto-assign next user from the specified role group via round robin
      // let roundRobinAssignedUser = null;
      // try {
      //   // Use the same roleId that was used to filter users above
      //   const roundRobinRoleId = roleId; // This is user_group_id or default '6877420fd4928f6a37ba1b95'
      //   const roundRobinContext = {
      //     context_type: 'role' as const,
      //     role_id: roundRobinRoleId,
      //     context_id: roundRobinRoleId
      //   };

      //   // Get next round robin user from Court Notice Paralegals
      //   const roundRobinResult = await this.roundRobinService.getNextRoundRobinUser(roundRobinContext);

      //   if (roundRobinResult) {
      //     roundRobinAssignedUser = {
      //       _id: roundRobinResult.assigned_user._id,
      //       value: `${roundRobinResult.assigned_user.name} (Auto-assigned)`,
      //       text: `${roundRobinResult.assigned_user.name} (Auto-assigned)`,
      //       is_active: true,
      //       type: 'auto-assigned',
      //       round_robin_info: {
      //         assignment_count: roundRobinResult.assignment_count,
      //         context: roundRobinResult.context,
      //         auto_assigned: true
      //       }
      //     };

      //     this.logger.log(`Auto-assigned round robin user: ${roundRobinResult.assigned_user.name} from role group ${roundRobinRoleId}`);
      //   }
      // } catch (error) {
      //   this.logger.error(`Error getting round robin user from role group ${roleId}: ${error.message}`);
      //   // Continue without round robin assignment if it fails
      // }

      // // Insert auto-assigned user at the beginning if available
      // if (roundRobinAssignedUser) {
      //   users.unshift(roundRobinAssignedUser);
      // }

      return { users };
    } catch (error) {
      throw error;
    }
  }

  // Location-related methods
  async syncLocationsFromMyCase(
    getLocationDto: GetLocationDto,
  ): Promise<SyncLocationsResponseDto> {
    try {
      this.logger.log('Starting location sync from MyCase');

      // Otherwise, fetch all locations from MyCase
      const myCaseLocations =
        await this.myCaseService.getAllLocationsFromMyCase(
          getLocationDto.access_token,
        );

      if (myCaseLocations.length === 0) {
        return {
          locations: [],
          synced_count: 0,
          total_count: 0,
          message: 'No locations found in MyCase',
        };
      }

      const result =
        await this.locationService.syncMultipleLocationsFromMyCase(
          myCaseLocations,
        );

      this.logger.log(`Completed location sync: ${result.message}`);
      return result;
    } catch (error) {
      this.logger.error(
        `Error syncing locations from MyCase: ${error.message}`,
      );
      throw error;
    }
  }

  async getLocationList(): Promise<LocationResponseDto[]> {
    try {
      return await this.locationService.getAllLocations();
    } catch (error) {
      this.logger.error(`Error fetching location list: ${error.message}`);
      throw error;
    }
  }

  async getLocationById(id: string): Promise<LocationResponseDto> {
    try {
      return await this.locationService.getLocationById(id);
    } catch (error) {
      this.logger.error(`Error fetching location by ID: ${error.message}`);
      throw error;
    }
  }

  async getEventById(data: { event_id: string }) {
    try {
      this.logger.log(`Searching for event with ID: ${data.event_id}`);

      // Find the event in MycaseClientMatter collection by event_id
      const eventRecord = await this.mycaseClientMatterModel.findOne({
        event_id: data.event_id,
        is_active: true,
      });

      if (!eventRecord) {
        this.logger.warn(`Event not found with IDJJJJJJJ: ${data.event_id}`);
        return {
          success: false,
          message: `Event not found with ID: ${data.event_id}`,
          event: null,
        };
      }

      this.logger.log(`Event found: ${eventRecord}`);

      // Transform the response to match the proto format
      const response = {
        id: eventRecord.event?.id || '',
        caseNumber: eventRecord.event?.caseNumber || '',
        clientName: eventRecord.event?.clientName || '',
        description: eventRecord.event?.description || '',
        date: eventRecord.event?.date || '',
        startTime: eventRecord.event?.startTime || '',
        endTime: eventRecord.event?.endTime || '',
        isCompleted: eventRecord.event?.isCompleted || false,
        subject: eventRecord.event?.subject || '',
        courtNoticeType: eventRecord.event?.courtNoticeType || '',
        appointmentAction: eventRecord.event?.appointmentAction || '',
        charge: eventRecord.event?.charge || '',
        exCountyOfArrest: eventRecord.event?.county || '',
        courtLocation: eventRecord.event?.courtLocation || '',
        optionalAttendees: eventRecord.event?.optionalAttendees || '',
        requiredAttendees: eventRecord.event?.requiredAttendees || '',
        meetingLocation: eventRecord.event?.meetingLocation || '',
        startDate: eventRecord.event?.startDate || '',
        endDate: eventRecord.event?.endDate || '',
        allDay: eventRecord.event?.allDay || false,
        courtNoticeActions: eventRecord.event?.courtNoticeActions || '',
        appointmentToReschedule:
          eventRecord.event?.appointmentToReschedule || '',
        county: eventRecord.event?.county || '',
        meetingLink: eventRecord.event?.meetingLink || '',
        clientAttendance: eventRecord.event?.clientAttendance || '',
        court_notice_date: eventRecord.event?.court_notice_date || '',
        phoneDetails: eventRecord.event?.phoneDetails || '',
        meetingAddress: eventRecord.event?.meetingAddress || '',
      };

      return response;
    } catch (error) {
      this.logger.error(`Error in getEventById: ${error.message}`);
      throw new Error(`Failed to retrieve event: ${error.message}`);
    }
  }

  /**
   * Check if a client is available in MyCase
   * @param data Object containing client_id and optional tenant_id
   * @returns Object with availability status and client details
   */
  async checkClientAvailability(data: {
    client_id: string;
    tenant_id?: string;
    access_token?: string;
  }) {
    const { client_id, access_token } = data;

    try {
      this.logger.log(
        `Checking client availability for client_id: ${data.client_id}`,
      );

      // Step 1: Find client in local database by _id
      const objectId = new Types.ObjectId(client_id);
      const localClient = await this.clientModel.findById(objectId);

      if (!localClient) {
        this.logger.log(
          `Client with ID ${data.client_id} not found in local database`,
        );
        return {
          available: false,
          message: 'Client not found in local database',
          client_name: '',
          client_email: '',
          my_case_client_id: '',
        };
      }

      // Step 2: Check if client has my_case_client_id
      if (!localClient.my_case_client_id) {
        this.logger.log(
          `Client ${data.client_id} does not have MyCase client ID`,
        );
        return {
          available: false,
          message: 'Client does not have MyCase client ID',
          client_name: localClient.name || '',
          client_email: '',
          my_case_client_id: '',
        };
      }

      // Step 3: Check if we have access token
      if (!access_token) {
        this.logger.log('No access token provided for MyCase API call');
        return {
          available: false,
          message: 'Access token required for MyCase API call',
          client_name: localClient.name || '',
          client_email: '',
          my_case_client_id: localClient.my_case_client_id,
        };
      }

      // Step 4: Check client in MyCase
      try {
        const myCaseClient = await this.myCaseService.getClientFromMyCase(
          localClient.my_case_client_id,
          access_token,
        );

        if (!myCaseClient) {
          this.logger.log(
            `Client ${localClient.my_case_client_id} not found in MyCase`,
          );
          return {
            available: false,
            message: 'Client not found in MyCase',
            client_name: localClient.name || '',
            client_email: '',
            my_case_client_id: localClient.my_case_client_id,
          };
        }

        // Step 5: Check if client has email
        const hasEmail = myCaseClient.email && myCaseClient.email.trim() !== '';

        if (!hasEmail) {
          this.logger.log(
            `Client ${localClient.my_case_client_id} exists in MyCase but has no email`,
          );
          return {
            available: false,
            message: 'Client exists in MyCase but has no email',
            client_name: this.buildClientName(myCaseClient),
            client_email: '',
            my_case_client_id: localClient.my_case_client_id,
          };
        }

        // Step 6: Client is available with email
        this.logger.log(
          `Client ${localClient.my_case_client_id} is available in MyCase with email`,
        );
        return {
          available: true,
          message: 'Client is available in MyCase with email',
          client_name: this.buildClientName(myCaseClient),
          client_email: myCaseClient.email,
          my_case_client_id: localClient.my_case_client_id,
        };
      } catch (myCaseError) {
        this.logger.error(`MyCase API error: ${myCaseError.message}`);

        // Handle specific MyCase errors
        if (myCaseError.message.includes('Authentication failed')) {
          return {
            available: false,
            message: 'Authentication failed for MyCase API',
            client_name: localClient.name || '',
            client_email: '',
            my_case_client_id: localClient.my_case_client_id,
          };
        }

        return {
          available: false,
          message: `MyCase API error: ${myCaseError.message}`,
          client_name: localClient.name || '',
          client_email: '',
          my_case_client_id: localClient.my_case_client_id,
        };
      }
    } catch (error) {
      this.logger.error(`Error checking client availability: ${error.message}`);
      return {
        available: false,
        message: `Internal error: ${error.message}`,
        client_name: '',
        client_email: '',
        my_case_client_id: '',
      };
    }
  }

  /**
   * Build client name from MyCase client data
   * @param myCaseClient MyCase client object
   * @returns Formatted client name
   */
  private buildClientName(myCaseClient: any): string {
    const parts = [];

    if (myCaseClient.first_name) {
      parts.push(myCaseClient.first_name);
    }

    if (myCaseClient.last_name) {
      parts.push(myCaseClient.last_name);
    }

    return parts.join(' ').trim() || 'Unknown Client';
  }

  // Client-related methods
  async syncClientsFromMyCase(
    accessToken: string,
  ): Promise<SyncClientsResponseDto> {
    try {
      this.logger.log('Starting client sync from MyCase');

      // Fetch all clients from MyCase
      const myCaseClients =
        await this.myCaseService.getAllClientsFromMyCase(accessToken);

      if (!myCaseClients || myCaseClients.length === 0) {
        this.logger.log('No clients found in MyCase');
        return {
          clients: [],
          synced_count: 0,
          total_count: 0,
          message: 'No clients found in MyCase',
        };
      }

      // Sync clients using the client service
      const syncResult =
        await this.clientService.syncMultipleClientsFromMyCase(myCaseClients);

      this.logger.log(
        `Client sync completed: ${syncResult.synced_count} out of ${syncResult.total_count} clients synced`,
      );

      return syncResult;
    } catch (error) {
      this.logger.error(`Error syncing clients from MyCase: ${error.message}`);
      throw error;
    }
  }

  async syncMattersFromMyCase(
    accessToken: string,
  ): Promise<SyncMattersResponseDto> {
    try {
      this.logger.log('Starting matter sync from MyCase');

      // Fetch all matters from MyCase
      const myCaseMatters =
        await this.myCaseService.getAllMattersFromMyCase(accessToken);

      if (!myCaseMatters || myCaseMatters.length === 0) {
        this.logger.log('No matters found in MyCase');
        return {
          matters: [],
          synced_count: 0,
          total_count: 0,
          message: 'No matters found in MyCase',
        };
      }

      // Sync matters using the matter service
      const syncResult =
        await this.matterService.syncMultipleMattersFromMyCase(myCaseMatters);

      this.logger.log(
        `Matter sync completed: ${syncResult.synced_count} out of ${syncResult.total_count} matters synced`,
      );

      return syncResult;
    } catch (error) {
      this.logger.error(`Error syncing matters from MyCase: ${error.message}`);
      throw error;
    }
  }

  async syncEventsFromMyCase(
    accessToken: string,
    usePreviousDayFilter: boolean = false,
  ): Promise<SyncEventsResponseDto> {
    try {
      this.logger.log(
        `Starting event sync from MyCase${usePreviousDayFilter ? ' (previous day filter)' : ''}`,
      );

      if (!accessToken) {
        throw new Error('Access token is required for MyCase API calls');
      }

      let myCaseEvents: MyCaseEventInterface[];

      if (usePreviousDayFilter) {
        // For cron jobs - only fetch events from previous day
        myCaseEvents =
          await this.myCaseService.getEventsFromPreviousDay(accessToken);
        this.logger.log(
          `Fetched ${myCaseEvents.length} events from previous day from MyCase`,
        );
      } else {
        // For manual sync - fetch all events
        myCaseEvents =
          await this.myCaseService.getAllEventsFromMyCase(accessToken);
        this.logger.log(`Fetched ${myCaseEvents.length} events from MyCase`);
      }

      const result =
        await this.eventService.syncMultipleEventsFromMyCase(myCaseEvents);

      this.logger.log(
        `Event sync completed: ${result.synced_count} out of ${result.total_count} events synced`,
      );

      return result;
    } catch (error) {
      this.logger.error(`Error syncing events from MyCase: ${error.message}`);
      throw error;
    }
  }

  async saveMyCaseWebhookData(data: any) {
    try {
      this.logger.log(`Saving MyCase webhook data: ${JSON.stringify(data)}`);

      console.log('🚀 ~ saveMyCaseWebhookData ~ data:', data.resource);

      const accessToken = data.resource_body.access_token;

      // dhyey
      // Delegate to WebhookService for processing
      if (
        data.resource === 'Event' ||
        data.resource === 'Client' ||
        data.resource === 'Case'
      ) {
        await this.webhookService.handleWebhook(data, accessToken);
      }

      // Validate that we have all required fields
      if (
        !data.firm_uuid ||
        !data.action ||
        !data.resource ||
        !data.timestamp
      ) {
        throw new Error(
          'Missing required fields: firm_uuid, action, resource, or timestamp',
        );
      }

      // Validate resource_body is a valid JSON string
      let parsedResourceBody = {};
      try {
        if (data.resource_body && typeof data.resource_body === 'string') {
          parsedResourceBody = JSON.parse(data.resource_body);
          console.log(
            '🚀 ~ Parsed resource_body:',
            JSON.stringify(parsedResourceBody, null, 2),
          );
        } else {
          console.log(
            '🚀 ~ resource_body is not a string, treating as empty object',
          );
          data.resource_body = '{}';
        }
      } catch (parseError) {
        this.logger.error(
          `Invalid JSON in resource_body: ${parseError.message}`,
        );
        throw new Error(`Invalid JSON in resource_body: ${parseError.message}`);
      }

      // Create a new MyCaseHistory record
      const myCaseHistory = new this.myCaseHistoryModel({
        firm_uuid: data.firm_uuid,
        action: data.action,
        resource: data.resource,
        resource_body: data.resource_body, // Store as JSON string
        webhook_timestamp: data.timestamp,
        is_active: true,
      });

      const savedRecord = await myCaseHistory.save();
      this.logger.log(`MyCase webhook data saved with ID: ${savedRecord._id}`);
      console.log('🚀 ~ Saved record:', {
        id: savedRecord._id,
        firm_uuid: savedRecord.firm_uuid,
        action: savedRecord.action,
        resource: savedRecord.resource,
        resource_body_length: savedRecord.resource_body.length,
        resource_body_preview:
          savedRecord.resource_body.substring(0, 100) + '...',
      });

      return {
        success: true,
        message: 'MyCase webhook data saved successfully',
        id: savedRecord._id.toString(),
      };
    } catch (error) {
      this.logger.error(`Error saving MyCase webhook data: ${error.message}`);
      this.logger.error(`Error stack: ${error.stack}`);
      console.error('🚀 ~ saveMyCaseWebhookData ~ Error details:', {
        message: error.message,
        data: data,
        stack: error.stack,
      });
      throw new Error(`Failed to save MyCase webhook data: ${error.message}`);
    }
  }

  /**
   * Helper method to get parsed resource_body from a saved record
   * This can be used when you need to work with the resource_body as an object
   */
  getParsedResourceBody(myCaseHistoryRecord: any): any {
    try {
      if (
        myCaseHistoryRecord.resource_body &&
        typeof myCaseHistoryRecord.resource_body === 'string'
      ) {
        return JSON.parse(myCaseHistoryRecord.resource_body);
      }
      return {};
    } catch (error) {
      this.logger.error(`Error parsing resource_body: ${error.message}`);
      return {};
    }
  }

  // Helper method to update database entries with new S3 keys after successful rename
  private async updateDatabaseWithRenamedFiles(
    taskExecution,
    renamedFileMap: Map<string, { newKey: string; newName: string }>,
    workflow_id: string,
  ) {
    this.logger.log(
      `Updating database entries with renamed files for ${renamedFileMap.size} files`,
    );

    // Update files in derived fields (most common location for event files)
    if (taskExecution.derived_field && taskExecution.derived_field.length > 0) {
      for (const derivedField of taskExecution.derived_field) {
        if (derivedField.client && Array.isArray(derivedField.client)) {
          for (const client of derivedField.client) {
            if (client.matter && Array.isArray(client.matter)) {
              for (const matter of client.matter) {
                if (matter.event && Array.isArray(matter.event)) {
                  for (const event of matter.event) {
                    if (event.files && Array.isArray(event.files)) {
                      for (const file of event.files) {
                        if (file.key && renamedFileMap.has(file.key)) {
                          const renameInfo = renamedFileMap.get(file.key);
                          this.logger.log(
                            `Updating file in derived field: ${file.key} -> ${renameInfo.newKey}, name: ${file.name} -> ${renameInfo.newName}`,
                          );
                          file.key = renameInfo.newKey;
                          file.name = renameInfo.newName;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    // Update files in form fields (less common but possible)
    if (taskExecution.formField && taskExecution.formField.length > 0) {
      for (const formField of taskExecution.formField) {
        if (formField.fields && Array.isArray(formField.fields)) {
          for (const field of formField.fields) {
            if (field.value && Array.isArray(field.value)) {
              for (const value of field.value) {
                if (value.key && renamedFileMap.has(value.key)) {
                  const renameInfo = renamedFileMap.get(value.key);
                  this.logger.log(
                    `Updating file in form field: ${value.key} -> ${renameInfo.newKey}, name: ${value.name} -> ${renameInfo.newName}`,
                  );
                  value.key = renameInfo.newKey;
                  if (value.name) {
                    value.name = renameInfo.newName;
                  }
                }
              }
            }
          }
        }
      }
    }

    // Save the updated task execution
    await taskExecution.save();
    this.logger.log('Task execution updated with new file keys and names');

    // Update mycaseClientMatter records
    try {
      const mycaseRecords = await this.mycaseClientMatterModel.find({
        workflow_execution_id: workflow_id,
      });

      for (const record of mycaseRecords) {
        if (
          record.event &&
          record.event.files &&
          Array.isArray(record.event.files)
        ) {
          let hasUpdates = false;
          for (const file of record.event.files) {
            if (file.key && renamedFileMap.has(file.key)) {
              const renameInfo = renamedFileMap.get(file.key);
              this.logger.log(
                `Updating file in mycaseClientMatter: ${file.key} -> ${renameInfo.newKey}, name: ${file.name} -> ${renameInfo.newName}`,
              );
              file.key = renameInfo.newKey;
              file.name = renameInfo.newName;
              hasUpdates = true;
            }
          }

          if (hasUpdates) {
            await record.save();
            this.logger.log(`Updated mycaseClientMatter record: ${record._id}`);
          }
        }
      }
    } catch (error) {
      this.logger.error(
        `Error updating mycaseClientMatter records: ${error.message}`,
      );
    }
  }

  /**
   * Handle test success request and store it in WebhookResponse entity
   */
  async handleTestSuccess(data: any) {
    try {
      this.logger.log('Processing TestSuccess request in service');
      this.logger.log(`Request data: ${JSON.stringify(data)}`);

      // Prepare the webhook response data to store
      const webhookResponseData = {
        success: true,
        message: 'Test API is working successfully!',
        webhook_response: data || '{}',
        timestamp: new Date().toISOString(),
      };

      // Create a new WebhookResponse record
      const webhookResponse = new this.webhookResponseModel({
        method_name: 'TestSuccess',
        request_source: 'gRPC',
        webhook_response: JSON.stringify(webhookResponseData),
        is_active: true,
      });

      const savedRecord = await webhookResponse.save();
      this.logger.log(`WebhookResponse saved with ID: ${savedRecord._id}`);

      return {
        success: true,
        message: 'Test API is working successfully!',
        received_params_json: JSON.stringify(data || {}),
        record_id: savedRecord._id.toString(),
      };
    } catch (error) {
      this.logger.error(`Error in handleTestSuccess service: ${error.message}`);

      // Try to save error record
      try {
        const errorWebhookResponse = new this.webhookResponseModel({
          method_name: 'TestSuccess',
          request_source: 'gRPC',
          webhook_response: JSON.stringify({
            success: false,
            message: `Test failed: ${error.message}`,
            webhook_response: data || '{}',
            timestamp: new Date().toISOString(),
          }),
          error_message: error.message,
          is_active: true,
        });

        await errorWebhookResponse.save();
      } catch (saveError) {
        this.logger.error(`Error saving error record: ${saveError.message}`);
      }

      return {
        success: false,
        message: `Test failed: ${error.message}`,
        received_params_json: data.params_json || '{}',
      };
    }
  }
  async getAllUsersAndRoles(data: { search?: string }) {
    try {
      const { search } = data;
      const result = {
        combined: [],
      };

      // Get all active users
      let userQuery = { is_active: true, is_deleted: false };
      if (search) {
        const searchRegex = new RegExp(search, 'i');
        userQuery['$or'] = [
          { first_name: searchRegex },
          { last_name: searchRegex },
          { email: searchRegex },
        ];
      }

      const users = await this.userModel.find(userQuery).exec();
      console.log(users, 'userssssssssss');
      const userItems = users.map((user) => ({
        id: user.id,
        _id: user._id.toString(),
        name: `${user.first_name} ${user.last_name}`,
        email: user.email,
        type: 'user',
        roleId: user.roleId,
        is_active: user.is_active,
        displayName: `${user.first_name} ${user.last_name}`,
        category: 'user',
      }));

      // Get all active roles
      let roleQuery = { is_active: true, is_deleted: false };
      if (search) {
        const searchRegex = new RegExp(search, 'i');
        roleQuery['name'] = searchRegex;
      }

      const roles = await this.roleModel.find(roleQuery).exec();
      const roleItems = roles.map((role) => ({
        id: role.id,
        _id: role._id.toString(),
        name: role.name,
        type: 'role',
        is_active: role.is_active,
        displayName: role.name,
        category: 'role',
      }));

      // Combine users and roles into a single array
      result.combined = [...userItems, ...roleItems];

      // Sort combined results by display name
      result.combined.sort((a, b) =>
        a.displayName.localeCompare(b.displayName),
      );

      return result;
    } catch (error) {
      this.logger.error(`Error in getAllUsersAndRoles: ${error.message}`);
      throw error;
    }
  }

  async assignToTask(data: {
    task_execution_id: string;
    assignees: Array<{ id: string; type: 'user' | 'role' }>;
    assigned_by: string;
    notes?: string;
  }) {
    try {
      const { task_execution_id, assignees, assigned_by, notes } = data;
      const taskExecution = await this.taskExecutionModel
        .findById(task_execution_id)
        .exec();
      if (!taskExecution) {
        throw new Error('Task execution not found');
      }

      const userAssignees = assignees.filter(
        (assignee) => assignee.type === 'user',
      );
      const userIds = userAssignees.map((assignee) => assignee.id);

      await this.taskExecutionModel.findByIdAndUpdate(task_execution_id, {
        $set: {
          assigns: userIds,
          assign_by: new Types.ObjectId(assigned_by),
          notes: notes || taskExecution.notes,
        },
      });

      this.logger.log(
        `Assigned ${userIds.length} users to task ${task_execution_id}`,
      );
      return { success: true, message: 'Assignments updated successfully' };
    } catch (error) {
      this.logger.error(`Error in assignToTask: ${error.message}`);
      throw error;
    }
  }

  async removeAssignment(data: any): Promise<any> {
    return this.assignmentService.removeAssignment(
      data.task_execution_id,
      data.assignee_id,
      data.assignment_type,
    );
  }

  async assignUserToTask(data: {
    task_execution_id: string;
    user_id: string;
    work_flow_id: string;
    assigned_by: string;
    isdeSelected?: boolean;
  }): Promise<any> {
    try {
      const { task_execution_id, user_id, work_flow_id, assigned_by, isdeSelected } = data;

      if (!task_execution_id) throw new Error("task_execution_id is required");
      if (!work_flow_id) throw new Error("work_flow_id is required");
      if (!user_id) throw new Error("user_id is required");

      // 🔹 Find TaskExecution
      let taskExecution = await this.taskExecutionModel.findById(new Types.ObjectId(task_execution_id));
      if (!taskExecution) {
        taskExecution = await this.taskExecutionModel.findOne({
          task_id: new Types.ObjectId(task_execution_id),
          workflow_execution_id: new Types.ObjectId(work_flow_id),
          is_active: true,
          is_deleted: false,
        });
        if (!taskExecution) {
          throw new Error(`Task execution not found for task_id: ${task_execution_id} and workflow_execution_id: ${work_flow_id}`);
        }
      }

      if (taskExecution.workflow_execution_id.toString() !== work_flow_id) {
        throw new Error(`Workflow ID mismatch`);
      }

      const taskExecutionId = taskExecution._id;

      // 🔹 Resolve assignee as ObjectId
      let assigneeObjectId: Types.ObjectId;
      let user = null;
      if (!isNaN(parseInt(user_id))) {
        user = await this.userModel.findOne({ id: parseInt(user_id) });
      }
      if (!user && Types.ObjectId.isValid(user_id)) {
        user = await this.userModel.findOne({ _id: new Types.ObjectId(user_id) });
      }
      if (user) {
        assigneeObjectId = user._id;
      } else {
        let role = null;
        if (Types.ObjectId.isValid(user_id)) {
          role = await this.roleModel.findOne({ _id: new Types.ObjectId(user_id) });
        }
        if (!role && !isNaN(parseInt(user_id))) {
          role = await this.roleModel.findOne({ id: parseInt(user_id) });
        }
        if (!role) throw new Error(`No matching user or role found for ID ${user_id}`);
        assigneeObjectId = role._id;
      }

      // ---------- UPDATE TaskExecution ----------
      let updatedAssigns = taskExecution.assigns ? [...taskExecution.assigns] : [];

      // Remove duplicate by user_id only
      updatedAssigns = updatedAssigns.filter(a => a.user_id.toString() !== assigneeObjectId.toString());

      const newAssignObj = {
        user_id: assigneeObjectId,
        isdeSelected: !!isdeSelected,
        equals(userId: Types.ObjectId) {
          return this.user_id.equals(userId);
        }
      };

      if (!isdeSelected) {
        // Insert at index 0, move existing down
        updatedAssigns = [newAssignObj, ...updatedAssigns];
      } else {
        updatedAssigns.push(newAssignObj);
      }

      await this.taskExecutionModel.findByIdAndUpdate(
        taskExecutionId,
        { $set: { assigns: updatedAssigns } },
        { new: true }
      );

      // ---------- UPDATE WorkflowExecution ----------
      const workflowExecution = await this.workflowExecutionModel.findById(new Types.ObjectId(work_flow_id));
      if (!workflowExecution) {
        throw new Error(`Workflow execution not found for ID ${work_flow_id}`);
      }

      let wfAssigns: Types.ObjectId[] = workflowExecution.assigns ? [...workflowExecution.assigns] : [];
      // Remove duplicate
      wfAssigns = wfAssigns.filter(uid => uid.toString() !== assigneeObjectId.toString());

      if (!isdeSelected) {
        wfAssigns = [assigneeObjectId, ...wfAssigns]; // Insert new at index 0
      } else {
        wfAssigns.push(assigneeObjectId);
      }

      await this.workflowExecutionModel.findByIdAndUpdate(
        new Types.ObjectId(work_flow_id),
        { $set: { assigns: wfAssigns } },
        { new: true }
      );

      return {
        success: true,
        assigned_user_ids: [assigneeObjectId.toString()],
        isdeSelected
      };

    } catch (error) {
      this.logger.error(`Error in assignUserToTask: ${error.message}`);
      throw error;
    }
  }


  async removeUserFromTask(data: {
    task_execution_id: string;
    user_id: string;
    work_flow_id: string;
    isdeSelected?: boolean;
  }): Promise<any> {
    try {
      const { task_execution_id, user_id, work_flow_id, isdeSelected } = data;

      if (!task_execution_id) throw new Error("task_execution_id is required");
      if (!work_flow_id) throw new Error("work_flow_id is required");
      if (!user_id) throw new Error("user_id is required");

      // 🔹 Find TaskExecution (by ID or by task_id + workflow_execution_id)
      let taskExecution = await this.taskExecutionModel.findById(task_execution_id);
      if (!taskExecution) {
        taskExecution = await this.taskExecutionModel.findOne({
          task_id: new Types.ObjectId(task_execution_id),
          workflow_execution_id: new Types.ObjectId(work_flow_id),
          is_active: true,
          is_deleted: false,
        });
        if (!taskExecution) {
          throw new Error(
            `Task execution not found for ID/task_id: ${task_execution_id} and workflow_execution_id: ${work_flow_id}`
          );
        }
      }

      const taskExecutionId = taskExecution._id;

      // 🔹 Resolve user/role ObjectId
      let targetId: Types.ObjectId | null = null;
      let assigneeType: "user" | "role" = "user";

      let user = null;
      if (!isNaN(parseInt(user_id))) {
        user = await this.userModel.findOne({ id: parseInt(user_id) });
      }
      if (!user && Types.ObjectId.isValid(user_id)) {
        user = await this.userModel.findOne({ _id: new Types.ObjectId(user_id) });
      }

      if (user) {
        targetId = user._id;
      } else {
        let role = null;
        if (Types.ObjectId.isValid(user_id)) {
          role = await this.roleModel.findOne({ _id: new Types.ObjectId(user_id) });
        }
        if (!role && !isNaN(parseInt(user_id))) {
          role = await this.roleModel.findOne({ id: parseInt(user_id) });
        }
        if (!role) {
          throw new Error(`No matching user or role found for ID ${user_id}`);
        }
        targetId = role._id;
        assigneeType = "role";
      }

      // 🔹 Ensure match on user_id + isdeSelected in taskExecution
      const assignExists = taskExecution.assigns?.some(
        (assign: any) =>
          assign.user_id?.toString() === targetId.toString() &&
          assign.isdeSelected === isdeSelected
      );

      if (!assignExists) {
        return {
          success: false,
          message: `No matching assign found for ${assigneeType} with given isdeSelected flag`,
          task_execution_id: taskExecutionId.toString(),
          assignee_id: user_id,
        };
      }

      // 🔹 Remove from TaskExecution assigns
      await this.taskExecutionModel.updateOne(
        { _id: taskExecutionId },
        {
          $pull: {
            assigns: {
              user_id: targetId,
              isdeSelected: !!isdeSelected
            }
          }
        }
      );

      // 🔹 Remove from WorkflowExecution assigns (only ObjectId)
      await this.workflowExecutionModel.updateOne(
        {
          _id: new Types.ObjectId(work_flow_id),
          is_active: true,
          is_deleted: false,
        },
        {
          $pull: {
            assigns: targetId
          }
        }
      );

      return {
        success: true,
        message: `${assigneeType === "user" ? "User" : "Role"} removed from task & workflow successfully`,
      };

    } catch (error) {
      this.logger.error(`Error in removeUserFromTask: ${error.message}`);
      throw error;
    }
  }


  async getTaskAssignedUsers(data: {
    task_id: string;
    workflow_execution_id: string;
  }): Promise<any> {
    try {
      const { task_id, workflow_execution_id } = data;

      if (!task_id) throw new Error("task_id is required");
      if (!workflow_execution_id) throw new Error("workflow_execution_id is required");

      // Find task execution
      const taskExecution = await this.taskExecutionModel.findOne({
        task_id: new Types.ObjectId(task_id),
        workflow_execution_id: new Types.ObjectId(workflow_execution_id),
        is_active: true,
        is_deleted: false,
      });

      if (!taskExecution) {
        throw new Error(`Task execution not found for task_id: ${task_id} and workflow_execution_id: ${workflow_execution_id}`);
      }

      const assignedUsers: any[] = [];

      if (taskExecution.assigns?.length > 0) {
        for (const assigned of taskExecution.assigns) {
          const assignedId = assigned.user_id;


          // 1️⃣ Try finding in user table
          let user = await this.userModel.findOne({ _id: assignedId });
          if (user) {
            assignedUsers.push({
              _id: user._id,
              id: user.id,
              name: `${user.first_name} ${user.last_name}`,
              email: user.email,
              is_active: user.is_active,
              type: "user",
              displayName: `${user.first_name} ${user.last_name}`,
              category: "user",
              isdeSelected: assigned.isdeSelected
            });
            continue;
          }

          // 2️⃣ Try finding in role table
          const role = await this.roleModel.findOne({ _id: assignedId });
          if (role) {
            assignedUsers.push({
              _id: role._id,
              id: role.id,
              name: role.name,
              type: "role",
              displayName: role.name,
              category: "role",
              isdeSelected: assigned.isdeSelected
            });
          }
        }
      }

      return {
        success: true,
        assigned_users: assignedUsers,
        total_assigned: assignedUsers.length,
        message: `Found ${assignedUsers.length} assigned users/roles for this task`,
      };
    } catch (error) {
      this.logger.error(`Error in getTaskAssignedUsers: ${error.message}`);
      throw error;
    }
  }




  async checkUserTaskAssignment(data: {
    taskId: string;
    work_flow_id: string;
    user_id: string;
  }): Promise<any> {
    try {
      const { taskId, work_flow_id, user_id } = data;
      const user = await this.userModel.findOne({ id: parseInt(user_id) });
      if (!user) {
        throw new Error(`User with ID ${user_id} not found`);
      }
      const userId = user._id;

      let taskExecution = await this.taskExecutionModel.findById(
        new Types.ObjectId(taskId),
      );

      if (!taskExecution) {
        this.logger.log(
          `TaskExecution not found by ID: ${taskId}, trying alternate lookup`,
        );
        taskExecution = await this.taskExecutionModel.findOne({
          task_id: new Types.ObjectId(taskId),
          workflow_execution_id: new Types.ObjectId(work_flow_id),
          is_active: true,
          is_deleted: false,
        });

        if (!taskExecution) {
          throw new Error(
            `Task execution not found for taskId: ${taskId} and workflow_execution_id: ${work_flow_id}`,
          );
        }
      }

      const assigns = taskExecution.assigns || [];

      if (assigns.length === 0) {
        this.logger.log(
          `No assignees defined for task ${taskExecution._id}, access allowed`,
        );
        return {
          success: true,
          is_assigned: true,
          message: 'Access allowed (no assignees defined)',
        };
      }
      const isUserAssigned = assigns.some(
        (a) =>
          a.user_id?.equals(userId)

      );

      if (isUserAssigned) {
        this.logger.log(
          `User ${user_id} is directly assigned (non-deselected) to task ${taskExecution._id}`,
        );
        return {
          success: true,
          is_assigned: true,
          message: 'User is directly assigned to this task',
        };
      }
      const assignedRoleIds = assigns
        .filter(a => a.user_id)
        .map(a => a.user_id);

      if (assignedRoleIds.length > 0) {
        const userRoleMatch = await this.userRoleModel.findOne({
          roleId: { $in: assignedRoleIds },
          userId: userId,
          status: true,
        });

        if (userRoleMatch) {
          this.logger.log(
            `User ${user_id} is assigned via role ${userRoleMatch.roleId} to task ${taskExecution._id}`,
          );
          return {
            success: true,
            is_assigned: true,
            message: 'User is assigned via role to this task',
          };
        }
      }

      this.logger.log(
        `User ${user_id} is NOT assigned to task ${taskExecution._id}`,
      );
      return {
        success: true,
        is_assigned: false,
        message: 'User is not assigned to this task',
      };
    } catch (error) {
      this.logger.error(`Error in checkUserTaskAssignment: ${error.message}`);
      this.logger.error(`Stack: ${error.stack}`);
      throw error;
    }
  }



  /**
   * Handle round robin assignment for tasks
   *
   * This method implements a scalable round robin assignment system that:
   * 1. Maintains separate round robin states for different contexts (roles, workflows, etc.)
   * 2. Automatically handles user pool changes (users added/removed from roles)
   * 3. Ensures fair distribution of tasks among eligible users
   * 4. Provides detailed logging and error handling
   *
   * Usage:
   * - When a user selects "Round Robin Assignment" from the user list
   * - The system automatically assigns the next user in the rotation for that role
   * - State is persisted in the database for consistency across server restarts
   *
   * @param data Assignment data including task execution ID, role ID, and assigned by user
   * @returns Assignment result with detailed round robin information
   */
  async assignRoundRobinToTask(data: {
    task_execution_id: string;
    role_id: string;
    assigned_by: string;
    workflow_execution_id?: string;
    notes?: string;
  }): Promise<any> {
    try {
      const {
        task_execution_id,
        role_id,
        assigned_by,
        workflow_execution_id,
        notes,
      } = data;

      this.logger.log(
        `Processing round robin assignment for task: ${task_execution_id}, role: ${role_id}`,
      );

      // Define round robin context based on role
      const roundRobinContext: RoundRobinContext = {
        context_type: 'role',
        role_id: role_id,
        context_id: role_id,
      };

      // Get next user from round robin
      const roundRobinResult =
        await this.roundRobinService.getNextRoundRobinUser(roundRobinContext);

      if (!roundRobinResult) {
        throw new Error(
          `No users available for round robin assignment in role: ${role_id}`,
        );
      }

      // Convert user's _id to their numeric id for assignment
      const assignedUser = await this.userModel.findById(
        roundRobinResult.assigned_user._id,
      );
      if (!assignedUser) {
        throw new Error(
          `Assigned user not found: ${roundRobinResult.assigned_user._id}`,
        );
      }

      // Use existing assignment logic to assign the selected user
      const assignmentResult = await this.assignUserToTask({
        task_execution_id,
        user_id: assignedUser.id.toString(),
        work_flow_id: workflow_execution_id || '',
        assigned_by,
        // assignee_type: 'user',
      });

      this.logger.log(
        `Round robin assignment completed: ${JSON.stringify({
          task_execution_id,
          assigned_user: roundRobinResult.assigned_user.name,
          assignment_count: roundRobinResult.assignment_count,
          context: roundRobinContext,
        })}`,
      );

      return {
        success: true,
        message: `Round robin assignment completed successfully`,
        task_execution_id,
        assigned_user: {
          id: assignedUser.id,
          name: roundRobinResult.assigned_user.name,
          email: roundRobinResult.assigned_user.email,
        },
        round_robin_info: {
          context: roundRobinContext,
          assignment_count: roundRobinResult.assignment_count,
          next_user_index: roundRobinResult.next_user_index,
        },
        assignment_result: assignmentResult,
      };
    } catch (error) {
      this.logger.error(
        `Error in round robin assignment: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get next round robin user for workflow assignment based on user group
   */
  private async getNextRoundRobinAssignment(
    userGroupId?: string,
  ): Promise<{ id: string; value: string } | null> {
    try {
      // Use the provided user_group_id or default to the standard group
      const roleId = userGroupId;

      const context = {
        context_type: 'role' as const,
        context_id: undefined,
        role_id: roleId,
      };

      this.logger.log(
        `Getting round robin assignment for user group: ${roleId}`,
      );
      const roundRobinResult =
        await this.roundRobinService.getNextRoundRobinUser(context);

      console.log(roundRobinResult, 'roundRobinResult');

      if (roundRobinResult) {
        return {
          id: roundRobinResult.assigned_user.id,
          value: roundRobinResult.assigned_user.name,
        };
      }

      // Fallback to a default user if round robin fails
      this.logger.warn(
        `Round robin assignment failed for user group ${roleId}, using fallback`,
      );
      return {
        id: '',
        value: 'System Default',
      };
    } catch (error) {
      this.logger.error(
        `Error in round robin assignment for user group: ${error.message}`,
      );
      // Fallback to a default user
      return {
        id: '',
        value: 'System Default',
      };
    }
  }

  async assignMultipleUsersToFollowUpTasks(
    taskExecutionId: string,
    originalAssignValues: any[],
    assignedBy: string,
  ): Promise<any> {
    try {
      if (!originalAssignValues || originalAssignValues.length === 0) {
        this.logger.warn('No assignees to propagate to follow-up tasks');
        return { success: true, message: 'No assignees to propagate' };
      }

      // Convert the assignment values to the format expected by the assignment service
      const assignees = originalAssignValues.map((assignValue) => ({
        id: assignValue.id || assignValue.value,
        type: 'user' as 'user' | 'role', // Assuming these are user assignments, can be extended for roles
      }));

      // Use the assignment service to assign multiple users
      await this.assignmentService.assignToTask({
        task_execution_id: taskExecutionId,
        assignees: assignees,
        assigned_by: assignedBy,
        notes: 'Automatically assigned from secondary court notice',
      });

      this.logger.log(
        `Successfully assigned ${assignees.length} users to follow-up task ${taskExecutionId}`,
      );

      return {
        success: true,
        message: `Assigned ${assignees.length} users to follow-up task`,
        assigned_users_count: assignees.length,
        assigned_user_ids: assignees.map((a) => a.id),
      };
    } catch (error) {
      this.logger.error(
        `Error assigning multiple users to follow-up task: ${error.message}`,
      );
      throw error;
    }
  }
}

