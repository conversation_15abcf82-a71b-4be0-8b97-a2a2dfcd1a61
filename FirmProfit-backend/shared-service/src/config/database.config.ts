import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { User, User<PERSON><PERSON>, Tenant, TenantConfig, Example } from '@shared/database';

export const databaseConfig: TypeOrmModuleOptions = {
  type: 'postgres',
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT, 10),
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  // synchronize: false, // Set to false since tables already exist
  entities: [User, UserMfa, Tenant, TenantConfig, Example],
  synchronize: process.env.DB_SYNC === 'true',
  logging: process.env.DB_LOGGING === 'true',
  maxQueryExecutionTime: 10000,
  cache: {
    duration: 1000,
  },
  migrationsRun: true,
};
