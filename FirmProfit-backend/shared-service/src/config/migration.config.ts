import { User, UserMfa } from '@shared/database';
import { DataSource } from 'typeorm';

export default new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT),
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  entities: [User, UserMfa],
  migrations: ['src/migrations/*{.ts,.js}'],
  synchronize: false, // Always keep this false in production
  logging: true, // Enable to check SQL logs
  schema: 'stephen_schema',
  maxQueryExecutionTime: 10000,
  cache: {
    duration: 1000,
  },
  migrationsRun: true,
});
