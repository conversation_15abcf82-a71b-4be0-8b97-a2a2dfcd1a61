import { registerAs } from '@nestjs/config';

export default registerAs('mail', () => ({
  sourceEmail: process.env.AWS_SOURCE_EMAIL,
  gmailHost: process.env.GMAIL_HOST,
  mailPort: process.env.MAIL_PORT,
  gmailUser: process.env.GMAIL_USER,
  gmailPass: process.env.GMAIL_PASS,
  awsAccessKeyId: process.env.ACCESS_KEY_ID,
  awsSecretAccessKey: process.env.SECRET_ACCESS_KEY,
  awsRegion: process.env.AWS_REGION,
}));
