import { RpcException } from '@nestjs/microservices';
import { HttpStatus } from '@nestjs/common';
import * as MESSAGE from '../locales/en.json'; // Ensure correct import path

export const ErrorExceptions = {
  UserNotFound(): RpcException {
    return new RpcException({
      message: MESSAGE.USER_NOT_FOUND_ERROR,
      statusCode: HttpStatus.NOT_FOUND,
    });
  },

  UserInActive(): RpcException {
    return new RpcException({
      message: MESSAGE.USER_INACTIVE_ERROR,
      statusCode: HttpStatus.UNAUTHORIZED,
    });
  },

  InCorrectMFACode(): RpcException {
    return new RpcException({
      message: MESSAGE.INCORRECT_MFA_CODE_ERROR,
      statusCode: HttpStatus.CONFLICT,
    });
  },

  MULTIPLE_LOGIN_ATTEMPTS(): RpcException {
    return new RpcException({
      message: MESSAGE.MULTIPLE_LOGIN_ATTEMPTS,
      statusCode: HttpStatus.CONFLICT,
    });
  },

  INVALID_CRED(): RpcException {
    return new RpcException({
      message: MESSAGE.INVALID_CRED,
      statusCode: HttpStatus.UNAUTHORIZED,
    });
  },

  MFA_NOT_SETUP(): RpcException {
    return new RpcException({
      message: MESSAGE.MFA_NOT_SETUP_ERROR,
      statusCode: HttpStatus.NOT_FOUND,
    });
  },

  MULTIPLE_TIME_ATTEMPTS(): RpcException {
    return new RpcException({
      message: MESSAGE.MULTIPLE_TIME_ATTEMPTS,
      statusCode: HttpStatus.NOT_FOUND,
    });
  },

  INVALID_MFA_CODE(): RpcException {
    return new RpcException({
      message: MESSAGE.INVALID_MFA_CODE,
      statusCode: HttpStatus.NOT_FOUND,
    });
  },

  USER_NOT_FOUND_OR_MFA_NOT(): RpcException {
    return new RpcException({
      message: MESSAGE.USER_NOT_FOUND_MFA_NOT_VALID,
      statusCode: HttpStatus.NOT_FOUND,
    });
  },

  INVALID_TOKEN(): RpcException {
    return new RpcException({
      message: MESSAGE.INVALID_TOKEN,
      statusCode: HttpStatus.NOT_FOUND,
    });
  },

  TASK_NOT_FOUND(): RpcException {
    return new RpcException({
      message: MESSAGE.TASK_EXECUTION_NOT_FOUND,
      statusCode: HttpStatus.NOT_FOUND,
    });
  },

  WORK_FLOW_EXECUTION_NOT_FOUND(): RpcException {
    return new RpcException({
      message: MESSAGE.WORK_FLOW_EXECUTION_NOT_FOUND,
      statusCode: HttpStatus.NOT_FOUND,
    });
  },

  TEMPLATE_NOT_FOUND(): RpcException {
    return new RpcException({
      message: MESSAGE.TEMPLATE_NOT_FOUND,
      statusCode: HttpStatus.NOT_FOUND,
    });
  },

  INVALID_TASK_ID(): RpcException {
    return new RpcException({
      message: MESSAGE.INVALID_TASK_ID,
      statusCode: HttpStatus.NOT_FOUND,
    });
  },
};
