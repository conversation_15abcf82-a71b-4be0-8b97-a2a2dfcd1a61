import { Catch, ExceptionFilter, ArgumentsHost, Logger } from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';
import { Observable, throwError } from 'rxjs';
import { status } from '@grpc/grpc-js';

@Catch(RpcException)
export class GrpcExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GrpcExceptionFilter.name);

  catch(exception: RpcException, host: ArgumentsHost): Observable<any> {
    const error = exception.getError();
    const errorMessage = typeof error === 'object' ? error['message'] : error;

    this.logger.error(`gRPC Exception: ${errorMessage}`);

    // Map the error to a gRPC status code
    const statusCode = this.getStatusCode(exception);

    return throwError(() => ({
      code: statusCode,
      message: errorMessage,
      details: typeof error === 'object' ? error : {},
    }));
  }

  private getStatusCode(exception: RpcException): number {
    const error = exception.getError();

    if (typeof error === 'object' && error['code']) {
      return error['code'];
    }

    // Default mapping of common errors
    if (typeof error === 'string') {
      if (error.includes('not found')) {
        return status.NOT_FOUND;
      }
      if (error.includes('already exists')) {
        return status.ALREADY_EXISTS;
      }
      if (error.includes('invalid')) {
        return status.INVALID_ARGUMENT;
      }
      if (error.includes('unauthorized') || error.includes('authentication')) {
        return status.UNAUTHENTICATED;
      }
      if (error.includes('permission') || error.includes('forbidden')) {
        return status.PERMISSION_DENIED;
      }
    }

    // Default to internal error if we can't map it
    return status.INTERNAL;
  }
}
