import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateUserTable1723559323123 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'users',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'email',
            type: 'varchar',
          },
          {
            name: 'timezone',
            type: 'varchar',
          },
          {
            name: 'password',
            type: 'varchar',
          },
          {
            name: 'roleId',
            type: 'int',
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: false,
          },
          {
            name: 'isDeleted',
            type: 'boolean',
            default: false,
          },
          {
            name: 'mfaEnabled',
            type: 'boolean',
            default: false,
          },
          {
            name: 'loginAttemptCount',
            type: 'int',
            default: 0,
          },
          {
            name: 'token',
            type: 'varchar',
            isNullable: true,
            default: null,
          },
          {
            name: 'loginAttemptMFACount',
            type: 'int',
            default: 0,
          },
          {
            name: 'lastInCorrectLogin', // ✅ Added lastInCorrectLogin column
            type: 'timestamp',
            isNullable: true,
            default: null,
          },
          {
            name: 'lastInCorrectMFALogin',
            type: 'timestamp',
            isNullable: true,
            default: null,
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('users');
  }
}
