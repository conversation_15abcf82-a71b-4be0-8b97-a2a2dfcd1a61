// common.ts
/**
 * Format a timestamp as a relative time string (e.g., "just now", "2 minutes ago", "3 hours ago", "2 days ago", etc.)
 * @param timestamp - Date object, ISO date string, or timestamp to format
 * @returns Formatted relative time string
 */
export const getRelativeTimeString = (
  timestamp: Date | string | number,
): string => {
  const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  // Handle invalid dates
  if (isNaN(date.getTime())) {
    return 'Invalid date';
  }

  // Just now: less than 60 seconds ago
  if (diffInSeconds < 60) {
    return 'Just Now';
  }

  // Minutes: 60 seconds to 60 minutes
  if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
  }

  // Hours: 1 hour to 24 hours
  if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
  }

  // Days: 1 day to 7 days
  if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} day${days !== 1 ? 's' : ''} ago`;
  }

  // Weeks: 1 week to 4 weeks
  if (diffInSeconds < 2419200) {
    const weeks = Math.floor(diffInSeconds / 604800);
    return `${weeks} week${weeks !== 1 ? 's' : ''} ago`;
  }

  // Months: 1 month to 12 months
  if (diffInSeconds < 31536000) {
    const months = Math.floor(diffInSeconds / 2628000); // Approximate month in seconds
    return `${months} month${months !== 1 ? 's' : ''} ago`;
  }

  // Years: 1+ years
  const years = Math.floor(diffInSeconds / 31536000);
  console.log('===============', `${years} year${years !== 1 ? 's' : ''} ago`);
  return `${years} year${years !== 1 ? 's' : ''} ago`;
};

/**
 * Format a timestamp as a relative time string in a specific timezone
 * @param timestamp - Date object, ISO date string, or timestamp to format (assumed to be UTC)
 * @param userTimezone - User's timezone (e.g., 'America/New_York')
 * @returns Formatted relative time string adjusted for user timezone
 */
export const getRelativeTimeStringWithTimezone = (
  timestamp: Date | string | number,
  timezone: string
): string => {
  if (!timezone) {
    return "Invalid timezone";
  }
  const moment = require("moment-timezone");

  // Convert timestamp to moment in UTC, then to given timezone

  console.log(timestamp, timezone, "TIMEXONEE")
  const date = moment.utc(timestamp).tz(timezone);
  const now = moment.utc().tz(timezone);

  if (!date.isValid()) {
    return "Invalid date";
  }

  const diffInSeconds = Math.floor(now.diff(date, "seconds"));

  // Just now: less than 60 seconds ago
  if (diffInSeconds < 60) {
    return "Just Now";
  }

  // Minutes
  if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} minute${minutes !== 1 ? "s" : ""} ago`;
  }

  // Hours
  if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} hour${hours !== 1 ? "s" : ""} ago`;
  }

  // Days
  if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} day${days !== 1 ? "s" : ""} ago`;
  }

  // Weeks
  if (diffInSeconds < 2419200) {
    const weeks = Math.floor(diffInSeconds / 604800);
    return `${weeks} week${weeks !== 1 ? "s" : ""} ago`;
  }

  // Months
  if (diffInSeconds < 31536000) {
    const months = Math.floor(diffInSeconds / 2628000);
    return `${months} month${months !== 1 ? "s" : ""} ago`;
  }

  // Years
  const years = Math.floor(diffInSeconds / 31536000);
  return `${years} year${years !== 1 ? "s" : ""} ago`;
};

const moment = require("moment-timezone");

/**
 * Returns human-readable "ago" string based on *local clock time perception*
 * @param {Date|string|number} timestamp - UTC time stored in DB
 * @param {string} creationTimezone - Timezone where it was created (e.g., "Asia/Kolkata")
 * @param {string} currentTimezone - Timezone where user is now
 */
export const getRelativeTimeTimezoneShifted = (timestamp, creationTimezone, currentTimezone) => {
  if (!creationTimezone || !currentTimezone) {
    return "Invalid timezone";
  }

  // Step 1: Interpret the stored UTC as *local time* in creation timezone
  const createdLocalTime = moment.utc(timestamp).tz(creationTimezone);

  // Step 2: Convert the *clock time* to the current timezone
  const createdAsCurrentLocalTime = moment.tz(
    createdLocalTime.format("YYYY-MM-DD HH:mm:ss"),
    currentTimezone
  );

  // Step 3: Compare current local time with that shifted creation time
  const nowLocal = moment().tz(currentTimezone);
  const diffInSeconds = nowLocal.diff(createdAsCurrentLocalTime, "seconds");

  // Format output
  if (diffInSeconds < 60) return "Just Now";
  if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} minute${minutes !== 1 ? "s" : ""} ago`;
  }
  if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} hour${hours !== 1 ? "s" : ""} ago`;
  }
  const days = Math.floor(diffInSeconds / 86400);
  return `${days} day${days !== 1 ? "s" : ""} ago`;
}

/**
 * Get current date and time in a specific timezone with given format
 * @param timezone - Target timezone (e.g., 'Asia/Kolkata', 'Pacific/Auckland')
 * @param format - Moment.js format string (e.g., 'MM/DD/YYYY', 'YYYY-MM-DD HH:mm:ss')
 * @returns Formatted current date/time string in the specified timezone
 */
export const getCurrentDateInTimezone = (timezone: string, format: string): string => {
  console.log(timezone, "TIMEZONEEE")
  if (!moment.tz.zone(timezone)) {
    throw new Error(`Invalid timezone: ${timezone}`);
  }
  // Always get fresh current time to avoid caching issues
  return moment().tz(timezone).format(format);
}

/**
 * Format an existing date/timestamp to a specific timezone and format
 * @param date - Date object, ISO date string, or timestamp to format
 * @param timezone - Target timezone (e.g., 'Asia/Kolkata', 'Pacific/Auckland')
 * @param format - Moment.js format string (e.g., 'MM/DD/YYYY', 'YYYY-MM-DD HH:mm:ss')
 * @returns Formatted date string in the specified timezone
 */
export const formatDateInTimezone = (
  date: Date | string | number,
  timezone: string,
  format: string
): string => {
  if (!timezone) {
    throw new Error("Timezone is required");
  }

  if (!moment.tz.zone(timezone)) {
    throw new Error(`Invalid timezone: ${timezone}`);
  }

  if (!date) {
    return '';
  }

  try {
    // Create moment object from the input date
    // If it's already a formatted string, parse it first
    let momentDate;

    if (typeof date === 'string' && date.includes('/')) {
      // Handle MM/DD/YYYY format specifically
      momentDate = moment(date, 'MM/DD/YYYY');
    } else {
      // Handle ISO strings, timestamps, and Date objects
      momentDate = moment(date);
    }

    if (!momentDate.isValid()) {
      throw new Error("Invalid date provided");
    }

    // Convert to the target timezone and format
    return momentDate.tz(timezone).format(format);
  } catch (error) {
    console.error('Error formatting date in timezone:', error);
    return '';
  }
}

/**
 * Get today's date in a specific timezone with given format
 * @param timezone - Target timezone (e.g., 'Asia/Kolkata', 'Pacific/Auckland')
 * @param format - Moment.js format string (e.g., 'MM/DD/YYYY', 'YYYY-MM-DD')
 * @returns Today's date string in the specified timezone
 */
export const getTodayInTimezone = (timezone: string, format: string = 'MM/DD/YYYY'): string => {
  if (!moment.tz.zone(timezone)) {
    throw new Error(`Invalid timezone: ${timezone}`);
  }

  // Get today's date in the specified timezone
  return moment().tz(timezone).format(format);
}