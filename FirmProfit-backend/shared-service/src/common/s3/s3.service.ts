import { Injectable } from '@nestjs/common';
import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
  DeleteObjectCommand,
  GetObjectCommandOutput,
  CopyObjectCommand,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

@Injectable()
export class S3Service {
  private readonly s3Client: S3Client;

  constructor() {
    this.s3Client = new S3Client({
      region: process.env.AWS_REGION_s3,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID_s3,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY_s3,
      },
      requestHandler: {
        socketTimeout: 5 * 60 * 1000,
      },
      // useAccelerateEndpoint: true,
    });
  }

  async generatePresignedUrl(
    key: string,
    operation: 'get' | 'put' = 'get',
    contentType?: string,
    expiresIn: number = 3600, // Default 1 hour for uploads, 7 days for downloads
  ): Promise<string> {
    try {
      // const command = new GetObjectCommand({
      //   Bucket: process.env.AWS_BUCKET_NAME_s3,
      //   Key: key,
      // });
      // Choose command based on operation
      const command =
        operation === 'put'
          ? new PutObjectCommand({
              Bucket: process.env.AWS_BUCKET_NAME_s3,
              Key: key,
              ContentType: contentType, // Default to PDF for uploads
              ACL: 'bucket-owner-full-control', // This will auto-include x-amz-acl
            })
          : new GetObjectCommand({
              Bucket: process.env.AWS_BUCKET_NAME_s3,
              Key: key,
            });

      // Use shorter expiration for uploads
      const urlExpiration = operation === 'put' ? 3600 : expiresIn;

      const url = await getSignedUrl(this.s3Client, command, {
        expiresIn: urlExpiration,
      });

      return url;
    } catch (error) {
      throw new Error(`Failed to generate presigned URL: ${error.message}`);
    }
  }

  // async uploadFile(
  //   file: Express.Multer.File,
  //   bucketName: string,
  // ): Promise<string> {
  //   try {
  //     await this.s3Client.send(
  //       new PutObjectCommand({
  //         Bucket: bucketName,
  //         Key: file.originalname,
  //         Body: file.buffer,
  //         ContentType: file.mimetype,
  //       }),
  //     );
  //     return file.originalname;
  //   } catch (error) {
  //     throw new Error(`Failed to upload file to S3: ${error.message}`);
  //   }
  // }

  async deleteFile(key: string): Promise<void> {
    try {
      await this.s3Client.send(
        new DeleteObjectCommand({
          Bucket: process.env.AWS_BUCKET_NAME_s3,
          Key: key,
        }),
      );
    } catch (error) {
      throw new Error(`Failed to delete file from S3: ${error.message}`);
    }
  }

  // async renameFile(oldKey: string, newKey: string): Promise<void> {
  //   try {
  //     // Step 1: Copy the file to the new key
  //     await this.s3Client.send(
  //       new CopyObjectCommand({
  //         Bucket: process.env.AWS_BUCKET_NAME_s3,
  //         CopySource: `${process.env.AWS_BUCKET_NAME_s3}/${oldKey}`,
  //         Key: newKey,
  //         ACL: 'bucket-owner-full-control', // Optional: Keep access control consistent
  //       }),
  //     );

  //     // Step 2: Delete the old file
  //     // await this.s3Client.send(
  //     //   new DeleteObjectCommand({
  //     //     Bucket: process.env.AWS_BUCKET_NAME_s3,
  //     //     Key: oldKey,
  //     //   }),
  //     // );
  //   } catch (error) {
  //     throw new Error(`Failed to rename file in S3: ${error.message}`);
  //   }
  // }

  async renameFile(oldKey: string, newKey: string): Promise<void> {
    try {
      console.log(`Starting rename operation: ${oldKey} -> ${newKey}`);

      // Step 1: Copy the file to the new key (simplified version)
      const copyCommand = new CopyObjectCommand({
        Bucket: process.env.AWS_BUCKET_NAME_s3,
        CopySource: `${process.env.AWS_BUCKET_NAME_s3}/${oldKey}`,
        Key: newKey,
      });

      console.log(`Copying file...`);
      await this.s3Client.send(copyCommand);
      console.log(`File copied successfully`);

      // Step 2: Delete the old file
      const deleteCommand = new DeleteObjectCommand({
        Bucket: process.env.AWS_BUCKET_NAME_s3,
        Key: oldKey,
      });

      console.log(`Deleting old file...`);
      await this.s3Client.send(deleteCommand);
      console.log(`Old file deleted successfully`);

      console.log(`Successfully renamed ${oldKey} to ${newKey}`);
    } catch (error) {
      console.error(
        `Failed to rename file in S3 from ${oldKey} to ${newKey}:`,
        error,
      );
      console.error(`Error details:`, {
        name: error.name,
        message: error.message,
        statusCode: error.$metadata?.httpStatusCode,
        requestId: error.$metadata?.requestId,
      });

      throw new Error(`Failed to rename file in S3: ${error.message}`);
    }
  }

  async getObject(
    bucketName: string,
    key: string,
  ): Promise<GetObjectCommandOutput> {
    const command = new GetObjectCommand({
      Bucket: bucketName,
      Key: key,
    });
    const response = await this.s3Client.send(command);
    return response;
  }
}
