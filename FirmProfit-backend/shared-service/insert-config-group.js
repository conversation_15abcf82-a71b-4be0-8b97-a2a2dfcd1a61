// Simple script to insert configGroup data into MongoDB
// Usage: node insert-config-group.js

const { MongoClient, ObjectId } = require('mongodb');

const insertConfigGroup = async () => {
  // Replace these with your actual MongoDB connection details
  const uri = process.env.MONGO_URI || 'mongodb://localhost:27017';
  const dbName = process.env.MONGO_DB_NAME || 'firmprofit';
  
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db(dbName);
    const collection = db.collection('configGroup');
    
    const configGroupData = {
      name: 'Court Notice Configuration',
      description: 'Configuration for court notice and followup group assignments',
      newcourtnoticGroupid: new ObjectId('6877420fd4928f6a37ba1b95'),
      followupgroupid: new ObjectId('6877420fd4928f6a37ba1b95'),
      is_active: true,
      is_deleted: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    // Remove existing configGroup documents (optional)
    await collection.deleteMany({});
    console.log('Cleared existing configGroup documents');
    
    // Insert new configGroup document
    const result = await collection.insertOne(configGroupData);
    console.log('✅ ConfigGroup inserted successfully!');
    console.log('Inserted document ID:', result.insertedId);
    
    // Verify the insertion
    const inserted = await collection.findOne({ _id: result.insertedId });
    console.log('Verified inserted document:', JSON.stringify(inserted, null, 2));
    
  } catch (error) {
    console.error('❌ Error inserting configGroup:', error);
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
};

// Run the script
insertConfigGroup().catch(console.error);
