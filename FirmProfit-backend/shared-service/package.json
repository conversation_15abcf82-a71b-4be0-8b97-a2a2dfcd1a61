{"name": "shared-service", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "node --require ts-node/register ./node_modules/typeorm/cli.js", "migration:run": "dotenv -e .env -- npm run typeorm migration:run -- -d src/config/migration.config.ts", "seed": "dotenv -e .env -- ts-node -r tsconfig-paths/register src/seeds/index.ts", "seed:all": "npm run migration:run && npm run seed"}, "dependencies": {"@shared/database": "file:../shared", "@aws-sdk/client-s3": "^3.812.0", "@aws-sdk/client-ses": "^3.777.0", "@aws-sdk/credential-providers": "^3.777.0", "@aws-sdk/s3-request-presigner": "^3.812.0", "@grpc/grpc-js": "^1.13.2", "@grpc/proto-loader": "^0.7.13", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.12", "@nestjs/config": "^4.0.1", "@nestjs/core": "^11.0.12", "@nestjs/jwt": "^11.0.0", "@nestjs/microservices": "^11.0.12", "@nestjs/mongoose": "^11.0.3", "@nestjs/platform-express": "^11.0.12", "@nestjs/schedule": "^6.0.0", "@nestjs/typeorm": "^11.0.0", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "dotenv": "^16.4.7", "form-data": "^4.0.3", "google-protobuf": "^3.21.4", "jsonwebtoken": "^9.0.2", "mailcomposer": "^4.0.2", "moment": "^2.30.1", "moment-timezone": "^0.6.0", "mongoose": "^8.14.2", "nodemailer": "^6.10.0", "pg": "^8.14.1", "qrcode": "^1.5.4", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "speakeasy": "^2.0.0", "typeorm": "^0.3.21"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^11.0.12", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.0", "@types/form-data": "^2.2.1", "@types/jest": "^29.5.14", "@types/node": "^20.3.1", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "dotenv-cli": "^8.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.7.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.3.4", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}