syntax = "proto3";

package user;

service UserService {
  // User operations
  rpc SignUp (SignUpRequest) returns (UserResponse) {}
  rpc SignIn (SignInRequest) returns (SignInResponse) {}
  rpc VerifyUser (SignInRequest) returns (UserInfo) {}
  rpc ValidateUserToken (ValidateTokenRequest) returns (ValidateTokenResponse) {}
  rpc GetUserById (GetUserByIdRequest) returns (UserInfo) {}
  
  // MFA operations
  rpc EnableMfa (EnableMfaRequest) returns (EnableMfaResponse) {}
  rpc VerifyMfa (VerifyMfaRequest) returns (VerifyMfaResponse) {}
  rpc ValidateMfa (ValidateMfaRequest) returns (ValidateMfaResponse) {}
  rpc HandleMfa (HandleMfaRequest) returns (HandleMfaResponse) {}
  
  // Password operations
  rpc ResetPassword (ResetPasswordRequest) returns (ResetPasswordResponse) {}
  rpc CheckAuthTokenInDB (CheckTokenRequest) returns (CheckTokenResponse) {}
}

// User operation messages
message SignUpRequest {
  string email = 1;
  string password = 2;
  string username = 3;
  string firstName = 4;
  string lastName = 5;
  int32 roleId = 6;
  bool isActive = 7;
  bool isDeleted = 8;
}

message UserResponse {
  int32 id = 1;
  string email = 2;
  int32 roleId = 3;
  string username = 4;
  string firstName = 5;
  string lastName = 6;
  string accessToken = 7;
}

message SignInRequest {
  string email = 1;
  string password = 2;
  string timezone = 3;
}

message SignInResponse {
  UserInfo user = 1;
  string accessToken = 2;
  bool requiresMfa = 3;
}

message UserInfo {
  int32 id = 1;
  string email = 2;
  int32 roleId = 3;
  optional string username = 4;
  optional string firstName = 5;
  optional string lastName = 6;
  optional int32 loginAttemptCount = 7;
  optional bool lockedAccount = 8;
  optional bool mfaEnabled = 9;
  string access_token = 10;
}

message ValidateTokenRequest {
  string token = 1;
}

message ValidateTokenResponse {
  bool valid = 1;
  string message = 2;
}

message GetUserByIdRequest {
  int32 id = 1;
}

// MFA operation messages
message EnableMfaRequest {
  int32 userId = 1;
}

message EnableMfaResponse {
  string secret = 1;
  string qrCode = 2;
}

message VerifyMfaRequest {
  int32 userId = 1;
  string otp = 2;
}

message VerifyMfaResponse {
  bool success = 1;
  string message = 2;
  string accessToken = 3;
}

message ValidateMfaRequest {
  int32 userId = 1;
  string otp = 2;
}

message ValidateMfaResponse {
  string accessToken = 1;
  UserInfo user = 2;
}

message HandleMfaRequest {
  string action = 1;
  int32 userId = 2;
  optional string otp = 3;
}

message HandleMfaResponse {
  bool success = 1;
  string message = 2;
  optional string accessToken = 3;
  optional string secret = 4;
  optional string qrCode = 5;
  optional UserInfo user = 6;
}

// Password operation messages
message ResetPasswordRequest {
  string email = 1;
  string password = 2;
}

message ResetPasswordResponse {
  bool success = 1;
  string message = 2;
}

message CheckTokenRequest {
  string token = 1;
  string email = 2;
}

message CheckTokenResponse {
  bool valid = 1;
  string message = 2;
} 