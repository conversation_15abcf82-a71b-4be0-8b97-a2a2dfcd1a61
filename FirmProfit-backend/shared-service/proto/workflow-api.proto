syntax = "proto3";

package workflowapi;

import "google/protobuf/empty.proto";
import "google/protobuf/struct.proto";  // For google.protobuf.Value

service WorkflowService {
  rpc MyWorkFlow             (GetWorkflowRequest)      returns (WorkflowResponse);
  rpc workFlowRender<PERSON>pi      (GetWorkflowRenderDto)    returns (WorkflowRenderResponse);
  rpc handleOAuthCallbackApi (google.protobuf.Empty)   returns (handleOAuthCallbackResponse);
}

message GetWorkflowRenderDto {
  string work_flow_id = 1;
}

message handleOAuthCallbackResponse {
  string result = 1;
}

message GetWorkflowRequest {
  int32 page  = 1;
  int32 limit = 2;
}

message WorkflowResponse {
  string       status  = 1;
  string       message = 2;
  WorkflowData data    = 3;
}

message WorkflowRenderResponse {
  string data = 1;
 

}

message RenderWorkflow {
  string          id          = 1;
  string          name        = 2;
  string          description = 3;
  repeated RenderTask tasks   = 4;
}

message RenderTask {
  string          id                 = 1;
  string          name               = 2;
  string          description        = 3;
  string          icon               = 4;
  string          status             = 5;
  repeated FormField formFields      = 6;
  string          work_flow_id       = 7;
  bool            default_task       = 8;
  bool            selected_task      = 9;
  ConditionTask   condition_task     = 10;
  bool            is_reviewed       = 11;


  message ConditionTask {
    string value = 1;
    string id    = 2;
  }
}

message Condition {
  string            field      = 1;
  string            value      = 2;
  string            type       = 3;
  repeated Condition conditions = 4;
}
message EventItem {
  string id                 = 1;
  string caseNumber         = 2;
  string clientName         = 3;
  string description        = 4;
  string date               = 5;
  string startTime          = 6;
  string endTime            = 7;
  bool   isCompleted        = 8;
  string subject            = 9;
  string courtNoticeType    = 10;
  string appointmentAction  = 11;
  string charge             = 12;
  string exCountyOfArrest   = 13;
  string courtLocation      = 14;
  string optionalAttendees  = 15;
  string requiredAttendees  = 16;
  string clientAttendees    = 17;
  string meetingLocation    = 18;
  string startDate          = 19;
  string endDate            = 20;
  bool   allDay            = 21;
  string courtNoticeActions    = 22;
  string appointmentToReschedule    = 23;
 string county    = 24;
  string meetingLink    = 25;
  string clientAttendance    = 26;
  string court_notice_date    = 27;
  string phoneDetails    = 28;
  string meetingAddress    = 29;



}

message Matter {
  string        _id    = 1;
  string        name   = 2;
  repeated EventItem events = 3;
}

message Client {
  string         client_name = 1;
  repeated Matter matter      = 2;
}

message FormField {
  string                          id                    = 1;
  string                          type                  = 2;
  string                          title                 = 3;
  bool                            required              = 4;
  bool                            dynamic_fields        = 5;
  repeated Field                  fields                = 6;
  bool                            selected_task         = 7;
  bool                            dynamic_selected_task = 8;
  Condition                       condition             = 9;
  string                          label                 = 10;
  int32                           sequence              = 11;
  repeated Client                 client                = 12;
  map<string, EventItems>         event                = 13;  // Map of matter IDs to list of events
  string                          api_end_point                 = 14;

}

message EventItems {
  repeated EventItem event_items = 1;
}

message FieldValue {
  string id    = 1;
  string value = 2;
  string _id   = 3;
}

message Field {
  string               type        = 1;
  string               placeholder = 2;
  string               id          = 3;
  string               label       = 4;
  repeated Option      options     = 5;
  string               _id         = 6;
  repeated FieldValue  value       = 7;
  map<string, string>  validation  = 8;
  bool                 required    = 9;
  string               api_end_point       = 10;
}

message Option {
  string value = 1;
  string text  = 2;
  string _id   = 3;
}

message Workflow {
  int32              id          = 1;
  string             name        = 2;
  string             description = 3;
  repeated WorkflowStep steps     = 4;
  string             status      = 5;
  int32              createdBy   = 6;
  string             createdAt   = 7;
  optional int32     updatedBy   = 8;
  optional string    updatedAt   = 9;
  int32              version     = 10;
  optional int32     parentId    = 11;
  bool               isActive    = 12;
  bool               isDeleted   = 13;
}

message WorkflowData {
  int32 page      = 1;
  int32 limit     = 2;
  bool  processed = 3;
  string timestamp = 4;
}

enum WorkflowStep {
  UNKNOWN     = 0;
  STARTED     = 1;
  IN_PROGRESS = 2;
  COMPLETED   = 3;
  ON_TRACK   = 4;
}
