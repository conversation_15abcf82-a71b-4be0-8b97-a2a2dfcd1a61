{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "./dist/entities", "declaration": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "target": "ES2021", "module": "commonjs", "removeComments": false, "sourceMap": false}, "include": ["src/users/entities/**/*", "src/users/dto/**/*", "src/users/interfaces/**/*"], "exclude": ["node_modules", "test", "**/*spec.ts", "**/*test.ts"]}