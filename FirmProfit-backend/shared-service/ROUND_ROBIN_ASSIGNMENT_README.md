# Round Robin Assignment System

## Overview

This document describes the implementation of a scalable, maintainable, and reusable round robin assignment system for task allocation in the FirmProfit backend workflow service.

## Features

### 🎯 Core Functionality
- **Automatic User Pre-assignment**: System automatically selects the next user from Court Notice Paralegals group using round robin
- **User Choice Preserved**: Users can accept the auto-assigned user or change to someone else
- **Role-Based Context**: Maintains separate round robin states for different roles
- **Dynamic User Pool Management**: Automatically handles user additions/removals
- **State Persistence**: Maintains assignment state across server restarts
- **Fair Distribution**: Ensures each user gets equal task assignments over time

### 🏗️ Architecture
- **Scalable**: Supports multiple contexts (roles, workflows, global)
- **Maintainable**: Clean separation of concerns with dedicated service
- **Reusable**: Generic round robin logic applicable to various scenarios
- **Fault Tolerant**: Handles edge cases like inactive users, empty pools

## Implementation Details

### 1. Database Schema

#### RoundRobinState Entity
```typescript
{
  context_type: 'role' | 'workflow' | 'global',
  context_id: string,
  last_assigned_user: ObjectId,
  assignment_count: number,
  user_pool: ObjectId[],
  current_index: number,
  last_assignment_date: Date,
  is_active: boolean,
  is_deleted: boolean
}
```

### 2. Service Architecture

#### RoundRobinService
- **Primary Responsibility**: Manages round robin assignment logic
- **Key Methods**:
  - `getNextRoundRobinUser()`: Returns next user in rotation
  - `initializeRoundRobinState()`: Sets up new round robin context
  - `refreshUserPool()`: Updates user pool when changes occur
  - `getRoundRobinStats()`: Provides analytics and monitoring
  - `resetRoundRobinState()`: Administrative reset functionality

### 3. Integration Points

#### getUserList() Method
```typescript
// Returns user list with auto-assigned user from Court Notice Paralegals
{
  users: [
    {
      _id: 'user_123',
      value: 'John Doe (Auto-assigned)',
      text: 'John Doe (Auto-assigned)',
      is_active: true,
      type: 'auto-assigned',
      round_robin_info: {
        assignment_count: 5,
        context: { context_type: 'role', role_id: '1' },
        auto_assigned: true
      }
    },
    // ... other users (user can change to any of these)
  ]
}
```

#### Assignment Methods
- `assignUserToTask()`: Detects round robin selection and delegates
- `assignToTask()`: Supports round robin in bulk assignments
- `assignRoundRobinToTask()`: Direct round robin assignment method

## Usage Examples

### 1. Frontend Integration
```javascript
// System automatically provides round robin assigned user
const response = await getUsers({ role_id: 'paralegal_role' });
const autoAssignedUser = response.users[0]; // First user is auto-assigned from Court Notice Paralegals

// Frontend can display this as default selection
if (autoAssignedUser.type === 'auto-assigned') {
  console.log(`Auto-assigned: ${autoAssignedUser.value}`);
  console.log(`Assignment count: ${autoAssignedUser.round_robin_info.assignment_count}`);
}

// User can accept auto-assignment or change to someone else
const selectedUser = autoAssignedUser; // or user selects different user
const assignment = await assignToTask({
  task_execution_id: 'task_123',
  assignees: [{ id: selectedUser._id, type: 'user' }],
  assigned_by: 'user_456'
});
```

### 2. Direct API Usage
```javascript
// Get round robin statistics
const stats = await getRoundRobinStats({
  context_type: 'role',
  role_id: 'paralegal_role'
});

// Reset round robin state
await resetRoundRobinState({
  context_type: 'role',
  role_id: 'paralegal_role'
});
```

### 3. Administrative Management
```javascript
// Check current round robin status
const stats = await workflowService.roundRobinService.getRoundRobinStats({
  context_type: 'role',
  role_id: 'court_notice_paralegal'
});

console.log({
  initialized: stats.initialized,
  total_assignments: stats.total_assignments,
  user_pool_size: stats.user_pool_size,
  current_index: stats.current_index
});
```

## Configuration

### Default Role Context
The system uses a default role ID when no specific role is provided:
```typescript
const defaultRoleId = '6877420fd4928f6a37ba1b95';
```

### Context Types
- **role**: Round robin within a specific role
- **workflow**: Round robin within a workflow context
- **global**: Global round robin across all users

## API Endpoints

### GRPC Methods

#### GetRoundRobinStats
```typescript
@GrpcMethod('WorkflowSharedService', 'GetRoundRobinStats')
async getRoundRobinStats(data: { 
  context_type: 'role' | 'workflow' | 'global'; 
  context_id?: string; 
  role_id?: string 
})
```

#### ResetRoundRobinState
```typescript
@GrpcMethod('WorkflowSharedService', 'ResetRoundRobinState')
async resetRoundRobinState(data: { 
  context_type: 'role' | 'workflow' | 'global'; 
  context_id?: string; 
  role_id?: string 
})
```

#### AssignRoundRobinToTask
```typescript
@GrpcMethod('WorkflowSharedService', 'AssignRoundRobinToTask')
async assignRoundRobinToTask(data: { 
  task_execution_id: string; 
  role_id: string; 
  assigned_by: string; 
  workflow_execution_id?: string; 
  notes?: string 
})
```

## Error Handling

### Common Scenarios
1. **No Users Available**: Returns null when no eligible users found
2. **Invalid User in Pool**: Automatically removes and retries
3. **Context Not Found**: Creates new round robin state automatically
4. **User Pool Changes**: Automatically refreshes and adjusts indices

### Logging
All operations are logged with appropriate detail levels:
- Info: Normal operations and assignments
- Warn: Edge cases like empty user pools
- Error: Critical failures with full stack traces

## Performance Considerations

### Database Optimization
- Compound index on `(context_type, context_id)` for fast lookups
- Efficient user pool queries with targeted filters
- Minimal database round trips per assignment

### Memory Efficiency
- User pools cached in round robin state documents
- Automatic cleanup of invalid user references
- Periodic user pool refresh only when needed

## Monitoring and Analytics

### Statistics Available
- Total assignments per context
- Current round robin position
- User pool size and composition
- Last assignment timestamp
- Assignment distribution fairness

### Health Checks
```typescript
// Check if round robin is working properly
const stats = await getRoundRobinStats({ context_type: 'role', role_id: 'xxx' });
const isHealthy = stats.user_pool_size > 0 && stats.total_assignments > 0;
```

## Testing Strategy

### Unit Tests
- Round robin state management
- User pool updates
- Edge case handling
- Error scenarios

### Integration Tests
- End-to-end assignment flow
- Multiple context scenarios
- User pool change handling
- State persistence verification

## Migration and Deployment

### Database Migration
The system automatically creates required indexes and collections on first use.

### Backwards Compatibility
- Existing assignment methods continue to work
- Round robin is opt-in via special user ID
- No impact on current workflows

## Best Practices

### 1. Context Selection
- Use role-based contexts for most scenarios
- Consider workflow contexts for complex routing
- Global context for simple round robin

### 2. Monitoring
- Regular statistics review
- Monitor user pool health
- Check assignment fairness

### 3. Administration
- Periodic round robin state resets if needed
- User pool verification after role changes
- Performance monitoring for large user pools

## Future Enhancements

### Planned Features
1. **Weighted Round Robin**: Different assignment weights per user
2. **Time-Based Context**: Round robin based on time periods
3. **Skill-Based Routing**: Factor in user skills and expertise
4. **Load Balancing**: Consider current user workload
5. **Custom Assignment Rules**: Configurable assignment algorithms

### Extension Points
The system is designed for easy extension:
- New context types can be added
- Custom user pool logic
- Pluggable assignment strategies
- Enhanced analytics and reporting

---

## Summary

The round robin assignment system provides a robust, scalable solution for automatic user pre-assignment from the Court Notice Paralegals group. The system:

- **Automatically assigns** the next user in round-robin fashion from Court Notice Paralegals when getUserList is called
- **Preserves user choice** by allowing users to change the auto-assigned user to anyone else
- **Maintains fairness** through persistent round-robin state tracking
- **Integrates seamlessly** with existing workflow processes
- **Provides transparency** with detailed assignment information and analytics

The implementation follows NestJS best practices and provides a solid foundation for future enhancements while ensuring fair distribution of tasks and maintaining user autonomy in final assignment decisions. 