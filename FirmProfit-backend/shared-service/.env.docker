# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_DATABASE=firmprofit
 
# JWT Configuration
JWT_SECRET=firmprofit_secret_key_should_be_longer_in_production
JWT_EXPIRES_IN=24h
 
# Email Configuration
AWS_SOURCE_EMAIL=<EMAIL>
AWS_EMAIL_HOST=smtp.gmail.com
MAIL_PORT=587
ACCESS_KEY_ID=AKIAXPK5SCYOKV5PTVU3
SECRET_ACCESS_KEY=BBvmwSyja/xFl0f7HjkEfT+oPEq4wTSkQI8QHuG+KFYj
GMAIL_HOST=smtp.gmail.com
GMAIL_USER=<EMAIL>
GMAIL_PASS=tmbbkoczmymbokin
baseUrl=http://localhost:3002

# gRPC Configuration
GRPC_URL=0.0.0.0:3001 