import { Module } from '@nestjs/common';

// This is a simple module that only provides shared entities, schemas, DTOs and types
// Each service should configure their own database connections using these shared components

@Module({
  imports: [],
  exports: [],
})
export class SharedDatabaseModule {
  // This module serves as a namespace for shared database components
  // Actual database configuration should be done in each service's app.module.ts
} 