# MyCase Integration Implementation

This document describes the complete implementation of MyCase API integration with tenant-based configuration, encrypted data storage, and frontend token management.

## Overview

The implementation provides:
1. PostgreSQL schema with `tenant_config` table for storing tenant-specific MyCase configurations
2. Backend services for tenant configuration management with encryption
3. MyCase authentication service using tenant configurations
4. Frontend services and components for token management and API calls

## Database Schema

### Table: `tenant_config`

```sql
CREATE TABLE tenant_config (
    tenant_id UUID NOT NULL,
    key TEXT NOT NULL,
    config JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (tenant_id, key)
);
```

### Demo Data

```sql
INSERT INTO tenant_config (tenant_id, key, config) 
VALUES (
    '11111111-1111-1111-1111-111111111111',
    'my_case_config',
    '{"api_key": "demoApiKey123", "refresh_key": "demoRefreshKey456"}'::jsonb
);
```

## Backend Implementation

### 1. Database Entity

**File:** `api-gateway/src/database/entities/tenant-config.entity.ts`

```typescript
@Entity('tenant_config')
export class TenantConfig {
  @PrimaryColumn('uuid')
  tenant_id: string;

  @PrimaryColumn('text')
  key: string;

  @Column('jsonb')
  config: Record<string, any>;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
```

### 2. Tenant Configuration Service

**File:** `api-gateway/src/tenant/services/tenant-config.service.ts`

Features:
- Encrypted storage of sensitive data (API keys, secrets)
- CRUD operations for tenant configurations
- Automatic encryption/decryption of sensitive fields

Key methods:
- `getMyCaseConfig(tenantId: string)`: Retrieve and decrypt MyCase config
- `saveMyCaseConfig(tenantId: string, config: MyCaseConfig)`: Save and encrypt MyCase config
- `encrypt(text: string)`: Encrypt sensitive data
- `decrypt(encryptedText: string)`: Decrypt sensitive data

### 3. MyCase Authentication Service

**File:** `api-gateway/src/mycase/services/mycase-auth.service.ts`

Features:
- Authenticate using tenant configuration
- Token management and refresh
- Integration with tenant config service

Key methods:
- `authenticateWithTenantConfig(tenantId: string)`: Authenticate using tenant's MyCase config
- `getValidAccessToken()`: Get valid token, refresh if needed
- `getAuthHeaders()`: Get authorization headers for API calls

### 4. Tenant Configuration Controller

**File:** `api-gateway/src/tenant/controllers/tenant-config.controller.ts`

API Endpoints:
- `GET /tenant-config/:tenantId/mycase` - Get MyCase configuration
- `POST /tenant-config/:tenantId/mycase` - Save MyCase configuration
- `POST /tenant-config/:tenantId/mycase/authenticate` - Authenticate with MyCase
- `GET /tenant-config/:tenantId/configs` - Get all tenant configurations
- `DELETE /tenant-config/:tenantId/config/:key` - Delete configuration

## Frontend Implementation

### 1. MyCase Token Service

**File:** `src/services/api/myCaseTokenService.ts`

Features:
- Token storage in localStorage with expiry management
- Tenant-based configuration management
- Authenticated API calls to MyCase

Key methods:
- `storeToken(tokenResponse)`: Store token with expiry
- `getToken()`: Get valid token (checks expiry)
- `authenticateWithTenantConfig()`: Authenticate using backend
- `getMyCaseAuthHeaders()`: Get headers with `my-case-Authorization`
- `makeMyCaseRequest()`: Make authenticated requests to MyCase API

### 2. React Hook

**File:** `src/hooks/useMyCaseAuth.ts`

Features:
- React state management for authentication
- Automatic token validation
- Configuration management

### 3. React Component

**File:** `src/components/MyCase/MyCaseAuthComponent.tsx`

Features:
- Configuration form for MyCase credentials
- Authentication status display
- Token management UI
- Usage instructions

## Environment Configuration

Add to `api-gateway/.env`:

```env
# MyCase API Configuration
MYCASE_API_BASE_URL=https://api.mycase.com
MYCASE_CLIENT_ID=your_mycase_client_id
MYCASE_CLIENT_SECRET=your_mycase_client_secret

# Encryption Configuration
ENCRYPTION_KEY=your_encryption_key_change_in_production_32_chars
```

## Setup Instructions

### 1. Database Setup

Run the SQL script to create the table:

```bash
# Connect to your PostgreSQL database and run:
psql -d your_database -f api-gateway/create-tenant-config-table.sql
```

### 2. Backend Setup

1. Install dependencies:
```bash
cd api-gateway
npm install
```

2. Update environment variables in `.env`

3. The modules are already integrated into the main application

### 3. Frontend Setup

1. The services and components are ready to use
2. Import and use the components:

```typescript
import MyCaseAuthComponent from './components/MyCase/MyCaseAuthComponent';
import { useMyCaseAuth } from './hooks/useMyCaseAuth';
import { myCaseTokenService } from './services/api/myCaseTokenService';
```

## Usage Examples

### Backend API Usage

```typescript
// Get MyCase configuration
GET /tenant-config/11111111-1111-1111-1111-111111111111/mycase

// Save MyCase configuration
POST /tenant-config/11111111-1111-1111-1111-111111111111/mycase
{
  "api_key": "your_api_key",
  "refresh_key": "your_refresh_key",
  "client_id": "optional_client_id",
  "client_secret": "optional_client_secret"
}

// Authenticate with MyCase
POST /tenant-config/11111111-1111-1111-1111-111111111111/mycase/authenticate
```

### Frontend Usage

```typescript
// Using the hook
const { isAuthenticated, authenticate, token } = useMyCaseAuth();

// Set tenant ID
myCaseTokenService.setCurrentTenantId('11111111-1111-1111-1111-111111111111');

// Authenticate
await authenticate();

// Make MyCase API calls
const cases = await myCaseTokenService.getMyCaseData('/cases');
const newCase = await myCaseTokenService.createMyCaseData('/cases', caseData);
```

### Using the React Component

```jsx
<MyCaseAuthComponent 
  tenantId="11111111-1111-1111-1111-111111111111"
  onAuthSuccess={(token) => console.log('Authenticated:', token)}
  onAuthError={(error) => console.error('Auth failed:', error)}
/>
```

## Security Features

1. **Encryption**: Sensitive data (API keys, secrets) are encrypted before storage
2. **Token Expiry**: Automatic token validation and refresh
3. **Tenant Isolation**: Configuration is isolated per tenant
4. **Secure Headers**: Uses `my-case-Authorization` header for API calls

## API Integration Flow

1. **Configuration**: Store MyCase credentials per tenant (encrypted)
2. **Authentication**: Use tenant config to authenticate with MyCase API
3. **Token Storage**: Store access token in frontend localStorage
4. **API Calls**: Use stored token in `my-case-Authorization` header
5. **Token Refresh**: Automatic refresh when token expires

## Error Handling

- Invalid credentials: Returns 401 with clear error message
- Missing configuration: Returns 404 with tenant-specific message
- Token expiry: Automatic refresh or re-authentication prompt
- Network errors: Proper error propagation to frontend

## Testing

### Test the Backend API

```bash
# Test configuration endpoint
curl -X GET http://localhost:3000/tenant-config/11111111-1111-1111-1111-111111111111/mycase

# Test authentication endpoint
curl -X POST http://localhost:3000/tenant-config/11111111-1111-1111-1111-111111111111/mycase/authenticate
```

### Test Frontend Integration

1. Open the React component in your application
2. Configure MyCase credentials
3. Click "Authenticate with MyCase"
4. Verify token is stored and API calls work

## Troubleshooting

### Common Issues

1. **Node.js Version**: Ensure Node.js version supports nullish coalescing (`??`)
2. **Database Connection**: Verify PostgreSQL connection and table creation
3. **Environment Variables**: Check all required env vars are set
4. **CORS**: Ensure CORS is configured for MyCase API calls
5. **Token Expiry**: Check token expiry handling in frontend

### Debug Steps

1. Check backend logs for authentication errors
2. Verify database contains tenant configuration
3. Test API endpoints with curl/Postman
4. Check browser console for frontend errors
5. Verify localStorage contains valid token

## Migration Notes

If you need to run TypeScript migrations with an older Node.js version, consider:
1. Using the provided SQL script instead
2. Upgrading Node.js to version 14+ 
3. Using a different migration approach

## Future Enhancements

1. **Multi-tenant UI**: Admin interface for managing tenant configurations
2. **Audit Logging**: Track configuration changes and API usage
3. **Rate Limiting**: Implement rate limiting for MyCase API calls
4. **Webhook Support**: Handle MyCase webhooks for real-time updates
5. **Batch Operations**: Support bulk operations with MyCase API 