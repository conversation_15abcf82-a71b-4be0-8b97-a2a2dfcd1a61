#!/usr/bin/env node
const axios = require('axios');

console.log('🔍 Testing Shared Entity Integration...\n');

const CONNECTORS_BASE_URL = 'http://localhost:3003';

async function testAPIs() {
  const tests = [
    {
      name: 'Test All Users API',
      url: `${CONNECTORS_BASE_URL}/users`,
      method: 'GET'
    },
    {
      name: 'Test Active Users API',
      url: `${CONNECTORS_BASE_URL}/users/active`,
      method: 'GET'
    },
    {
      name: 'Test Paginated Users API',
      url: `${CONNECTORS_BASE_URL}/users/paginated?page=1&limit=5`,
      method: 'GET'
    },
    {
      name: 'Test User Statistics API',
      url: `${CONNECTORS_BASE_URL}/users/statistics`,
      method: 'GET'
    },
    {
      name: 'Test Shared Entity Integration',
      url: `${CONNECTORS_BASE_URL}/users/test/shared-entity`,
      method: 'GET'
    }
  ];

  let successCount = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    try {
      console.log(`🧪 ${test.name}...`);
      const response = await axios.get(test.url, {
        timeout: 5000,
        validateStatus: () => true // Don't throw on HTTP errors
      });

      if (response.status >= 200 && response.status < 300) {
        console.log(`✅ ${test.name} - SUCCESS`);
        
        if (response.data.success) {
          console.log(`   📊 Response: ${response.data.message}`);
          if (response.data.data && response.data.data.total !== undefined) {
            console.log(`   📈 Total records: ${response.data.data.total}`);
          }
        }
        
        successCount++;
      } else {
        console.log(`❌ ${test.name} - FAILED (Status: ${response.status})`);
        console.log(`   📋 Response: ${response.data?.message || response.statusText}`);
      }
    } catch (error) {
      console.log(`❌ ${test.name} - ERROR`);
      if (error.code === 'ECONNREFUSED') {
        console.log(`   🔌 Connection refused - Is the connectors service running on port 3003?`);
      } else {
        console.log(`   📋 Error: ${error.message}`);
      }
    }
    console.log('');
  }

  console.log(`\n📊 Test Results: ${successCount}/${totalTests} tests passed\n`);

  if (successCount === totalTests) {
    console.log('🎉 All tests passed! Shared entity integration is working correctly.');
    console.log('✅ User entities are successfully shared between shared-service and connectors');
    console.log('✅ No code duplication detected');
    console.log('✅ APIs are accessible and functional');
  } else {
    console.log('⚠️  Some tests failed. Please check the services are running and properly configured.');
    console.log('\n🚀 To start the services:');
    console.log('   1. Terminal 1: cd shared-service && npm run start:dev');
    console.log('   2. Terminal 2: cd connectors && npm run start:dev');
  }
}

// Show usage information
console.log('📋 Instructions:');
console.log('1. Start shared-service: cd shared-service && npm run start:dev');
console.log('2. Start connectors: cd connectors && npm run start:dev'); 
console.log('3. Run this test: node test-setup.js\n');

// Run tests
testAPIs().catch(console.error); 