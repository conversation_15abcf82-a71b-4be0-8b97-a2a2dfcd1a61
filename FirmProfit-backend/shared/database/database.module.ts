import { Module, DynamicModule } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule, ConfigService } from '@nestjs/config';

// Import all TypeORM entities
import {
  User,
  UserMfa,
  Tenant,
  TenantConfig,
  Example
} from './entities';

// Import Mongoose schemas
import {
  Category,
  CategorySchema,
  Location,
  LocationSchema,
  Email,
  EmailSchema,
  Client,
  ClientSchema
} from './schemas';

@Module({})
export class SharedDatabaseModule {
  static forRoot(): DynamicModule {
    return {
      module: SharedDatabaseModule,
      imports: [
        // TypeORM Configuration (PostgreSQL)
        TypeOrmModule.forRootAsync({
          name: 'default',
          imports: [ConfigModule],
          inject: [ConfigService],
          useFactory: (configService: ConfigService) => {
            const dbConfig = configService.get('database') || configService.get('db')?.postgres;
            
            return {
              type: 'postgres',
              host: dbConfig?.host || configService.get('DB_HOST') || 'localhost',
              port: dbConfig?.port || configService.get('DB_PORT') || 5432,
              username: dbConfig?.username || configService.get('DB_USERNAME') || 'postgres',
              password: dbConfig?.password || configService.get('DB_PASSWORD') || 'postgres',
              database: dbConfig?.database || configService.get('DB_NAME') || 'firmprofit',
              entities: [User, UserMfa, Tenant, TenantConfig, Example],
              synchronize: configService.get('NODE_ENV') !== 'production' && (dbConfig?.synchronize || configService.get('DB_SYNC') === 'true'),
              logging: configService.get('NODE_ENV') !== 'production' && (dbConfig?.logging || configService.get('DB_LOGGING') === 'true'),
              schema: dbConfig?.schema || configService.get('DB_SCHEMA') || 'public',
              maxQueryExecutionTime: 10000,
              cache: {
                duration: 1000,
              },
            };
          },
        }),

        // Register TypeORM entities for dependency injection
        TypeOrmModule.forFeature([
          User,
          UserMfa,
          Tenant,
          TenantConfig,
          Example
        ]),

        // Conditional MongooseModule registration
        ...SharedDatabaseModule.createMongooseModule(),
      ],
      exports: [
        TypeOrmModule,
        MongooseModule,
      ],
    };
  }

  private static createMongooseModule() {
    const modules = [];
    
    // Add MongooseModule only if configuration is available
    modules.push(
      MongooseModule.forRootAsync({
        imports: [ConfigModule],
        inject: [ConfigService],
        useFactory: async (configService: ConfigService) => {
          const mongoConfig = configService.get('db')?.mongo || {
            uri: configService.get('MONGO_URI'),
            dbName: configService.get('MONGO_DB_NAME'),
          };

          if (!mongoConfig?.uri) {
            console.warn('⚠️ MongoDB URI not found, using default connection');
            // Return a default configuration instead of null
            return {
              uri: 'mongodb://localhost:27017',
              dbName: 'firmprofit_default',
            };
          }

          return {
            uri: mongoConfig.uri,
            dbName: mongoConfig.dbName,
          };
        },
      })
    );

    // Add Mongoose schemas for dependency injection
    modules.push(
      MongooseModule.forFeature([
        { name: Category.name, schema: CategorySchema },
        { name: Location.name, schema: LocationSchema },
        { name: Email.name, schema: EmailSchema },
        { name: Client.name, schema: ClientSchema },
      ])
    );

    return modules;
  }
}

// Default export for backward compatibility
export default SharedDatabaseModule.forRoot(); 