import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { MongooseModuleOptions } from '@nestjs/mongoose';
import { ConfigService } from '@nestjs/config';

// Import all entities and schemas
import { User, UserMfa, Tenant, TenantConfig, Example } from './entities';
import { 
  Category, 
  CategorySchema, 
  Location, 
  LocationSchema, 
  Email, 
  EmailSchema, 
  Client, 
  ClientSchema 
} from './schemas';

/**
 * Get TypeORM configuration with shared entities
 */
export function getTypeOrmConfig(configService: ConfigService): TypeOrmModuleOptions {
  const dbConfig = configService.get('database') || configService.get('db')?.postgres;
  
  return {
    type: 'postgres',
    host: dbConfig?.host || configService.get('DB_HOST') || 'localhost',
    port: dbConfig?.port || configService.get('DB_PORT') || 5432,
    username: dbConfig?.username || configService.get('DB_USERNAME') || 'postgres',
    password: dbConfig?.password || configService.get('DB_PASSWORD') || 'postgres',
    database: dbConfig?.database || configService.get('DB_NAME') || 'firmprofit',
    entities: [User, UserMfa, Tenant, TenantConfig, Example],
    synchronize: configService.get('NODE_ENV') !== 'production' && (dbConfig?.synchronize || configService.get('DB_SYNC') === 'true'),
    logging: configService.get('NODE_ENV') !== 'production' && (dbConfig?.logging || configService.get('DB_LOGGING') === 'true'),
    schema: dbConfig?.schema || configService.get('DB_SCHEMA') || 'public',
    maxQueryExecutionTime: 10000,
    cache: {
      duration: 1000,
    },
  };
}

/**
 * Get Mongoose configuration
 */
export function getMongooseConfig(configService: ConfigService): MongooseModuleOptions {
  const mongoConfig = configService.get('db')?.mongo || {
    uri: configService.get('MONGO_URI'),
    dbName: configService.get('MONGO_DB_NAME'),
  };

  if (!mongoConfig?.uri) {
    console.warn('⚠️ MongoDB URI not found, using default connection');
    return {
      uri: 'mongodb://localhost:27017',
      dbName: 'firmprofit_default',
    };
  }

  return {
    uri: mongoConfig.uri,
    dbName: mongoConfig.dbName,
  };
}

/**
 * Get shared TypeORM entities for feature registration
 */
export function getSharedEntities() {
  return [User, UserMfa, Tenant, TenantConfig, Example];
}

/**
 * Get shared Mongoose schemas for feature registration
 */
export function getSharedSchemas() {
  return [
    { name: Category.name, schema: CategorySchema },
    { name: Location.name, schema: LocationSchema },
    { name: Email.name, schema: EmailSchema },
    { name: Client.name, schema: ClientSchema },
  ];
}
