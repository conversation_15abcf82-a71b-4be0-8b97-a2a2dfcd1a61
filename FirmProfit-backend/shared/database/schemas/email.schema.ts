import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type EmailDocument = Email & Document;

// Define attachment schema for better type safety
@Schema({ _id: false }) // _id: false for subdocuments
export class AttachmentMetadata {
  @Prop({ required: true })
  filename: string;

  @Prop({ required: true })
  contentType: string;

  @Prop({ required: true })
  size: number;

  @Prop({ required: false }) // S3 key path
  s3Key?: string;

  @Prop({ required: false }) // S3 bucket name
  s3Bucket?: string;

  @Prop({ required: false }) // Presigned URL (temporary)
  s3Url?: string;

  @Prop({ required: false }) // Local path (fallback)
  savedPath?: string;

  @Prop({ required: false })
  contentId?: string;

  @Prop({ required: true })
  checksum: string;

  @Prop({ required: true })
  attachmentId: string;

  @Prop({ required: true, enum: ['local', 's3', 'both'] })
  storageType: string;

  @Prop({ required: true })
  uploadedAt: string;
}

// Define email metadata schema
@Schema({ _id: false })
export class EmailMetadata {
  @Prop({ type: Object, default: {} })
  attributes?: Record<string, any>;

  @Prop({ type: Object, default: {} })
  headers?: Record<string, any>;

  @Prop({ default: 0 })
  bodySize: number;

  @Prop({ default: false })
  hasAttachments: boolean;

  @Prop({ default: 0 })
  attachmentCount: number;

  @Prop({ type: [AttachmentMetadata], default: [] })
  attachments: AttachmentMetadata[];

  @Prop({ default: 0 })
  totalAttachmentSize: number;

  @Prop({ required: false })
  tenantId?: string;

  @Prop({ default: false })
  error?: boolean;

  @Prop({ required: false })
  errorDetails?: string;

  @Prop({ required: false })
  originalHash?: string;
}

@Schema({
  timestamps: true,
  collection: 'emails',
})
export class Email {
  @Prop({ required: true })
  subject: string;

  @Prop({ required: true })
  from: string;

  @Prop({ required: true })
  body: string;

  @Prop({ required: true })
  receivedAt: string;

  // Unique identifier fields for duplicate prevention
  @Prop({ required: false, index: true })
  messageId: string;

  @Prop({ required: true, unique: true, index: true })
  uniqueHash: string;

  // IMAP specific fields
  @Prop({ required: false })
  uid: number;

  @Prop({ required: false })
  seqno: number;

  // Tenant identification
  @Prop({ required: false, index: true })
  tenantId: string;

  @Prop({ default: Date.now })
  processedAt: Date;

  @Prop({ default: false })
  isProcessed: boolean;

  @Prop({ default: false })
  isMarkedAsSeen: boolean;

  @Prop({ default: null })
  errorMessage: string;

  @Prop({ type: EmailMetadata, default: () => ({}) })
  metadata: EmailMetadata;
}

export const EmailSchema = SchemaFactory.createForClass(Email);
export const AttachmentMetadataSchema = SchemaFactory.createForClass(AttachmentMetadata);
export const EmailMetadataSchema = SchemaFactory.createForClass(EmailMetadata);

// Create compound index for better query performance
EmailSchema.index({ tenantId: 1, createdAt: -1 });
EmailSchema.index({ 'metadata.hasAttachments': 1, tenantId: 1 });
EmailSchema.index({ 'metadata.attachments.s3Key': 1 }); // Index for S3 key lookups 