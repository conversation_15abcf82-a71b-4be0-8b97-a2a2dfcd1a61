// Mongoose Schemas Export
export { Category, CategorySchema, CategoryDocument } from './category.schema';
export { Location, LocationSchema, LocationDocument } from './location.schema';
export { 
  Email, 
  EmailSchema, 
  EmailDocument, 
  AttachmentMetadata, 
  AttachmentMetadataSchema, 
  EmailMetadata, 
  EmailMetadataSchema 
} from './email.schema';
export { 
  Client, 
  ClientSchema, 
  ClientDocument, 
  Address, 
  AddressSchema, 
  PeopleGroup, 
  PeopleGroupSchema 
} from './client.schema'; 

export { User, UserSchema } from '../entities/user.entity';

export { WorkflowExecution, WorkflowExecutionSchema, WorkflowExecutionDocument } from './workflow-execution.entity';
