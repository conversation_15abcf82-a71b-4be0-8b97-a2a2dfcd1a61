import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  <PERSON>inColumn,
} from 'typeorm';
import { User } from './user.entity';

@Entity('user_mfa')
export class UserMfa {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  userId: number;

  @Column()
  secret: string;

  @Column({ default: false })
  isVerified: boolean;

  @Column()
  createdAt: Date;

  @Column({ nullable: true })
  verifiedAt: Date;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;
} 