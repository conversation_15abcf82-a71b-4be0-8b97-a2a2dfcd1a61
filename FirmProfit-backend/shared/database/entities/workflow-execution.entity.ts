import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Schema as MongooseSchema, Types } from 'mongoose';
import {Work_flow_execution_status} from '../enum/template-status.enum'

export type WorkflowExecutionDocument = HydratedDocument<WorkflowExecution>;

@Schema({ timestamps: true, collection: 'work_flow_execution' })
export class WorkflowExecution {
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'Template',
    required: true,
  })
  template_id: Types.ObjectId;

  @Prop({ type: Number, required: true, unique: true })
  execution_id: number;

  @Prop({
    type: Date,
    required: true,
    default: () => new Date(),
  })
  start_date: Date;

  @Prop({
    type: Date,
    default: function (this: WorkflowExecution) {
      const start = this.start_date || new Date();
      return new Date(start.getTime() + 24 * 60 * 60 * 1000); // Add 1 day
    },
  })
  end_date: Date;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    required: false,
  })
  parent_id: Types.ObjectId;

  @Prop({ type: String, enum: Work_flow_execution_status, required: true })
  status: Work_flow_execution_status;

  @Prop({
    type: String,
    enum: Work_flow_execution_status,
    required: false,
    default: Work_flow_execution_status.PENDING,
  })
  workflow_status: Work_flow_execution_status;

  @Prop({ type: String })
  last_activity: string;

  @Prop({
    type: [MongooseSchema.Types.ObjectId],
    ref: 'User',
    required: false,
    default: [], // Ensures empty array is allowed by default
  })
  assigns: Types.ObjectId[];

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'users',
    required: true,
  })
  assign_by: Types.ObjectId;

  @Prop({
    type: [MongooseSchema.Types.ObjectId],
    ref: 'client',
    default: [],
    required: true,
  })
  run_by: Types.ObjectId[];

  @Prop({ type: String })
  notes: string;

  @Prop({ type: Boolean, default: false })
  is_manually_run: boolean;

  @Prop({ default: true })
  is_active: boolean;

  @Prop({ default: false })
  is_deleted: boolean;

  @Prop({ default: false })
  is_archive: boolean;

  @Prop({ type: String })
  archive_at: string;

  @Prop({ type: String })
  archive_by: string;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'Task',
    required: false,
  })
  last_task_id: Types.ObjectId;

  // MyCase archive retry tracking fields
  @Prop({ type: Number, default: 0, min: 0, max: 3 })
  mycase_archive_retry_count: number;

  @Prop({ type: String })
  mycase_archive_last_error: string;

  @Prop({ type: Date })
  mycase_archive_last_attempt: Date;

  @Prop({
    type: String,
    enum: ['PENDING', 'IN_PROGRESS', 'SUCCESS', 'FAILED'],
    default: 'PENDING',
  })
  mycase_archive_status: string;
}

export const WorkflowExecutionSchema =
  SchemaFactory.createForClass(WorkflowExecution);
