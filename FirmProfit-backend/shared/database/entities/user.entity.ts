import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { UserMfa } from './mfa.entity';
import { SchemaFactory } from '@nestjs/mongoose';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  email: string;

  @Column({ name: 'firstName', nullable: true })
  first_name: string;

  @Column({ name: 'lastName', nullable: true })
  last_name: string;

  @Column()
  password: string;

  @Column({ nullable: true })
  timezone: string;

  @Column()
  roleId: number;

  @Column({ default: false })
  isActive: boolean;

  @Column({ default: false })
  isDeleted: boolean;

  @Column({ default: false })
  mfaEnabled: boolean;

  @Column({ type: 'varchar', nullable: true })
  token: string | null;

  @Column({ default: 0 })
  loginAttemptCount: number;

  @Column({ default: 0 })
  loginAttemptMFACount: number;

  @Column({ type: 'timestamp', nullable: true })
  lastInCorrectLogin: Date | null;

  @Column({ type: 'timestamp', nullable: true })
  lastInCorrectMFALogin: Date | null;

  @OneToMany(() => UserMfa, (mfa) => mfa.user)
  mfa: UserMfa[];
}



export const UserSchema = SchemaFactory.createForClass(User);
