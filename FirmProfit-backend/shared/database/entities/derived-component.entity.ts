import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import {
  Appointment_action,
  Court_notice_type,
  Meeting_location,
} from '../enum/template-status.enum';

export type DerivedComponentDocument = HydratedDocument<DerivedComponent>;

@Schema({ _id: false })
export class Event {
  @Prop({ required: true }) id: string;
  @Prop({ required: true }) case_number: string;
  @Prop({ required: true }) client_name: string;
  @Prop({ required: true }) subject: string;
  @Prop({ type: String, enum: Court_notice_type, required: true })
  court_notice_type: Court_notice_type;
  @Prop({ type: String, enum: Appointment_action, required: true })
  appointment_action: Appointment_action;
  @Prop({ required: true }) charge: string;
  @Prop({ required: true }) ex_county_of_arrest: string;
  @Prop({ required: true }) court_location: string;
  @Prop({ required: true }) optional_attendees: string;
  @Prop({ required: true }) required_attendees: string;
  @Prop({ required: true }) client_attendees: string;
  @Prop({ type: String, enum: Meeting_location, required: true })
  meeting_location: Meeting_location;
  @Prop() description?: string;
  @Prop({ required: true }) start_date: string;
  @Prop({ required: true }) end_date: string;
  @Prop() start_time?: string;
  @Prop() end_time?: string;
  @Prop({ default: false }) all_days: boolean;
  @Prop({ default: false }) is_completed: boolean;
}

export const EventSchema = SchemaFactory.createForClass(Event);

@Schema({ _id: false })
export class Matter {
  @Prop({ required: true }) _id: string;
  @Prop({ required: true }) name: string;
  @Prop({ type: [Event], default: [] }) event: Event[];
}

export const MatterSchema = SchemaFactory.createForClass(Matter);

@Schema({ _id: false })
export class DerivedEntity {
  @Prop({ required: true }) _id: string;
  @Prop({ required: true }) name: string;
  @Prop({ type: [Matter], default: [] }) matter: Matter[];
}

export const DerivedEntitySchema = SchemaFactory.createForClass(DerivedEntity);

@Schema({ timestamps: true, collection: 'court_notice' })
export class DerivedComponent {
  @Prop({ required: true }) name: string;
  @Prop({ type: [DerivedEntity], default: [] }) structure: DerivedEntity[];
}

export const DerivedComponentSchema =
  SchemaFactory.createForClass(DerivedComponent);
