import {
  <PERSON><PERSON>ty,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('tenants')
export class Tenant {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ unique: true })
  name: string;

  @Column({ unique: true })
  schema: string;

  @Column()
  logo: string;

  @Column({ default: true })
  is_active: boolean;

  @Column({ nullable: true })
  description: string;

  @Column({ unique: true })
  domain: string;

  @Column({
    type: 'jsonb',
    nullable: true,
    default: null,
  })
  config: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 