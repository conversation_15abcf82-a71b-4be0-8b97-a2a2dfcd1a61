import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';

// File DTO for workflow attachments
export class FileDto {
  @IsString()
  name: string;

  @IsString()
  url: string;

  @IsString()
  type: string;

  @IsNumber()
  size: number;

  @IsString()
  id: string;

  @IsString()
  key: string;

  @IsString()
  uniqueId: string;

  @IsString()
  mycaseDocumentId: string;
}

// Rename File DTO
export class RenameFileDto {
  @IsString()
  fileId: string;

  @IsString()
  newName: string;
}

// Location DTO
export class GetLocationDto {
  @IsString()
  firmId: string;

  @IsString()
  search: string;
}

export class LocationResponseDto {
  id: number;
  name: string;
  address: string;
}

// Workflow Form DTOs
export class FormValueDto {
  @IsString()
  fieldId: string;

  value: any;
}

export class FormDto {
  @IsArray()
  formValues: FormValueDto[];
} 