import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Min<PERSON><PERSON><PERSON>, Length, IsBoolean, IsOptional } from 'class-validator';

// Sign In DTO
export class SignInDto {
  @IsEmail({}, { message: 'Please provide a valid email address' })
  email: string;

  @IsString()
  password: string;
}

// Sign Up DTO with all optional fields
export class SignUpDto {
  @IsEmail({}, { message: 'Please provide a valid email address' })
  email: string;

  @IsString()
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  password: string;

  @IsNumber()
  roleId: number;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @IsBoolean()
  @IsOptional()
  isDeleted?: boolean;
}

// Login DTO (alias for SignIn for consistency)
export class LoginDto extends SignInDto {}

// Register DTO (alias for SignUp for consistency)
export class RegisterDto extends SignUpDto {}

// MFA DTOs
export class VerifyMfaDto {
  @IsNumber()
  userId: number;

  @IsString()
  @Length(6, 6, { message: 'OTP must be exactly 6 characters' })
  otp: string;
}

// Token validation DTO
export class ValidateTokenDto {
  @IsString()
  token: string;
}

// Password reset DTO
export class ResetPasswordDto {
  @IsString()
  token: string;

  @IsString()
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  newPassword: string;
} 