# @shared/database

Shared database module for FirmProfit microservices containing common entities, schemas, DTOs, and types.

## Structure

```
shared/
├── database/
│   ├── entities/          # TypeORM entities (PostgreSQL)
│   ├── schemas/           # Mongoose schemas (MongoDB)
│   ├── database.module.ts # Shared database module
│   └── index.ts          # Database exports
├── dto/                  # Common Data Transfer Objects
├── types/                # Shared TypeScript types and interfaces
├── index.ts             # Main exports
├── package.json         # Dependencies
├── tsconfig.json        # TypeScript config
└── README.md           # This file
```

## Installation

Since this is a local shared module, you need to install it in each service that uses it:

### In your service's package.json:
```json
{
  "dependencies": {
    "@shared/database": "file:../shared"
  }
}
```

## Usage

### 1. Import the Shared Database Module

In your service's app.module.ts:

```typescript
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { SharedDatabaseModule } from '@shared/database';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    SharedDatabaseModule, // Add this
    // ... your other modules
  ],
})
export class AppModule {}
```

### 2. Use TypeORM Entities

```typescript
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, Tenant } from '@shared/database';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    
    @InjectRepository(Tenant)
    private tenantRepository: Repository<Tenant>,
  ) {}
  
  async findUser(id: number): Promise<User> {
    return this.userRepository.findOne({ where: { id } });
  }
}
```

### 3. Use Mongoose Schemas

```typescript
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Email, EmailDocument, Client, ClientDocument } from '@shared/database';

@Injectable()
export class EmailService {
  constructor(
    @InjectModel(Email.name)
    private emailModel: Model<EmailDocument>,
    
    @InjectModel(Client.name)
    private clientModel: Model<ClientDocument>,
  ) {}
  
  async findEmails(tenantId: string): Promise<Email[]> {
    return this.emailModel.find({ tenantId }).exec();
  }
}
```

### 4. Use Shared DTOs

```typescript
import { Body, Controller, Post } from '@nestjs/common';
import { SignInDto, CreateTenantDto } from '@shared/database';

@Controller('auth')
export class AuthController {
  @Post('signin')
  async signIn(@Body() signInDto: SignInDto) {
    // Use the shared DTO
    return this.authService.signIn(signInDto);
  }
  
  @Post('tenant')
  async createTenant(@Body() createTenantDto: CreateTenantDto) {
    return this.tenantService.create(createTenantDto);
  }
}
```

### 5. Use Shared Types

```typescript
import { JwtPayload, MyCaseEvent, ApiResponse } from '@shared/database';

export class AuthService {
  validateToken(token: string): JwtPayload {
    // Use shared JWT payload type
  }
  
  async syncMyCaseEvents(): Promise<ApiResponse<MyCaseEvent[]>> {
    // Use shared types for consistent API responses
  }
}
```

## Environment Configuration

The shared database module supports both structured and flat environment configurations:

### Option 1: Structured Configuration (Recommended)
```typescript
// config/database.config.ts
export default () => ({
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 5432,
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'postgres',
    database: process.env.DB_NAME || 'firmprofit',
    schema: process.env.DB_SCHEMA || 'public',
    synchronize: process.env.DB_SYNC === 'true',
    logging: process.env.DB_LOGGING === 'true',
  },
  db: {
    postgres: { /* same as above */ },
    mongo: {
      uri: process.env.MONGO_URI,
      dbName: process.env.MONGO_DB_NAME,
    }
  }
});
```

### Option 2: Flat Configuration
```env
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_NAME=firmprofit
DB_SCHEMA=public
DB_SYNC=false
DB_LOGGING=false
MONGO_URI=mongodb://localhost:27017
MONGO_DB_NAME=firmprofit
```

## Features

### Multi-Database Support
- **PostgreSQL**: Using TypeORM for relational data
- **MongoDB**: Using Mongoose for document-based data

### Automatic Configuration
- Supports both structured and environment variable-based configuration
- Graceful fallback for missing configurations
- Environment-specific settings (development vs production)

### Type Safety
- Comprehensive TypeScript types for all entities and DTOs
- Shared interfaces for consistency across services
- Proper import/export organization

### Schema Organization
- Separated TypeORM entities and Mongoose schemas
- Consolidated DTOs to prevent duplication
- Shared types for common interfaces

## Migration from Individual Services

To migrate from individual service database configurations:

1. **Remove local database modules** from individual services
2. **Import SharedDatabaseModule** instead
3. **Update imports** to use `@shared/database` imports
4. **Remove duplicate DTOs and types**
5. **Update environment configuration** to match expected format

### Example Migration for API Gateway:

**Before:**
```typescript
// api-gateway/src/app.module.ts
import { DatabaseModule } from './database/database.module';
import { TenantConfig } from './database/entities/tenant-config.entity';
```

**After:**
```typescript
// api-gateway/src/app.module.ts
import { SharedDatabaseModule, TenantConfig } from '@shared/database';
```

## Contributing

When adding new entities, schemas, or types:

1. Add to appropriate folder (`entities/`, `schemas/`, `dto/`, `types/`)
2. Export from the corresponding `index.ts` file
3. Update this README if necessary
4. Test with all consuming services

## Troubleshooting

### Common Issues:

1. **Module not found**: Ensure you've installed the shared module correctly
2. **Type errors**: Check that all peer dependencies are installed
3. **Database connection issues**: Verify environment configuration
4. **Circular dependencies**: Ensure proper import organization 