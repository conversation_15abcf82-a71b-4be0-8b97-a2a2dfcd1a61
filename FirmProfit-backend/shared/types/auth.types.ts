// JWT Payload Interface (consolidated from both services)
export interface JwtPayload {
  sub: number; // User ID
  email: string; // User email
  roleId: number; // User role ID
  isMfaAuthenticated?: boolean; // Whether MFA has been authenticated
  requiresMfa?: boolean; // Whether MFA is required
  iat?: number; // Issued at timestamp
  exp?: number; // Expiration timestamp
}

// User Authentication Status
export interface AuthStatus {
  isAuthenticated: boolean;
  user?: {
    id: number;
    email: string;
    roleId: number;
    mfaEnabled: boolean;
  };
  requiresMfa?: boolean;
}

// Token Validation Result
export interface TokenValidationResult {
  valid: boolean;
  payload?: JwtPayload;
  error?: string;
} 