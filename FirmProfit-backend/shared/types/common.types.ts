// Common Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  statusCode?: number;
}

export interface PaginatedResponse<T = any> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// File Types
export interface FileUpload {
  filename: string;
  originalname: string;
  mimetype: string;
  size: number;
  buffer?: Buffer;
  path?: string;
  s3Key?: string;
  s3Url?: string;
}

export interface S3UploadResult {
  Location: string;
  ETag: string;
  Bucket: string;
  Key: string;
}

// Database Types
export type EntityId = string | number;

export interface TimestampedEntity {
  createdAt: Date;
  updatedAt: Date;
}

// Configuration Types
export interface DatabaseConfig {
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  schema?: string;
  synchronize?: boolean;
  logging?: boolean;
}

export interface MongoConfig {
  uri: string;
  dbName: string;
}

// Tenant Types
export interface TenantContext {
  tenantId: string;
  schema: string;
  domain: string;
}

// Event Types
export enum EventStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export enum TaskStatus {
  DRAFT = 'draft',
  IN_PROGRESS = 'in_progress', 
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
} 