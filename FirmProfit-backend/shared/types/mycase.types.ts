// MyCase Authentication Types
export interface MyCaseAuthCredentials {
  username: string;
  password: string;
  client_id: string;
  client_secret: string;
}

export interface MyCaseTokenResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  scope: string;
}

export interface MyCase2FARequest {
  username: string;
  password: string;
  two_factor_code: string;
  client_id: string;
  client_secret: string;
}

export interface MyCaseRefreshTokenRequest {
  refresh_token: string;
  client_id: string;
  client_secret: string;
  grant_type: 'refresh_token';
}

// MyCase Event Types
export interface MyCaseEvent {
  id?: number;
  title: string;
  description?: string;
  start_date: string;
  end_date?: string;
  start_time?: string;
  end_time?: string;
  all_day?: boolean;
  location?: string;
  matter_id?: number;
  contact_id?: number;
  event_type?: string;
  reminder_minutes?: number;
  created_at?: string;
  updated_at?: string;
}

export interface MyCaseEventResponse {
  id: number;
  title: string;
  description?: string;
  start_date: string;
  end_date?: string;
  start_time?: string;
  end_time?: string;
  all_day: boolean;
  location?: string;
  matter_id?: number;
  contact_id?: number;
  event_type?: string;
  reminder_minutes?: number;
  created_at: string;
  updated_at: string;
}

// MyCase Client Types
export interface MyCaseClientInterface {
  id: number;
  first_name: string;
  last_name: string;
  email?: string;
  phone_number?: string;
  address?: {
    address1?: string;
    address2?: string;
    city?: string;
    state?: string;
    zip_code?: string;
    country?: string;
  };
}

// MyCase Matter Types
export interface MyCaseMatterInterface {
  id: number;
  name: string;
  case_number?: string;
  description?: string;
  opened_date?: string;
  closed_date?: string;
  outstanding_balance?: number;
  clients?: MyCaseClientInterface[];
}

// MyCase Location Types
export interface MyCaseLocationInterface {
  id: number;
  name: string;
  address?: string;
} 