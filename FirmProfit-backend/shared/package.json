{"name": "@shared/database", "version": "1.0.0", "description": "Shared database module for FirmProfit microservices", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "lint": "eslint . --ext .ts", "type-check": "tsc --noEmit", "install-deps": "npm install"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/mongoose": "^11.0.3", "@nestjs/typeorm": "^10.0.0", "class-transformer": "^0.5.0", "class-validator": "^0.14.0", "mongoose": "^8.16.3", "reflect-metadata": "^0.1.13", "typeorm": "^0.3.0"}, "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "typescript": "^5.0.0"}, "peerDependencies": {"pg": "^8.8.0"}, "keywords": ["<PERSON><PERSON><PERSON>", "database", "typeorm", "mongoose", "shared", "microservices"], "author": "FirmProfit Team", "license": "MIT"}