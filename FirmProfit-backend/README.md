# FirmProfit Backend

A microservices-based backend application built with **NestJS**, designed to efficiently manage authentication, user profiles, and shared services. The backend follows a **gRPC-based architecture** for communication between services.

## 🚀 Architecture

### 🏗 API Gateway
- Entry point for all client requests.
- Handles authentication and authorization.
- Routes requests to appropriate microservices.

### 🖥 Microservices
- **API Gateway**: Manages authentication and authorization.
- **Shared Service**: Handles user management, profiles, and other common services.

## 📌 Prerequisites
Ensure you have the following installed before proceeding:
- **Node.js** (v14 or higher)
- **Docker** & **Docker Compose**
- **PostgreSQL** (if running locally without Docker)

## 🛠 Installation & Setup

### 1️⃣ Clone the Repository
```sh
git clone https://github.com/your-repo/firmprofit-backend.git
cd firmprofit-backend
```

### 2️⃣ Local Development Setup

#### Install Dependencies in Each Service
```sh
# API Gateway
cd api-gateway
npm install 

# Shared Service
cd ../shared-service
npm install
```

#### Start the Services Locally
```sh
# Start the Shared Service
cd shared-service
npm run start:dev

# Start the API Gateway (in a different terminal)
cd api-gateway
npm run start:dev
```

## 📦 Running with Docker
To run the entire system using Docker and Docker Compose:

```sh
# Build and start all services
docker-compose up -d

# View logs from all services
docker-compose logs -f

# Stop all services
docker-compose down
```

For more detailed instructions on Docker setup, refer to [DOCKER-SETUP.md](./DOCKER-SETUP.md).

## 📡 Service Communication
- **gRPC** is used for communication between the API Gateway and Shared Service.
- The API Gateway acts as a gRPC client to the Shared Service.
- For more information on the gRPC implementation, see [GRPC_MIGRATION.md](./GRPC_MIGRATION.md).

---
Made with ❤️ by the FirmProfit Team

