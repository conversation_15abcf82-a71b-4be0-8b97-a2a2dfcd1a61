#!/bin/bash

# Navigate to the API directory
cd /home/<USER>/v2/FirmProfit-backend

# Build Docker images
echo "Building Docker images..."
docker compose build

# Start containers in detached mode
echo "Starting Docker containers..."
docker compose up -d

# Prune unused Docker images
echo "Pruning unused Docker images..."
docker image prune -f

echo "after.sh script completed successfully."

# cd /home/<USER>/v2/FirmProfit-backend/connectors

# pm2 restart connector-api
