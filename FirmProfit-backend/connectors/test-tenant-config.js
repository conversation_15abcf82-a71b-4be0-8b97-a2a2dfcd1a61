#!/usr/bin/env node

/**
 * Test script to verify tenant configuration database connection
 * Run with: node test-tenant-config.js
 */

require('dotenv').config();
const { DataSource } = require('typeorm');

// Simple test to verify database connection and tenant config retrieval
async function testTenantConfig() {
  console.log('🧪 Testing Tenant Configuration Database Connection...\n');

  const dataSource = new DataSource({
    type: 'postgres',
    host: process.env.POSTGRES_HOST || 'localhost',
    port: parseInt(process.env.POSTGRES_PORT, 10) || 5432,
    username: process.env.POSTGRES_USERNAME || 'postgres',
    password: process.env.POSTGRES_PASSWORD || 'postgres',
    database: process.env.POSTGRES_DATABASE || 'firmprofit',
    schema: 'public',
    entities: [],
    synchronize: false,
    logging: false,
  });

  try {
    // Initialize connection
    console.log('🔌 Connecting to PostgreSQL...');
    await dataSource.initialize();
    console.log('✅ Connected to PostgreSQL successfully\n');

    // Test if tenant_config table exists
    console.log('🔍 Checking if tenant_config table exists...');
    const tableExists = await dataSource.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'tenant_config'
      );
    `);
    
    if (tableExists[0].exists) {
      console.log('✅ tenant_config table found\n');
    } else {
      console.log('❌ tenant_config table not found');
      process.exit(1);
    }

    // Check table structure
    console.log('📋 Checking table structure...');
    const columns = await dataSource.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_schema = 'public' AND table_name = 'tenant_config'
      ORDER BY ordinal_position;
    `);
    
    console.log('Table columns:');
    columns.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });
    console.log('');

    // Check existing configurations
    console.log('📊 Checking existing tenant configurations...');
    const configs = await dataSource.query(`
      SELECT tenant_id, key, created_at, updated_at
      FROM public.tenant_config
      ORDER BY tenant_id, key;
    `);

    if (configs.length > 0) {
      console.log(`Found ${configs.length} existing configurations:`);
      configs.forEach(config => {
        console.log(`  - Tenant: ${config.tenant_id}, Key: ${config.key}`);
      });
    } else {
      console.log('No existing configurations found');
    }
    console.log('');

    // Check for IMAP configurations specifically
    console.log('📧 Checking for IMAP configurations...');
    const imapConfigs = await dataSource.query(`
      SELECT tenant_id, config
      FROM public.tenant_config
      WHERE key = 'imap_config';
    `);

    if (imapConfigs.length > 0) {
      console.log(`Found ${imapConfigs.length} IMAP configurations:`);
      imapConfigs.forEach(config => {
        const cfg = config.config;
        console.log(`  - Tenant: ${config.tenant_id}`);
        console.log(`    Host: ${cfg.host}:${cfg.port}`);
        console.log(`    Username: ${cfg.username ? cfg.username.substring(0, 3) + '***' : 'Not set'}`);
        console.log(`    Has Password: ${!!cfg.password}`);
        console.log('');
      });
    } else {
      console.log('❌ No IMAP configurations found');
      console.log('💡 Run "npm run seed:imap" to add sample configurations\n');
    }

    // Test specific tenant lookup
    console.log('🎯 Testing tenant "1" configuration lookup...');
    const tenant1Config = await dataSource.query(`
      SELECT config
      FROM public.tenant_config
      WHERE tenant_id = $1 AND key = 'imap_config';
    `, ['1']);

    if (tenant1Config.length > 0) {
      console.log('✅ Tenant "1" IMAP configuration found');
      const cfg = tenant1Config[0].config;
      console.log(`   Host: ${cfg.host}:${cfg.port}`);
      console.log(`   TLS: ${cfg.tls}`);
      console.log(`   Polling Interval: ${cfg.pollingInterval}ms`);
    } else {
      console.log('❌ Tenant "1" IMAP configuration not found');
    }

    console.log('\n🎉 Database connection test completed successfully!');

  } catch (error) {
    console.error('❌ Database connection test failed:');
    console.error('Error:', error.message);
    if (error.code) {
      console.error('Error Code:', error.code);
    }
    process.exit(1);
  } finally {
    if (dataSource.isInitialized) {
      await dataSource.destroy();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the test
testTenantConfig().catch(console.error); 