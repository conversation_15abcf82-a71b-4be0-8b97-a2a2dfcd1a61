# Using Existing tenant_config Table Setup

This guide explains how to configure the Gmail connector service to use the existing `tenant_config` table in the public schema.

## ✅ What's Been Updated

The service has been modified to:
- **Connect to existing table**: Uses the existing `tenant_config` table in the public schema
- **No new tables**: Doesn't create any new database tables
- **Schema-aware**: Explicitly connects to the `public` schema
- **Fallback support**: Falls back to environment variables if database config fails

## 📋 Quick Setup Steps

### 1. Environment Configuration

Create/update your `.env` file in the `connectors` directory:

```bash
# PostgreSQL Configuration (for existing tenant_config table)
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USERNAME=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DATABASE=firmprofit

# MongoDB Configuration (for emails)
MONGODB_URI=mongodb://localhost:27017/gmail-connector
DB_NAME=gmail-connector

# Default tenant to use on startup
DEFAULT_TENANT_ID=1

# Security
ENCRYPTION_KEY=your-32-character-encryption-key-here

# Development settings
NODE_ENV=development
```

### 2. Install Dependencies

```bash
cd connectors
npm install
```

### 3. Seed IMAP Configurations

Add sample IMAP configurations to the existing tenant_config table:

```bash
npm run seed:imap
```

This will add IMAP configurations for tenants with IDs: `1`, `2`, `stephen-tenant-id`, `moskovich-tenant-id`.

### 4. Test Database Connection

Before starting the full service, test the database connection:

```bash
# Start the service
npm run start:dev

# In another terminal, test the database connection
curl http://localhost:3001/gmail/test-db-connection
```

Expected response:
```json
{
  "success": true,
  "message": "Database connection successful",
  "tenantId": "1",
  "configFound": true,
  "host": "imap.gmail.com",
  "port": 993,
  "hasCredentials": true,
  "timestamp": "2024-01-20T10:30:00.000Z"
}
```

### 5. Check Current Configuration

```bash
curl http://localhost:3001/gmail/current-config
```

### 6. Test Tenant Switching

```bash
# Switch to tenant 2
curl -X POST http://localhost:3001/gmail/tenant/2/switch

# Get tenant 2's configuration
curl http://localhost:3001/gmail/tenant/2/config
```

## 🗄️ Database Table Structure

The service expects the existing `tenant_config` table to have this structure:

```sql
-- This table should already exist
public.tenant_config (
    tenant_id TEXT,
    key TEXT,
    config JSONB,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    PRIMARY KEY (tenant_id, key)
)
```

## 📋 IMAP Configuration Format

The IMAP configurations are stored in the `config` JSONB column with key `'imap_config'`:

```json
{
  "host": "imap.gmail.com",
  "port": 993,
  "username": "<EMAIL>",
  "password": "password",
  "tls": true,
  "tlsOptions": {
    "servername": "imap.gmail.com",
    "rejectUnauthorized": false
  },
  "keepalive": true,
  "autotls": "always",
  "maxReconnectAttempts": 5,
  "pollingInterval": 30000,
  "enableDuplicateChecking": true,
  "enableMarkAsSeen": true
}
```

## 🔍 Troubleshooting

### Database Connection Issues

1. **Check PostgreSQL connection**:
```bash
curl http://localhost:3001/gmail/test-db-connection
```

2. **Verify table exists**:
```sql
SELECT * FROM public.tenant_config WHERE key = 'imap_config';
```

3. **Check logs**:
```bash
npm run start:dev
# Look for database connection logs
```

### Configuration Not Found

1. **Verify tenant ID exists**:
```sql
SELECT tenant_id, key FROM public.tenant_config WHERE key = 'imap_config';
```

2. **Add configuration manually**:
```sql
INSERT INTO public.tenant_config (tenant_id, key, config, created_at, updated_at)
VALUES (
    '1',
    'imap_config',
    '{"host": "imap.gmail.com", "port": 993, "username": "<EMAIL>", "password": "your_password", "tls": true}'::jsonb,
    NOW(),
    NOW()
);
```

### Service Logs

The service provides detailed logging:
- `🔧` Tenant initialization
- `📡` Configuration fetching
- `✅` Successful operations
- `❌` Errors with details

## 🚀 API Endpoints

### Database Testing
- `GET /gmail/test-db-connection` - Test database connection

### Configuration Management
- `GET /gmail/current-config` - Get current IMAP config
- `GET /gmail/tenant/:id/config` - Get tenant IMAP config
- `POST /gmail/tenant/:id/config` - Save tenant IMAP config

### Tenant Management
- `POST /gmail/tenant/:id/switch` - Switch to tenant
- `GET /gmail/tenant/:id/stats` - Get tenant email stats

### Service Status
- `GET /gmail/status` - Service connection status
- `GET /gmail/health` - Health check

## 📝 Notes

- **IMAP Connection**: Currently disabled for testing (`// await this.connectToImap(imapConfig);`)
- **Encryption**: Sensitive fields (username, password) can be encrypted in production
- **Fallback**: Service falls back to environment variables if database config fails
- **Schema**: Explicitly uses `public` schema
- **No Migrations**: Doesn't create new tables, uses existing ones

## 🔄 Next Steps

1. Test database connection
2. Verify IMAP configurations are loaded
3. Enable IMAP connection when ready
4. Test email processing
5. Configure encryption for production use 