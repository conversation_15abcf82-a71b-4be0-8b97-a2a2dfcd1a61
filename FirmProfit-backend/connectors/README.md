# Gmail Connectors Service

A NestJS service for connecting to Gmail via IMAP and sending parsed email data to the API Gateway via gRPC and SQS queues.

## Features

- Gmail IMAP connection with IDLE support
- Email parsing using mailparser
- Real-time email monitoring
- gRPC communication with API Gateway
- **SQS Queue Integration** - Automatic email forwarding to SQS queues
- **Multi-tenant Support** - Tenant-specific IMAP and SQS configurations
- **S3 Attachment Storage** - Automatic upload of email attachments to S3
- Configurable via environment variables and database.

## Installation

```bash
npm install
```

## Configuration

Create a `.env` file with the following variables:

```
PORT=3003
GMAIL_USER=<EMAIL>
GMAIL_PASSWORD=your-app-password
API_GATEWAY_HOST=localhost
API_GATEWAY_PORT=3000

# SQS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
DEFAULT_SQS_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/123456789012/email-queue

# S3 Configuration (for attachments)
S3_BUCKET_NAME=your-s3-bucket
ATTACHMENT_FALLBACK_LOCAL=true

# Multi-tenant Configuration
TENANT_ID=1
ENCRYPTION_KEY=your-encryption-key-change-in-production

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/gmail-connector
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USERNAME=postgres
POSTGRES_PASSWORD=root
POSTGRES_DATABASE=firmprofit
```

## SQS Integration

### Overview
When emails are successfully processed and stored in MongoDB, they are automatically sent to configured SQS queues. This enables downstream services to process emails asynchronously.

### Queue Configuration
SQS queue URLs are configured per tenant in the `tenant_config` table:

```sql
-- Example tenant configuration
INSERT INTO tenant_config (tenant_id, key, config, created_at, updated_at)
VALUES (
  '1', 
  'imap_config', 
  -- 'https://sqs.us-east-1.amazonaws.com/123456789012/tenant-1-email-queue',
  '{"host": "imap.gmail.com", "port": 993, "username": "encrypted_username", "password": "encrypted_password"}',
  NOW(),
  NOW()
);
```

### Message Format
SQS messages contain the complete email data in JSON format:

```json
{
  "subject": "EMAIL SUBJECT",
  "from": "<EMAIL>",
  "body": "Email content...",
  "receivedAt": "2025-07-16T13:59:40.000Z",
  "messageId": "<<EMAIL>>",
  "uniqueHash": "unique-hash-string",
  "uid": 29,
  "seqno": 29,
  "tenantId": "1",
  "processedAt": "2025-07-16T14:00:23.559Z",
  "isProcessed": true,
  "isMarkedAsSeen": true,
  "metadata": {
    "hasAttachments": true,
    "attachmentCount": 1,
    "attachments": [
      {
        "filename": "document.pdf",
        "contentType": "application/pdf",
        "size": 8787537,
        "s3Key": "attachments/1/hash/filename.pdf",
        "s3Bucket": "bucket-name",
        "s3Url": "presigned-url",
        "storageType": "s3"
      }
    ]
  }
}
```

### Message Attributes
Each SQS message includes attributes for easy filtering:

- `tenantId`: Tenant identifier
- `messageType`: Always "email_processed"
- `uniqueHash`: Email unique hash for deduplication
- `hasAttachments`: "true" or "false"

## API Endpoints

### SQS Management
- `POST /gmail/sqs/test` - Test SQS integration with latest email
  - Query param: `?emailHash=<hash>` - Test with specific email
- `GET /gmail/sqs/info` - Get SQS configuration information
- `POST /gmail/sqs/resend/:emailHash` - Resend specific email to SQS queue

### Email Management
- `GET /gmail/status` - Get IMAP connection status
- `GET /gmail/stats` - Get email processing statistics
- `GET /gmail/recent?limit=10` - Get recent emails
- `POST /gmail/process-unseen` - Manually process unseen emails
- `GET /gmail/duplicate-check/:hash` - Check if email hash is duplicate

### Tenant Management
- `POST /gmail/tenant/switch/:tenantId` - Switch to different tenant configuration

### Attachment Management
- `GET /gmail/attachments/s3` - Get emails with S3 attachments
- `GET /gmail/attachments/s3/:attachmentId` - Get S3 attachment info
- `POST /gmail/attachments/regenerate-urls/:emailId` - Regenerate presigned URLs
- `GET /gmail/attachments/s3/stats` - Get S3 storage statistics
- `POST /gmail/attachments/delete/:emailId` - Delete email attachments

## Running the Service

```bash
# Development
npm run start:dev

# Production
npm run start:prod
```

## Testing SQS Integration

1. **Check SQS Configuration:**
   ```bash
   curl http://localhost:3003/gmail/sqs/info
   ```

2. **Test with Latest Email:**
   ```bash
   curl -X POST http://localhost:3003/gmail/sqs/test
   ```

3. **Test with Specific Email:**
   ```bash
   curl -X POST "http://localhost:3003/gmail/sqs/test?emailHash=your-email-hash"
   ```

4. **Resend Email to Queue:**
   ```bash
   curl -X POST http://localhost:3003/gmail/sqs/resend/your-email-hash
   ```

## Troubleshooting

### SQS Issues
- **Queue URL not found**: Check tenant configuration in `tenant_config` table
- **AWS credentials**: Verify `AWS_ACCESS_KEY_ID` and `AWS_SECRET_ACCESS_KEY`
- **Queue permissions**: Ensure IAM user has `sqs:SendMessage` permission

### Email Processing Issues
- **IMAP connection**: Check Gmail app password and IMAP settings
- **Duplicate detection**: Emails with same hash are automatically skipped
- **Attachment upload**: Check S3 configuration and bucket permissions

## Health Check

```
GET /health
```

Returns the health status of the service.

## Architecture

```
┌─────────────────┐    gRPC     ┌─────────────────┐    gRPC     ┌─────────────────┐
│   Connectors    │────────────▶│   API Gateway   │────────────▶│ Shared Service  │
│   (Port 3003)   │             │ (Port 3000/3004)│             │  (Port 3001)    │
└─────────────────┘             └─────────────────┘             └─────────────────┘
        │                                │
        ▼                                ▼
┌─────────────────┐                ┌─────────────────┐
│  Gmail IMAP     │                │   Database      │
│  (Port 993)     │                │   Storage       │
└─────────────────┘                └─────────────────┘
        │                                │
        ▼                                ▼
┌─────────────────┐                ┌─────────────────┐
│   SQS Queue     │                │   S3 Storage    │
│   (AWS)         │                │   (Attachments) │
└─────────────────┘                └─────────────────┘
```

## Project Structure

```
connectors/
├── src/
│   ├── gmail/
│   │   ├── gmail.service.ts      # IMAP connection & email processing
│   │   ├── gmail.controller.ts   # REST API endpoints
│   │   ├── gmail.module.ts       # Gmail module configuration
│   │   └── schemas/
│   │       └── email.schema.ts   # Email MongoDB schema
│   ├── grpc-client/
│   │   ├── grpc-client.service.ts # gRPC client for API Gateway
│   │   └── grpc-client.module.ts  # gRPC client configuration
│   ├── common/
│   │   ├── s3/
│   │   │   └── s3.service.ts     # S3 attachment storage
│   │   └── sqs/
│   │       └── sqs.service.ts    # SQS queue integration
│   ├── config/
│   │   ├── imap-config.service.ts # Tenant IMAP configuration
│   │   └── db.config.ts          # Database configuration
│   ├── database/
│   │   └── entities/
│   │       └── tenant-config.entity.ts # Tenant configuration entity
│   ├── app.module.ts             # Main application module
│   ├── app.controller.ts         # Health check endpoints
│   └── main.ts                   # Application bootstrap
├── proto/
│   └── email.proto               # gRPC service definition
├── package.json                  # Dependencies & scripts
├── Dockerfile                    # Container configuration
└── README.md                     # This documentation
```
