#!/usr/bin/env node

/**
 * Test script for Gmail Email Processing Solution
 * 
 * This script demonstrates how to test the email processing system
 * Run with: node test-email-processing.js
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/gmail';
const COLORS = {
  GREEN: '\x1b[32m',
  RED: '\x1b[31m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  RESET: '\x1b[0m'
};

function log(message, color = COLORS.RESET) {
  console.log(`${color}${message}${COLORS.RESET}`);
}

function logSuccess(message) {
  log(`✓ ${message}`, COLORS.GREEN);
}

function logError(message) {
  log(`✗ ${message}`, COLORS.RED);
}

function logInfo(message) {
  log(`ℹ ${message}`, COLORS.BLUE);
}

function logWarning(message) {
  log(`⚠ ${message}`, COLORS.YELLOW);
}

async function testConnection() {
  try {
    logInfo('Testing connection...');
    const response = await axios.get(`${BASE_URL}/test-connection`);
    
    if (response.data.connected && response.data.mailboxOpen) {
      logSuccess('Connection test passed - connected and mailbox open');
      return true;
    } else {
      logError(`Connection test failed - connected: ${response.data.connected}, mailbox open: ${response.data.mailboxOpen}`);
      return false;
    }
  } catch (error) {
    logError(`Connection test failed: ${error.message}`);
    return false;
  }
}

async function testHealth() {
  try {
    logInfo('Testing health status...');
    const response = await axios.get(`${BASE_URL}/health`);
    
    if (response.data.status === 'healthy') {
      logSuccess('Health check passed');
      return true;
    } else {
      logWarning('Health check shows unhealthy status');
      return false;
    }
  } catch (error) {
    logError(`Health check failed: ${error.message}`);
    return false;
  }
}

async function testMarkAsSeen() {
  try {
    logInfo('Testing mark as seen functionality...');
    const response = await axios.get(`${BASE_URL}/test-mark-as-seen`);
    
    if (response.data.success) {
      logSuccess('Mark as seen test passed');
      return true;
    } else {
      logError(`Mark as seen test failed: ${response.data.message}`);
      return false;
    }
  } catch (error) {
    logError(`Mark as seen test failed: ${error.message}`);
    return false;
  }
}

async function testStats() {
  try {
    logInfo('Testing statistics...');
    const response = await axios.get(`${BASE_URL}/detailed-stats`);
    
    const stats = response.data.statistics;
    
    console.log('\n📊 Email Processing Statistics:');
    console.log(`   Total emails: ${stats.total}`);
    console.log(`   Processed: ${stats.processed}`);
    console.log(`   Marked as seen: ${stats.markedAsSeen}`);
    console.log(`   Errors: ${stats.errors}`);
    console.log(`   Duplicates prevented: ${stats.duplicatesPreventedByHash}`);
    
    if (stats.total > 0) {
      const processingRate = (stats.processed / stats.total * 100).toFixed(1);
      const markAsSeenRate = (stats.markedAsSeen / stats.processed * 100).toFixed(1);
      const errorRate = (stats.errors / stats.total * 100).toFixed(1);
      
      console.log(`   Processing rate: ${processingRate}%`);
      console.log(`   Mark as seen rate: ${markAsSeenRate}%`);
      console.log(`   Error rate: ${errorRate}%`);
      
      if (processingRate > 90) {
        logSuccess('Processing rate is good (>90%)');
      } else {
        logWarning('Processing rate is low (<90%)');
      }
      
      if (markAsSeenRate > 90) {
        logSuccess('Mark as seen rate is good (>90%)');
      } else {
        logWarning('Mark as seen rate is low (<90%) - this indicates the mark as seen functionality may not be working properly');
      }
      
      if (errorRate < 10) {
        logSuccess('Error rate is acceptable (<10%)');
      } else {
        logWarning('Error rate is high (>10%)');
      }
    }
    
    return true;
  } catch (error) {
    logError(`Statistics test failed: ${error.message}`);
    return false;
  }
}

async function testRecentEmails() {
  try {
    logInfo('Testing recent emails retrieval...');
    const response = await axios.get(`${BASE_URL}/recent?limit=5`);
    
    const emails = response.data;
    
    console.log(`\n📧 Recent Emails (${emails.length}):`);
    emails.forEach((email, index) => {
      console.log(`   ${index + 1}. ${email.subject} - from ${email.from}`);
      console.log(`      Received: ${email.receivedAt}`);
      console.log(`      Processed: ${email.isProcessed ? 'Yes' : 'No'}`);
      console.log(`      Marked as seen: ${email.isMarkedAsSeen ? 'Yes' : 'No'}`);
      if (email.uniqueHash) {
        console.log(`      Hash: ${email.uniqueHash.substring(0, 16)}...`);
      }
      console.log('');
    });
    
    logSuccess('Recent emails retrieved successfully');
    return true;
  } catch (error) {
    logError(`Recent emails test failed: ${error.message}`);
    return false;
  }
}

async function testDuplicateCheck() {
  try {
    logInfo('Testing duplicate checking...');
    
    // Test with a dummy hash
    const dummyHash = 'abcd1234567890abcd1234567890abcd1234567890abcd1234567890abcd1234';
    const response = await axios.get(`${BASE_URL}/duplicate-check/${dummyHash}`);
    
    if (response.data.isDuplicate === false) {
      logSuccess('Duplicate check working (dummy hash not found)');
    } else {
      logInfo('Duplicate check found existing email');
    }
    
    return true;
  } catch (error) {
    logError(`Duplicate check test failed: ${error.message}`);
    return false;
  }
}

async function testProcessUnseen() {
  try {
    logInfo('Testing manual unseen email processing...');
    const response = await axios.post(`${BASE_URL}/process-unseen`);
    
    if (response.data.message === 'Processing unseen emails initiated') {
      logSuccess('Manual processing initiated successfully');
      return true;
    } else {
      logWarning('Unexpected response from manual processing');
      return false;
    }
  } catch (error) {
    logError(`Manual processing test failed: ${error.message}`);
    return false;
  }
}

async function testSimulateEmail() {
  try {
    logInfo('Testing email simulation...');
    
    const mockEmail = {
      subject: 'Test Email for Duplicate Prevention',
      from: '<EMAIL>',
      body: 'This is a test email to demonstrate the duplicate prevention system.',
      messageId: '<<EMAIL>>'
    };
    
    const response = await axios.post(`${BASE_URL}/simulate-email`, mockEmail);
    
    if (response.data.message.includes('Email simulation endpoint')) {
      logSuccess('Email simulation endpoint working');
      return true;
    } else {
      logWarning('Unexpected response from email simulation');
      return false;
    }
  } catch (error) {
    logError(`Email simulation test failed: ${error.message}`);
    return false;
  }
}

async function runAllTests() {
  console.log('🧪 Running Email Processing Tests\n');
  
  const tests = [
    { name: 'Connection Test', func: testConnection },
    { name: 'Health Check', func: testHealth },
    { name: 'Mark as Seen Test', func: testMarkAsSeen },
    { name: 'Statistics Test', func: testStats },
    { name: 'Recent Emails Test', func: testRecentEmails },
    { name: 'Duplicate Check Test', func: testDuplicateCheck },
    { name: 'Manual Processing Test', func: testProcessUnseen },
    { name: 'Email Simulation Test', func: testSimulateEmail }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test.func();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      logError(`Test ${test.name} threw an error: ${error.message}`);
      failed++;
    }
    
    console.log(''); // Add spacing between tests
  }
  
  console.log('📋 Test Results:');
  console.log(`   Passed: ${passed}`);
  console.log(`   Failed: ${failed}`);
  console.log(`   Total: ${passed + failed}`);
  
  if (failed === 0) {
    logSuccess('All tests passed! 🎉');
  } else {
    logWarning(`${failed} test(s) failed. Check the logs above.`);
  }
}

// Instructions for running the test
function showInstructions() {
  console.log('📝 Email Processing Test Instructions:\n');
  console.log('1. Make sure your Gmail connector service is running');
  console.log('2. Ensure MongoDB is running and accessible');
  console.log('3. Configure your environment variables in .env file');
  console.log('4. Run this test with: node test-email-processing.js');
  console.log('5. Check the results for any issues\n');
}

// Check if this script is being run directly
if (require.main === module) {
  showInstructions();
  runAllTests().catch(error => {
    logError(`Test runner failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  testConnection,
  testHealth,
  testMarkAsSeen,
  testStats,
  testRecentEmails,
  testDuplicateCheck,
  testProcessUnseen,
  testSimulateEmail,
  runAllTests
}; 