import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
  DeleteObjectCommand,
  GetObjectCommandOutput,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

export interface S3UploadResult {
  success: boolean;
  key?: string;
  url?: string;
  size?: number;
  error?: string;
  etag?: string;
}

@Injectable()
export class S3Service {
  private readonly logger = new Logger(S3Service.name);
  private readonly s3Client: S3Client;
  private readonly bucketName: string;

  constructor(private readonly configService: ConfigService) {
    this.bucketName = this.configService.get('AWS_BUCKET_NAME_s3') || 'elite-assets-dev';
    
    this.s3Client = new S3Client({
      region: this.configService.get('AWS_REGION_s3') || 'us-east-1',
      credentials: {
        accessKeyId: this.configService.get('AWS_ACCESS_KEY_ID_s3'),
        secretAccessKey: this.configService.get('AWS_SECRET_ACCESS_KEY_s3'),
      },
      requestHandler: {
        socketTimeout: 5 * 60 * 1000, // 5 minutes
      },
    });

    this.logger.log(`S3Service initialized with bucket: ${this.bucketName}`);
  }

  /**
   * Upload email attachment to S3 with structured key
   * @param buffer File buffer
   * @param originalFilename Original filename
   * @param contentType MIME type
   * @param tenantId Tenant identifier
   * @param emailId Email identifier
   * @param attachmentId Unique attachment identifier
   * @returns Upload result with S3 key and metadata
   */
  async uploadEmailAttachment(
    buffer: Buffer,
    originalFilename: string,
    contentType: string,
    tenantId: string | null,
    emailId: string,
    attachmentId: string,
  ): Promise<S3UploadResult> {
    try {
      // Generate structured S3 key
      const s3Key = this.generateAttachmentKey(
        tenantId,
        emailId,
        attachmentId,
        originalFilename,
      );

      this.logger.log(`Uploading attachment to S3: ${s3Key}`);

      const putCommand = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: s3Key,
        Body: buffer,
        ContentType: contentType,
        Metadata: {
          originalFilename: originalFilename,
          tenantId: tenantId || 'fallback',
          emailId: emailId,
          attachmentId: attachmentId,
          uploadedAt: new Date().toISOString(),
        },
        // Set appropriate ACL for security
        ACL: 'bucket-owner-full-control',
      });

      const response = await this.s3Client.send(putCommand);

      this.logger.log(`Successfully uploaded attachment: ${s3Key}, ETag: ${response.ETag}`);

      return {
        success: true,
        key: s3Key,
        size: buffer.length,
        etag: response.ETag,
      };
    } catch (error) {
      this.logger.error(`Failed to upload attachment to S3: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Generate structured S3 key for email attachments
   * Format: attachments/{tenantId}/{YYYY}/{MM}/{DD}/{emailId}/{attachmentId}_{sanitized_filename}
   */
  private generateAttachmentKey(
    tenantId: string | null,
    emailId: string,
    attachmentId: string,
    originalFilename: string,
  ): string {
    const tenant = tenantId || 'fallback';
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    
    // Sanitize filename for S3 key
    const sanitizedFilename = this.sanitizeS3Key(originalFilename);
    
    return `attachments/${tenant}/${emailId}/${attachmentId}_${sanitizedFilename}`;
  }

  /**
   * Sanitize filename for S3 key compatibility
   */
  private sanitizeS3Key(filename: string): string {
    return filename
      .replace(/[^a-zA-Z0-9._-]/g, '_') // Replace invalid chars with underscore
      .replace(/_{2,}/g, '_') // Replace multiple underscores with single
      .replace(/^_+|_+$/g, '') // Remove leading/trailing underscores
      .toLowerCase(); // Convert to lowercase for consistency
  }

  /**
   * Generate presigned URL for attachment download
   */
  async generatePresignedUrl(
    key: string,
    operation: 'get' | 'put' = 'get',
    expiresIn: number = 3600,
  ): Promise<string> {
    try {
      const command = operation === 'put'
        ? new PutObjectCommand({
            Bucket: this.bucketName,
            Key: key,
          })
        : new GetObjectCommand({
            Bucket: this.bucketName,
            Key: key,
          });

      const url = await getSignedUrl(this.s3Client, command, { expiresIn });
      return url;
    } catch (error) {
      this.logger.error(`Failed to generate presigned URL for ${key}: ${error.message}`);
      throw new Error(`Failed to generate presigned URL: ${error.message}`);
    }
  }

  /**
   * Download file from S3
   */
  async getObject(key: string): Promise<GetObjectCommandOutput> {
    try {
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });
      
      return await this.s3Client.send(command);
    } catch (error) {
      this.logger.error(`Failed to get object ${key}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Delete file from S3
   */
  async deleteFile(key: string): Promise<void> {
    try {
      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });
      
      await this.s3Client.send(command);
      this.logger.log(`Successfully deleted file: ${key}`);
    } catch (error) {
      this.logger.error(`Failed to delete file ${key}: ${error.message}`);
      throw new Error(`Failed to delete file from S3: ${error.message}`);
    }
  }

  /**
   * Get S3 bucket name
   */
  getBucketName(): string {
    return this.bucketName;
  }

  /**
   * Download file buffer from S3
   */
  async downloadFileBuffer(key: string): Promise<Buffer> {
    try {
      this.logger.log(`Downloading file from S3: ${key}`);

      const response = await this.getObject(key);

      if (!response.Body) {
        throw new Error('No file content received from S3');
      }

      // Convert stream to buffer
      const chunks: Buffer[] = [];
      const stream = response.Body as NodeJS.ReadableStream;

      return new Promise((resolve, reject) => {
        stream.on('data', (chunk) => chunks.push(chunk));
        stream.on('end', () => resolve(Buffer.concat(chunks)));
        stream.on('error', reject);
      });
    } catch (error) {
      this.logger.error(`Error downloading file from S3: ${error.message}`);
      throw error;
    }
  }

  /**
   * List files in a specific path
   */
  async listAttachments(
    tenantId?: string,
    emailId?: string,
    limit: number = 100,
  ): Promise<any[]> {
    try {
      const { ListObjectsV2Command } = await import('@aws-sdk/client-s3');
      
      let prefix = 'attachments/';
      if (tenantId) {
        prefix += `${tenantId}/`;
        if (emailId) {
          prefix += `*/*/${emailId}/`;
        }
      }

      const command = new ListObjectsV2Command({
        Bucket: this.bucketName,
        Prefix: prefix,
        MaxKeys: limit,
      });

      const response = await this.s3Client.send(command);
      return response.Contents || [];
    } catch (error) {
      this.logger.error(`Failed to list attachments: ${error.message}`);
      throw error;
    }
  }
} 