import { Injectable } from '@nestjs/common';
import {
  MessageAttributeValue,
  SQSClient,
  SendMessageCommand,
  SendMessageCommandInput,
} from '@aws-sdk/client-sqs';

@Injectable()
export class SQSService {
  private readonly sqsClient: SQSClient;

  constructor() {
    this.sqsClient = new SQSClient({
      region: process.env.AWS_REGION,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      },
    });
  }

  async sendMessage(
    queueUrl: string,
    messageBody: object,
    messageAttributes?: Record<string, MessageAttributeValue>,
    delaySeconds?: number,
  ): Promise<string> {
    try {
      const params: SendMessageCommandInput = {
        QueueUrl: queueUrl,
        MessageBody: JSON.stringify(messageBody),
        MessageAttributes: messageAttributes,
      };
      console.log("🚀 ~ SQSService ~ params:", params)

      if (delaySeconds) {
        params.DelaySeconds = delaySeconds;
      }

      const command = new SendMessageCommand(params);
      const response = await this.sqsClient.send(command);

      return response.MessageId;
    } catch (error) {
      throw new Error(`Failed to send message to SQS: ${error.message}`);
    }
  }
} 