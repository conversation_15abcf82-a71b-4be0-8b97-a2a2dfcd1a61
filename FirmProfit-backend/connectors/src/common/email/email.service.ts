import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';

interface EmailOptions {
  recipients: string[];
  subject: string;
  htmlContent: string;
  attachments?: any[];
}

@Injectable()
export class EmailNotificationService {
  private logger = new Logger(EmailNotificationService.name);
  private transporter: nodemailer.Transporter;
  private sourceEmail: string;

  constructor(private readonly configService: ConfigService) {
    this.sourceEmail = this.configService.get('AWS_SOURCE_EMAIL') || '<EMAIL>';

    // Initialize AWS SES transporter : code to be done
    // this.transporter = nodemailer.createTransporter({
    //   host: `email-smtp.${this.configService.get('AWS_REGION') || 'us-east-1'}.amazonaws.com`,
    //   port: 587,
    //   secure: false,
    //   auth: {
    //     user: this.configService.get('AWS_ACCESS_KEY_ID'),
    //     pass: this.configService.get('AWS_SECRET_ACCESS_KEY'),
    //   },
    // });
  }

  async sendFailureNotification(options: EmailOptions): Promise<void> {
    try {
      // const mailOptions: nodemailer.SendMailOptions = {
      //   from: `FirmProfit AI <${this.sourceEmail}>`,
      //   to: options.recipients.join(','),
      //   subject: options.subject,
      //   html: options.htmlContent,
      //   attachments: options.attachments || [],
      // };

      // const info = await this.transporter.sendMail(mailOptions);
      // this.logger.log(
      //   `Failure notification sent to ${options.recipients.join(',')}. Message ID: ${info.messageId}`,
      // );
    } catch (error) {
      this.logger.error(`Failed to send failure notification: ${error.message}`, error);
      throw error;
    }
  }

  generateFailureNotificationHtml(
    emailSubject: string,
    emailFrom: string,
    errorMessage: string,
    attachmentCount: number
  ): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Court Notice Processing Failure</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
          }
          .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #dc3545;
          }
          .alert {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
          }
          .details {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
          }
          .detail-row {
            margin: 10px 0;
            display: flex;
            flex-wrap: wrap;
          }
          .detail-label {
            font-weight: bold;
            width: 120px;
            color: #495057;
          }
          .detail-value {
            flex: 1;
            color: #212529;
          }
          .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            font-size: 14px;
            color: #6c757d;
          }
          .button {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h2>🚨 Court Notice Processing Failure Alert</h2>
          <p>An automated court notice processing has failed and requires manual intervention.</p>
        </div>

        <div class="alert">
          <strong>AI agent couldn't process this court notice, please finish this manually.</strong>
        </div>

        <div class="details">
          <h3>Email Details</h3>
          <div class="detail-row">
            <span class="detail-label">Subject:</span>
            <span class="detail-value">${emailSubject}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">From:</span>
            <span class="detail-value">${emailFrom}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Attachments:</span>
            <span class="detail-value">${attachmentCount} file(s)</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Error:</span>
            <span class="detail-value">${errorMessage}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Time:</span>
            <span class="detail-value">${new Date().toLocaleString()}</span>
          </div>
        </div>

        <div style="margin: 30px 0;">
          <h3>Required Actions</h3>
          <ol>
            <li>Review the original email and attachments manually</li>
            <li>Extract court notice information manually</li>
            <li>Create appropriate client and matter records if needed</li>
            <li>Set up calendar events and workflow tasks manually</li>
            <li>Upload court notice files to the appropriate case</li>
          </ol>
        </div>

        <div class="footer">
          <p>This is an automated notification from FirmProfit AI Court Notice Processing System.</p>
          <p>Generated at: ${new Date().toISOString()}</p>
        </div>
      </body>
      </html>
    `;
  }
} 