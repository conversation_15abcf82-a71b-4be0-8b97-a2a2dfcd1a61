import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { registerAs } from '@nestjs/config';

// Configuration factory for NestJS ConfigModule
export const DatabaseConfiguration = registerAs('db', () => ({
  mongo: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/gmail-connector',
    dbName: process.env.DB_NAME || 'gmail-connector',
  },
  postgres: {
    host: process.env.POSTGRES_HOST || 'localhost',
    port: parseInt(process.env.POSTGRES_PORT, 10) || 5432,
    username: process.env.POSTGRES_USERNAME || 'postgres',
    password: process.env.POSTGRES_PASSWORD || 'postgres',
    database: process.env.POSTGRES_DATABASE || 'firmprofit',
    schema: process.env.POSTGRES_SCHEMA || 'public', // Use same schema as shared-service
    synchronize: process.env.NODE_ENV !== 'production',
    logging: process.env.NODE_ENV !== 'production',
  },
}));

@Injectable()
export class DbConfig {
  constructor(private configService: ConfigService) {}

  get mongoUri(): string {
    return this.configService.get<string>('MONGODB_URI') || 'mongodb://localhost:27017/gmail-connector';
  }

  get dbName(): string {
    return this.configService.get<string>('DB_NAME') || 'gmail-connector';
  }

  get postgresConfig() {
    return {
      host: this.configService.get<string>('POSTGRES_HOST', 'localhost'),
      port: this.configService.get<number>('POSTGRES_PORT', 5432),
      username: this.configService.get<string>('POSTGRES_USERNAME', 'postgres'),
      password: this.configService.get<string>('POSTGRES_PASSWORD', 'postgres'),
      database: this.configService.get<string>('POSTGRES_DATABASE', 'firmprofit'),
      schema: this.configService.get<string>('POSTGRES_SCHEMA', 'stephen_schema'), // Use same schema as shared-service
      synchronize: this.configService.get<string>('NODE_ENV') !== 'production',
      logging: this.configService.get<string>('NODE_ENV') !== 'production',
    };
  }

  // Email/IMAP Configuration (will be deprecated in favor of database config)
  get gmailConfig() {
    return {
      user: this.configService.get<string>('GMAIL_USER'),
      password: this.configService.get<string>('GMAIL_PASSWORD'),
      host: this.configService.get<string>('IMAP_HOST', 'imap.gmail.com'),
      port: this.configService.get<number>('IMAP_PORT', 993),
      tls: this.configService.get<boolean>('IMAP_TLS', true),
      tlsOptions: {
        servername: this.configService.get<string>('IMAP_HOST', 'imap.gmail.com'),
        rejectUnauthorized: this.configService.get<boolean>('IMAP_REJECT_UNAUTHORIZED', false),
      },
      // Processing settings
      maxReconnectAttempts: this.configService.get<number>('IMAP_MAX_RECONNECT_ATTEMPTS', 5),
      pollingInterval: this.configService.get<number>('IMAP_POLLING_INTERVAL', 30000),
      enableDuplicateChecking: this.configService.get<boolean>('ENABLE_DUPLICATE_CHECKING', true),
      enableMarkAsSeen: this.configService.get<boolean>('ENABLE_MARK_AS_SEEN', true),
    };
  }

  get isProduction(): boolean {
    return this.configService.get<string>('NODE_ENV') === 'production';
  }
} 