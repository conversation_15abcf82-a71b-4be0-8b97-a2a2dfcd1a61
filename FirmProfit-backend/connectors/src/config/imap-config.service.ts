import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import { TenantConfig, ImapConfig } from '../database/entities/tenant-config.entity';

@Injectable()
export class ImapConfigService {
  private readonly logger = new Logger(ImapConfigService.name);
  private readonly encryptionKey: string;
  private readonly algorithm = 'aes-256-cbc';

  constructor(
    @InjectRepository(TenantConfig)
    private readonly tenantConfigRepository: Repository<TenantConfig>,
    private readonly configService: ConfigService,
  ) {
    this.encryptionKey = this.configService.get<string>(
      'ENCRYPTION_KEY',
      'default-encryption-key-change-in-production',
    );
  }

  /**
   * Encrypt sensitive data
   */
  private encrypt(text: string): string {
    try {
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipher(this.algorithm, this.encryptionKey);
      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      return iv.toString('hex') + ':' + encrypted;
    } catch (error) {
      this.logger.error('Error encrypting data:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  /**
   * Decrypt sensitive data
   */
  private decrypt(encryptedText: string): string {
    try {
      const textParts = encryptedText.split(':');
      if (textParts.length !== 2) {
        // If not encrypted (legacy data), return as is
        return encryptedText;
      }
      
      const iv = Buffer.from(textParts[0], 'hex');
      const encryptedData = textParts[1];
      const decipher = crypto.createDecipher(this.algorithm, this.encryptionKey);
      let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      return decrypted;
    } catch (error) {
      this.logger.error('Error decrypting data:', error);
      // Return original text if decryption fails (for backward compatibility)
      return encryptedText;
    }
  }

  /**
   * Get IMAP configuration for a tenant
   */
  async getImapConfig(tenantId: string): Promise<ImapConfig> {
    try {

      console.log('tenantId ----------       11111111        ', tenantId);
      
      const config = await this.tenantConfigRepository.findOne({
        where: {
          tenant_id: tenantId,
          key: 'imap_config',
        },
      });

      console.log('config', config);

      if (!config) {
        throw new NotFoundException(
          `IMAP configuration not found for tenant: ${tenantId}`,
        );
      }

      // Decrypt sensitive fields
      const decryptedConfig = { ...config.config } as ImapConfig;
      
      if (decryptedConfig.username) {
        decryptedConfig.username = this.decrypt(decryptedConfig.username);
      }

      if (decryptedConfig.password) {
        decryptedConfig.password = this.decrypt(decryptedConfig.password);
      }

      // Apply default values if not specified
      const finalConfig: ImapConfig = {
        host: decryptedConfig.host || 'imap.gmail.com',
        port: decryptedConfig.port || 993,
        username: decryptedConfig.username,
        password: decryptedConfig.password,
        tls: decryptedConfig.tls !== undefined ? decryptedConfig.tls : true,
        tlsOptions: decryptedConfig.tlsOptions || {
          servername: decryptedConfig.host || 'imap.gmail.com',
          rejectUnauthorized: false,
        },
        keepalive: decryptedConfig.keepalive !== undefined ? decryptedConfig.keepalive : true,
        autotls: decryptedConfig.autotls || 'always',
        maxReconnectAttempts: decryptedConfig.maxReconnectAttempts || 5,
        pollingInterval: decryptedConfig.pollingInterval || 30000,
        enableDuplicateChecking: decryptedConfig.enableDuplicateChecking !== undefined ? decryptedConfig.enableDuplicateChecking : true,
        enableMarkAsSeen: decryptedConfig.enableMarkAsSeen !== undefined ? decryptedConfig.enableMarkAsSeen : true,
        gmailLabel: decryptedConfig.gmailLabel || ['Firmprofit Recieved'],
      };

      this.logger.log(`Retrieved IMAP config for tenant: ${tenantId}`);
      return finalConfig;
    } catch (error) {
      this.logger.error(
        `Failed to get IMAP config for tenant ${tenantId}:`,
        error.message,
      );
      throw error;
    }
  }

  /**
   * Save or update IMAP configuration for a tenant
   */
  async saveImapConfig(tenantId: string, config: ImapConfig): Promise<TenantConfig> {
    try {
      // Encrypt sensitive fields
      const encryptedConfig = { ...config };
      
      if (encryptedConfig.username) {
        encryptedConfig.username = this.encrypt(encryptedConfig.username);
      }
      
      if (encryptedConfig.password) {
        encryptedConfig.password = this.encrypt(encryptedConfig.password);
      }

      const existingConfig = await this.tenantConfigRepository.findOne({
        where: {
          tenant_id: tenantId,
          key: 'imap_config',
        },
      });

      if (existingConfig) {
        existingConfig.config = encryptedConfig;
        const savedConfig = await this.tenantConfigRepository.save(existingConfig);
        this.logger.log(`Updated IMAP config for tenant: ${tenantId}`);
        return savedConfig;
      } else {
        const newConfig = this.tenantConfigRepository.create({
          tenant_id: tenantId,
          key: 'imap_config',
          config: encryptedConfig,
        });
        const savedConfig = await this.tenantConfigRepository.save(newConfig);
        this.logger.log(`Created IMAP config for tenant: ${tenantId}`);
        return savedConfig;
      }
    } catch (error) {
      this.logger.error(
        `Failed to save IMAP config for tenant ${tenantId}:`,
        error.message,
      );
      throw error;
    }
  }

  /**
   * Get fallback IMAP configuration from environment variables
   */
  getFallbackImapConfig(): ImapConfig {
    this.logger.warn('Using fallback IMAP configuration from environment variables');
    
    return {
      host: this.configService.get<string>('IMAP_HOST', 'imap.gmail.com'),
      port: this.configService.get<number>('IMAP_PORT', 993),
      username: this.configService.get<string>('GMAIL_USER'),
      password: this.configService.get<string>('GMAIL_PASSWORD'),
      tls: this.configService.get<boolean>('IMAP_TLS', true),
      tlsOptions: {
        servername: this.configService.get<string>('IMAP_HOST', 'imap.gmail.com'),
        rejectUnauthorized: this.configService.get<boolean>('IMAP_REJECT_UNAUTHORIZED', false),
      },
      keepalive: true,
      autotls: 'always',
      maxReconnectAttempts: this.configService.get<number>('IMAP_MAX_RECONNECT_ATTEMPTS', 5),
      pollingInterval: this.configService.get<number>('IMAP_POLLING_INTERVAL', 30000),
      enableDuplicateChecking: this.configService.get<boolean>('ENABLE_DUPLICATE_CHECKING', true),
      enableMarkAsSeen: this.configService.get<boolean>('ENABLE_MARK_AS_SEEN', true),
      gmailLabel: this.configService.get<string[]>('GMAIL_LABEL', ['Firmprofit Recieved']),
    };
  }

  /**
   * Get IMAP configuration with fallback to environment variables
   */
  async getImapConfigWithFallback(tenantId?: string): Promise<ImapConfig> {
    if (!tenantId) {
      return this.getFallbackImapConfig();
    }

    try {
      return await this.getImapConfig(tenantId);
    } catch (error) {
      this.logger.warn(
        `Failed to get IMAP config for tenant ${tenantId}, using fallback:`,
        error.message,
      );
      return this.getFallbackImapConfig();
    }
  }

 
  /**
   * Get fallback queue URL from environment variables
   */
  getFallbackQueueUrl(): string | null {
    const fallbackQueueUrl = this.configService.get<string>('DEFAULT_SQS_QUEUE_URL');
    if (fallbackQueueUrl) {
      this.logger.warn('Using fallback queue URL from environment variables');
    }
    return fallbackQueueUrl;
  }

  /**
   * Get queue URL with fallback to environment variables
   */
  async getQueueUrlWithFallback(tenantId?: string): Promise<string | null> {
    if (!tenantId) {
      return this.getFallbackQueueUrl();
    }

    try {
      const queueUrl = process.env.COURT_NOTICE_RESULT_QUEUE_URL;
      console.log("🚀 ~ ImapConfigService ~ getQueueUrlWithFallback ~ queueUrl:", queueUrl)
      return queueUrl;
    } catch (error) {
      this.logger.warn(
        `Failed to get queue URL for tenant ${tenantId}, using fallback:`,
        error.message,
      );
      return this.getFallbackQueueUrl();
    }
  }
} 