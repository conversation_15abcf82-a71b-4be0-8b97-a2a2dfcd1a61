import 'reflect-metadata';
import { DataSource } from 'typeorm';
import { seedImapConfigs, seedImapConfigsWithEncryption } from './imap-config.seed';
import { TenantConfig } from '../database/entities/tenant-config.entity';

// Initialize your DataSource for the existing public schema
const dataSource = new DataSource({
  type: 'postgres',
  host: process.env.POSTGRES_HOST || 'localhost',
  port: parseInt(process.env.POSTGRES_PORT, 10) || 5432,
  username: process.env.POSTGRES_USERNAME || 'postgres',
  password: process.env.POSTGRES_PASSWORD || 'postgres',
  database: process.env.POSTGRES_DATABASE || 'firmprofit',
  schema: 'public', // Use existing public schema
  entities: [TenantConfig],
  synchronize: false, // Don't create tables, use existing ones
  logging: true,
});

// Run the seed
export const runSeeds = async (): Promise<void> => {
  try {
    console.log('🚀 Initializing DataSource for connectors seeding...');
    await dataSource.initialize();
    console.log('✅ DataSource initialized successfully');

    console.log('🔍 Checking existing tenant_config table...');
    
    // Test the connection and table access
    const tenantConfigRepository = dataSource.getRepository(TenantConfig);
    const totalConfigs = await tenantConfigRepository.count();
    console.log(`📊 Found ${totalConfigs} existing configurations in tenant_config table`);

    // Seed IMAP configurations
    console.log('🌱 Starting IMAP configuration seeding...');
    
    if (process.env.NODE_ENV === 'production') {
      // Use encrypted configs for production
      console.log('🔐 Using encrypted configurations for production environment');
      await seedImapConfigsWithEncryption(dataSource, process.env.ENCRYPTION_KEY);
    } else {
      // Use unencrypted configs for development
      console.log('🔓 Using unencrypted configurations for development environment');
      await seedImapConfigs(dataSource);
    }

    const finalCount = await tenantConfigRepository.count();
    console.log(`📊 Total configurations after seeding: ${finalCount}`);
    console.log('🎉 All seeds completed successfully');

  } catch (error) {
    console.error('❌ Error during DataSource initialization or seeding:', error);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  } finally {
    try {
      await dataSource.destroy();
      console.log('🔌 Database connection closed');
    } catch (closeError) {
      console.error('❌ Error closing database connection:', closeError);
    }
  }
};

// Run the seeds if this file is executed directly
if (require.main === module) {
  console.log('🌱 Starting IMAP configuration seeding...');
  runSeeds();
}