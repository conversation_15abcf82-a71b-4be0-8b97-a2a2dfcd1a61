import { DataSource } from 'typeorm';
import { TenantConfig } from '../database/entities/tenant-config.entity';

export const seedImapConfigs = async (dataSource: DataSource): Promise<void> => {
  const tenantConfigRepository = dataSource.getRepository(TenantConfig);

  console.log('🌱 Seeding IMAP configurations...');

  // Check if IMAP configurations already exist
  const existingCount = await tenantConfigRepository.count({
    where: { key: 'imap_config' },
  });

  if (existingCount > 0) {
    console.log('✅ IMAP configurations already exist in tenant_config table');
    return;
  }

  // Sample IMAP configurations for different tenants
  const imapConfigs = [
    {
      tenant_id: '1',
      key: 'imap_config',
      // queue_url: 'https://sqs.us-east-1.amazonaws.com/513981683228/courtNotice',
      config: {
        host: 'imap.gmail.com',
        port: 993,
        username: '<EMAIL>',
        password: 'vgzq lawh gdaw scjz',
        tls: true,
        tlsOptions: {
          servername: 'imap.gmail.com',
          rejectUnauthorized: false,
        },
        keepalive: true,
        autotls: 'always',
        maxReconnectAttempts: 5,
        pollingInterval: 30000,
        enableDuplicateChecking: true,
        enableMarkAsSeen: true,
        gmailLabel: ['Firmprofit Recieved'],
      },
    },
    {
      tenant_id: '2',
      key: 'imap_config',
      // queue_url: 'https://sqs.us-east-1.amazonaws.com/513981683228/courtNotice',
      config: {
        host: 'imap.gmail.com',
        port: 993,
        username: '<EMAIL>',
        password: 'demo_password2',
        tls: true,
        tlsOptions: {
          servername: 'imap.gmail.com',
          rejectUnauthorized: false,
        },
        keepalive: true,
        autotls: 'always',
        maxReconnectAttempts: 3,
        pollingInterval: 45000,
        enableDuplicateChecking: true,
        enableMarkAsSeen: false,
        gmailLabel: ['Firmprofit Recieved'],
      },
    },
    {
      tenant_id: 'stephen-tenant-id',
      key: 'imap_config',
      // queue_url: 'https://sqs.us-east-1.amazonaws.com/513981683228/courtNotice',
      config: {
        host: 'imap.gmail.com',
        port: 993,
        username: '<EMAIL>',
        password: 'stephen_password',
        tls: true,
        tlsOptions: {
          servername: 'imap.gmail.com',
          rejectUnauthorized: false,
        },
        keepalive: true,
        autotls: 'always',
        maxReconnectAttempts: 5,
        pollingInterval: 30000,
        enableDuplicateChecking: true,
        enableMarkAsSeen: true,
        gmailLabel: ['Firmprofit Recieved'],
      },
    },
    {
      tenant_id: 'moskovich-tenant-id',
      key: 'imap_config',
      // queue_url: 'https://sqs.us-east-1.amazonaws.com/513981683228/courtNotice',
      config: {
        host: 'imap.gmail.com',
        port: 993,
        username: '<EMAIL>',
        password: 'moskovich_password',
        tls: true,
        tlsOptions: {
          servername: 'imap.gmail.com',
          rejectUnauthorized: false,
        },
        keepalive: true,
        autotls: 'always',
        maxReconnectAttempts: 5,
        pollingInterval: 30000,
        enableDuplicateChecking: true,
        enableMarkAsSeen: true,
        gmailLabel: ['Firmprofit Recieved'],
      },
    },
  ];

  try {
    // Insert IMAP configurations
    await tenantConfigRepository.save(imapConfigs);
    console.log(`✅ Successfully seeded ${imapConfigs.length} IMAP configurations`);
  } catch (error) {
    console.error('❌ Error seeding IMAP configurations:', error);
    throw error;
  }
};

export const seedImapConfigsWithEncryption = async (
  dataSource: DataSource,
  encryptionKey: string = 'default-encryption-key-change-in-production'
): Promise<void> => {
  const crypto = require('crypto');
  const algorithm = 'aes-256-cbc';

  const encrypt = (text: string): string => {
    try {
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipher(algorithm, encryptionKey);
      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      return iv.toString('hex') + ':' + encrypted;
    } catch (error) {
      console.error('Error encrypting data:', error);
      return text; // Return original text if encryption fails
    }
  };

  const tenantConfigRepository = dataSource.getRepository(TenantConfig);

  console.log('🔐 Seeding encrypted IMAP configurations...');

  // Check if IMAP configurations already exist
  const existingCount = await tenantConfigRepository.count({
    where: { key: 'imap_config' },
  });

  if (existingCount > 0) {
    console.log('✅ Encrypted IMAP configurations already exist in tenant_config table');
    return;
  }

  // Sample IMAP configurations with encrypted sensitive data
  const encryptedImapConfigs = [
    {
      tenant_id: '1',
      key: 'imap_config',
      // queue_url: 'https://sqs.us-east-1.amazonaws.com/513981683228/courtNotice',
      config: {
        host: 'imap.gmail.com',
        port: 993,
        username: encrypt('<EMAIL>'),
        password: encrypt('demo_password'),
        tls: true,
        tlsOptions: {
          servername: 'imap.gmail.com',
          rejectUnauthorized: false,
        },
        keepalive: true,
        autotls: 'always',
        maxReconnectAttempts: 5,
        pollingInterval: 30000,
        enableDuplicateChecking: true,
        enableMarkAsSeen: true,
      },
    },
    {
      tenant_id: '2',
      key: 'imap_config',
      // queue_url: 'https://sqs.us-east-1.amazonaws.com/513981683228/courtNotice',
      config: {
        host: 'imap.gmail.com',
        port: 993,
        username: encrypt('<EMAIL>'),
        password: encrypt('demo_password2'),
        tls: true,
        tlsOptions: {
          servername: 'imap.gmail.com',
          rejectUnauthorized: false,
        },
        keepalive: true,
        autotls: 'always',
        maxReconnectAttempts: 3,
        pollingInterval: 45000,
        enableDuplicateChecking: true,
        enableMarkAsSeen: false,
      },
    },
  ];

  try {
    // Insert encrypted IMAP configurations
    await tenantConfigRepository.save(encryptedImapConfigs);
    console.log(`🔐 Successfully seeded ${encryptedImapConfigs.length} encrypted IMAP configurations`);
  } catch (error) {
    console.error('❌ Error seeding encrypted IMAP configurations:', error);
    throw error;
  }
}; 