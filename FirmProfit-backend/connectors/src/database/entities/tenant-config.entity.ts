import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryC<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity({ 
  name: 'tenant_config',
  schema: 'public' // Explicitly reference the public schema
})
export class TenantConfig {
  @PrimaryColumn('text')
  tenant_id: string;

  @PrimaryColumn('text')
  key: string;

  // @PrimaryColumn('text')
  // queue_url: string;

  @Column('jsonb')
  config: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  created_at: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updated_at: Date;
}

export interface ImapConfig {
  host: string;
  port: number;
  username: string;
  password: string;
  tls: boolean;
  tlsOptions?: {
    servername: string;
    rejectUnauthorized: boolean;
  };
  keepalive?: boolean;
  autotls?: string;
  maxReconnectAttempts?: number;
  pollingInterval?: number;
  enableDuplicateChecking?: boolean;
  enableMarkAsSeen?: boolean;
  gmailLabel?: string[];
} 