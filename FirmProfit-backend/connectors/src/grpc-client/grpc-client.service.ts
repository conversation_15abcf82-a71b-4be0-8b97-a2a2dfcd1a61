import { Injectable, Logger, OnModuleInit, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ClientGrpc, ClientsModule, Transport } from '@nestjs/microservices';
import { join } from 'path';
import { firstValueFrom, Observable } from 'rxjs';

interface EmailMessage {
  subject: string;
  from: string;
  body: string;
  receivedAt: string;
}

interface EmailAck {
  success: boolean;
  message: string;
}

interface EmailServiceClient {
  handleNewEmail(data: EmailMessage): Observable<EmailAck>;
}

@Injectable()
export class GrpcClientService implements OnModuleInit {
  private readonly logger = new Logger(GrpcClientService.name);
  private emailServiceClient: EmailServiceClient;

  constructor(
    private readonly configService: ConfigService,
    @Inject('EMAIL_SERVICE') private readonly client: ClientGrpc,
  ) {}

  onModuleInit() {
    this.emailServiceClient = this.client.getService<EmailServiceClient>('EmailService');
    this.logger.log('gRPC Email Service client initialized');
  }

  async handleNewEmail(emailData: EmailMessage): Promise<EmailAck> {
    try {
      this.logger.log(`Sending email to API Gateway: ${emailData.subject}`);
      
      const result = await firstValueFrom(
        this.emailServiceClient.handleNewEmail(emailData)
      );
      
      return result;
    } catch (error) {
      this.logger.error('Error sending email to API Gateway:', error);
      throw error;
    }
  }
} 