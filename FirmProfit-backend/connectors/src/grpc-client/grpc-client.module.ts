import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { GrpcClientService } from './grpc-client.service';
import { join } from 'path';

@Module({
  imports: [
    ConfigModule,
    ClientsModule.registerAsync([
      {
        name: 'EMAIL_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => {
          const apiGatewayHost = configService.get<string>('API_GATEWAY_HOST', 'localhost');
          const apiGatewayPort = configService.get<number>('API_GATEWAY_GRPC_PORT', 3004);

          console.log('API Gateway gRPC Config:');
          console.log('Host:', apiGatewayHost);
          console.log('Port:', apiGatewayPort);

          return {
            transport: Transport.GRPC,
            options: {
              package: 'email',
              protoPath: join(__dirname, '../../proto/email.proto'),
              url: `${apiGatewayHost}:${apiGatewayPort}`,
              loader: {
                keepCase: true,
                longs: String,
                enums: String,
                defaults: true,
                oneofs: true,
              },
              maxSendMessageLength: 1024 * 1024 * 4, // 4MB
              maxReceiveMessageLength: 1024 * 1024 * 4, // 4MB
            },
          };
        },
        inject: [ConfigService],
      },
    ]),
  ],
  providers: [GrpcClientService],
  exports: [GrpcClientService],
})
export class GrpcClientModule {} 