import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { SqsConsumerService } from './sqs-consumer.service';
import { SqsConsumerController } from './sqs-consumer.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Template, TemplateSchema } from './entity/template.entity';
import { FormComponent, FormComponentSchema } from './entity/form-component.entity';
import { DerivedComponent, DerivedComponentSchema } from './entity/derived-component.entity';
import { Trigger, TriggerSchema } from './entity/trigger.entity';
import { Task, TaskSchema } from './entity/task.entity';
import { ClientSchema, LocationSchema, User, UserSchema } from '@shared/database';
import { WorkflowExecution, WorkflowExecutionSchema } from './entity/workflow-execution.entity';
import { TaskExecution, TaskExecutionSchema } from './entity/task-execution.entity';
import { County, CountySchema } from './entity/counties.entity';
import { Attendees, AttendeesSchema } from './entity/attendees';
import { CourtLocation, CourtLocationSchema } from './entity/court.location.entity';
import { UserMetaData, UserMetaDataSchema } from './entity/user-meta.entity';
import { CourtNoticeType, CourtNoticeTypeSchema } from './entity/court.notice.type.entity';
import { Matter, MatterSchema } from './entity/matter.entity';
import { Client } from '@nestjs/microservices';
import { CourtNoticeAction, CourtNoticeActionSchema } from './entity/court-notice-action';
import { MycaseClientMatter, MycaseClientMatterSchema } from './entity/mycase.client.matter.entity';
import { MyCaseHistory, MyCaseHistorySchema } from './entity/my-case-history.entity';
import { EventActionHistory, EventActionHistorySchema } from './entity/event-action-history';
import { ProcessingFailureLog, ProcessingFailureLogSchema } from './entity/processing-failure-log.entity';
import { EmailModule } from '../common/email/email.module';
import { Email, EmailSchema } from '../gmail/schemas/email.schema';
import { RoundRobinState, RoundRobinStateSchema } from './entity/round-robin-state.entity';
import { Role, RoleSchema } from './entity/roles.entity';
import { UserRole, UserRoleSchema } from './entity/user-role.entity';
import { ApiRequest, ApiRequestSchema } from './entity/api-request.entity';
import { RoundRobinService } from './round-robin.service';

@Module({
  imports: [
    ConfigModule,
    EmailModule,
    MongooseModule.forFeature([
      { name: Template.name, schema: TemplateSchema },
      { name: FormComponent.name, schema: FormComponentSchema },
      { name: DerivedComponent.name, schema: DerivedComponentSchema },
      { name: Trigger.name, schema: TriggerSchema },
      { name: Task.name, schema: TaskSchema },
      { name: User.name, schema: UserSchema },
      { name: WorkflowExecution.name, schema: WorkflowExecutionSchema },
      { name: TaskExecution.name, schema: TaskExecutionSchema },
      { name: County.name, schema: CountySchema },
      { name: Attendees.name, schema: AttendeesSchema },
      { name: CourtLocation.name, schema: CourtLocationSchema },
      { name: UserMetaData.name, schema: UserMetaDataSchema },
      { name: CourtNoticeType.name, schema: CourtNoticeTypeSchema },
      { name: Matter.name, schema: MatterSchema },
      { name: Client.name, schema: ClientSchema },
      { name: CourtNoticeAction.name, schema: CourtNoticeActionSchema },
      { name: MycaseClientMatter.name, schema: MycaseClientMatterSchema },
      { name: MyCaseHistory.name, schema: MyCaseHistorySchema },
      { name: EventActionHistory.name, schema: EventActionHistorySchema },
      { name: ProcessingFailureLog.name, schema: ProcessingFailureLogSchema },
      { name: Email.name, schema: EmailSchema },
      { name: RoundRobinState.name, schema: RoundRobinStateSchema },
      { name: Role.name, schema: RoleSchema },
      { name: UserRole.name, schema: UserRoleSchema },
      { name: ApiRequest.name, schema: ApiRequestSchema },
    ]),
  ],
  controllers: [SqsConsumerController],
  providers: [SqsConsumerService, RoundRobinService],
  exports: [SqsConsumerService],
})
export class SqsConsumerModule {} 