import {
  Injectable,
  Logger,
  OnModuleInit,
  OnModuleDestroy,
} from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import {
  SQSClient,
  ReceiveMessageCommand,
  DeleteMessageCommand,
  Message,
} from "@aws-sdk/client-sqs";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose"; // Import Types from mongoose
import axios from "axios";
// Remove the * as mongoose import and use specific imports

// Import entities
import { Client, ClientDocument } from "./entity/client.entity";
import { Matter, MatterDocument } from "./entity/matter.entity";
import { Template, TemplateDocument } from "./entity/template.entity";
import {
  WorkflowExecution,
  WorkflowExecutionDocument,
} from "./entity/workflow-execution.entity";
import {
  TaskExecution,
  TaskExecutionDocument,
} from "./entity/task-execution.entity";
import {
  FormComponent,
  FormComponentDocument,
} from "./entity/form-component.entity";
import {
  ProcessingFailureLog,
  ProcessingFailureLogDocument,
} from "./entity/processing-failure-log.entity";
import { User, UserDocument } from "./entity/user.entity";
import { UserMetaData, UserMetaDataDocument } from "./entity/user-meta.entity";
import { Email, EmailDocument } from "../gmail/schemas/email.schema";
import { ApiRequest, ApiRequestDocument } from "./entity/api-request.entity";

// Import enums
import { Work_flow_execution_status } from "./enum/template-status.enum";

// Import services
import { EmailNotificationService } from "../common/email/email.service";
import { RoundRobinService } from "./round-robin.service";
import { MycaseClientMatter, MycaseClientMatterDocument } from "./entity/mycase.client.matter.entity";

interface QueueMessageData {
  subject: string;
  from: string;
  body: string;
  receivedAt: string;
  messageId: string;
  uniqueHash: string;
  uid: number;
  seqno: number;
  tenantId: string;
  processedAt: string;
  isProcessed: boolean;
  isMarkedAsSeen: boolean;
  errorMessage: string | null;
  metadata: {
    attachments: Array<{
      filename: string;
      contentType: string;
      size: number;
      s3Key: string;
      s3Bucket: string;
      s3Url: string;
      contentId: string;
      checksum: string;
      attachmentId: string;
      storageType: string;
      uploadedAt: string;
    }>;
    [key: string]: any;
  };
}

interface ExtractionAPIResponse {
  message_id: string;
  fields: string[];
  extracted_details_attachment: Array<{
    [url: string]: Array<{
      message: string;
      records: Array<{
        [key: string]: string;
      }>;
    }>;
  }>;
  extracted_details_rpa: any[];
  extracted_details_email_body: any[];
  processing_summary_email_attachments: {
    total_files: number;
    successful: number;
    failed: number;
  };
  processing_summary_rpa: {
    total_files: number;
    successful: number;
    failed: number;
  };
  processing_summary_email_body: {
    total_files: number;
    successful: number;
    failed: number;
  };
  message: string;
}

@Injectable()
export class SqsConsumerService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(SqsConsumerService.name);
  private sqsClient: SQSClient;
  private queueUrl: string;
  private isRunning = false;
  private pollingInterval: NodeJS.Timeout;

  constructor(
    private readonly configService: ConfigService,
    private readonly emailNotificationService: EmailNotificationService,
    private readonly roundRobinService: RoundRobinService,
    @InjectModel(Client.name) private clientModel: Model<ClientDocument>,
    @InjectModel(Matter.name) private matterModel: Model<MatterDocument>,
    @InjectModel(Template.name) private templateModel: Model<TemplateDocument>,
    @InjectModel(WorkflowExecution.name)
    private workflowExecutionModel: Model<WorkflowExecutionDocument>,
    @InjectModel(TaskExecution.name)
    private taskExecutionModel: Model<TaskExecutionDocument>,
    @InjectModel(FormComponent.name)
    private formComponentModel: Model<FormComponentDocument>,
    @InjectModel(ProcessingFailureLog.name)
    private processingFailureLogModel: Model<ProcessingFailureLogDocument>,
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    @InjectModel(UserMetaData.name)
    private userMetaDataModel: Model<UserMetaDataDocument>,
    @InjectModel(Email.name) private emailModel: Model<EmailDocument>,
    @InjectModel(ApiRequest.name) private apiRequestModel: Model<ApiRequestDocument>,
    @InjectModel(MycaseClientMatter.name) private mycaseClientMatterModel: Model<MycaseClientMatterDocument>
  ) { }

  onModuleInit() {
    this.initializeSqsClient();
    this.startPolling();
  }

  onModuleDestroy() {
    this.stopPolling();
  }

  private initializeSqsClient() {
    const awsRegion =
      this.configService.get("AWS_SQS_REGION") ||
      this.configService.get("AWS_REGION") ||
      "us-east-1";
    const accessKeyId =
      this.configService.get("AWS_SQS_ACCESS_KEY_ID") ||
      this.configService.get("AWS_ACCESS_KEY_ID");
    const secretAccessKey =
      this.configService.get("AWS_SQS_SECRET_ACCESS_KEY") ||
      this.configService.get("AWS_SECRET_ACCESS_KEY");
    this.queueUrl = this.configService.get("COURT_NOTICE_RESULT_QUEUE_URL");

    if (!this.queueUrl) {
      throw new Error("COURT_NOTICE_RESULT_QUEUE_URL is not configured");
    }

    if (!accessKeyId || !secretAccessKey) {
      throw new Error(
        "AWS credentials are not configured. Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY or AWS_SQS_ACCESS_KEY_ID and AWS_SQS_SECRET_ACCESS_KEY"
      );
    }

    this.sqsClient = new SQSClient({
      region: awsRegion,
      credentials: {
        accessKeyId,
        secretAccessKey,
      },
    });

    this.logger.log(`SQS Consumer initialized for queue: ${this.queueUrl}`);
    this.logger.log(`Using AWS Region: ${awsRegion}`);
    this.logger.log(
      `Using AWS Access Key ID: ${accessKeyId.substring(0, 8)}...`
    );
  }

  private startPolling() {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    this.logger.log("Starting SQS polling for court notice results...");

    this.pollQueue();
    this.pollingInterval = setInterval(() => {
      this.pollQueue();
    }, 5000);
  }

  private stopPolling() {
    this.isRunning = false;
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
    }
    this.logger.log("SQS polling stopped");
  }

  private async pollQueue() {
    if (!this.isRunning) {
      return;
    }

    try {
      const command = new ReceiveMessageCommand({
        QueueUrl: this.queueUrl,
        MaxNumberOfMessages: 10,
        WaitTimeSeconds: 20,
        VisibilityTimeout: 30,
      });

      const response = await this.sqsClient.send(command);

      if (response.Messages && response.Messages.length > 0) {
        this.logger.log(
          `Received ${response.Messages.length} messages from queue`
        );

        for (const message of response.Messages) {
          await this.processMessage(message);
        }
      } else {
        this.logger.debug("No messages received from queue");
      }
    } catch (error) {
      this.logger.error("Error polling SQS queue:", error);
    }
  }

  private async processMessage(message: Message) {
    try {
      this.logger.log("=== COURT NOTICE RESULT MESSAGE ===");
      this.logger.log(`Message ID: ${message.MessageId}`);

      if (message.Body) {
        this.logger.log("Message Body:");
        console.log(JSON.stringify(JSON.parse(message.Body), null, 2));

        const messageData: QueueMessageData = JSON.parse(message.Body);

        // Process the message asynchronously without waiting, but pass receiptHandle for deletion after API call
        this.processCourtNoticeMessageAsync(messageData, message.ReceiptHandle).catch((error) => {
          this.logger.error("Error in async processing:", error);
          // Delete message on error to prevent reprocessing
          if (message.ReceiptHandle) {
            this.deleteMessage(message.ReceiptHandle).catch(deleteError => {
              this.logger.error("Failed to delete message after error:", deleteError);
            });
          }
        });
      }
    } catch (error) {
      this.logger.error("Error processing message:", error);
      this.logger.error("Raw message:", message);
      // Delete message on parsing error to prevent reprocessing
      if (message.ReceiptHandle) {
        this.deleteMessage(message.ReceiptHandle).catch(deleteError => {
          this.logger.error("Failed to delete message after parsing error:", deleteError);
        });
      }
    }
  }

  private async processCourtNoticeMessageAsync(
    messageData: QueueMessageData,
    receiptHandle?: string
  ): Promise<void> {
    console.log(
      "🚀 ~ SqsConsumerService ~ processCourtNoticeMessageAsync ~ messageData:",
      messageData
    );
    try {
      // Step 1: Call API to process attachments (fire and forget)
      const extractedDataPromise = this.callAttachmentProcessingAPI(messageData, receiptHandle);
      
      // Continue processing without waiting for API response
      const extractedData = await extractedDataPromise;
      console.log(
        "🚀 ~ SqsConsumerService ~ processCourtNoticeMessageAsync ~ extractedData:",
        extractedData
      );

      if (!extractedData) {
        this.logger.error(
          "Failed to get extracted data from API - processing will be handled by error logging"
        );
        return;
      }

      // Check if extraction actually found any meaningful data
      const hasValidData = this.validateExtractedData(extractedData);
      console.log(
        "🚀 ~ SqsConsumerService ~ processCourtNoticeMessageAsync ~ hasValidData:",
        hasValidData
      );
      if (!hasValidData) {
        this.logger.warn(
          "No meaningful data extracted from court notice attachments"
        );
        this.logger.warn(
          "Extracted details:",
          extractedData.extracted_details_attachment
        );

        // Create workflow execution with default values for invalid data
        const workflowSuccess = await this.createWorkflowExecution(
          null, // No client found
          null, // No matter found
          extractedData,
          messageData,
          [] // No valid combinations for invalid data
        );
        return;
      }

      // Step 2: Process extracted details and create/find client and matter
      const { clientId, matterId, allValidCombinations } = await this.processExtractedDetails(
        extractedData,
        messageData.tenantId
      );
        console.log("🚀 ~ SqsConsumerService ~ processCourtNoticeMessageAsync ~ clientId:", clientId)
        console.log("🚀 ~ SqsConsumerService ~ processCourtNoticeMessageAsync ~ matterId:", matterId)



      // Step 3: Create workflow execution
      const workflowSuccess = await this.createWorkflowExecution(
        clientId,
        matterId,
        extractedData,
        messageData,
        allValidCombinations
      );

      if (workflowSuccess) {
        this.logger.log("Court notice processing completed successfully");
      } else {
        this.logger.error("Failed to create workflow execution");
      }
    } catch (error) {
      this.logger.error("Error processing court notice message:", error);

      // Log the processing failure and send notification
      await this.logProcessingFailure(
        messageData,
        "WORKFLOW_CREATION_FAILURE",
        `Failed to create workflow: ${error.message}`,
        error
      );
    }
  }

  private validateExtractedData(extractedData: ExtractionAPIResponse): boolean {
    // Check if we have any extraction data from any source
    const hasAttachmentData =
      extractedData.extracted_details_attachment &&
      extractedData.extracted_details_attachment.length > 0;
    const hasRpaData =
      extractedData.extracted_details_rpa &&
      extractedData.extracted_details_rpa.length > 0;
    const hasEmailBodyData =
      extractedData.extracted_details_email_body &&
      extractedData.extracted_details_email_body.length > 0;

    // If no data from any source, return false
    if (!hasAttachmentData && !hasRpaData && !hasEmailBodyData) {
      return false;
    }

    // Check extracted_details_attachment for meaningful content (records)
    if (hasAttachmentData) {
      for (const attachmentData of extractedData.extracted_details_attachment) {
        for (const urlKey in attachmentData) {
          const extractionResults = attachmentData[urlKey];
          for (const result of extractionResults) {
            if (result.records && result.records.length > 0) {
              // Check if records have meaningful data
              for (const record of result.records) {
                const recordValues = Object.values(record);
                if (
                  recordValues.some(
                    (value) =>
                      value &&
                      value !== "N/A" &&
                      typeof value === "string" &&
                      value.trim().length > 0
                  )
                ) {
                  return true;
                }
              }
            }
          }
        }
      }
    }

    // Check extracted_details_rpa for meaningful content (same structure as attachment)
    if (hasRpaData) {
      for (const rpaData of extractedData.extracted_details_rpa) {
        for (const urlKey in rpaData) {
          const extractionResults = rpaData[urlKey];
          for (const result of extractionResults) {
            if (result.records && result.records.length > 0) {
              // Check if records have meaningful data
              for (const record of result.records) {
                const recordValues = Object.values(record);
                if (
                  recordValues.some(
                    (value) =>
                      value &&
                      value !== "N/A" &&
                      typeof value === "string" &&
                      value.trim().length > 0
                  )
                ) {
                  return true;
                }
              }
            }
          }
        }
      }
    }

    // Check extracted_details_email_body for meaningful content (different structure - direct array)
    if (hasEmailBodyData) {
      for (const emailResult of extractedData.extracted_details_email_body) {
        if (emailResult.records && emailResult.records.length > 0) {
          // Check if records have meaningful data
          for (const record of emailResult.records) {
            const recordValues = Object.values(record);
            if (
              recordValues.some(
                (value) =>
                  value &&
                  value !== "N/A" &&
                  typeof value === "string" &&
                  value.trim().length > 0
              )
            ) {
              return true;
            }
          }
        }
      }
    }

    return false;
  }

  /**
   * Query email data from database using message ID or from address
   */
  private async queryEmailFromDatabase(
    messageData: QueueMessageData
  ): Promise<EmailDocument | null> {
    try {
      // First try to find by messageId
      let email = await this.emailModel
        .findOne({
          messageId: messageData.messageId,
        })
        .lean()
        .exec();

      // If not found by messageId, try by uniqueHash
      if (!email && messageData.uniqueHash) {
        email = await this.emailModel
          .findOne({
            uniqueHash: messageData.uniqueHash,
          })
          .lean()
          .exec();
      }

      // If still not found, try by from and subject combination
      if (!email) {
        email = await this.emailModel
          .findOne({
            from: messageData.from,
            subject: messageData.subject,
            tenantId: messageData.tenantId,
          })
          .lean()
          .exec();
      }

      if (email) {
        this.logger.log(`Found email in database: ${email._id}`);
        return email as EmailDocument;
      } else {
        this.logger.warn(
          `Email not found in database for messageId: ${messageData.messageId}`
        );
        return null;
      }
    } catch (error) {
      this.logger.error("Error querying email from database:", error);
      return null;
    }
  }

  private async callAttachmentProcessingAPI(
    messageData: QueueMessageData,
    receiptHandle?: string
  ): Promise<ExtractionAPIResponse | null> {
    // Initialize payload outside try block so it's accessible in catch block
    let payload: any = null;
    let apiRequestId: string | null = null;
    let startTime: number | null = null;
    let messageDeleted = false; // Track if message has been deleted

    try {
      const hasAttachments =
        messageData.metadata.attachments &&
        messageData.metadata.attachments.length > 0;
      const hasEmailBody =
        messageData.body && messageData.body.trim().length > 0;

      if (!hasAttachments && !hasEmailBody) {
        this.logger.log(
          "No attachments and no email body content found in message - proceeding with empty payload"
        );
      }

      // Query email data from database
      const emailData = await this.queryEmailFromDatabase(messageData);

      // Extract all presigned URLs from attachments
      const presignedUrls =
        messageData.metadata.attachments?.map(
          (attachment) => attachment.s3Url
        ) || [];

      if (hasAttachments) {
        this.logger.log(`Processing ${presignedUrls.length} attachment(s)`);
        this.logger.log(
          `Attachment filenames: ${messageData.metadata.attachments?.map((a) => a.filename).join(", ") || ""}`
        );
      }

      if (hasEmailBody) {
        this.logger.log(`Processing email body content`);
      }

      // API endpoint
      const apiEndpoint =
        this.configService.get("ATTACHMENT_PROCESSING_API_URL") ||
        "http://localhost:5000/process_email";
      const email_id = messageData.from || "<EMAIL>";

      // Extract RPA URLs from email data
      const rpaUrls: string[] = [];
      if (emailData?.clickHereLinks && emailData.clickHereLinks.length > 0) {
        rpaUrls.push(...emailData.clickHereLinks.map((link) => link.url));
      }
      // Default RPA URL if no links found
      // if (rpaUrls.length === 0) {
      //   rpaUrls.push("");
      // }

      // Get email body from database or fallback to messageData
      const email_body = emailData?.body || messageData.body || "";

      payload = {
        data: {
          message_id: email_id,
          attachment: { presigned_urls: presignedUrls },
          rpa: { urls: rpaUrls },
          fields: [
            "case_number",
            "Defendant Name",
            "charge",
            "attorney_name",
            "startTime",
            "endTime",
            "startDate",
            "endDate",
            "courtNoticeType",
            "courtLocation",
            "optionalAttendees",
            "requiredAttendees",
            "clientAttendance",
            "meetingtype",
            "phoneDetails",
            "meetingAddress",
            "meetingLink",
            "appointmentAction",
            "region",
            "deadlineornot",
          ],
          email_body: email_body,
        },
      };

      this.logger.log("API Payload:", JSON.stringify(payload, null, 2));

      // Save API request details to database
      try {
        const apiRequest = new this.apiRequestModel({
          url: apiEndpoint,
          request: payload,
          messageId: messageData.messageId,
          tenantId: messageData.tenantId,
          status: 'PENDING'
        });
        const savedApiRequest = await apiRequest.save();
        apiRequestId = savedApiRequest._id.toString();
        this.logger.log(`API request saved with ID: ${apiRequestId}`);
      } catch (saveError) {
        this.logger.error("Failed to save API request to database:", saveError);
        // Continue with the API call even if saving fails
      }

      startTime = Date.now();
      
      // Initiate the API call (fire and forget)
      const apiCallPromise = axios.post(apiEndpoint, payload, {
        headers: {
          "Content-Type": "application/json",
        },
      });

      // Delete the message from queue immediately after API call is initiated
      if (receiptHandle) {
        try {
          await this.deleteMessage(receiptHandle);
          messageDeleted = true; // Mark as deleted
          this.logger.log(
            `✅ Message deleted from queue after API call initiated: ${messageData.messageId}`
          );
        } catch (deleteError) {
          this.logger.error("Failed to delete message from queue:", deleteError);
        }
      }

      // Now wait for the API response
      const response = await apiCallPromise;
      const processingTime = Date.now() - startTime;

      // Update API request with response and processing time
      if (apiRequestId) {
        try {
          await this.apiRequestModel.findByIdAndUpdate(apiRequestId, {
            status: 'SUCCESS',
            response: response.data,
            processingTime: processingTime
          });
          this.logger.log(`API request ${apiRequestId} updated with success status`);
        } catch (updateError) {
          this.logger.error("Failed to update API request with response:", updateError);
        }
      }

      this.logger.log("API Response received:", response.data);

      // 🔍 DETAILED DEBUG: Log the complete Python API response structure
      this.logger.log("🐍 PYTHON API RESPONSE - Complete JSON structure:");
      this.logger.log(JSON.stringify(response.data, null, 2));

      // 🔍 DEBUG: Log individual sections for better visibility
      if (response.data.extracted_details_email_body) {
        this.logger.log("🐍 PYTHON API - Email Body Details:");
        this.logger.log(
          JSON.stringify(response.data.extracted_details_email_body, null, 2)
        );

        // Log each record in email body for detailed inspection
        response.data.extracted_details_email_body.forEach(
          (emailResult, index) => {
            this.logger.log(`🐍 PYTHON API - Email Body Entry ${index + 1}:`);
            this.logger.log(JSON.stringify(emailResult, null, 2));

            if (emailResult.records) {
              emailResult.records.forEach((record, recordIndex) => {
                this.logger.log(
                  `🐍 PYTHON API - Record ${recordIndex + 1} Details:`
                );
                this.logger.log(JSON.stringify(record, null, 2));

                // Specifically check for appointmentAction
                if (record.appointmentAction) {
                  this.logger.log(
                    `🎯 FOUND appointmentAction in record ${recordIndex + 1}:`
                  );
                  this.logger.log(
                    JSON.stringify(record.appointmentAction, null, 2)
                  );
                } else {
                  this.logger.log(
                    `❌ appointmentAction NOT FOUND in record ${recordIndex + 1}`
                  );
                  this.logger.log(
                    `Available fields in record: ${Object.keys(record).join(", ")}`
                  );
                }
              });
            } else {
              this.logger.log(
                `❌ No records found in email body entry ${index + 1}`
              );
            }
          }
        );
      } else {
        this.logger.log(
          "❌ No extracted_details_email_body found in Python API response"
        );
      }

      if (response.data.extracted_details_attachment) {
        this.logger.log("🐍 PYTHON API - Attachment Details:");
        this.logger.log(
          JSON.stringify(response.data.extracted_details_attachment, null, 2)
        );
      }

      if (response.data.extracted_details_rpa) {
        this.logger.log("🐍 PYTHON API - RPA Details:");
        this.logger.log(
          JSON.stringify(response.data.extracted_details_rpa, null, 2)
        );
      }

      this.logger.log("🐍 PYTHON API - Processing Summary:");
      this.logger.log(
        `Email Body: ${response.data.processing_summary_email_body?.successful || 0}/${response.data.processing_summary_email_body?.total_files || 0} successful`
      );
      this.logger.log(
        `Attachments: ${response.data.processing_summary_email_attachments?.successful || 0}/${response.data.processing_summary_email_attachments?.total_files || 0} successful`
      );
      this.logger.log(
        `RPA: ${response.data.processing_summary_rpa?.successful || 0}/${response.data.processing_summary_rpa?.total_files || 0} successful`
      );

      // Sanitize the entire response to replace all "N/A" values with empty strings
      const sanitizedResponse = this.sanitizeApiResponse(response.data);
      this.logger.log("API Response after sanitization:", sanitizedResponse);

      // Validate the response structure (basic structure check only)
      if (!sanitizedResponse) {
        this.logger.error("Invalid API response structure:", sanitizedResponse);
        await this.logProcessingFailure(
          messageData,
          "API_FAILURE",
          "Invalid API response structure",
          {
            apiEndpoint,
            requestPayload: payload,
            responseData: sanitizedResponse,
          }
        );
        return null;
      }

      // Check if the response contains any meaningful data
      const hasAttachmentContent =
        sanitizedResponse.extracted_details_attachment?.some((detail) => {
          return Object.values(detail).some((urlResults: any) => {
            return (
              Array.isArray(urlResults) &&
              urlResults.some((result) => {
                return (
                  result.records &&
                  Array.isArray(result.records) &&
                  result.records.length > 0
                );
              })
            );
          });
        });

      const hasRpaContent = sanitizedResponse.extracted_details_rpa?.some(
        (detail) => {
          return Object.values(detail).some((urlResults: any) => {
            return (
              Array.isArray(urlResults) &&
              urlResults.some((result) => {
                return (
                  result.records &&
                  Array.isArray(result.records) &&
                  result.records.length > 0
                );
              })
            );
          });
        }
      );

      const hasEmailContent =
        sanitizedResponse.extracted_details_email_body?.some((detail) => {
          return (
            detail.records &&
            Array.isArray(detail.records) &&
            detail.records.length > 0
          );
        });

      if (!hasAttachmentContent && !hasRpaContent && !hasEmailContent) {
        this.logger.warn("API returned empty extraction results");
        this.logger.warn(
          "Raw extracted details:",
          JSON.stringify(
            {
              attachment: sanitizedResponse.extracted_details_attachment,
              rpa: sanitizedResponse.extracted_details_rpa,
              email_body: sanitizedResponse.extracted_details_email_body,
            },
            null,
            2
          )
        );
        // Still return the response but log the warning
      }

      return sanitizedResponse;
    } catch (error) {
      const errorMessage =
        error.response?.data?.message || error.message || "Unknown API error";
      const statusCode = error.response?.status || "UNKNOWN";

      this.logger.error(`🚨 ATTACHMENT PROCESSING API FAILURE 🚨`);
      this.logger.error(`Status Code: ${statusCode}`);
      this.logger.error(`Error Message: ${errorMessage}`);
      this.logger.error(
        `API Endpoint: ${this.configService.get("ATTACHMENT_PROCESSING_API_URL")}`
      );
      this.logger.error(
        `Full Error Response:`,
        error.response?.data || error.message
      );

      // Delete the message from queue if API call fails and it hasn't been deleted yet
      if (receiptHandle && !messageDeleted) {
        try {
          await this.deleteMessage(receiptHandle);
          this.logger.log(
            `✅ Message deleted from queue after API call failure: ${messageData.messageId}`
          );
        } catch (deleteError) {
          this.logger.error("Failed to delete message from queue after API failure:", deleteError);
        }
      }

      // Update API request with error information if it was saved
      if (apiRequestId) {
        try {
          await this.apiRequestModel.findByIdAndUpdate(apiRequestId, {
            status: 'FAILED',
            errorMessage: errorMessage,
            response: error.response?.data || null,
            processingTime: startTime ? Date.now() - startTime : null
          });
          this.logger.log(`API request ${apiRequestId} updated with error status`);
        } catch (updateError) {
          this.logger.error("Failed to update API request with error:", updateError);
        }
      }

      // Log detailed failure information for activity logs
      await this.logProcessingFailure(
        messageData,
        "API_FAILURE",
        `Attachment processing API failed: ${errorMessage} (Status: ${statusCode})`,
        {
          statusCode,
          apiEndpoint: this.configService.get("ATTACHMENT_PROCESSING_API_URL"),
          requestPayload: payload,
          responseData: error.response?.data,
          errorMessage: error.message,
          errorStack: error.stack,
        }
      );

      return null;
    }
  }

  /**
   * Recursively sanitizes an API response object to replace all "N/A" values with empty strings
   * @param obj The object to sanitize
   * @returns The sanitized object with all "N/A" values replaced with empty strings
   */
  private sanitizeApiResponse(obj: any): any {
    if (obj === null || obj === undefined) {
      return obj;
    }

    // If it's a string and equals "N/A", replace with empty string
    if (typeof obj === "string" && obj === "N/A") {
      return "";
    }

    // If it's a string but not "N/A", return as is
    if (typeof obj === "string") {
      return obj;
    }

    // If it's an array, recursively sanitize each element
    if (Array.isArray(obj)) {
      return obj.map((item) => this.sanitizeApiResponse(item));
    }

    // If it's an object, recursively sanitize each property
    if (typeof obj === "object") {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = this.sanitizeApiResponse(value);
      }
      return sanitized;
    }

    // For all other types (number, boolean, etc.), return as is
    return obj;
  }

  private async processExtractedDetails(
    extractedData: ExtractionAPIResponse,
    tenantId: string
  ): Promise<{
    clientId: string | null;
    matterId: string | null;
    allValidCombinations: Array<{ clientId: string; matterId: string; clientName: string; caseNumber: string }>;
  }> {
    try {
      // Parse extracted details to find client and matter information
      const parsedData = this.parseExtractedDetails(extractedData);

      // NEW APPROACH: Find ALL valid client-matter combinations
      const allValidCombinations = await this.findAllValidClientMatterCombinations(
          extractedData,
          tenantId
        );

      this.logger.log(`✅ FOUND ${allValidCombinations.length} VALID CLIENT-MATTER COMBINATIONS`);

      // For backward compatibility, still return the first valid combination as primary
      let clientId: string | null = null;
      let matterId: string | null = null;

      if (allValidCombinations.length > 0) {
        const firstCombination = allValidCombinations[0];
        clientId = firstCombination.clientId;
        matterId = firstCombination.matterId;

        this.logger.log(
          `✅ PRIMARY CLIENT-MATTER: "${firstCombination.clientName}" - "${firstCombination.caseNumber}" (Client ID: ${clientId}, Matter ID: ${matterId})`
        );
      } else {
        // Fallback to original logic if no valid combinations found
        const validClientResult = await this.findFirstValidClient(
          extractedData,
          tenantId
        );
        clientId = validClientResult.clientId;
        const validClientName = validClientResult.clientName;

        if (clientId) {
          // Check if fallback client was used
          const clientResult = await this.findExistingClient(
            validClientName,
            tenantId
          );
          const isClientFallback = clientResult.isFallback;

          this.logger.log(
            `✅ FOUND VALID CLIENT (fallback): "${validClientName}" (ID: ${clientId})`
          );

          // Try to find matter for this client
          const matterResult = await this.findExistingMatter(
            parsedData.caseNumber,
            clientId,
            isClientFallback
          );
          matterId = matterResult.matterId;

          // Skip validation if either client or matter is fallback
          if (
            clientId &&
            matterId &&
            !isClientFallback &&
            !matterResult.isFallback
          ) {
            const isValidMatch = await this.validateClientMatterMatch(
              clientId,
              matterId,
              validClientName || parsedData.clientName,
              parsedData.matterName,
              parsedData.caseNumber,
              tenantId
            );

            if (!isValidMatch) {
              this.logger.warn(
                `Matter ${matterId} found but does not belong to client ${clientId} (${validClientName}). Nullifying matter.`
              );
              matterId = null;
            }
          } else if (isClientFallback || matterResult.isFallback) {
            this.logger.log(
              `🔄 SKIPPING VALIDATION: Using fallback IDs (Client fallback: ${isClientFallback}, Matter fallback: ${matterResult.isFallback})`
            );
          }
        } else {
          this.logger.warn(
            `❌ NO VALID CLIENTS FOUND in any of the extracted records`
          );
        }
      }

      this.logger.log(
        `Found records - Primary Client: ${clientId ? "Found" : "Not Found"}, Primary Matter: ${matterId ? "Found" : "Not Found"}, Total Valid Combinations: ${allValidCombinations.length}`
      );

      return { clientId, matterId, allValidCombinations };
    } catch (error) {
      this.logger.error("Error processing extracted details:", error);
      return { clientId: null, matterId: null, allValidCombinations: [] };
    }
  }

  // New method to find the first valid client from all client names in extracted data
  private async findFirstValidClient(
    extractedData: ExtractionAPIResponse,
    tenantId: string
  ): Promise<{ clientId: string | null; clientName: string | null }> {
    // Merge all records from different sources
    const mergedRecords = this.mergeAllExtractedRecords(extractedData);

    // Collect all unique client names
    const allClientNames = new Set<string>();
    for (const record of mergedRecords) {
      if (record["Defendant Name"]) {
        const cleanName = record["Defendant Name"].replace(/[",]/g, "").trim();
        if (cleanName) {
          allClientNames.add(cleanName);
        }
      }
    }

    this.logger.log(
      `🔍 VALIDATING ${allClientNames.size} unique client names for database existence`
    );

    // Try to find a valid client from all the client names
    for (const clientName of Array.from(allClientNames)) {
      this.logger.log(`🔍 Checking client: "${clientName}"`);

      const clientResult = await this.findExistingClient(clientName, tenantId);
      if (clientResult.clientId) {
        this.logger.log(
          `✅ VALID CLIENT FOUND: "${clientName}" (ID: ${clientResult.clientId})`
        );
        return { clientId: clientResult.clientId, clientName };
      } else {
        this.logger.warn(
          `❌ CLIENT NOT FOUND: "${clientName}" does not exist in database`
        );
      }
    }

    this.logger.warn(
      `🚫 NO VALID CLIENTS FOUND among all ${allClientNames.size} extracted client names`
    );
    return { clientId: null, clientName: null };
  }

  private async validateClientMatterMatch(
    clientId: string,
    matterId: string,
    extractedClientName: string,
    extractedMatterName: string,
    extractedCaseNumber: string,
    tenantId: string
  ): Promise<boolean> {
    try {
      this.logger.log(
        `�� Validating client-matter match: Client ${clientId}, Matter ${matterId}, Extracted Name: "${extractedClientName}", Matter: "${extractedMatterName}", Case: "${extractedCaseNumber}"`
      );

      // Step 1: Get the matter details to check its associated clients
      const matter = await this.matterModel.findById(matterId).lean().exec();

      if (!matter) {
        this.logger.warn(`Matter ${matterId} not found during validation`);
        return false;
      }

      // Step 2: Get the client details to verify the client name
      const client = await this.clientModel.findById(clientId).lean().exec();

      if (!client) {
        this.logger.warn(`Client ${clientId} not found during validation`);
        return false;
      }

      // Step 3: Check if the matter belongs to the found client
      const matterBelongsToClient =
        matter.clients?.some((matterClient: any) => {
          // Handle both string and number client IDs
          const matterClientId =
            matterClient.id?.toString() || matterClient._id?.toString();
          return matterClientId === clientId;
        }) || matter.client_id?.toString() === clientId;

      if (!matterBelongsToClient) {
        this.logger.warn(
          `❌ Matter validation failed: Matter ${matterId} does not belong to client ${clientId}`
        );
        return false;
      }

      // Step 4: Verify that the client name matches what was extracted from PDF
      const clientFullName =
        client.name ||
        `${client.first_name || ""} ${client.last_name || ""}`.trim();
      const extractedNameNormalized = extractedClientName
        .toLowerCase()
        .replace(/[^a-z\s]/g, "");
      const dbClientNameNormalized = clientFullName
        .toLowerCase()
        .replace(/[^a-z\s]/g, "");

      // Check for name match (either exact or partial match)
      const nameMatches =
        dbClientNameNormalized.includes(extractedNameNormalized) ||
        extractedNameNormalized.includes(dbClientNameNormalized);

      if (!nameMatches) {
        this.logger.warn(
          `❌ Name validation failed: Extracted name "${extractedClientName}" does not match DB client name "${clientFullName}"`
        );
        return false;
      }

      // Step 6: Verify that the matter name and case number from PDF match the database matter
      const matterValidation = this.validateMatterDetails(
        matter,
        extractedMatterName,
        extractedCaseNumber
      );

      if (!matterValidation.isValid) {
        this.logger.warn(
          `❌ Matter details validation failed: ${matterValidation.reason}`
        );
        return false;
      }

      this.logger.log(
        `✅ Client-Matter validation successful: Client "${clientFullName}" owns Matter "${matter.name || matter.case_number}" - All validations passed`
      );
      return true;
    } catch (error) {
      this.logger.error("Error validating client-matter match:", error);
      return false;
    }
  }

  private validateMatterDetails(
    matter: any,
    extractedMatterName: string,
    extractedCaseNumber: string
  ): { isValid: boolean; reason?: string } {
    try {
      this.logger.log(
        `🔍 Validating matter details: DB Matter "${matter.name || "N/A"}", DB Case "${matter.case_number || "N/A"}", Extracted Matter "${extractedMatterName}", Extracted Case "${extractedCaseNumber}"`
      );

      // Step 1: Validate case number match (if both exist)
      if (extractedCaseNumber && matter.case_number) {
        const extractedCaseNormalized = extractedCaseNumber
          .toLowerCase()
          .replace(/[^a-z0-9-]/g, "");
        const dbCaseNormalized = matter.case_number
          .toLowerCase()
          .replace(/[^a-z0-9-]/g, "");

        if (extractedCaseNormalized !== dbCaseNormalized) {
          return {
            isValid: false,
            reason: `Case number mismatch: Extracted "${extractedCaseNumber}" vs DB "${matter.case_number}"`,
          };
        }
        this.logger.log(
          `✅ Case number validation passed: "${extractedCaseNumber}"`
        );
      }

      // Step 3: If case number exists but extracted case number doesn't, that's suspicious
      if (!extractedCaseNumber && matter.case_number) {
        this.logger.warn(
          `⚠️ Warning: Matter has case number "${matter.case_number}" but no case number was extracted from PDF`
        );
      }

      return { isValid: true };
    } catch (error) {
      this.logger.error("Error validating matter details:", error);
      return {
        isValid: false,
        reason: `Validation error: ${error.message}`,
      };
    }
  }

  private compareKeywords(str1: string, str2: string): boolean {
    // Extract meaningful keywords (longer than 2 characters)
    const keywords1 = str1.split(/\s+/).filter((word) => word.length > 2);
    const keywords2 = str2.split(/\s+/).filter((word) => word.length > 2);

    if (keywords1.length === 0 || keywords2.length === 0) {
      return false;
    }

    // Check if at least 50% of keywords from either string match
    const matches1 = keywords1.filter((word) =>
      keywords2.some((k) => k.includes(word) || word.includes(k))
    );
    const matches2 = keywords2.filter((word) =>
      keywords1.some((k) => k.includes(word) || word.includes(k))
    );

    const matchRatio1 = matches1.length / keywords1.length;
    const matchRatio2 = matches2.length / keywords2.length;

    return matchRatio1 >= 0.5 || matchRatio2 >= 0.5;
  }

  private parseExtractedDetails(extractedData: ExtractionAPIResponse): {
    clientName: string;
    caseNumber: string;
    matterName: string;
    countyOfArrest: string;
    allRecords: any[];
  } {
    let clientName = "";
    let caseNumber = "";
    let matterName = "";
    let countyOfArrest = "";
    const allRecords: any[] = [];

    // Merge all records from different sources
    const mergedRecords = this.mergeAllExtractedRecords(extractedData);

    this.logger.log(
      `🔍 SEARCHING FOR VALID CLIENT among ${mergedRecords.length} records`
    );

    // Collect all unique client names first
    const allClientNames = new Set<string>();
    for (const record of mergedRecords) {
      if (record["Defendant Name"]) {
        const cleanName = record["Defendant Name"].replace(/[",]/g, "").trim();
        if (cleanName) {
          allClientNames.add(cleanName);
        }
      }
    }

    this.logger.log(
      `📋 Found ${allClientNames.size} unique client names: ${Array.from(allClientNames).join(", ")}`
    );

    // Process merged records to extract client/matter information
    for (const record of mergedRecords) {
      // Extract client name from "Defendant Name" field - but don't stop at first one
      // Instead, we'll find the first VALID client name that exists in database
      if (record["Defendant Name"] && !clientName) {
        const potentialClientName = record["Defendant Name"]
          .replace(/[",]/g, "")
          .trim();

        // For now, just take the first non-empty client name
        // The actual validation will happen in processExtractedDetails
        if (potentialClientName) {
          clientName = potentialClientName;
          this.logger.log(
            `📝 PRELIMINARY CLIENT NAME: "${clientName}" (will be validated later)`
          );
        }
      }

      // Extract case number
      if (record["case_number"] && !caseNumber) {
        caseNumber = record["case_number"];
      }

      // Extract county
      if (record["ex_county_of_arrest"] && !countyOfArrest) {
        countyOfArrest = record["ex_county_of_arrest"];
      } else if (record["county"] && !countyOfArrest) {
        countyOfArrest = record["county"];
      }

      // Create matter name from available information
      if (!matterName) {
        if (record["charge"]) {
          matterName = record["charge"];
        } else if (record["subject"]) {
          matterName = record["subject"];
        } else if (record["description"]) {
          matterName = record["description"];
        }
      }

      // Add record to allRecords for event processing
      allRecords.push(record);
    }

    // Clean up the values by removing any extra formatting
    clientName = clientName.replace(/[",]/g, "").trim();
    caseNumber = caseNumber.replace(/[",]/g, "").trim();
    matterName = matterName.replace(/[",]/g, "").trim();
    countyOfArrest = countyOfArrest.replace(/[",]/g, "").trim();

    // If no specific matter name found, create one from case and client
    if (!matterName && (clientName || caseNumber)) {
      matterName = `${caseNumber ? caseNumber + " - " : ""}${clientName || "Court Notice Matter"}`;
    }

    this.logger.log(
      `Parsed - Preliminary Client: ${clientName}, Case: ${caseNumber}, Matter: ${matterName}, County: ${countyOfArrest}, Total Records: ${allRecords.length}`
    );
    return { clientName, caseNumber, matterName, countyOfArrest, allRecords };
  }

  /**
   * Merges all records from extracted_details_attachment, extracted_details_rpa, and extracted_details_email_body
   */
  private mergeAllExtractedRecords(
    extractedData: ExtractionAPIResponse
  ): any[] {
    const allRecords: any[] = [];
    let attachmentRecordCount = 0;
    let rpaRecordCount = 0;
    let emailBodyRecordCount = 0;

    // Process extracted_details_attachment
    if (
      extractedData.extracted_details_attachment &&
      extractedData.extracted_details_attachment.length > 0
    ) {
      this.logger.log(
        `Processing ${extractedData.extracted_details_attachment.length} attachment data entries`
      );

      for (const attachmentData of extractedData.extracted_details_attachment) {
        const urlKeys = Object.keys(attachmentData);
        this.logger.log(`Attachment data has URL keys: ${urlKeys.join(", ")}`);

        for (const urlKey in attachmentData) {
          const extractionResults = attachmentData[urlKey];
          this.logger.log(
            `Processing URL key: ${urlKey}, has ${Array.isArray(extractionResults) ? extractionResults.length : 0} extraction results`
          );

          if (Array.isArray(extractionResults)) {
            for (const result of extractionResults) {
              if (result.records && Array.isArray(result.records)) {
                this.logger.log(
                  `Found ${result.records.length} records in attachment for URL: ${urlKey}`
                );
                allRecords.push(...result.records);
                attachmentRecordCount += result.records.length;
              }
            }
          }
        }
      }
    }

    // Process extracted_details_rpa (same structure as attachment)
    if (
      extractedData.extracted_details_rpa &&
      extractedData.extracted_details_rpa.length > 0
    ) {
      this.logger.log(
        `Processing ${extractedData.extracted_details_rpa.length} RPA data entries`
      );

      for (const rpaData of extractedData.extracted_details_rpa) {
        const urlKeys = Object.keys(rpaData);
        this.logger.log(`RPA data has URL keys: ${urlKeys.join(", ")}`);

        for (const urlKey in rpaData) {
          const extractionResults = rpaData[urlKey];
          this.logger.log(
            `Processing RPA URL key: ${urlKey}, has ${Array.isArray(extractionResults) ? extractionResults.length : 0} extraction results`
          );

          if (Array.isArray(extractionResults)) {
            for (const result of extractionResults) {
              if (result.records && Array.isArray(result.records)) {
                this.logger.log(
                  `Found ${result.records.length} records in RPA for URL: ${urlKey}`
                );
                allRecords.push(...result.records);
                rpaRecordCount += result.records.length;
              }
            }
          }
        }
      }
    }

    // Process extracted_details_email_body (different structure - direct array)
    if (
      extractedData.extracted_details_email_body &&
      Array.isArray(extractedData.extracted_details_email_body)
    ) {
      this.logger.log(
        `Processing ${extractedData.extracted_details_email_body.length} email body entries`
      );

      for (const emailResult of extractedData.extracted_details_email_body) {
        if (emailResult.records && Array.isArray(emailResult.records)) {
          this.logger.log(
            `Found ${emailResult.records.length} records in email body`
          );
          allRecords.push(...emailResult.records);
          emailBodyRecordCount += emailResult.records.length;
        }
      }
    }

    this.logger.log(
      `Record count summary - Attachment: ${attachmentRecordCount}, RPA: ${rpaRecordCount}, Email Body: ${emailBodyRecordCount}`
    );
    this.logger.log(
      `Merged total records: ${allRecords.length} from all sources`
    );

    // Verify expected count for static response: should be 6 total (2+1+2+1=6)
    this.logger.log(
      `Expected vs Actual: Static response should generate 6 events total. Current count: ${allRecords.length}`
    );

    return allRecords;
  }

  private async findExistingClient(
    clientName: string,
    tenantId: string
  ): Promise<{ clientId: string | null; isFallback: boolean }> {
    this.logger.log(
      `🔍 Client Lookup - Name: "${clientName}", TenantId: "${tenantId || "undefined"}"`
    );

    try {
      if (!clientName) {
        this.logger.log("❌ No client name provided");
        return null;
      }

      if (!tenantId) {
        this.logger.warn(
          "⚠️ No tenant ID provided, searching without tenant filter"
        );
      }

      // Clean up the client name and handle different formats
      const cleanName = clientName.replace(/[",]/g, "").trim();

      // Parse name formats: "JENKINS, PATRICK LEOTWAINE, Jr." -> firstName: "PATRICK", lastName: "JENKINS"
      let firstName = "";
      let lastName = "";

      if (cleanName.includes(",")) {
        // Format: "Last, First Middle, Suffix"
        const parts = cleanName.split(",").map((p) => p.trim());
        lastName = parts[0] || "";
        firstName = parts[1] ? parts[1].split(" ")[0] : ""; // Take first word after comma
      } else {
        // Format: "First Last" or "First Middle Last"
        const nameParts = cleanName.split(" ");
        firstName = nameParts[0] || "";
        lastName = nameParts[nameParts.length - 1] || "";
      }

      this.logger.log(`📝 Parsed - First: "${firstName}", Last: "${lastName}"`);

      // Build search query
      const searchQuery: any = { is_active: true };
      if (tenantId) {
        searchQuery.firm_uuid = tenantId;
      }

      // Strategy 1: Simple name field match (same as working simple query)
      let client = await this.clientModel
        .findOne({
          name: { $regex: new RegExp(clientName, "i") },
          is_active: true,
        })
        .lean()
        .exec();

      if (client) {
        this.logger.log(`✅ Found by simple name match: ${client._id}`);
        return { clientId: client._id.toString(), isFallback: false };
      }

      // Strategy 2: First and Last name combination
      if (firstName && lastName) {
        client = await this.clientModel
          .findOne({
            ...searchQuery,
            $or: [
              // Try first_name + last_name
              {
                $and: [
                  { first_name: { $regex: new RegExp(`^${firstName}$`, "i") } },
                  { last_name: { $regex: new RegExp(`^${lastName}$`, "i") } },
                ],
              },
              // Try reverse (in case first/last are swapped)
              {
                $and: [
                  { first_name: { $regex: new RegExp(`^${lastName}$`, "i") } },
                  { last_name: { $regex: new RegExp(`^${firstName}$`, "i") } },
                ],
              },
              // Try name field with firstName lastName format
              {
                name: {
                  $regex: new RegExp(`^${firstName}\\s+${lastName}$`, "i"),
                },
              },
              // Try name field with lastName, firstName format
              {
                name: {
                  $regex: new RegExp(`^${lastName},\\s*${firstName}`, "i"),
                },
              },
            ],
          })
          .lean()
          .exec();

        if (client) {
          this.logger.log(`✅ Found by first/last name: ${client._id}`);
          return { clientId: client._id.toString(), isFallback: false };
        }
      }

      // Strategy 3: Partial matching as fallback
      client = await this.clientModel
        .findOne({
          ...searchQuery,
          $or: [
            {
              name: {
                $regex: new RegExp(
                  cleanName.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"),
                  "i"
                ),
              },
            },
            {
              first_name: {
                $regex: new RegExp(
                  firstName || cleanName.split(" ")[0] || "",
                  "i"
                ),
              },
            },
            {
              last_name: {
                $regex: new RegExp(
                  lastName || cleanName.split(" ").slice(-1)[0] || "",
                  "i"
                ),
              },
            },
          ],
        })
        .lean()
        .exec();

      if (client) {
        this.logger.log(`✅ Found by partial match: ${client._id}`);
        return { clientId: client._id.toString(), isFallback: false };
      }

      this.logger.log(`❌ No client found for: "${clientName}"`);
      this.logger.warn(
        `🚫 CLIENT NOT IN DATABASE: "${clientName}" does not exist in the database. No events will be created for this client.`
      );

      // Debug: Log available clients in the database for troubleshooting
      try {
        const availableClients = await this.clientModel
          .find({ is_active: true })
          .select("name first_name last_name")
          .limit(10)
          .lean()
          .exec();

        this.logger.debug(`📋 Available clients in database (first 10):`);
        availableClients.forEach((client, index) => {
          const clientFullName =
            client.name ||
            `${client.first_name || ""} ${client.last_name || ""}`.trim();
          this.logger.debug(`   ${index + 1}. "${clientFullName}"`);
        });
      } catch (debugError) {
        this.logger.debug(
          `Error logging available clients: ${debugError.message}`
        );
      }

      // Fallback: Use default client ID from environment variables
      const fallbackClientId =
        this.configService.get<string>("FALLBACK_CLIENT_ID");
      if (fallbackClientId) {
        this.logger.log(
          `🔄 USING FALLBACK CLIENT: Using fallback client ID "${fallbackClientId}" from environment variables`
        );
        return { clientId: fallbackClientId, isFallback: true };
      }

      return { clientId: null, isFallback: false };
    } catch (error) {
      this.logger.error("Error finding existing client:", error);
      this.logger.error("Error details:", {
        message: error.message,
        name: error.name,
        code: error.code,
        stack: error.stack,
      });
      throw error;
    }
  }

  private async findExistingMatter(
    caseNumber: string,
    clientId: string | null,
    isClientFallback: boolean = false
    // tenantId: string
  ): Promise<{ matterId: string | null; isFallback: boolean }> {
    try {
      if (!caseNumber) {
        return { matterId: null, isFallback: false };
      }

      // If client is fallback, use fallback matter immediately
      if (isClientFallback) {
        const fallbackMatterId =
          this.configService.get<string>("FALLBACK_MATTER_ID");
        if (fallbackMatterId) {
          this.logger.log(
            `🔄 USING FALLBACK MATTER: Using fallback matter ID "${fallbackMatterId}" from environment variables (client is fallback)`
          );
          return { matterId: fallbackMatterId, isFallback: true };
        }
      }

      this.logger.log(
        `🔍 Matter Lookup - Case Number: "${caseNumber}", ClientId: "${clientId || "undefined"}"}"`
      );

      // Try to find existing matter by case number first using lean()
      let matter = await this.matterModel
        .findOne({
          case_number: caseNumber,
          // firm_uuid: tenantId,
          // is_active: true,
        })
        .lean()
        .exec();

      // If not found by case number only, try with client validation if clientId exists
      if (!matter && clientId) {
        matter = await this.matterModel
          .findOne({
            case_number: caseNumber,
            "clients.id": parseInt(clientId), // Only search with clientId if it exists
            // firm_uuid: tenantId,
            // is_active: true,
          })
          .lean()
          .exec();
      }

      if (matter) {
        this.logger.log(
          `✅ Found existing matter by case number: ${matter._id} - ${matter.name} (Case: ${matter.case_number})`
        );
        return { matterId: matter._id.toString(), isFallback: false };
      }

      this.logger.log(`❌ No matter found with case number: "${caseNumber}"`);

      // Fallback: Use default matter ID from environment variables
      const fallbackMatterId =
        this.configService.get<string>("FALLBACK_MATTER_ID");
      if (fallbackMatterId) {
        this.logger.log(
          `🔄 USING FALLBACK MATTER: Using fallback matter ID "${fallbackMatterId}" from environment variables`
        );
        return { matterId: fallbackMatterId, isFallback: true };
      }

      return { matterId: null, isFallback: false };
    } catch (error) {
      this.logger.error("Error finding existing matter:", error);
      throw error;
    }
  }

  private async createWorkflowExecution(
    clientId: string | null,
    matterId: string | null,
    extractedData: ExtractionAPIResponse,
    messageData: QueueMessageData,
    allValidCombinations: Array<{ clientId: string; matterId: string; clientName: string; caseNumber: string }> = []
  ): Promise<boolean> {
    try {
      // Step 1: Find template with template_id: 14
      const template = await this.templateModel
        .findOne({ template_id: 14 })
        .populate("task_id");
      if (!template) {
        throw new Error("Template with template_id 14 not found");
      }

      // Get userGroupId from template, fallback to default if not available
      const userGroupId: string =
        (template as any).templateGroupId?.toString() ||
        "6877420fd4928f6a37ba1b95";

      // Step 2: Find pending and failed state components
      const pendingState = await this.formComponentModel.findOne({
        type: "pending_message",
      });
      const failedState = await this.formComponentModel.findOne({
        type: "failed_message",
      });

      // Step 3: Generate execution ID
      const executionId = Math.floor(Math.random() * 100000);

      // Step 4: Check if client exists in database
      let clientNotFound = !clientId || !matterId;

      // Log the status for debugging
      this.logger.log(
        `🔍 CLIENT MATTER STATUS: clientId: ${clientId ? "✅ Found" : "❌ Not Found"}, matterId: ${matterId ? "✅ Found" : "❌ Not Found"}`
      );

      if (!clientId) {
        this.logger.warn(
          `⚠️  CLIENT NOT FOUND: No valid client was identified from the extracted data.`
        );
      }

      if (!matterId) {
        this.logger.warn(
          `⚠️  MATTER NOT FOUND: No valid matter was identified from the extracted data.`
        );
      }

      if (clientNotFound) {
        this.logger.warn(
          `🚫 EVENT CREATION BLOCKED: Client or matter missing - clientId: ${!!clientId}, matterId: ${!!matterId}`
        );
      } else {
        this.logger.log(
          `✅ EVENT CREATION ALLOWED: Both client and matter found and passed to workflow execution`
        );
      }

      // Step 5: Create workflow execution
      const startDate = new Date();
      const endDate = new Date(startDate.getTime() + 24 * 60 * 60 * 1000); // Add 1 day

      // Parse extracted data to get client name and case information
      const parsedData = this.parseExtractedDetails(extractedData);

      // Create a descriptive name for the workflow execution
      let workflowName = "Court Notice";
      if (parsedData.clientName) {
        workflowName = parsedData.clientName;
        if (parsedData.caseNumber) {
          workflowName += ` - ${parsedData.caseNumber}`;
        }
      } else if (parsedData.caseNumber) {
        workflowName = `Court Notice - ${parsedData.caseNumber}`;
      }

      // Create notes with information about found/not found client and matter
      let notes = "Auto-generated execution from court notice";
      if (!clientId && !matterId) {
        notes += " - Client and Matter not found in system";
      } else if (!clientId) {
        notes += " - Client not found in system";
      } else if (!matterId) {
        notes += " - Matter not found in system";
      }

      const workflowExecutionObj = new this.workflowExecutionModel({
        template_id: template._id,
        execution_id: executionId,
        start_date: startDate,
        end_date: endDate,
        status: Work_flow_execution_status.PENDING,
        last_activity: "1 day",
        assigns: [], // Use default user if client not found
        assign_by: "67f644975909b1b40b527650", // Let Mongoose handle ObjectId conversion
        run_by: this.buildRunByClientIds(clientId, allValidCombinations), // Populate with all client IDs to show all client names in workflow run
        notes: notes,
        is_manually_run: true,
        client_not_found: clientNotFound, // Set based on actual database lookup
        is_active: true,
        is_deleted: false,
      });

      const savedWorkflowExecution = await workflowExecutionObj.save();
      const workflowExecutionId = savedWorkflowExecution._id;

      // Step 5: Create task execution records
      await this.createTaskExecutions(
        template,
        workflowExecutionId,
        pendingState,
        failedState,
        startDate,
        endDate,
        clientId,
        matterId,
        extractedData,
        messageData
      );

      // Step 6: Create UserMetaData entry
      await this.createUserMetaData(
        workflowExecutionId,
        extractedData,
        messageData
      );

      this.logger.log(
        "Workflow execution created successfully",
        workflowExecutionId
      );
      return true; // Indicate success
    } catch (error) {
      this.logger.error("Error creating workflow execution:", error);
      return false; // Indicate failure
    }
  }

  private buildRunByClientIds(clientId: string | null, allValidCombinations: Array<{ clientId: string; matterId: string; clientName: string; caseNumber: string }>): string[] {
    // If we have multiple valid combinations, use all unique client IDs from them
    if (allValidCombinations.length > 0) {
      const uniqueClientIds = [...new Set(allValidCombinations.map(combination => combination.clientId))];
      this.logger.log(`📝 WORKFLOW RUN_BY: Adding ${uniqueClientIds.length} unique client IDs: [${uniqueClientIds.join(', ')}]`);
      return uniqueClientIds;
    }

    // Fall back to single client ID if no valid combinations but client exists
    if (clientId) {
      this.logger.log(`📝 WORKFLOW RUN_BY: Adding single client ID: ${clientId}`);
      return [clientId];
    }

    // No clients found
    this.logger.log(`📝 WORKFLOW RUN_BY: No clients to add`);
    return [];
  }

  private async findByFormComponent(id, formFieldid) {
    const details = await this.formComponentModel.findOne({
      form_field_id: formFieldid,
    });
    return details._id || id;
  }

  private determineEventSectionValue(
    extractedData: ExtractionAPIResponse | null,
    messageData: QueueMessageData | null
  ): string {
    // Check if all arrays are empty
    const isAiRpaEmpty =
      !extractedData?.extracted_details_rpa ||
      extractedData.extracted_details_rpa.length === 0;
    const isEmailBodyEmpty =
      !extractedData?.extracted_details_email_body ||
      extractedData.extracted_details_email_body.length === 0;
    const isAttachmentsEmpty =
      !extractedData?.extracted_details_attachment ||
      extractedData.extracted_details_attachment.length === 0;
    const isMessageAttachmentsEmpty =
      !messageData?.metadata?.attachments ||
      messageData.metadata.attachments.length === 0;
    const isMessageBodyEmpty =
      !messageData?.body || messageData.body.trim().length === 0;

    // If all arrays are empty, return "no", otherwise return "yes"
    if (
      isAiRpaEmpty &&
      isEmailBodyEmpty &&
      isAttachmentsEmpty &&
      isMessageAttachmentsEmpty &&
      isMessageBodyEmpty
    ) {
      return "no";
    }

    return "yes";
  }

  private async createTaskExecutions(
    template: any,
    workflowExecutionId: any,
    pendingState: any,
    failedState: any,
    startDate: Date,
    endDate: Date,
    clientId: string | null = null,
    matterId: string | null = null,
    extractedData: ExtractionAPIResponse | null = null,
    messageData: QueueMessageData | null = null
  ) {
    // Build derived field data if client and matter are found
    const derivedFieldData = await this.buildDerivedFieldData(
      clientId,
      matterId,
      extractedData,
      messageData
    );

    // ... existing code ...

    const instructionDetails = await this.findByFormComponent(
      "68076794e1af8cdccbe00c0e",
      "instruction_section"
    );
    const selectDetails = await this.findByFormComponent(
      "68076d77e1af8cdccbe00c16",
      "assign_workflow"
    );
    const radioButtonGroupDetails = await this.findByFormComponent(
      "6807762ae1af8cdccbe00c18",
      "event_section"
    );
    const checkboxDetails = await this.findByFormComponent(
      "680778a9e1af8cdccbe00c1e",
      "confirm-notice-dates"
    );
    const buttonDetails = await this.findByFormComponent(
      "680776cfe1af8cdccbe00c1a",
      "update-mycases"
    );
    const successMessageDetails = await this.findByFormComponent(
      "680777b0e1af8cdccbe00c1c",
      "success-message"
    );
    // const pendingMessageDetails = await this.findByFormComponent('6807762ae1af8cdccbe00c18','vehicle_insurance');

    // Pre-compute round-robin assignment for use in task definitions
    // const roundRobinAssignment = (await this.getNextRoundRobinAssignment(
    //   (template as any).templateGroupId?.toString() || '6877420fd4928f6a37ba1b95'
    // )) || {
    //   id: "",
    //   value: "System Default"
    // };

    const records = [
      {
        workflow_execution_id: workflowExecutionId,
        task_id: template.task_id[0]._id,
        start_date: startDate,
        end_date: endDate,
        task_status: "DRAFT",
        last_activity: startDate,
        assigns: [], // Let Mongoose handle ObjectId conversion
        assign_by: "67f644975909b1b40b527650", // Let Mongoose handle ObjectId conversion
        notes: "Task Execution Note 1",
        view_id: 1000,
        template_id: template._id,
        is_active: true,
        is_deleted: false,
        formField: [
          {
            id: "instruction-section-1",
            type: "instruction",
            label: "Review the court notice and follow the steps below.",
            required: false,
            sequence: 1,
            fields: [
              {
                form_component_id: instructionDetails, // Let Mongoose handle ObjectId conversion
                value: [{ id: "", value: "" }],
              },
            ],
          },
          {
            id: "assign-section",
            type: "assign",
            label: "Assign this workflow to",
            required: true,
            sequence: 2,
            fields: [
              {
                form_component_id: selectDetails, // Let Mongoose handle ObjectId conversion
                value: [
                  (await this.getNextRoundRobinAssignment(
                    (template as any).templateGroupId?.toString() ||
                      "6877420fd4928f6a37ba1b95"
                  )) || {
                    id: "",
                    value: "System Default",
                  },
                ],
              },
            ],
          },
          {
            id: "has-events-section",
            type: "radio_button_group",
            label:
              "Do you have any events in the calendar in this court notice?",
            sequence: 3,
            dynamic_fields: true,
            required: true,
            fields: [
              {
                form_component_id: radioButtonGroupDetails, // Let Mongoose handle ObjectId conversion
                value: [
                  {
                    id: "",
                    value: this.determineEventSectionValue(
                      extractedData,
                      messageData
                    ),
                  },
                ],
              },
            ],
          },
          {
            id: "confirm-dates-section",
            type: "checkbox",
            label: "Confirm that the date above are correct.",
            sequence: 10,
            required: true,
            new_field_add: true,
            condition: {
              field: radioButtonGroupDetails,
              value: "yes",
            },
            fields: [
              {
                form_component_id: checkboxDetails, // Let Mongoose handle ObjectId conversion
                value: [{ id: "", value: "" }],
              },
            ],
          },
        ],
        derived_field:
          derivedFieldData.length > 0
            ? derivedFieldData
            : [
                {
                  id: "court-notice-section",
                  type: "courtNotice",
                  label:
                    "Review the court notice and ensure all the dates are correct.",
                  sequence: 7,
                  matter_list_api:
                    "${process.env.NEXT_PUBLIC_BASE_URL}/workflow/matter-list}",
                  condition: {
                    field: "6807762ae1af8cdccbe00c18",
                    value: "yes",
                  },
                },
              ],
        __v: 377,
        createdAt: startDate,
        updatedAt: startDate,
        default_task: true,
        selected_task: false,
        sequence: 1,
        isReviewed: true,
        is_reviewed: true,
        task_visible_status: "IN_REVIEW",
      },
      {
        workflow_execution_id: workflowExecutionId,
        task_id: template.task_id[1]._id,
        start_date: startDate,
        end_date: endDate,
        task_status: "DRAFT",
        last_activity: startDate,
        assigns: [], // Let Mongoose handle ObjectId conversion
        assign_by: "67f644975909b1b40b527650", // Let Mongoose handle ObjectId conversion
        notes: "Task Execution Note 1",
        view_id: 1000,
        template_id: template._id,
        is_active: true,
        is_deleted: false,
        formField: [
          {
            id: "instruction-section-1",
            type: "instruction",
            label: "Review the court notice and follow the steps below.",
            required: false,
            sequence: 1,
            fields: [
              {
                form_component_id: instructionDetails, // Let Mongoose handle ObjectId conversion
                value: [{ id: "", value: "" }],
              },
            ],
          },
          {
            id: "assign-section",
            type: "assign",
            label: "Assign this workflow to",
            required: true,
            sequence: 2,
            fields: [
              {
                form_component_id: selectDetails, // Let Mongoose handle ObjectId conversion
                value: [
                  {
                    id: "",
                    value: "System Default",
                  },
                ],
              },
            ],
          },
          {
            id: "has-events-section",
            type: "radio_button_group",
            label:
              "Do you have any events in the calendar in this court notice?",
            dynamic_fields: true,
            required: true,
            sequence: 3,
            fields: [
              {
                form_component_id: radioButtonGroupDetails, // Let Mongoose handle ObjectId conversion
                value: [{ id: "", value: "yes" }],
              },
            ],
          },
          {
            id: "update-section",
            type: "button",
            label:
              "If the information above is correct, submit below to update MyCase",
            dynamic_fields: true,
            sequence: 8,
            new_field_add: true,
            required: true,
            condition: {
              field: radioButtonGroupDetails,
              value: "yes",
            },
            fields: [
              {
                form_component_id: buttonDetails, // Let Mongoose handle ObjectId conversion
                value: [{ id: "", value: "" }],
              },
            ],
          },
          {
            id: "pending-section",
            type: "pending_message",
            label:
              "Updating MyCase taking longer than usual. This may take up to 30 seconds. Please wait.",
            sequence: 9,
            new_field_add: true,
            condition: {
              field: buttonDetails,
              value: "pending",
            },
            fields: [
              {
                form_component_id: pendingState._id.toString(), // Convert to string
                value: [{ id: "", value: "" }],
              },
            ],
          },
          {
            id: "success-section",
            type: "success_message",
            label: "Dates are successfully submitted in MyCase.",
            sequence: 10,
            new_field_add: true,
            condition: {
              field: buttonDetails,
              value: "true",
            },
            fields: [
              {
                form_component_id: successMessageDetails, // Let Mongoose handle ObjectId conversion
                value: [{ id: "", value: "" }],
              },
            ],
          },
          {
            id: "confirm-dates-section",
            type: "checkbox",
            label:
              "Confirm that the court notice dates above and the court notice files are entered in MyCase appropriately, if not enter manually. Click here to access MyCase.",
            sequence: 11,
            new_field_add: true,
            condition: {
              field: buttonDetails,
              value: "true",
            },
            fields: [
              {
                form_component_id: checkboxDetails, // Let Mongoose handle ObjectId conversion
                value: [{ id: "", value: "" }],
              },
            ],
          },
          {
            id: "failed-section",
            type: "failed_message",
            label: "Failed to update MyCase.",
            sequence: 12,
            new_field_add: true,
            condition: {
              field: buttonDetails,
              value: "failed",
            },
            fields: [
              {
                form_component_id: failedState._id.toString(), // Convert to string
                value: [{ id: "", value: "" }],
              },
            ],
          },
          {
            id: "confirm-dates-section-failed",
            type: "checkbox",
            label:
              "Please update MyCase calendar and upload court notice file manually. Click here to access MyCase.",
            sequence: 13,
            new_field_add: true,
            condition: {
              field: buttonDetails,
              value: "failed",
            },
            fields: [
              {
                form_component_id: checkboxDetails, // Let Mongoose handle ObjectId conversion
                value: [{ id: "", value: "" }],
              },
            ],
          },
        ],
        derived_field:
          derivedFieldData.length > 0
            ? derivedFieldData
            : [
                {
                  id: "court-notice-section",
                  type: "courtNotice",
                  label:
                    "Please review the events below and update the missing information marked as TBD in calendar.",
                  sequence: 7,
                  matter_list_api:
                    "${process.env.NEXT_PUBLIC_BASE_URL}/workflow/matter-list}",
                  condition: {
                    field: radioButtonGroupDetails,
                    value: "yes",
                  },
                },
              ],
        __v: 229,
        createdAt: startDate,
        updatedAt: startDate,
        default_task: true,
        selected_task: false,
        sequence: 2,
        isReviewed: true,
        is_reviewed: true,
        task_visible_status: "DRAFT",
      },
    ];

    await this.taskExecutionModel.insertMany(records);
    this.logger.log("2 Task Execution records inserted.");
  }

  private async createManualReviewTaskExecution(
    template: any,
    workflowExecutionId: any,
    messageData: QueueMessageData,
    startDate: Date,
    endDate: Date
  ) {
    try {
      const record = {
        workflow_execution_id: workflowExecutionId,
        task_id: template.task_id[0]._id,
        start_date: startDate,
        end_date: endDate,
        task_status: "DRAFT",
        last_activity: startDate,
        assigns: [],
        assign_by: "67f644975909b1b40b527650",
        notes: `Manual Review Required - AI extraction failed for court notice from ${messageData.from}`,
        view_id: 1000,
        template_id: template._id,
        is_active: true,
        is_deleted: false,
        formField: [
          {
            id: "manual-review-instruction",
            type: "instruction",
            label: `Manual review required for court notice. AI extraction failed to find meaningful data. Please review the attachments manually and extract the required information.`,
            required: false,
            sequence: 1,
            fields: [
              {
                form_component_id: "68076794e1af8cdccbe00c0e",
                value: [{ id: "", value: "" }],
              },
            ],
          },
          {
            id: "attachment-info",
            type: "instruction",
            label: `Original email from: ${messageData.from}. Attachments: ${messageData.metadata.attachments?.map((a) => a.filename).join(", ") || "None"}`,
            required: false,
            sequence: 2,
            fields: [
              {
                form_component_id: "68076794e1af8cdccbe00c0e",
                value: [{ id: "", value: "" }],
              },
            ],
          },
          {
            id: "assign-section",
            type: "assign",
            label: "Assign this manual review to",
            required: true,
            sequence: 3,
            fields: [
              {
                form_component_id: "68076d77e1af8cdccbe00c16",
              },
            ],
          },
        ],
        derived_field: [],
        __v: 0,
        createdAt: startDate,
        updatedAt: startDate,
        default_task: true,
        selected_task: false,
        sequence: 1,
        isReviewed: false,
        is_reviewed: false,
        task_visible_status: "DRAFT",
      };

      await this.taskExecutionModel.create(record);
      this.logger.log("Manual review task execution created");
    } catch (error) {
      this.logger.error("Error creating manual review task execution:", error);
    }
  }

  private async createUserMetaData(
    workflowExecutionId: any,
    extractedData: ExtractionAPIResponse,
    messageData: QueueMessageData
  ): Promise<void> {
    const from = messageData.metadata.headers.from?.value || "";
    const to = messageData.metadata.headers.to?.value || "";

    const fromAddresses = from?.map((item) => item.address).join(", ");
    const toAddresses = to?.map((item) => item.address).join(", ");

    try {
      // Prepare email info object with all relevant email metadata
      const emailInfo = {
        messageId: messageData.messageId,
        uniqueHash: messageData.uniqueHash,
        uid: messageData.uid,
        seqno: messageData.seqno,
        tenantId: messageData.tenantId,
        processedAt: messageData.processedAt,
        receivedAt: messageData.receivedAt,
        extractedData: extractedData,
        attachmentCount: messageData.metadata?.attachments?.length || 0,
        attachments: messageData.metadata?.attachments || [],
      };

      // Prepare document array with attachments and extracted data
      const documents = [];

      // Add attachments as documents
      if (
        messageData.metadata?.attachments &&
        messageData.metadata.attachments.length > 0
      ) {
        messageData.metadata.attachments.forEach((attachment, index) => {
          documents.push({
            type: "attachment",
            index: index,
            filename: attachment.filename,
            contentType: attachment.contentType,
            size: attachment.size,
            s3Key: attachment.s3Key,
            s3Bucket: attachment.s3Bucket,
            s3Url: attachment.s3Url,
            checksum: attachment.checksum,
            attachmentId: attachment.attachmentId,
            storageType: attachment.storageType,
            uploadedAt: attachment.uploadedAt,
          });
        });
      }

      // Add extracted data as a document
      documents.push({
        type: "extracted_data",
        message_id: extractedData.message_id,
        extracted_details_attachment:
          extractedData.extracted_details_attachment,
        fields: extractedData.fields,
        processing_summary_email_attachments:
          extractedData.processing_summary_email_attachments,
        processing_summary_rpa: extractedData.processing_summary_rpa,
        processing_summary_email_body:
          extractedData.processing_summary_email_body,
      });

      const userMetaData = new this.userMetaDataModel({
        workflow_execution_id: workflowExecutionId,
        email_info: emailInfo,
        email_from: fromAddresses,
        email_to: toAddresses, // Using tenantId as email_to since we don't have actual recipient
        email_body: messageData.body,
        email_subject: messageData.subject,
        document: messageData.metadata.attachments,
      });

      const savedUserMetaData = await userMetaData.save();
      this.logger.log(
        `✅ UserMetaData entry created successfully: ${savedUserMetaData._id} for workflow execution: ${workflowExecutionId}`
      );
    } catch (error) {
      this.logger.error("❌ Error creating UserMetaData entry:", error);
      this.logger.error(
        "UserMetaData creation failed for workflow:",
        workflowExecutionId
      );
      // Don't throw error to avoid failing the entire workflow creation
    }
  }

  private async createUserMetaDataForManualReview(
    workflowExecutionId: any,
    messageData: QueueMessageData
  ): Promise<void> {
    try {
      // Prepare email info object for manual review
      const emailInfo = {
        messageId: messageData.messageId,
        uniqueHash: messageData.uniqueHash,
        uid: messageData.uid,
        seqno: messageData.seqno,
        tenantId: messageData.tenantId,
        processedAt: messageData.processedAt,
        receivedAt: messageData.receivedAt,
        extractionFailed: true,
        manualReviewRequired: true,
        attachmentCount: messageData.metadata?.attachments?.length || 0,
        attachments: messageData.metadata?.attachments || [],
      };

      // Prepare document array with attachments (no extracted data since extraction failed)
      const documents = [];

      // Add attachments as documents
      if (
        messageData.metadata?.attachments &&
        messageData.metadata.attachments.length > 0
      ) {
        messageData.metadata.attachments.forEach((attachment, index) => {
          documents.push({
            type: "attachment",
            index: index,
            filename: attachment.filename,
            contentType: attachment.contentType,
            size: attachment.size,
            s3Key: attachment.s3Key,
            s3Bucket: attachment.s3Bucket,
            s3Url: attachment.s3Url,
            checksum: attachment.checksum,
            attachmentId: attachment.attachmentId,
            storageType: attachment.storageType,
            uploadedAt: attachment.uploadedAt,
          });
        });
      }
      const from = messageData.metadata.headers.from?.value || "";
      const to = messageData.metadata.headers.to?.value || "";

      const fromAddresses = from?.map((item) => item.address).join(", ");
      const toAddresses = to?.map((item) => item.address).join(", ");
      // Add manual review note as a document
      documents.push({
        type: "manual_review_note",
        reason: "AI extraction failed to find meaningful data",
        requiresManualReview: true,
        createdAt: new Date().toISOString(),
      });

      const userMetaData = new this.userMetaDataModel({
        workflow_execution_id: workflowExecutionId,
        email_info: emailInfo,
        email_from: fromAddresses,
        email_to: toAddresses, // Using tenantId as email_to since we don't have actual recipient
        email_body: messageData.body,
        email_subject: messageData.subject,
        document: messageData.metadata?.attachments || [],
      });

      const savedUserMetaData = await userMetaData.save();
      this.logger.log(
        `✅ UserMetaData entry created for manual review workflow: ${savedUserMetaData._id} for workflow execution: ${workflowExecutionId}`
      );
    } catch (error) {
      this.logger.error(
        "❌ Error creating UserMetaData entry for manual redocumentsview:",
        error
      );
      this.logger.error(
        "UserMetaData creation failed for manual review workflow:",
        workflowExecutionId
      );
      // Don't throw error to avoid failing the entire workflow creation
    }
  }

  private async deleteMessage(receiptHandle: string) {
    try {
      const deleteCommand = new DeleteMessageCommand({
        QueueUrl: this.queueUrl,
        ReceiptHandle: receiptHandle,
      });

      await this.sqsClient.send(deleteCommand);
      this.logger.log("Message deleted from queue successfully");
    } catch (error) {
      this.logger.error("Error deleting message from queue:", error);
    }
  }

  async triggerPoll() {
    this.logger.log("Manual poll triggered");
    await this.pollQueue();
  }

  getStatus() {
    return {
      isRunning: this.isRunning,
      queueUrl: this.queueUrl,
    };
  }

  /**
   * Log processing failure and send notification to users
   */
  private async logProcessingFailure(
    messageData: QueueMessageData,
    failureType: string,
    errorMessage: string,
    errorDetails: any
  ): Promise<void> {
    try {
      // Create failure log entry
      const failureLog = new this.processingFailureLogModel({
        tenantId: messageData.tenantId,
        messageId: messageData.messageId,
        uniqueHash: messageData.uniqueHash,
        emailSubject: messageData.subject,
        emailFrom: messageData.from,
        failureType,
        errorMessage,
        errorDetails,
        attachments: messageData.metadata.attachments || [],
        notificationSent: false,
        retryCount: 0,
        isActive: true,
      });

      const savedLog = await failureLog.save();
      this.logger.log(`📋 Processing failure logged with ID: ${savedLog._id}`);

      // Get users to notify for this tenant
      const notificationRecipients = await this.getUserEmailsForNotification(
        messageData.tenantId
      );

      if (notificationRecipients.length > 0) {
        // Send notification email
        // await this.sendFailureNotification(savedLog, notificationRecipients, messageData);

        // Update log to mark notification as sent
        await this.processingFailureLogModel.updateOne(
          { _id: savedLog._id },
          {
            $set: {
              notificationSent: true,
              notificationRecipients,
              notificationSentAt: new Date(),
            },
          }
        );

        this.logger.log(
          `📧 Failure notification sent to ${notificationRecipients.length} user(s)`
        );
      } else {
        this.logger.warn(
          `⚠️ No active users found for tenant ${messageData.tenantId} to send notifications`
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to log processing failure: ${error.message}`,
        error
      );
    }
  }

  /**
   * Get user emails for notifications based on tenant
   */
  private async getUserEmailsForNotification(
    tenantId: string
  ): Promise<string[]> {
    try {
      // For now, get all active users - in production you might want to filter by role or specific notification preferences
      const users = await this.userModel
        .find({
          is_active: true,
          is_deleted: false,
        })
        .select("email first_name last_name")
        .lean();

      const emails = users
        .filter((user) => user.email && user.email.includes("@"))
        .map((user) => user.email);

      this.logger.log(`Found ${emails.length} user email(s) for notifications`);
      return emails;
    } catch (error) {
      this.logger.error(
        `Error getting user emails for notifications: ${error.message}`
      );
      return [];
    }
  }

  /**
   * Send failure notification email to users
   */
  private async sendFailureNotification(
    failureLog: ProcessingFailureLogDocument,
    recipients: string[],
    messageData: QueueMessageData
  ): Promise<void> {
    try {
      const attachmentCount = messageData.metadata.attachments?.length || 0;

      const htmlContent =
        this.emailNotificationService.generateFailureNotificationHtml(
          failureLog.emailSubject,
          failureLog.emailFrom,
          failureLog.errorMessage,
          attachmentCount
        );

      // await this.emailNotificationService.sendFailureNotification({
      //   recipients,
      //   subject: '🚨 Court Notice Processing Failure - Manual Action Required',
      //   htmlContent,
      // });

      this.logger.log(`✅ Failure notification email sent successfully`);
    } catch (error) {
      this.logger.error(
        `Failed to send failure notification email: ${error.message}`,
        error
      );
      throw error;
    }
  }

  // Legacy methods for backward compatibility (used by manual review workflow)
  private async findOrCreateClient(
    clientName: string,
    tenantId: string
  ): Promise<string> {
    // First try to find existing client
    const existingClientResult = await this.findExistingClient(
      clientName,
      tenantId
    );
    if (existingClientResult.clientId) {
      return existingClientResult.clientId;
    }

    // If not found, create new client (only for manual review cases)
    console.log(
      "🚀 ~ SqsConsumerService ~ findOrCreateClient ~ tenantId:",
      tenantId
    );
    console.log(
      "🚀 ~ SqsConsumerService ~ findOrCreateClient ~ clientName:",
      clientName
    );
    try {
      if (!clientName) {
        throw new Error("Client name is required");
      }

      // Clean up the client name
      const cleanName = clientName.replace(/[",]/g, "").trim();

      // Parse name into components
      const nameParts = cleanName.split(" ").filter((part) => part.length > 0);
      const firstName = nameParts[0] || "Unknown";
      const middleName =
        nameParts.length > 2 ? nameParts.slice(1, -1).join(" ") : "";
      const lastName =
        nameParts.length > 1 ? nameParts[nameParts.length - 1] : "Client";

      // Ensure we have valid first and last names (required fields)
      if (!firstName || firstName.trim() === "") {
        throw new Error("First name cannot be empty");
      }
      if (!lastName || lastName.trim() === "") {
        throw new Error("Last name cannot be empty");
      }

      // Generate a unique my_case_client_id to avoid duplicates
      const uniqueId = `auto_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

      // Create new client with proper validation including all required fields
      const clientData = {
        my_case_client_id: uniqueId,
        name: cleanName,
        first_name: firstName.trim(),
        middle_name: middleName.trim(),
        last_name: lastName.trim(),
        firm_uuid: tenantId,
        is_active: true,
        type: "client",
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      console.log("===========clientData=======", clientData);

      // Get collection reference outside try block for retry access
      const collection = this.clientModel.db.collection("client");

      try {
        // Direct MongoDB insertion to bypass BSON version conflicts
        const result = await collection.insertOne(clientData);

        this.logger.log(
          `Created new client with direct insertion: ${result.insertedId} - ${cleanName}`
        );
        return result.insertedId.toString();
      } catch (saveError) {
        this.logger.error("Error saving client to database:", saveError);

        // Log the specific validation errors
        if (saveError.name === "ValidationError") {
          this.logger.error("Validation errors:", saveError.errors);
        } else if (saveError.code === 11000) {
          this.logger.error("Duplicate key error:", saveError.message);
          // Try with a different unique ID
          const retryId = `auto_retry_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
          const retryClientData = { ...clientData, my_case_client_id: retryId };
          console.log("Retrying with new ID:", retryClientData);

          const retryResult = await collection.insertOne(retryClientData);
          this.logger.log(
            `Created new client on retry with direct insertion: ${retryResult.insertedId}`
          );
          return retryResult.insertedId.toString();
        }

        throw saveError;
      }
    } catch (error) {
      this.logger.error("Error finding/creating client:", error);
      this.logger.error("Error details:", {
        message: error.message,
        name: error.name,
        code: error.code,
        stack: error.stack,
      });
      throw error;
    }
  }

  private async buildDerivedFieldData(
    clientId: string | null,
    matterId: string | null,
    extractedData: ExtractionAPIResponse | null,
    messageData: QueueMessageData | null
  ): Promise<any[]> {
    console.log(
      "🚀 ~ SqsConsumerService ~ buildDerivedFieldData ~ matterId:",
      matterId
    );
    console.log(
      "🚀 ~ SqsConsumerService ~ buildDerivedFieldData ~ clientId:",
      clientId
    );
    try {
      // Always return the basic structure, but with empty client array if client/matter not found
      const basicStructure = {
        id: "court-notice-section",
        type: "courtNotice",
        label: "Review the court notice and ensure all the dates are correct.",
        sequence: 7,
        matter_list_api:
          "${process.env.NEXT_PUBLIC_BASE_URL}/workflow/matter-list}",
        condition: {
          field: "6807762ae1af8cdccbe00c18",
          value: "yes",
        },
        client: [],
      };

      // NEW APPROACH: Find all valid client-matter combinations
      if (!extractedData) {
        this.logger.log("❌ No extracted data available");
        return [basicStructure];
      }

      // Find all valid client-matter combinations
      const allClientMatterCombinations =
        await this.findAllValidClientMatterCombinations(
          extractedData,
          messageData?.tenantId || "1"
        );

      if (allClientMatterCombinations.length === 0) {
        this.logger.log("❌ NO VALID CLIENT-MATTER COMBINATIONS FOUND");
        return [basicStructure];
      }

      this.logger.log(
        `✅ FOUND ${allClientMatterCombinations.length} VALID CLIENT-MATTER COMBINATIONS`
      );

      // Build client entries for all valid combinations
      const allClientEntries = [];

      for (const combination of allClientMatterCombinations) {
        const {
          clientId: combClientId,
          matterId: combMatterId,
          clientName,
          caseNumber,
        } = combination;

        // Fetch client and matter details
        const client = await this.clientModel
          .findById(combClientId)
          .lean()
          .exec();
        const matter = await this.matterModel
          .findById(combMatterId)
          .lean()
          .exec();

        if (!client || !matter) {
          this.logger.warn(
            `❌ Skipping combination - Client or matter not found: ${clientName} - ${caseNumber}`
          );
          continue;
        }

        this.logger.log(
          `✅ Processing combination: ${clientName} - ${caseNumber}`
        );

        // Parse events for this specific client-matter combination
        const eventResults = await this.parseExtractedDetailsForSpecificClient(
          extractedData,
          clientName,
          caseNumber
        );
        const parsedEventData = eventResults.successfulEvents;

        // Build events for this client-matter combination
        const events = await this.buildEventsFromExtractedData(
          parsedEventData,
          client,
          matter,
          messageData?.tenantId || "1"
        );

        // Get attorney name from matter staff
        const attorneyName = this.getAttorneyNameFromMatter(matter);

        // Build client name
        const clientDisplayName =
          client.name ||
          `${client.first_name || ""} ${client.last_name || ""}`.trim();

        // Build matter display name
        const matterDisplayName =
          matter.name ||
          `${clientDisplayName} - ${matter.case_number || "Unknown Case"}`;

        // Create client entry for this combination
        const clientEntry = {
          client_name: `${clientDisplayName} | ${matterDisplayName}`,
          client_matter_id: matter._id,
          matter: [
            {
              _id: matterDisplayName,
              name: matterDisplayName,
              attorney: attorneyName,
              event: events,
              matter_id: matterDisplayName,
              ex_county_of_arrest: matter.ex_county_of_arrest || "",
              case_number: matter.case_number || "",
              my_case_matter_id: matter.my_case_matter_id || "",
            },
          ],
        };

        allClientEntries.push(clientEntry);

        this.logger.log(
          `✅ Built client entry for: ${clientDisplayName}, matter: ${matterDisplayName}, events: ${events.length}`
        );
      }

      // Build the complete derived field structure with all client entries
      const derivedFieldData = [
        {
          id: "court-notice-section",
          type: "courtNotice",
          label:
            "Review the court notice and ensure all the dates are correct.",
          sequence: 7,
          matter_list_api:
            "${process.env.NEXT_PUBLIC_BASE_URL}/workflow/matter-list}",
          condition: {
            field: "6807762ae1af8cdccbe00c18",
            value: "yes",
          },
          client: allClientEntries,
        },
      ];

      this.logger.log(
        `✅ SUCCESS: Built derived field data for ${allClientEntries.length} client-matter combinations`
      );

      return derivedFieldData;
    } catch (error) {
      this.logger.error("❌ ERROR in buildDerivedFieldData:", error);

      // Return basic structure on error
      return [
        {
          id: "court-notice-section",
          type: "courtNotice",
          label:
            "Review the court notice and ensure all the dates are correct.",
          sequence: 7,
          matter_list_api:
            "${process.env.NEXT_PUBLIC_BASE_URL}/workflow/matter-list}",
          condition: {
            field: "6807762ae1af8cdccbe00c18",
            value: "yes",
          },
          client: [],
        },
      ];
    }
  }

  // New method to find all valid client-matter combinations
  private async findAllValidClientMatterCombinations(
    extractedData: ExtractionAPIResponse,
    tenantId: string
  ): Promise<
    Array<{
      clientId: string;
      matterId: string;
      clientName: string;
      caseNumber: string;
    }>
  > {
    const validCombinations = [];
    const allRecords = this.mergeAllExtractedRecords(extractedData);

    // Group records by client name
    const recordsByClient = new Map<string, any[]>();

    for (const record of allRecords) {
      const clientName = record.client_name || record["Defendant Name"] || "";
      if (clientName) {
        if (!recordsByClient.has(clientName)) {
          recordsByClient.set(clientName, []);
        }
        recordsByClient.get(clientName).push(record);
      }
    }

    this.logger.log(
      `🔍 ANALYZING ${recordsByClient.size} UNIQUE CLIENTS for valid combinations`
    );

    // Check each unique client-matter combination
    for (const [clientName, clientRecords] of recordsByClient) {
      // Check if client exists in database
      const clientResult = await this.findExistingClient(clientName, tenantId);

      if (!clientResult.clientId) {
        this.logger.warn(
          `❌ CLIENT NOT FOUND: "${clientName}" - Skipping all records for this client`
        );
        continue;
      }

      this.logger.log(
        `✅ CLIENT FOUND: "${clientName}" (ID: ${clientResult.clientId})`
      );
      const clientId = clientResult.clientId;

      // Check each unique case number for this client
      const uniqueCaseNumbers = new Set<string>();
      for (const record of clientRecords) {
        const caseNumber = record.case_number || record["Case Number"] || "";
        if (caseNumber) {
          uniqueCaseNumbers.add(caseNumber);
        }
      }

      for (const caseNumber of uniqueCaseNumbers) {
        const matterResult = await this.findExistingMatter(
          caseNumber,
          clientId,
          clientResult.isFallback
        );

        if (!matterResult.matterId) {
          this.logger.warn(
            `❌ MATTER NOT FOUND: Case "${caseNumber}" for client "${clientName}"`
          );
          continue;
        }

        this.logger.log(
          `✅ MATTER FOUND: Case "${caseNumber}" (ID: ${matterResult.matterId}) for client "${clientName}"`
        );
        const matterId = matterResult.matterId;

        // Skip validation if either client or matter is fallback
        let isValidMatch = true;
        if (!clientResult.isFallback && !matterResult.isFallback) {
          isValidMatch = await this.validateClientMatterMatch(
            clientId,
            matterId,
            clientName,
            "", // matter name not critical for validation
            caseNumber,
            tenantId
          );
        } else {
          this.logger.log(
            `🔄 SKIPPING VALIDATION: Using fallback IDs (Client fallback: ${clientResult.isFallback}, Matter fallback: ${matterResult.isFallback})`
          );
        }

        if (isValidMatch) {
          validCombinations.push({
            clientId,
            matterId,
            clientName,
            caseNumber,
          });
          this.logger.log(
            `✅ VALID COMBINATION: "${clientName}" - "${caseNumber}"`
          );
        } else {
          this.logger.warn(
            `❌ INVALID COMBINATION: "${clientName}" - "${caseNumber}" - Validation failed`
          );
        }
      }
    }

    this.logger.log(
      `🎯 FOUND ${validCombinations.length} VALID CLIENT-MATTER COMBINATIONS`
    );
    return validCombinations;
  }

  // New method to parse events for a specific client-matter combination
  private async parseExtractedDetailsForSpecificClient(
    extractedData: ExtractionAPIResponse,
    targetClientName: string,
    targetCaseNumber: string
  ): Promise<{
    successfulEvents: any[];
    failedEvents: Array<{ record: any; reason: string; clientName: string }>;
  }> {
    const allRecords = this.mergeAllExtractedRecords(extractedData);
    const successfulEvents = [];
    const failedEvents = [];

    // Filter records for the specific client and case number
    const filteredRecords = allRecords.filter((record) => {
      const clientName = record.client_name || record["Defendant Name"] || "";
      const caseNumber = record.case_number || record["Case Number"] || "";

      return clientName === targetClientName && caseNumber === targetCaseNumber;
    });

    this.logger.log(
      `📋 Found ${filteredRecords.length} records for client "${targetClientName}" case "${targetCaseNumber}"`
    );

    // All filtered records are successful since we've already validated the client-matter combination
    for (const record of filteredRecords) {
      successfulEvents.push(record);
    }

    return { successfulEvents, failedEvents };
  }

  private getAttorneyNameFromMatter(matter: any): string {
    // Try to get attorney name from matter staff if available
    if (matter.staff && matter.staff.length > 0) {
      const attorney = matter.staff.find(
        (staff: any) =>
          staff.title &&
          (staff.title.toLowerCase().includes("attorney") ||
            staff.title.toLowerCase().includes("lawyer"))
      );
      if (attorney && attorney.name) {
        return attorney.name;
      }
    }

    // Fallback to empty string if no attorney found
    return "";
  }

  /**
   * Get next round-robin assignment for a specific role ID
   */
  private async getNextRoundRobinAssignment(
    roleId: string
  ): Promise<{ id: string; value: string } | null> {
    try {
      this.logger.log(`Getting round-robin assignment for role ID: ${roleId}`);
      return await this.roundRobinService.getNextRoundRobinAssignment(roleId);
    } catch (error) {
      this.logger.error(
        `Error getting round-robin assignment for role ${roleId}: ${error.message}`
      );
      return null;
    }
  }

  // Keep the original parseExtractedDetailsForEvents method for backwards compatibility
  private async parseExtractedDetailsForEvents(
    extractedData: ExtractionAPIResponse
  ): Promise<{
    successfulEvents: any[];
    failedEvents: Array<{ record: any; reason: string; clientName: string }>;
  }> {
    const allRecords = this.mergeAllExtractedRecords(extractedData);
    this.logger.log(
      `Starting to process ${allRecords.length} records for event creation`
    );

    const successfulEvents = [];
    const failedEvents = [];

    // Group records by client name
    const recordsByClient = new Map<string, any[]>();

    for (const record of allRecords) {
      const clientName = record.client_name || record["Defendant Name"] || "";
      if (clientName) {
        if (!recordsByClient.has(clientName)) {
          recordsByClient.set(clientName, []);
        }
        recordsByClient.get(clientName).push(record);
      }
    }

    this.logger.log(`Found ${recordsByClient.size} unique clients in records`);

    // Process each client separately
    for (const [clientName, clientRecords] of recordsByClient) {
      this.logger.log(
        `Processing ${clientRecords.length} records for client: "${clientName}"`
      );

      // Check if client exists in database
      const isValidClient = await this.isValidClientForEvents(clientName);

      if (!isValidClient) {
        this.logger.warn(
          `❌ CLIENT INVALID: "${clientName}" not found in database`
        );
        // Add all records for this invalid client to failed events
        for (const record of clientRecords) {
          failedEvents.push({
            record,
            reason: `Client "${clientName}" not found in database`,
            clientName,
          });
        }
        continue;
      }

      this.logger.log(`✅ CLIENT VALIDATED: "${clientName}" found in database`);

      // Process each record for this valid client
      for (let i = 0; i < clientRecords.length; i++) {
        const record = clientRecords[i];
        this.logger.log(
          `Processing record ${i + 1}/${clientRecords.length} for client "${clientName}": ${JSON.stringify(record)}`
        );

        // Find client and matter for this specific record
        const caseNumber = record.case_number || record["Case Number"] || "";

        try {
          // Find client in database
          const clientResult = await this.findExistingClient(
            clientName,
            extractedData.message_id.split("_")[0]
          );
          if (!clientResult.clientId) {
            this.logger.warn(
              `❌ CLIENT NOT FOUND: "${clientName}" not found in database for record ${i + 1}`
            );
            failedEvents.push({
              record,
              reason: `Client "${clientName}" not found in database`,
              clientName,
            });
            continue;
          }

          // Find matter for this client and case number
          const matterResult = await this.findExistingMatter(
            caseNumber,
            clientResult.clientId,
            clientResult.isFallback
          );
          if (!matterResult.matterId) {
            this.logger.warn(
              `❌ MATTER NOT FOUND: Case "${caseNumber}" not found for client "${clientName}"`
            );
            failedEvents.push({
              record,
              reason: `Matter with case number "${caseNumber}" not found for client "${clientName}"`,
              clientName,
            });
            continue;
          }

          this.logger.log(
            `✅ RECORD APPROVED: Client "${clientName}" (ID: ${clientResult.clientId}) and matter "${caseNumber}" (ID: ${matterResult.matterId}) found. Creating event for this record.`
          );
          successfulEvents.push(record);
        } catch (error) {
          this.logger.error(
            `❌ ERROR processing record for client "${clientName}": ${error.message}`
          );
          failedEvents.push({
            record,
            reason: `Error processing record: ${error.message}`,
            clientName,
          });
        }
      }
    }

    this.logger.log(
      `Event processing completed: ${successfulEvents.length} successful events, ${failedEvents.length} failed events from ${allRecords.length} total records`
    );

    if (failedEvents.length > 0) {
      this.logger.warn(`❌ FAILED EVENTS SUMMARY:`);
      failedEvents.forEach((failedEvent, index) => {
        this.logger.warn(
          `   ${index + 1}. Client: "${failedEvent.clientName}" - Reason: ${failedEvent.reason}`
        );
      });
    }

    return { successfulEvents, failedEvents };
  }

  // Helper method to validate if a client name exists in database
  private async isValidClientForEvents(clientName: string): Promise<boolean> {
    try {
      // Clean up the client name
      const cleanName = clientName.replace(/[",]/g, "").trim();

      if (!cleanName) {
        return false;
      }

      // Parse name formats: "JENKINS, PATRICK LEOTWAINE, Jr." -> firstName: "PATRICK", lastName: "JENKINS"
      let firstName = "";
      let lastName = "";

      // Handle formats like "Smith, John" or "Smith John"
      if (cleanName.includes(",")) {
        const parts = cleanName.split(",");
        lastName = parts[0].trim();
        firstName = parts[1] ? parts[1].trim().split(/\s+/)[0] : "";
      } else {
        const nameParts = cleanName.split(/\s+/);
        firstName = nameParts[0] || "";
        lastName = nameParts[nameParts.length - 1] || "";
      }

      // Check if client exists in database using the same logic as findExistingClient
      const client = await this.clientModel
        .findOne({
          is_active: true,
          $or: [
            // Simple name field match
            { name: { $regex: new RegExp(cleanName, "i") } },
            // First and Last name combination
            ...(firstName && lastName
              ? [
                  {
                    $and: [
                      {
                        first_name: {
                          $regex: new RegExp(`^${firstName}$`, "i"),
                        },
                      },
                      {
                        last_name: { $regex: new RegExp(`^${lastName}$`, "i") },
                      },
                    ],
                  },
                  {
                    $and: [
                      {
                        first_name: {
                          $regex: new RegExp(`^${lastName}$`, "i"),
                        },
                      },
                      {
                        last_name: {
                          $regex: new RegExp(`^${firstName}$`, "i"),
                        },
                      },
                    ],
                  },
                  {
                    name: {
                      $regex: new RegExp(`^${firstName}\\s+${lastName}$`, "i"),
                    },
                  },
                  {
                    name: {
                      $regex: new RegExp(`^${lastName},\\s*${firstName}`, "i"),
                    },
                  },
                ]
              : []),
            // Partial matching
            {
              name: {
                $regex: new RegExp(cleanName.split(/\s+/).join(".*"), "i"),
              },
            },
          ],
        })
        .lean()
        .exec();

      const exists = !!client;

      if (exists) {
        this.logger.log(
          `✅ CLIENT VALIDATED: "${cleanName}" found in database`
        );
      } else {
        this.logger.warn(
          `❌ CLIENT INVALID: "${cleanName}" not found in database`
        );
      }

      return exists;
    } catch (error) {
      this.logger.error(`Error validating client "${clientName}":`, error);
      return false; // Default to false on error to prevent creating invalid events
    }
  }

  private async buildEventsFromExtractedData(
    parsedEventData: any[],
    client: any,
    matter: any,
    tenantId: string
  ): Promise<any[]> {
    if (!parsedEventData || parsedEventData.length === 0) {
      this.logger.warn("No parsed event data provided for event creation");
      return [];
    }

    // Group events by client-matter combination
    const eventsByClientMatter = new Map<
      string,
      { client: any; matter: any; records: any[] }
    >();

    for (const record of parsedEventData) {
      const clientName = record.client_name || record["Defendant Name"] || "";
      const caseNumber = record.case_number || record["Case Number"] || "";
      const key = `${clientName}_${caseNumber}`;

      if (!eventsByClientMatter.has(key)) {
        // For now, we'll use the passed client/matter, but this should be updated
        // to fetch the correct client/matter for each record
        eventsByClientMatter.set(key, {
          client,
          matter,
          records: [],
        });
      }

      eventsByClientMatter.get(key).records.push(record);
    }

    const allEvents = [];

    for (const [
      key,
      { client: eventClient, matter: eventMatter, records },
    ] of eventsByClientMatter) {
      this.logger.log(
        `Building events for ${records.length} records. Client: ${eventClient?.firstName} ${eventClient?.lastName}, Matter: ${eventMatter?.matterName}`
      );

      for (let i = 0; i < records.length; i++) {
        const eventData = records[i];

        const courtNoticeAction = this.determineCourtNoticeAction(eventData);
        this.logger.log(
          `Court notice action: '${courtNoticeAction}' - eventType is ${eventData.eventType}`
        );

        if (courtNoticeAction === "Calendar event and save court notice") {
          const calendarEvent = await this.createCalendarEvent(
            eventData,
            eventClient,
            eventMatter,
            i,
            tenantId
          );
          if (calendarEvent) {
            allEvents.push(calendarEvent);
          }
        } else if (courtNoticeAction === "Deadline") {
          const deadlineEvent = await this.createDeadlineEvent(
            eventData,
            eventClient,
            eventMatter,
            i,
            tenantId
          );
          if (deadlineEvent) {
            allEvents.push(deadlineEvent);
          }
        } else {
          const genericEvent = await this.createEventFromData(
            eventData,
            eventClient,
            eventMatter,
            i,
            tenantId
          );
          if (genericEvent) {
            allEvents.push(genericEvent);
          }
        }
      }
    }

    this.logger.log(
      `✅ FINAL EVENT COUNT: Created ${allEvents.length} total events from ${parsedEventData.length} validated records`
    );
    return allEvents;
  }

  /**
   * Check if this is a reschedule appointment by finding existing synced events
   * that match the previous appointment details
   */
  private async checkForRescheduleAppointment(
    appointmentAction: any,
    caseNumber: string,
    tenantId: string
  ): Promise<{
    isReschedule: boolean;
    existingEventId?: string;
    appointmentToReschedule?: string;
  }> {
    try {
      // Only check for reschedule if appointmentAction type is 'reschedule'
      if (!appointmentAction || appointmentAction.type !== "reschedule") {
        return { isReschedule: false };
      }

      const {
        previousstartdate,
        previousenddate,
        previousstarttime,
        previousendtime,
      } = appointmentAction;

      // Validate that all previous appointment details are provided
      if (!previousstartdate || !previousstarttime) {
        this.logger.warn(
          `Reschedule appointment missing previous details for case ${caseNumber}. Previous details: startDate="${previousstartdate}", startTime="${previousstarttime}"`
        );

        // 🔧 FALLBACK: Try to use the new appointment details as search criteria
        // This handles cases where AI extraction didn't properly parse previous details
        this.logger.log(
          `🔧 Attempting fallback reschedule detection using new appointment details`
        );

        const { newstartdate, newenddate, newstarttime, newendtime } =
          appointmentAction;

        if (newstartdate && newstarttime) {
          this.logger.log(
            `🔧 Using new appointment details as fallback search criteria: ${newstartdate} ${newstarttime}-${newendtime}`
          );

          // Use new appointment details to search for ANY existing appointment for this case
          // The user can manually confirm which appointment to reschedule in the frontend
          return await this.findAnyExistingAppointmentForCase(caseNumber);
        }

        return { isReschedule: false };
      }

      // Convert previous date/time to match the format stored in the database
      const sanitizeDate = (dateValue: string): string => {
        if (!dateValue) return "";
        try {
          const date = new Date(dateValue);
          return date.toISOString().split("T")[0]; // Format as YYYY-MM-DD
        } catch (error) {
          this.logger.warn(`Invalid date format: ${dateValue}`);
          return dateValue;
        }
      };

      const sanitizeTime = (timeValue: string): string => {
        if (!timeValue) return "";
        // Handle various time formats (HH:mm, H:mm, etc.)
        const timeRegex = /^(\d{1,2}):(\d{2})$/;
        const match = timeValue.match(timeRegex);
        if (match) {
          const hours = match[1].padStart(2, "0");
          const minutes = match[2];
          return `${hours}:${minutes}`;
        }
        return timeValue;
      };

      const previousStartDate = sanitizeDate(previousstartdate);
      const previousEndDate = sanitizeDate(previousenddate);
      const previousStartTime = sanitizeTime(previousstarttime);
      const previousEndTime = sanitizeTime(previousendtime);

      this.logger.log(`Checking for existing appointment to reschedule:
        Case: ${caseNumber}
        Previous Start Date: ${previousStartDate}
        Previous End Date: ${previousEndDate} 
        Previous Start Time: ${previousStartTime}
        Previous End Time: ${previousEndTime}`);

      // Find existing synced events in mycase_client_matter collection
      const existingEvents = await this.mycaseClientMatterModel
        .find({
          "event.caseNumber": caseNumber,
          eventStatus: "Synced",
          is_active: true,
        })
        .exec();

      for (const eventRecord of existingEvents) {
        const event = eventRecord.event;

        // Check if dates and times match
        const eventStartDate = sanitizeDate(event.startDate);
        const eventEndDate = sanitizeDate(event.endDate);
        const eventStartTime = sanitizeTime(event.startTime);
        const eventEndTime = sanitizeTime(event.endTime);

        this.logger.log(`Comparing with existing event ${event.id}:
          Event Start Date: ${eventStartDate} vs Previous: ${previousStartDate}
          Event End Date: ${eventEndDate} vs Previous: ${previousEndDate}
          Event Start Time: ${eventStartTime} vs Previous: ${previousStartTime}
          Event End Time: ${eventEndTime} vs Previous: ${previousEndTime}`);

        if (
          eventStartDate === previousStartDate &&
          eventEndDate === previousEndDate &&
          eventStartTime === previousStartTime &&
          eventEndTime === previousEndTime
        ) {
          this.logger.log(
            `✅ Found matching appointment to reschedule: ${event.id}`
          );
          return {
            isReschedule: true,
            existingEventId: event.id,
            appointmentToReschedule: event.id,
          };
        }
      }

      this.logger.log(
        `❌ No matching previous appointment found for case ${caseNumber}`
      );
      return { isReschedule: false };
    } catch (error) {
      this.logger.error(
        `Error checking for reschedule appointment: ${error.message}`,
        error.stack
      );
      return { isReschedule: false };
    }
  }

  /**
   * Fallback method to find any existing appointment for a case when
   * previous appointment details are not properly extracted
   */
  private async findAnyExistingAppointmentForCase(caseNumber: string): Promise<{
    isReschedule: boolean;
    existingEventId?: string;
    appointmentToReschedule?: string;
  }> {
    try {
      this.logger.log(
        `🔧 Fallback: Searching for ANY existing synced appointment for case ${caseNumber}`
      );

      // Find any existing synced events for this case number
      const existingEvents = await this.mycaseClientMatterModel
        .find({
          "event.caseNumber": caseNumber,
          eventStatus: "Synced",
          is_active: true,
        })
        .exec();

      if (existingEvents.length > 0) {
        // Take the most recent event (or first one found)
        const latestEvent = existingEvents[0].event;

        this.logger.log(
          `🔧 Fallback: Found existing appointment for case ${caseNumber}: ${latestEvent.id}`
        );
        this.logger.log(
          `🔧 Existing appointment details: Date=${latestEvent.startDate}, Time=${latestEvent.startTime}-${latestEvent.endTime}`
        );

        return {
          isReschedule: true,
          existingEventId: latestEvent.id,
          appointmentToReschedule: latestEvent.id,
        };
      } else {
        this.logger.log(
          `🔧 Fallback: No existing appointments found for case ${caseNumber}`
        );
        return { isReschedule: false };
      }
    } catch (error) {
      this.logger.error(
        `🔧 Fallback: Error finding existing appointment for case ${caseNumber}: ${error.message}`
      );
      return { isReschedule: false };
    }
  }

  private createDefaultEvent(client: any, matter: any): any {
    const clientName =
      client.name ||
      `${client.first_name || ""} ${client.last_name || ""}`.trim();
    const matterDisplayName =
      matter.name || `${clientName} - ${matter.case_number || "Unknown Case"}`;
    const sanitizeTime = (timeValue: string): string => {
      if (!timeValue || typeof timeValue !== "string") {
        return "";
      }
      // Remove AM/PM and extra spaces, keep only the time numbers
      return timeValue.replace(/\s*(AM|PM)/gi, "").trim();
    };

    const eventFormData = {
      id: `evt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      caseNumber: matter.case_number || "",
      clientName: `${clientName} | ${matterDisplayName}`,
      description: `Court Notice; ${matterDisplayName} Client must appear`,
      date: "",
      startTime: "",
      endTime: "",
      isCompleted: false,
      subject: "",
      courtNoticeType: "",
      courtNoticeActions: "Save court notice only",
      appointmentAction: "New",
      eventStatus: "New",
      charge: matter.charge || "",
      county: matter.ex_county_of_arrest || "",
      courtLocation: "",
      optionalAttendees: "",
      requiredAttendees: "",
      clientAttendance: "Client must appear",
      meetingLocation: "",
      phoneDetails: "",
      meetingAddress: "",
      meetingLink: "",
      appointmentToReschedule: "",
      court_notice_date: "",
      startDate: "",
      endDate: "",
      isCancel: false,
      allDay: false,
      isAddSecondary: false,
      files: [],
      my_case_event_id: "",
    };

    // Generate subject using the helper function
    eventFormData.subject = this.generateSubject(eventFormData);

    return eventFormData;
  }

  /**
   * Determines the court notice action based on end time
   * @param eventData The event data to analyze
   * @returns The appropriate court notice action string
   */
  private determineCourtNoticeAction(eventData: any): string {
    const startTime = eventData.startTime;
    const endTime = eventData.endTime;
    const startDate = eventData.startDate;
    const endDate = eventData.endDate;
    const date = eventData.date;
    const eventType = eventData.deadlineornot;

    // Check if we have any date information
    const hasDateInfo =
      (date && date !== "N/A" && date.trim() !== "") ||
      (startDate && startDate !== "N/A" && startDate.trim() !== "") ||
      (endDate && endDate !== "N/A" && endDate.trim() !== "");

    // Check if we have time information
    const hasStartTime =
      startTime && startTime !== "N/A" && startTime.trim() !== "";
    const hasEndTime = endTime && endTime !== "N/A" && endTime.trim() !== "";

    // Check if we have both start and end time to calculate difference
    if (hasStartTime && hasEndTime && hasDateInfo) {
      if (eventType === "deadline") {
        this.logger.log(
          `Court notice action: 'Calendar DEADLINE and save court notice' - eventType is ${eventType}`
        );
        return "Calendar DEADLINE and save court notice";
      } else {
        this.logger.log(
          `Court notice action: 'Calendar event and save court notice' - eventType is ${eventType}`
        );
        return "Calendar event and save court notice";
      }

      // Default to calendar event if no clear time
    }
    this.logger.log(
      `Court notice action: 'Save court notice' - default for unclear time`
    );
    return "Save court notice only";
  }

  /**
   * Creates a court notice only event (no calendar scheduling)
   * Used only for automated SQS processing
   */
  private createCourtNoticeOnlyEvent(client: any, matter: any): any {
    const clientName =
      client.name ||
      `${client.first_name || ""} ${client.last_name || ""}`.trim();
    const matterDisplayName =
      matter.name || `${clientName} - ${matter.case_number || "Unknown Case"}`;

    return {
      id: `evt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      caseNumber: matter.case_number || "",
      clientName: "Save court notice only",
      description: "Save court notice only",
      date: "",
      startTime: "",
      endTime: "",
      isCompleted: false,
      subject: "Save court notice only",
      courtNoticeType: "",
      courtNoticeActions: "Save court notice only",
      appointmentAction: "New",
      eventStatus: "New",
      charge: matter.charge || "",
      county: matter.ex_county_of_arrest || "",
      courtLocation: "",
      optionalAttendees: "",
      requiredAttendees: "",
      clientAttendance: "Client must appear",
      meetingLocation: "",
      phoneDetails: "",
      meetingAddress: "",
      meetingLink: "",
      appointmentToReschedule: "",
      court_notice_date: "",
      startDate: "",
      endDate: "",
      isCancel: false,
      allDay: false,
      isAddSecondary: false,
      files: [],
      my_case_event_id: "",
    };
  }

  /**
   * Creates a calendar event (end time < 8)
   * Used only for automated SQS processing
   */
  private async createCalendarEvent(
    eventData: any,
    client: any,
    matter: any,
    index: number,
    tenantId?: string
  ): Promise<any> {
    const clientName =
      client.name ||
      `${client.first_name || ""} ${client.last_name || ""}`.trim();
    const matterDisplayName =
      matter.name || `${clientName} - ${matter.case_number || "Unknown Case"}`;

    const sanitizeTime = (timeValue: string): string => {
      if (!timeValue || timeValue.trim() === "" || timeValue === "N/A") {
        return "";
      }
      return timeValue.replace(/\s*(AM|PM)/gi, "").trim();
    };

    const sanitizeDate = (dateValue: string): string => {
      if (!dateValue || dateValue.trim() === "" || dateValue === "N/A") {
        return "";
      }
      return dateValue.trim();
    };

    // Derive attendees from client attendance and client name
    const normalizedAttendance = (eventData.clientAttendance || "")
      .trim()
      .toLowerCase();
    let requiredAttendeesDerived = "";
    let optionalAttendeesDerived = "";
    if (normalizedAttendance === "required") {
      requiredAttendeesDerived = clientName;
    } else if (normalizedAttendance === "tbd") {
      optionalAttendeesDerived = clientName;
    }

    let appointmentAction = "New";
    let appointmentToReschedule = "";

    // Debug logging for calendar event
    this.logger.log(
      `🔍 Debug - Calendar Event data: appointmentAction = ${JSON.stringify(eventData.appointmentAction)}`
    );

    if (eventData.appointmentAction && tenantId) {
      const rescheduleCheck = await this.checkForRescheduleAppointment(
        eventData.appointmentAction,
        eventData.caseNumber || matter.case_number || "",
        tenantId
      );

      if (rescheduleCheck.isReschedule) {
        appointmentAction = "Reschedule";
        appointmentToReschedule = rescheduleCheck.appointmentToReschedule || "";
        this.logger.log(
          `✅ Calendar Event - Setting appointmentAction to 'Reschedule' for existing event: ${rescheduleCheck.existingEventId}`
        );
      }
    }

    const eventFormData = {
      id: `evt-${Date.now()}-${index}-${Math.random().toString(36).substr(2, 9)}`,
      caseNumber: eventData.caseNumber || matter.case_number || "",
      clientName:
        eventData.clientName || `${clientName} | ${matterDisplayName}`,
      description: "",
      date: sanitizeDate(eventData.date),
      startTime: sanitizeTime(eventData.startTime),
      endTime: sanitizeTime(eventData.endTime),
      isCompleted: false,
      subject: "",
      courtNoticeType: eventData.courtNoticeType || "",
      courtNoticeActions: "Calendar event and save court notice",
      appointmentAction: appointmentAction,
      eventStatus: "New",
      charge: eventData.charge || "",
      county: matter.ex_county_of_arrest,
      courtLocation: eventData.courtLocation || "",
      optionalAttendees: optionalAttendeesDerived,
      requiredAttendees: requiredAttendeesDerived,
      clientAttendance: eventData.clientAttendance || "Client must appear",
      meetingLocation: eventData.meetingtype || "",
      phoneDetails: eventData.phoneDetails || "",
      meetingAddress: eventData.meetingAddress || "",
      meetingLink: eventData.meetingLink || "",
      appointmentToReschedule: appointmentToReschedule,
      court_notice_date: sanitizeDate(eventData.court_notice_date),
      startDate: sanitizeDate(eventData.startDate),
      endDate: sanitizeDate(eventData.endDate),
      isCancel: false,
      allDay: false,
      isAddSecondary: false,
      files: [],
      my_case_event_id: "",
    };

    // Generate subject using the helper function
    eventFormData.subject = this.generateSubject(eventFormData);

    return eventFormData;
  }

  /**
   * Creates a deadline event (end time >= 8)
   * Used only for automated SQS processing
   */
  private async createDeadlineEvent(
    eventData: any,
    client: any,
    matter: any,
    index: number,
    tenantId?: string
  ): Promise<any> {
    const clientName =
      client.name ||
      `${client.first_name || ""} ${client.last_name || ""}`.trim();
    const matterDisplayName =
      matter.name || `${clientName} - ${matter.case_number || "Unknown Case"}`;

    const sanitizeTime = (timeValue: string): string => {
      if (!timeValue || timeValue.trim() === "" || timeValue === "N/A") {
        return "";
      }
      return timeValue.replace(/\s*(AM|PM)/gi, "").trim();
    };

    const sanitizeDate = (dateValue: string): string => {
      if (!dateValue || dateValue.trim() === "" || dateValue === "N/A") {
        return "";
      }
      return dateValue.trim();
    };

    // Derive attendees from client attendance and client name
    const normalizedAttendance = (eventData.clientAttendance || "")
      .trim()
      .toLowerCase();
    let requiredAttendeesDerived = "";
    let optionalAttendeesDerived = "";
    if (normalizedAttendance === "required") {
      requiredAttendeesDerived = clientName;
    } else if (normalizedAttendance === "tbd") {
      optionalAttendeesDerived = clientName;
    }
    let appointmentAction = "New";
    let appointmentToReschedule = "";

    if (eventData.appointmentAction && tenantId) {
      const rescheduleCheck = await this.checkForRescheduleAppointment(
        eventData.appointmentAction,
        eventData.caseNumber || matter.case_number || "",
        tenantId
      );

      if (rescheduleCheck.isReschedule) {
        appointmentAction = "Reschedule";
        appointmentToReschedule = rescheduleCheck.appointmentToReschedule || "";
        this.logger.log(
          `✅ Deadline Event - Setting appointmentAction to 'Reschedule' for existing event: ${rescheduleCheck.existingEventId}`
        );
      }
    }

    const eventFormData = {
      id: `evt-${Date.now()}-${index}-${Math.random().toString(36).substr(2, 9)}`,
      caseNumber: eventData.caseNumber || matter.case_number || "",
      clientName:
        eventData.clientName || `${clientName} | ${matterDisplayName}`,
      description: "",
      date: sanitizeDate(eventData.date),
      startTime: sanitizeTime(eventData.startTime),
      endTime: sanitizeTime(eventData.endTime),
      isCompleted: false,
      subject: "",
      courtNoticeType: eventData.courtNoticeType || "",
      courtNoticeActions: "Calendar deadline and save court notice",
      appointmentAction: appointmentAction,
      eventStatus: "New",
      charge: eventData.charge || "",
      county: matter.ex_county_of_arrest,
      courtLocation: eventData.courtLocation || "",
      optionalAttendees: optionalAttendeesDerived,
      requiredAttendees: requiredAttendeesDerived,
      clientAttendance: eventData.clientAttendance || "Client must appear",
      meetingLocation: eventData.meetingtype || "",
      phoneDetails: eventData.phoneDetails || "",
      meetingAddress: eventData.meetingAddress || "",
      meetingLink: eventData.meetingLink || "",
      appointmentToReschedule: appointmentToReschedule,
      court_notice_date: sanitizeDate(eventData.court_notice_date),
      startDate: sanitizeDate(eventData.startDate),
      endDate: sanitizeDate(eventData.endDate),
      isCancel: false,
      allDay: true,
      isAddSecondary: false,
      files: [],
      my_case_event_id: "",
    };

    // Generate subject using the helper function
    eventFormData.subject = this.generateSubject(eventFormData);

    return eventFormData;
  }

  private async createEventFromData(
    eventData: any,
    client: any,
    matter: any,
    index: number,
    tenantId?: string
  ): Promise<any> {
    const clientName =
      client.name ||
      `${client.first_name || ""} ${client.last_name || ""}`.trim();
    const matterDisplayName =
      matter.name || `${clientName} - ${matter.case_number || "Unknown Case"}`;

    // Simple time sanitization function
    const sanitizeTime = (timeValue: string): string => {
      if (!timeValue || timeValue.trim() === "" || timeValue === "N/A") {
        return "";
      }
      // Remove AM/PM and extra spaces, keep only the time numbers
      return timeValue.replace(/\s*(AM|PM)/gi, "").trim();
    };

    // Simple date sanitization function
    const sanitizeDate = (dateValue: string): string => {
      if (!dateValue || dateValue.trim() === "" || dateValue === "N/A") {
        return "";
      }
      return dateValue.trim();
    };

    // Derive attendees from client attendance and client name
    const normalizedAttendance = (eventData.clientAttendance || "")
      .trim()
      .toLowerCase();
    let requiredAttendeesDerived = "";
    let optionalAttendeesDerived = "";
    if (normalizedAttendance === "required") {
      requiredAttendeesDerived = clientName;
    } else if (normalizedAttendance === "tbd") {
      optionalAttendeesDerived = clientName;
    }

    let appointmentAction = "New";
    let appointmentToReschedule = "";

    // Debug: Log the received eventData to see what appointmentAction data is available
    this.logger.log(
      `🔍 Debug - Event data received: ${JSON.stringify(eventData, null, 2)}`
    );

    if (eventData.appointmentAction) {
      this.logger.log(
        `🔍 Debug - appointmentAction found: ${JSON.stringify(eventData.appointmentAction, null, 2)}`
      );

      if (tenantId) {
        const rescheduleCheck = await this.checkForRescheduleAppointment(
          eventData.appointmentAction,
          eventData.caseNumber || matter.case_number || "",
          tenantId
        );

        if (rescheduleCheck.isReschedule) {
          appointmentAction = "Reschedule";
          appointmentToReschedule =
            rescheduleCheck.appointmentToReschedule || "";
          this.logger.log(
            `✅ Setting appointmentAction to 'Reschedule' for existing event: ${rescheduleCheck.existingEventId}`
          );
        }
      } else {
        this.logger.warn(
          `🔍 Debug - tenantId is missing, cannot check for reschedule`
        );
      }
    } else {
      this.logger.warn(
        `🔍 Debug - appointmentAction not found in eventData. Available fields: ${Object.keys(eventData).join(", ")}`
      );
    }

    const eventFormData = {
      id: `evt-${Date.now()}-${index}-${Math.random().toString(36).substr(2, 9)}`,
      caseNumber: eventData.caseNumber || matter.case_number || "",
      clientName:
        eventData.clientName || `${clientName} | ${matterDisplayName}`,
      description: "",
      date: sanitizeDate(eventData.date),
      startTime: sanitizeTime(eventData.startTime),
      endTime: sanitizeTime(eventData.endTime),
      isCompleted: false,
      subject: "",
      courtNoticeType: eventData.courtNoticeType || "",
      courtNoticeActions: this.determineCourtNoticeAction(eventData),
      appointmentAction: appointmentAction,
      eventStatus: "New",
      charge: eventData.charge || "",
      county: matter.ex_county_of_arrest || "",
      courtLocation: eventData.courtLocation || "",
      optionalAttendees: optionalAttendeesDerived,
      requiredAttendees: requiredAttendeesDerived,
      clientAttendance: eventData.clientAttendance || "",
      meetingLocation: eventData.meetingtype || "",
      phoneDetails: eventData.phoneDetails || "",
      meetingAddress: eventData.meetingAddress || "",
      meetingLink: eventData.meetingLink || "",
      appointmentToReschedule: appointmentToReschedule,
      court_notice_date: sanitizeDate(eventData.court_notice_date),
      startDate: sanitizeDate(eventData.startDate),
      endDate: sanitizeDate(eventData.endDate),
      isCancel: false,
      allDay:
        this.determineCourtNoticeAction(eventData) ===
        "Calendar DEADLINE and save court notice"
          ? true
          : false,
      isAddSecondary: false,
      files: [],
      my_case_event_id: "",
    };

    // Generate subject using the helper function
    eventFormData.subject = this.generateSubject(eventFormData);

    return eventFormData;
  }

  /**
   * Formats the charge and county part for subject generation
   */
  private formatChargeCountyPart(charge: string, county: string): string {
    const chargePart = charge?.trim() || '';
    const countyPart = county?.trim() || '';

    if (chargePart && countyPart) {
      return `${chargePart} - ${countyPart}`;
    } else if (chargePart) {
      return chargePart;
    } else if (countyPart) {
      return countyPart;
    }

    return '';
  }

  /**
   * Generates subject based on event form data
   */
  private generateSubject(formData: any): string {
    // Handle special cases first
    if (formData.courtNoticeActions === 'Save court notice only') {
      return 'Save court notice only';
    }

    // Generate client name part (first and last name)
    let clientNamePart = '';
    if (formData.clientName) {
      const nameParts = formData.clientName.split(' ');
      if (nameParts.length >= 2) {
        clientNamePart = `${nameParts[0]} ${nameParts[1]}`;
      } else if (nameParts.length === 1) {
        clientNamePart = nameParts[0];
      }
    }

    const chargeCountyPart = this.formatChargeCountyPart(formData.charge, formData.county);
    const caseNumberPart = formData.caseNumber?.trim() || '';
    const clientAttendancePart = formData.clientAttendance?.trim() || '';

    // Handle court notice type unless appointment is Cancel
    let courtNoticeTypePart = '';
    if (formData.appointmentAction !== 'Cancel') {
      courtNoticeTypePart = formData.courtNoticeType?.trim()
        ? `${formData.courtNoticeType.trim()};`
        : '';

      // Append 'DEADLINE' if needed
      if (formData.courtNoticeActions === 'Calendar DEADLINE and save court notice') {
        courtNoticeTypePart = courtNoticeTypePart ? `${courtNoticeTypePart} DEADLINE` : 'DEADLINE';
      }
    }

    const parts = [
      courtNoticeTypePart,
      clientNamePart,
      chargeCountyPart,
      caseNumberPart,
      clientAttendancePart,
    ].filter(part => part && part.trim() !== '');

    const subject = parts.join(' ').replace(/\s+/g, ' ').trim();

    return subject;
  }
}
