import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { RoundRobinState, RoundRobinStateDocument } from './entity/round-robin-state.entity';
import { User, UserDocument } from './entity/user.entity';
import { UserRole, UserRoleDocument } from './entity/user-role.entity';
import { Role, RoleDocument } from './entity/roles.entity';

export interface RoundRobinContext {
    context_type: 'role' | 'workflow' | 'global';
    context_id?: string;
    role_id?: string;
}

export interface RoundRobinAssignmentResult {
    assigned_user: {
        _id: string;
        name: string;
        email: string;
    };
    context: RoundRobinContext;
    assignment_count: number;
    next_user_index: number;
}

@Injectable()
export class RoundRobinService {
    private readonly logger = new Logger(RoundRobinService.name);

    constructor(
        @InjectModel(RoundRobinState.name) 
        private roundRobinStateModel: Model<RoundRobinStateDocument>,
        @InjectModel(User.name) 
        private userModel: Model<UserDocument>,
        @InjectModel(UserRole.name) 
        private userRoleModel: Model<UserRoleDocument>,
        @InjectModel(Role.name) 
        private roleModel: Model<RoleDocument>,
    ) {}

    /**
     * Get the next user in round robin for a given context
     */
    async getNextRoundRobinUser(context: RoundRobinContext): Promise<RoundRobinAssignmentResult | null> {
        try {
            this.logger.log(`Getting next round robin user for context: ${JSON.stringify(context)}`);

            // Get or create round robin state for this context
            let roundRobinState = await this.getRoundRobinState(context);

            if (!roundRobinState) {
                roundRobinState = await this.initializeRoundRobinState(context);
            }

            // Refresh user pool if needed (handles dynamic user changes)
            const currentUserPool = await this.getUserPoolForContext(context);
            
            if (currentUserPool.length === 0) {
                this.logger.warn(`No users available for round robin in context: ${JSON.stringify(context)}`);
                return null;
            }

            // Get next user using round robin logic
            const nextUserIndex = (roundRobinState.current_index + 1) % currentUserPool.length;
            const nextUserId = currentUserPool[nextUserIndex];

            console.log('🚀 ~ RoundRobinService ~ getNextRoundRobinUser ~ nextUserId:', nextUserId);

            // Get user details
            const user = await this.userModel.findById(nextUserId).exec();
            console.log('🚀 ~ RoundRobinService ~ getNextRoundRobinUser ~ user:', user);
            
            if (!user) {
                this.logger.error(`User not found: ${nextUserId}`);
                // Remove invalid user and try again
                await this.removeInvalidUserFromPool(roundRobinState, nextUserId);
                return this.getNextRoundRobinUser(context);
            }

            // Update round robin state
            await this.updateRoundRobinState(roundRobinState, nextUserId, nextUserIndex);

            const newUser = user.toObject();

            return {
                assigned_user: {
                    _id: user._id.toString(),
                    name: newUser.first_name + ' ' + newUser.last_name,
                    email: newUser.email,
                },
                context,
                assignment_count: roundRobinState.assignment_count + 1,
                next_user_index: nextUserIndex,
            };

        } catch (error) {
            this.logger.error(`Error in getNextRoundRobinUser: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Initialize round robin state for a new context
     */
    private async initializeRoundRobinState(context: RoundRobinContext): Promise<RoundRobinStateDocument> {
        try {
            const userPool = await this.getUserPoolForContext(context);
            
            if (userPool.length === 0) {
                throw new Error(`No users available for round robin in context: ${JSON.stringify(context)}`);
            }

            // Start with the last user in the pool so next assignment gets the first user
            const initialIndex = userPool.length - 1;
            const initialUser = userPool[initialIndex];

            const roundRobinState = new this.roundRobinStateModel({
                context_type: context.context_type,
                context_id: context.context_id || null,
                last_assigned_user: initialUser,
                assignment_count: 0,
                user_pool: userPool,
                current_index: initialIndex,
                last_assignment_date: new Date(),
                is_active: true,
                is_deleted: false,
            });

            await roundRobinState.save();
            this.logger.log(`Initialized round robin state for context: ${JSON.stringify(context)}`);
            
            return roundRobinState;
        } catch (error) {
            this.logger.error(`Error initializing round robin state: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get existing round robin state for a context
     */
    private async getRoundRobinState(context: RoundRobinContext): Promise<RoundRobinStateDocument | null> {
        const query: any = {
            context_type: context.context_type,
            is_active: true,
            is_deleted: false,
        };

        if (context.context_id) {
            query.context_id = context.context_id;
        }

        return this.roundRobinStateModel.findOne(query).exec();
    }

    /**
     * Get user pool for a given context
     */
    private async getUserPoolForContext(context: RoundRobinContext): Promise<Types.ObjectId[]> {
        try {
            let userIds: Types.ObjectId[] = [];

            switch (context.context_type) {
                case 'role':
                    if (context.role_id) {
                        userIds = await this.getUsersByRoleId(context.role_id);
                    }
                    break;
                
                case 'workflow':
                    // For workflow context, you might want to get users based on workflow permissions
                    // This can be extended based on your business logic
                    if (context.context_id) {
                        userIds = await this.getUsersByWorkflowAccess(context.context_id);
                    }
                    break;
                
                case 'global':
                    // Get all active users for global round robin
                    userIds = await this.getAllActiveUsers();
                    break;
                
                default:
                    throw new Error(`Unknown context type: ${context.context_type}`);
            }

            return userIds;
        } catch (error) {
            this.logger.error(`Error getting user pool for context: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get users by role ID (supports both numeric ID and MongoDB ObjectId)
     */
    private async getUsersByRoleId(roleId: string): Promise<Types.ObjectId[]> {
        try {
            let targetRoleId;
            
            // Check if roleId is a valid MongoDB ObjectId (24 hex characters)
            if (roleId.length === 24 && /^[0-9a-fA-F]{24}$/.test(roleId)) {
                // It's a MongoDB ObjectId, use it directly for userRole lookup
                targetRoleId = roleId;
                this.logger.log(`Using MongoDB ObjectId directly for role lookup: ${roleId}`);
            } else {
                // It's a numeric ID, find the role first to get its MongoDB _id
                const numericId = parseInt(roleId);
                if (isNaN(numericId)) {
                    this.logger.warn(`Invalid role ID format: ${roleId}`);
                    return [];
                }
                
                const role = await this.roleModel.findOne({ id: numericId }).exec();
                if (!role) {
                    this.logger.warn(`Role not found for numeric ID: ${roleId}`);
                    return [];
                }
                targetRoleId = role._id.toString();
                this.logger.log(`Found role ${role.name} with MongoDB _id: ${targetRoleId}`);
            }

            // Find users associated with this role using the MongoDB _id
            const userRoles = await this.userRoleModel
                .find({ roleId: targetRoleId, status: true })
                .populate('userId')
                .exec();

            this.logger.log(`Found ${userRoles.length} user-role relationships for role ${targetRoleId}`);

            // Debug: Log all users and their properties
            const allUsers = userRoles.map(userRole => userRole.userId as any).filter(user => user);
            this.logger.log(`All users found: ${JSON.stringify(allUsers.map(u => ({
                _id: u._id,
                name: `${u.first_name} ${u.last_name}`,
                is_active: u.is_active,
                is_deleted: u.is_deleted,
                my_case_staff_id: u.my_case_staff_id
            })))}`);

            // More lenient filtering - only check if user exists and is not deleted
            const users = userRoles
                .map(userRole => userRole.userId as any)
                .filter(user => {
                    if (!user) {
                        this.logger.warn('Found null/undefined user in userRole relationship');
                        return false;
                    }
                    
                    // Log why each user is being filtered out
                    if (user.is_deleted) {
                        this.logger.warn(`User ${user._id} (${user.first_name} ${user.last_name}) is deleted`);
                        return false;
                    }
                    
                    // Temporarily make is_active and my_case_staff_id optional for round-robin
                    if (!user.is_active) {
                        this.logger.warn(`User ${user._id} (${user.first_name} ${user.last_name}) is not active - including anyway`);
                    }
                    
                    if (!user.my_case_staff_id) {
                        this.logger.warn(`User ${user._id} (${user.first_name} ${user.last_name}) has no my_case_staff_id - including anyway`);
                    }
                    
                    return true;
                })
                .map(user => user._id);

            this.logger.log(`Found ${users.length} valid users for role ID ${roleId}`);
            return users;
        } catch (error) {
            this.logger.error(`Error getting users for role ID ${roleId}: ${error.message}`, error.stack);
            return [];
        }
    }

    /**
     * Get users by workflow access (can be extended based on requirements)
     */
    private async getUsersByWorkflowAccess(workflowId: string): Promise<Types.ObjectId[]> {
        // This is a placeholder - implement based on your workflow permission logic
        // For now, return all active users
        return this.getAllActiveUsers();
    }

    /**
     * Get all active users
     */
    private async getAllActiveUsers(): Promise<Types.ObjectId[]> {
        const users = await this.userModel
            .find({ 
                is_deleted: { $ne: true } // Only exclude explicitly deleted users
            })
            .select('_id first_name last_name is_active my_case_staff_id')
            .exec();

        this.logger.log(`Found ${users.length} total users for global round-robin`);
        return users.map(user => user._id);
    }

    /**
     * Update round robin state after assignment
     */
    private async updateRoundRobinState(
        roundRobinState: RoundRobinStateDocument,
        assignedUserId: Types.ObjectId,
        newIndex: number
    ): Promise<void> {
        roundRobinState.last_assigned_user = assignedUserId;
        roundRobinState.current_index = newIndex;
        roundRobinState.assignment_count += 1;
        roundRobinState.last_assignment_date = new Date();
        
        await roundRobinState.save();
        
        this.logger.log(`Updated round robin state: user ${assignedUserId}, index ${newIndex}, count ${roundRobinState.assignment_count}`);
    }

    /**
     * Remove invalid user from pool
     */
    private async removeInvalidUserFromPool(
        roundRobinState: RoundRobinStateDocument,
        invalidUserId: Types.ObjectId
    ): Promise<void> {
        roundRobinState.user_pool = roundRobinState.user_pool.filter(
            id => id.toString() !== invalidUserId.toString()
        );
        
        // Adjust current index if necessary
        if (roundRobinState.current_index >= roundRobinState.user_pool.length) {
            roundRobinState.current_index = 0;
        }
        
        await roundRobinState.save();
        this.logger.log(`Removed invalid user ${invalidUserId} from round robin pool`);
    }

    /**
     * Get round robin assignment for a specific role ID (helper method)
     */
    async getNextRoundRobinAssignment(roleId: string): Promise<{ id: string; value: string } | null> {
        try {
            this.logger.log(`Starting round robin assignment for role: ${roleId}`);
            
            const context: RoundRobinContext = {
                context_type: 'role',
                context_id: undefined,
                role_id: roleId,
            };

            const roundRobinResult = await this.getNextRoundRobinUser(context);
            
            if (!roundRobinResult) {
                this.logger.warn(`No users available for round robin assignment in role: ${roleId}`);
                
                // Fallback: Try to get any user from the role, ignoring round-robin state
                const fallbackUser = await this.getFallbackUserForRole(roleId);
                if (fallbackUser) {
                    this.logger.log(`Using fallback user: ${fallbackUser.value}`);
                    return fallbackUser;
                }
                
                return null;
            }

            // Convert MongoDB _id to numeric user ID for form value
            const user = await this.userModel.findById(roundRobinResult.assigned_user._id);
            if (!user) {
                this.logger.error(`User not found for assignment: ${roundRobinResult.assigned_user._id}`);
                return null;
            }

            console.log('🚀 ~ RoundRobinService ~ getNextRoundRobinAssignment ~ user:', roundRobinResult);

            const result = {
                id: user.id?.toString() || roundRobinResult.assigned_user._id,
                value: roundRobinResult.assigned_user.name
            };
            
            this.logger.log(`Successfully assigned user via round-robin: ${JSON.stringify(result)}`);
            return result;
            
        } catch (error) {
            this.logger.error(`Error getting round robin assignment: ${error.message}`, error.stack);
            
            // Final fallback: Try to get any user from the role
            const fallbackUser = await this.getFallbackUserForRole(roleId);
            if (fallbackUser) {
                this.logger.log(`Using fallback user after error: ${fallbackUser.value}`);
                return fallbackUser;
            }
            
            return null;
        }
    }

    /**
     * Fallback method to get any available user for a role
     */
    private async getFallbackUserForRole(roleId: string): Promise<{ id: string; value: string } | null> {
        try {
            this.logger.log(`Attempting fallback user lookup for role: ${roleId}`);
            
            // Get users directly without round-robin logic
            const userIds = await this.getUsersByRoleId(roleId);
            
            if (userIds.length === 0) {
                this.logger.warn(`No users found for fallback assignment in role: ${roleId}`);
                return null;
            }

            // Get the first available user
            const user = await this.userModel.findById(userIds[0]);
            if (!user) {
                this.logger.error(`Fallback user not found: ${userIds[0]}`);
                return null;
            }

            return {
                id: user.id?.toString() || user._id.toString(),
                value: `${user.first_name} ${user.last_name}`
            };
            
        } catch (error) {
            this.logger.error(`Error in fallback user lookup: ${error.message}`);
            return null;
        }
    }
}
