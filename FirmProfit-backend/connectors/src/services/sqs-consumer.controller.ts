import { Controller, Get, Post } from '@nestjs/common';
import { SqsConsumerService } from './sqs-consumer.service';

@Controller('sqs')
export class SqsConsumerController {
  constructor(private readonly sqsConsumerService: SqsConsumerService) {}

  @Get('status')
  getStatus() {
    return {
      message: 'SQS Consumer Status',
      status: this.sqsConsumerService.getStatus(),
      timestamp: new Date().toISOString(),
    };
  }

  @Post('trigger-poll')
  async triggerPoll() {
    await this.sqsConsumerService.triggerPoll();
    return {
      message: 'Manual poll triggered',
      timestamp: new Date().toISOString(),
    };
  }
} 