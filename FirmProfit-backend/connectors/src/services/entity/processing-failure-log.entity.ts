import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type ProcessingFailureLogDocument = ProcessingFailureLog & Document;

@Schema({ timestamps: true, collection: 'processing_failure_logs' })
export class ProcessingFailureLog {
  @Prop({ required: true })
  tenantId: string;

  @Prop({ required: true })
  messageId: string;

  @Prop({ required: true })
  uniqueHash: string;

  @Prop({ required: true })
  emailSubject: string;

  @Prop({ required: true })
  emailFrom: string;

  @Prop({ required: true })
  failureType: string; // 'API_FAILURE', 'PARSING_FAILURE', 'WORKFLOW_CREATION_FAILURE'

  @Prop({ required: true })
  errorMessage: string;

  @Prop({ type: MongooseSchema.Types.Mixed })
  errorDetails: any;

  @Prop({ type: MongooseSchema.Types.Mixed })
  attachments: any[];

  @Prop({ default: false })
  notificationSent: boolean;

  @Prop({ type: [String], default: [] })
  notificationRecipients: string[];

  @Prop({ type: Date })
  notificationSentAt: Date;

  @Prop({ default: 0 })
  retryCount: number;

  @Prop({ type: Date })
  lastRetryAt: Date;

  @Prop({ default: true })
  isActive: boolean;
}

export const ProcessingFailureLogSchema = SchemaFactory.createForClass(ProcessingFailureLog); 