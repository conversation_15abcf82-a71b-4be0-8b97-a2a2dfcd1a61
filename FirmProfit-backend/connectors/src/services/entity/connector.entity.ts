// schemas/connector.schema.ts

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { Authentication_detail } from '../interface/common.interface';

export type ConnectorDocument = HydratedDocument<Connector>;

@Schema({ timestamps: true, collection: 'connector' })
export class Connector {
  @Prop({ required: true })
  name: string;

  @Prop()
  logo: string;

  @Prop({
    type: [
      {
        key: { type: String, required: true },
        value: { type: String, required: true },
      },
    ],
    default: [],
    _id: false,
  })
  authentication_details: Authentication_detail[];

  @Prop()
  apiUrl: string;

  @Prop()
  description: string;

  @Prop({ type: Number, required: true })
  type: number;

  @Prop({ type: Boolean, default: true })
  is_active: boolean;
}

export const ConnectorSchema = SchemaFactory.createForClass(Connector);
