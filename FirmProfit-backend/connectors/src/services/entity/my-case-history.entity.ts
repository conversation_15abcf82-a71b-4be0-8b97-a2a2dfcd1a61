/* eslint-disable @typescript-eslint/no-unused-vars */
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Schema as MongooseSchema, Types } from 'mongoose';
import { SchemaTypes } from 'mongoose';

export type MyCaseHistoryDocument = HydratedDocument<MyCaseHistory>;

@Schema({
  timestamps: true,
  collection: 'mycase_history',
  // Allow additional fields that might be added in the future
  strict: false,
})
export class MyCaseHistory {
  @Prop({ required: true, type: String })
  firm_uuid: string;

  @Prop({ required: true, type: String })
  action: string;

  @Prop({ type: SchemaTypes.Mixed, required: false })
  response: Record<string, any>; // Accepts any JSON object

  @Prop({ required: true, type: String })
  resource: string;

  @Prop({
    required: true,
    type: String,
    // Store as JSON string to preserve nested objects and arrays
    // This prevents data loss during gRPC transmission
    // Example: "{\"id\":338308378,\"staff\":[{\"id\":61054170}]}"
  })
  resource_body: string;

  @Prop({ required: true, type: String })
  webhook_timestamp: string;

  @Prop({ default: true, type: Boolean })
  is_active: boolean;

  // Automatically added by timestamps: true
  createdAt?: Date;
  updatedAt?: Date;
}

export const MyCaseHistorySchema = SchemaFactory.createForClass(MyCaseHistory);

// Add indexes for better query performance
MyCaseHistorySchema.index({ firm_uuid: 1, action: 1 });
MyCaseHistorySchema.index({ resource: 1, is_active: 1 });
MyCaseHistorySchema.index({ webhook_timestamp: 1 });
MyCaseHistorySchema.index({ createdAt: -1 });

// Add a compound index for common queries
MyCaseHistorySchema.index({
  firm_uuid: 1,
  resource: 1,
  is_active: 1,
  createdAt: -1,
});
