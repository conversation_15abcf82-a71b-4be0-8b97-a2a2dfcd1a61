import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Schema as MongooseSchema } from 'mongoose';

export type UserDocument = HydratedDocument<User>;

@Schema({ timestamps: true, collection: 'users' }) // explicitly set collection name
export class User {
  @Prop({ type: Number })
  id: number;

  @Prop({ type: String, required: true })
  email: string;

  @Prop({ type: String, required: true })
  password: string;

  @Prop({ type: String, required: true })
  first_name: string;

  @Prop({ type: String, required: true })
  last_name: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Role', required: true })
  roleId: string;

  @Prop({ type: Boolean, default: false })
  is_active: boolean;

  @Prop({ type: Boolean, default: false })
  is_deleted: boolean;

  @Prop({ type: Boolean, default: false })
  mfa_enabled: boolean;

  @Prop({ type: String, default: null })
  token: string | null;

  @Prop({ type: Number, default: 0 })
  login_attempt_count: number;

  @Prop({ type: Number, default: 0 })
  login_attempt_mfa_count: number;

  @Prop({ type: String, required: true })
  my_case_staff_id?: string;

  @Prop({ type: Date, default: null })
  last_incorrect_login: Date | null;

  @Prop({ type: Date, default: null })
  last_incorrect_mfa_login: Date | null;
}

export const UserSchema = SchemaFactory.createForClass(User);
