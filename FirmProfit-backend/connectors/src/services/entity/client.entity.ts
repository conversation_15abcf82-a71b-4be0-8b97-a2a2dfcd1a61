import { <PERSON>p, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type ClientDocument = HydratedDocument<Client>;

@Schema({ _id: false })
export class Address {
  @Prop({ required: false })
  address1?: string;

  @Prop({ required: false })
  address2?: string;

  @Prop({ required: false })
  city?: string;

  @Prop({ required: false })
  state?: string;

  @Prop({ required: false })
  country?: string;

  @Prop({ required: false })
  zip_code?: string;
}

export const AddressSchema = SchemaFactory.createForClass(Address);

@Schema({ _id: false })
export class PeopleGroup {
  @Prop({ required: false })
  id?: number;
}

export const PeopleGroupSchema = SchemaFactory.createForClass(PeopleGroup);

@Schema({ timestamps: true, collection: 'client' })
export class Client {
  @Prop({ required: false })
  my_case_client_id: string;

  @Prop({ required: false })
  name?: string;

  @Prop({ required: true })
  first_name: string;

  @Prop({ required: false })
  middle_initial?: string;

  @Prop({ required: false })
  middle_name?: string;

  @Prop({ required: true })
  last_name: string;

  @Prop({ required: false })
  cell_phone_number?: string;

  @Prop({ required: false })
  work_phone_number?: string;

  @Prop({ required: false })
  home_phone_number?: string;

  @Prop({ type: AddressSchema, required: false })
  address?: Address;

  @Prop({ required: false })
  notes?: string;

  @Prop({ required: false })
  birthdate?: string;

  @Prop({ default: false })
  archived: boolean;

  @Prop({ type: PeopleGroupSchema, required: false })
  people_group?: PeopleGroup;

  @Prop({ default: true })
  is_active: boolean;

  @Prop({ required: false })
  email: string;

  @Prop({ default: 'client' })
  type: string;

  @Prop({ required: false })
  firm_uuid?: string;

  // Timestamps from MyCase
  @Prop({ required: false })
  my_case_created_at?: string;

  @Prop({ required: false })
  my_case_updated_at?: string;
}

export const ClientSchema = SchemaFactory.createForClass(Client);

