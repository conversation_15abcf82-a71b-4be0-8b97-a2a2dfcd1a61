import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Schema as MongooseSchema, Types } from 'mongoose';
import { EventAction, EventStatus } from '../enum/template-status.enum';

export type EventActionHistoryDocument = HydratedDocument<EventActionHistory>;

@Schema({ timestamps: true, collection: 'event_action_history' })
export class EventActionHistory {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Matter', required: false })
  client_matter_id?: Types.ObjectId;

  @Prop({ enum: EventStatus, default: EventStatus.NEW })
  eventStatus?: EventStatus;

  @Prop({ type: String })
  event_id?: string;

  @Prop({ enum: EventAction })
  action?: EventAction;

  //   @Prop({ type: String })
  // action?: string;

  @Prop({ type: String })
  my_case_event_id?: string;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'work_flow_execution',
    required: true,
  })
  workflow_execution_id: Types.ObjectId;
}

export const EventActionHistorySchema =
  SchemaFactory.createForClass(EventActionHistory);
