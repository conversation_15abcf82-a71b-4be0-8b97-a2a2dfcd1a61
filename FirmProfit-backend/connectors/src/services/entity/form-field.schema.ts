import { Schema as MongooseSchema } from 'mongoose';

export const FormFieldSchema = new MongooseSchema(
  {
    id: { type: String },
    type: { type: String },
    label: { type: String, required: false },
    required: { type: Boolean },
    dynamic_fields: { type: Boolean },
    sequence: { type: Number },
    dynamic_selected_task: { type: Boolean },
    new_field_add: { type: Boolean },
    condition: {
      type: MongooseSchema.Types.Mixed,
    },
    fields: [
      {
        form_component_id: {
          type: MongooseSchema.Types.ObjectId,
          ref: 'form_component',
        },
        value: {
          type: [
            {
              id: { type: String, default: null },
              value: { type: String },
            },
          ],
          default: [],
        },
      },
    ],
  },
  { _id: false },
);
