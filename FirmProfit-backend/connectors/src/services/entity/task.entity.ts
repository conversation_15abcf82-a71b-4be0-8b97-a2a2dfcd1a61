import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>hemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Schema as MongooseSchema, Types } from 'mongoose';
import { Task_type } from '../enum/template-status.enum';
import { IFormField } from '../interface/common.interface';
import { Derived<PERSON>ield, DerivedFieldSchema } from './derived.field.entity';

export type TaskDocument = HydratedDocument<Task>;

const ConditionSchema = new MongooseSchema(
  {
    type: {
      type: String,
      enum: ['AND', 'OR'],
      required: false,
    },
    field: {
      type: String,
      required: function () {
        return !this.type;
      },
    },
    value: {
      type: MongooseSchema.Types.Mixed,
      required: function () {
        return !this.type;
      },
    },
    conditions: {
      type: [MongooseSchema.Types.Mixed],
      validate: {
        validator: function (v) {
          // If this is a complex condition (has type), conditions must be an array
          return !this.type || (Array.isArray(v) && v.length > 0);
        },
        message: 'Conditions must be an array for complex conditions',
      },
    },
  },
  { _id: false },
);

const ConditionTaskSchema = new MongooseSchema(
  {
    value: { type: MongooseSchema.Types.Mixed, required: false },
    id: { type: String, required: false },
  },
  { _id: false },
);

// Define the form field schema explicitly
const FormFieldSchema = new MongooseSchema(
  {
    id: { type: String, required: true },
    type: { type: String, required: true },
    label: { type: String, required: false },
    sequence: { type: Number, required: true },
    dynamic_fields: { type: Boolean, default: false },
    api_end_point: { type: String, required: false },
    dynamic_selected_task: { type: Boolean, default: false },
    fields: [{ type: Types.ObjectId, ref: 'FormComponent' }],
    condition: { type: ConditionSchema, required: false },
  },
  { _id: false },
);

@Schema({ timestamps: true, collection: 'task' })
export class Task {
  @Prop({ required: true, unique: true })
  task_id: number;

  @Prop({ required: true })
  name: string;

  @Prop({ required: false })
  description: string;

  @Prop({ required: false, default: '' })
  icon: string | null;

  @Prop({
    type: String,
    enum: Task_type,
    required: true,
  })
  type: Task_type;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'View',
    required: false,
  })
  view_id: Types.ObjectId;

  @Prop({ required: true })
  previous_node: string;

  @Prop({ required: true })
  next_node: string;

  @Prop({ default: true })
  is_active: boolean;

  @Prop({ default: false })
  is_deleted: boolean;

  @Prop({ type: Boolean, default: false })
  default_task: boolean;

  @Prop({ type: Boolean, default: false })
  selected_task: boolean;

  @Prop({ type: ConditionTaskSchema, required: false })
  condition_task: {
    value: any;
    id: string;
  };

  // Use the explicit FormFieldSchema instead of inline definition
  @Prop({ type: [FormFieldSchema], default: [] })
  formField: IFormField[];

  @Prop({ type: [DerivedFieldSchema], default: [] })
  derived_field: DerivedField[];
}

export const TaskSchema = SchemaFactory.createForClass(Task);
