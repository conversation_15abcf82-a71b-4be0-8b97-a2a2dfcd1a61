import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Schema as MongooseSchema, Types } from 'mongoose';

export type UserMetaDataDocument = HydratedDocument<UserMetaData>;

@Schema({ timestamps: true, collection: 'user_meta_data' })
export class UserMetaData {
 @Prop({
     type: MongooseSchema.Types.ObjectId,
     ref: 'work_flow_execution',
     required: true,
   })
   workflow_execution_id: Types.ObjectId;

  @Prop({ required: true })
  email_info: MongooseSchema.Types.Mixed; // This can store a JSON object

  @Prop({ required: true })
  email_from: string;

  @Prop({ required: true })
  email_to: string;

  @Prop({ required: true })
  email_body: MongooseSchema.Types.Mixed; // This can store a JSON object

  @Prop({ required: true })
  email_subject: string;

  @Prop({ type: [MongooseSchema.Types.Mixed], required: true }) // Array to hold documents
  document: MongooseSchema.Types.Array;
}

export const UserMetaDataSchema = SchemaFactory.createForClass(UserMetaData);
