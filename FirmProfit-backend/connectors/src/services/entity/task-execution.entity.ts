// schemas/task-execution.schema.ts
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Schema as MongooseSchema, Types } from 'mongoose';
import { Task_status, TASK_VISIBLE_STATUS } from '../enum/template-status.enum';
import { IFormField } from '../interface/common.interface';
import { DerivedField, DerivedFieldSchema } from './derived.field.entity';
import { FormFieldSchema } from './form-field.schema';

export type TaskExecutionDocument = HydratedDocument<TaskExecution>;

const ConditionSchema = new MongooseSchema(
  {
    type: {
      type: String,
      enum: ['AND', 'OR'],
      required: false,
    },
    field: {
      type: String,
      required: function () {
        return !this.type;
      },
    },
    value: {
      type: MongooseSchema.Types.Mixed,
      required: function () {
        return !this.type;
      },
    },
    conditions: {
      type: [MongooseSchema.Types.Mixed],
      validate: {
        validator: function (v) {
          // If this is a complex condition (has type), conditions must be an array
          return !this.type || (Array.isArray(v) && v.length > 0);
        },
        message: 'Conditions must be an array for complex conditions',
      },
    },
  },
  { _id: false },
);

const ConditionTaskSchema = new MongooseSchema(
  {
    value: { type: MongooseSchema.Types.Mixed, required: false },
    id: { type: String, required: false },
  },
  { _id: false },
);

@Schema({ timestamps: true, collection: 'task_execution' })
export class TaskExecution {
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'work_flow_execution',
    required: true,
  })
  workflow_execution_id: Types.ObjectId;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'Task',
    required: true,
  })
  task_id: Types.ObjectId;

  @Prop({ type: Date, required: true })
  start_date: Date;

  @Prop({ type: Date })
  end_date: Date;

  @Prop({ type: String, enum: Task_status, required: true })
  task_status: Task_status;

  @Prop({ type: String })
  last_activity: string;

  @Prop({
    type: [MongooseSchema.Types.ObjectId],
    ref: "User",
    required: false,
    default: [], // Ensures empty array is allowed by default
  })
  assigns: Types.ObjectId[];

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'User',
    required: true,
  })
  assign_by: Types.ObjectId;

  @Prop({ type: String })
  notes: string;

  @Prop({ type: Number, required: false })
  view_id: number;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'Template',
    required: true,
  })
  template_id: Types.ObjectId;

  @Prop({ type: Number, required: false })
  sequence: number;

  @Prop({ type: Boolean, default: false })
  default_task: boolean;

  @Prop({
    type: String,
    enum: TASK_VISIBLE_STATUS,
    required: true,
    default: TASK_VISIBLE_STATUS.DRAFT,
  })
  task_visible_status: TASK_VISIBLE_STATUS;

  @Prop({ type: Boolean, default: false })
  selected_task: boolean;

  @Prop({ type: Boolean, default: false })
  is_reviewed: boolean;

  @Prop({ type: ConditionTaskSchema, required: false })
  condition_task: {
    value: any;
    id: string;
  };

  @Prop({ type: [FormFieldSchema] })
  formField: IFormField[];

  @Prop({ type: [DerivedFieldSchema], default: [] })
  derived_field: DerivedField[];

  @Prop({ default: true })
  is_active: boolean;

  @Prop({ default: false })
  is_deleted: boolean;
}

export const TaskExecutionSchema = SchemaFactory.createForClass(TaskExecution);
