import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Schema as MongooseSchema } from 'mongoose';

export type UserRoleDocument = HydratedDocument<UserRole>;

@Schema({ timestamps: true, collection: 'userrole' })
export class UserRole {
  @Prop({ type: Number })
  id: number;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Role', required: true })
  roleId: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
  userId: string;

  @Prop({ type: Boolean, default: false })
  status: boolean;
}

export const UserRoleSchema = SchemaFactory.createForClass(UserRole);
