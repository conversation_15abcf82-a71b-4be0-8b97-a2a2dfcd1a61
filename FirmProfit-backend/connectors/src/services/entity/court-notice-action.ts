import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type CourtNoticeActionDocument = HydratedDocument<CourtNoticeAction>;

@Schema({ timestamps: true, collection: 'court_notice_action' })
export class CourtNoticeAction {
  @Prop({ required: true })
  value: string;

  @Prop({ required: true })
  text: string;

  @Prop({ default: true })
  is_active: boolean;
}

export const CourtNoticeActionSchema =
  SchemaFactory.createForClass(CourtNoticeAction);
