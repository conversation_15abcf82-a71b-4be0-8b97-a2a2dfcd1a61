import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, Schema as MongooseSchema, Types } from "mongoose";

export type MatterDocument = HydratedDocument<Matter>;

@Schema({ _id: false })
export class ClientReference {
  @Prop({ required: true })
  id: number;
}

export const ClientReferenceSchema =
  SchemaFactory.createForClass(ClientReference);

@Schema({ _id: false })
export class StaffReference {
  @Prop({ required: true })
  id: number;

  @Prop({ default: false })
  lead_lawyer: boolean;

  @Prop({ default: false })
  originating_lawyer: boolean;

  @Prop({ required: false })
  case_rate?: string;
}

export const StaffReferenceSchema =
  SchemaFactory.createForClass(StaffReference);

@Schema({ _id: false })
export class CaseStageReference {
  @Prop({ required: true })
  id: number;
}

export const CaseStageReferenceSchema =
  SchemaFactory.createForClass(CaseStageReference);

@Schema({ _id: false })
export class PracticeAreaReference {
  @Prop({ required: true })
  id: number;
}

export const PracticeAreaReferenceSchema = SchemaFactory.createForClass(
  PracticeAreaReference
);

@Schema({ _id: false })
export class BillingContact {
  @Prop({ required: true })
  id: number;
}

export const BillingContactSchema =
  SchemaFactory.createForClass(BillingContact);

@Schema({ timestamps: true, collection: "matter" })
export class Matter {
  @Prop({ required: false })
  my_case_matter_id: string;

  @Prop({ required: false })
  name: string;

  @Prop({ required: false })
  case_number?: string;

  @Prop({ required: false })
  description?: string;

  @Prop({ required: false })
  opened_date?: string;

  @Prop({ required: false })
  closed_date?: string;

  @Prop({ required: false })
  sol_date?: string;

  @Prop({ required: false, default: 0 })
  outstanding_balance?: number;

  @Prop({ type: [ClientReferenceSchema], default: [] })
  clients: ClientReference[];

  @Prop({ type: [StaffReferenceSchema], default: [] })
  staff: StaffReference[];

  @Prop({ type: CaseStageReferenceSchema, required: false })
  case_stage_reference?: CaseStageReference;

  @Prop({ type: PracticeAreaReferenceSchema, required: false })
  practice_area_reference?: PracticeAreaReference;

  @Prop({ type: BillingContactSchema, required: false })
  billing_contact?: BillingContact;

  @Prop({ default: false })
  potential_case: boolean;

  @Prop({ default: false })
  is_active: boolean;

  @Prop({ required: false })
  firm_uuid?: string;

  @Prop({ required: false })
  attorney?: string;

  // Legacy fields for backward compatibility
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: "client",
    required: false,
  })
  client_id?: Types.ObjectId;

  @Prop({ type: String, required: false })
  ex_county_of_arrest?: string;

  // Timestamps from MyCase
  @Prop({ required: false })
  my_case_created_at?: string;

  @Prop({ required: false })
  my_case_updated_at?: string;
}

export const MatterSchema = SchemaFactory.createForClass(Matter);

// Add indexes for performance
MatterSchema.index({ my_case_matter_id: 1 });
MatterSchema.index({ case_number: 1 });
MatterSchema.index({ firm_uuid: 1 });
MatterSchema.index({ is_active: 1 });
MatterSchema.index({ "clients.id": 1 });
MatterSchema.index({ "staff.id": 1 });
