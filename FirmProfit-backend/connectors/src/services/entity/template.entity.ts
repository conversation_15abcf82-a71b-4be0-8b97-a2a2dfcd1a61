// schemas/template.schema.ts
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Schema as MongooseSchema, Types } from 'mongoose';
import { Template_status } from '../enum/template-status.enum';

export type TemplateDocument = HydratedDocument<Template>;

@Schema({ timestamps: true, collection: 'template' })
export class Template {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true, unique: true })
  template_id: number;

  @Prop({ required: false, default: '' })
  template_workflow_id: string;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'Category',
    required: true,
  })
  category_id: Types.ObjectId;

  @Prop({
    type: [MongooseSchema.Types.ObjectId],
    ref: 'Task',
    required: true,
  })
  task_id: Types.ObjectId[];

  @Prop()
  parent_id: Types.ObjectId;

  @Prop()
  version: number | null;

  @Prop({
    type: String,
    enum: Template_status,
    required: true,
    default: Template_status.DRAFT,
  })
  status: Template_status;

  @Prop({ default: true })
  is_active: boolean;

  @Prop({ default: false })
  is_deleted: boolean;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'UserGroup',
    required: false,
    default: '6877420fd4928f6a37ba1b95'
  })
  templateGroupId: Types.ObjectId;
}

export const TemplateSchema = SchemaFactory.createForClass(Template);
