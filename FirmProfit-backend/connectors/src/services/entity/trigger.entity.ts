import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Schema as MongooseSchema } from 'mongoose';
import { Trigger_type } from '../enum/template-status.enum';

export type TriggerDocument = HydratedDocument<Trigger>;

@Schema({ timestamps: true })
export class Trigger {
  @Prop({ required: true })
  name: string;

  @Prop()
  description: string;

  @Prop({ type: String, enum: Trigger_type, required: true })
  trigger_type: Trigger_type;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'Connector',
    required: true,
  })
  connector_id: MongooseSchema.Types.ObjectId;

  @Prop({ type: Boolean, default: true })
  is_active: boolean;
}

export const TriggerSchema = SchemaFactory.createForClass(Trigger);
