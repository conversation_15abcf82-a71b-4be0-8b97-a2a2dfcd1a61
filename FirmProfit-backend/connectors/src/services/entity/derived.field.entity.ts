import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Schema as MongooseSchema, Types } from 'mongoose';

@Schema({ _id: true, strict: false })
class EventSub {
  @Prop({ required: false }) id: string;
  @Prop({ required: false }) caseNumber: string;
  @Prop({ required: false }) clientName: string;
  @Prop({ required: false }) description: string;
  @Prop({ required: false }) date: string;
  @Prop({ required: false }) startTime: string;
  @Prop({ required: false }) endTime: string;
  @Prop({ default: false }) isCompleted: boolean;
  @Prop({ required: false }) subject: string;
  @Prop({ required: false }) courtNoticeType: string;
  @Prop({ required: false }) courtNoticeActions: string;
  @Prop({ default: 'New' })
  appointmentAction: string;
  @Prop({ default: 'New' })
  eventStatus?: string;
  @Prop({ required: false }) charge: string;
  @Prop({ required: false }) county: string;
  @Prop({ required: false }) courtLocation: string;
  @Prop({ required: false }) optionalAttendees: string;
  @Prop({ required: false }) requiredAttendees: string;
  @Prop({ required: false }) clientAttendance: string;
  @Prop({ required: false }) meetingLocation: string;
  @Prop({ required: false }) phoneDetails: string;
  @Prop({ required: false }) meetingAddress: string;
  @Prop({ required: false }) meetingLink: string;
  @Prop({ required: false }) appointmentToReschedule?: string;
  @Prop({ required: false }) court_notice_date?: string;
  @Prop({ required: false }) startDate: string;
  @Prop({ required: false }) endDate: string;
  @Prop({ default: false }) isCancel?: boolean;
  @Prop({ default: false }) allDay: boolean;
  @Prop({ default: false }) isAddSecondary: boolean;
  @Prop({
    type: [
      {
        id: Number,
        name: String,
        key: String,
        uniqueId: String,
        mycaseDocumentId: String, // Store MyCase document ID for updates
      },
    ],
    default: [],
  })
  files: any[];
  @Prop() eventActionHistoryModelId?: string;
  @Prop({ required: false }) my_case_event_id?: string;
  @Prop({ required: false }) my_case_matter_id?: string;
  @Prop({ required: false }) event_history_id?: string;
}
export const EventSubSchema = SchemaFactory.createForClass(EventSub);

// Configure the schema to allow all fields to be saved
EventSubSchema.set('strict', false);
EventSubSchema.set('minimize', false);

@Schema({ _id: false })
class MatterSub {
  @Prop() _id: string;
  @Prop({ required: false }) name?: string; // <-- make optional here
  @Prop({ required: false }) attorney?: string; // <-- make optional here
  @Prop({ type: [EventSubSchema], default: [] }) event: EventSub[];
  @Prop({ required: false }) matter_id?: string; // MongoDB ObjectId of the matter
  @Prop({ required: false }) ex_county_of_arrest?: string; // MongoDB ObjectId of the matter
  @Prop({ required: false }) case_number?: string; // MongoDB ObjectId of the matter
  @Prop({ required: false }) my_case_matter_id?: string; // MongoDB ObjectId of the matter
}
export const MatterSubSchema = SchemaFactory.createForClass(MatterSub);

@Schema({ _id: false })
class ClientSub {
  @Prop({ required: false }) client_name?: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Matter', required: false })
  client_matter_id?: Types.ObjectId;

  @Prop({ type: [MatterSubSchema], default: [] }) matter: MatterSub[];
}
export const ClientSubSchema = SchemaFactory.createForClass(ClientSub);

@Schema({ _id: false })
export class DerivedField {
  @Prop() id: string;
  @Prop() type: string;
  @Prop() label: string;
  @Prop() sequence: number;
  @Prop() matter_list_api: string;
  @Prop({ type: Object }) condition: any;
  @Prop({ type: [ClientSubSchema], default: [] }) client: ClientSub[];
}
export const DerivedFieldSchema = SchemaFactory.createForClass(DerivedField);

// Optionally export subtypes if you want access elsewhere
export { EventSub, MatterSub, ClientSub };
