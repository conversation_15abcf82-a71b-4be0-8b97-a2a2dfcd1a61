import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Schema as MongooseSchema } from 'mongoose';

export type ApiRequestDocument = HydratedDocument<ApiRequest>;

@Schema({ timestamps: true, collection: 'api_requests' })
export class ApiRequest {
  @Prop({ required: true })
  url: string;

  @Prop({ type: MongooseSchema.Types.Mixed, required: true })
  request: any;

  @Prop({ required: false })
  messageId?: string;

  @Prop({ required: false })
  tenantId?: string;

  @Prop({ required: false })
  status?: string;

  @Prop({ type: MongooseSchema.Types.Mixed, required: false })
  response?: any;

  @Prop({ required: false })
  errorMessage?: string;

  @Prop({ required: false })
  processingTime?: number;
}

export const ApiRequestSchema = SchemaFactory.createForClass(ApiRequest); 