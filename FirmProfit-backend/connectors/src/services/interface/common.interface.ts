import { Task_status, Template_status } from '../enum/template-status.enum';
import { Types } from 'mongoose';

export interface Authentication_detail {
  key: string;
  value: string;
}

export interface User_info {
  user_id: number;
  name: string;
}

export interface Task {
  name: string;
  guId: number;
  type: 'formComponent' | 'derivedComponent' | 'connector';
  id: number;
  assigns: User_info[];
  startDate: Date;
  endDate: Date;
  Task_status: Task_status;
  lastActivity: Date;
  notes: string;
  assignBy: User_info;
  completedBy: User_info;
  previousNode: number;
  nextNode: number;
}

export interface View {
  name: string;
  tasks: Task[];
}

export interface WorkFlowJson {
  name: string;
  guId: number;
  categoryId: number;
  parentId: number | null;
  version: number;
  status: Template_status;
  isActive: boolean;
  isDeleted: boolean;
  assigns: User_info[];
  isManuallyRun: boolean;
  lastActivity: Date;
  view: View[];
  startDate: Date;
  endDate: Date;
  previousNode: number;
  nextNode: number;
}

// Interface for simple condition
export interface SimpleCondition {
  field: string;
  value: string | boolean;
}

// Interface for complex AND/OR condition
export interface ComplexCondition {
  type: 'AND' | 'OR';
  conditions: SimpleCondition[];
}

// Union type for all condition types
export type FormFieldCondition = SimpleCondition | ComplexCondition;

export interface IFormField {
  id: string;
  type: string;
  label?: string;
  required?: boolean;
  dynamic_fields?: boolean;
  dynamic_selected_task?: boolean;
  new_field_add?: boolean;
  form_field_id?: string;
  api_end_point?: string;
  sequence: number;
  condition?: FormFieldCondition;
  fields?: {
    form_component_id: Types.ObjectId;
    value: {
      id: string | null;
      value: string;
    }[];
  }[];
}
