import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { MongooseModule } from '@nestjs/mongoose';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { GmailModule } from './gmail/gmail.module';
// import { GrpcClientModule } from './grpc-client/grpc-client.module';
import { SqsConsumerModule } from './services/sqs-consumer.module';
import { DatabaseConfiguration } from './config/db.config';
import { TenantConfig } from './database/entities/tenant-config.entity';
import { WorkflowExecution } from './services/entity/workflow-execution.entity';
import { Client } from './services/entity/client.entity';
import { Matter } from './services/entity/matter.entity';
import { SharedDatabaseModule } from '@shared/database';

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [DatabaseConfiguration],
      isGlobal: true,
      envFilePath: '.env',
    }),
    // MongoDB Connection
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        const mongoConfig = configService.get('db').mongo;

        console.log('✅ Connecting to MongoDB with URI:', mongoConfig?.uri);
        console.log('✅ Using Database:', mongoConfig?.dbName);

        if (!mongoConfig?.uri) {
          throw new Error(
            '❌ MONGO_URI is undefined! Check your environment variables.',
          );
        }

        return {
          uri: mongoConfig.uri,
          dbName: mongoConfig.dbName,
        };
      },
    }),
    // PostgreSQL Connection - using existing public schema
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        const postgresConfig = configService.get('db').postgres;

        console.log('✅ Connecting to PostgreSQL with host:', postgresConfig?.host);
        console.log('✅ Using Database:', postgresConfig?.database);
        console.log('✅ Using Schema: stephen_schema (shared with shared-service)');

        if (!postgresConfig?.host) {
          throw new Error(
            '❌ POSTGRES_HOST is undefined! Check your environment variables.',
          );
        }

        return {
          type: 'postgres',
          host: postgresConfig.host,
          port: postgresConfig.port,
          username: postgresConfig.username,
          password: postgresConfig.password,
          database: postgresConfig.database,
          schema: 'public', // Use same schema as shared-service
          entities: [TenantConfig, WorkflowExecution,Client,Matter], // Include shared entities
          synchronize: false, // Don't auto-create tables, use existing ones
          logging: postgresConfig.logging,
        };
      },
    }),
    // Register entities for dependency injection
    TypeOrmModule.forFeature([TenantConfig]),
    ScheduleModule.forRoot(),
    GmailModule,
    SqsConsumerModule,
    SharedDatabaseModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {} 