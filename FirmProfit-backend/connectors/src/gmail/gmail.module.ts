import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { TypeOrmModule } from '@nestjs/typeorm';
import { GmailService } from './gmail.service';
import { GmailController } from './gmail.controller';
import { Email, EmailSchema } from './schemas/email.schema';
// import { GrpcClientModule } from '../grpc-client/grpc-client.module';
import { TenantConfig } from '../database/entities/tenant-config.entity';
import { ImapConfigService } from '../config/imap-config.service';
import { S3Module } from '../common/s3/s3.module';
import { SQSModule } from '../common/sqs/sqs.module';
import { SharedDatabaseModule } from '@shared/database';
// import { WorkflowExecution, WorkflowExecutionSchema } from '@shared/database';
// Import shared entities

@Module({
  imports: [
    ConfigModule,
    MongooseModule.forFeature([
      { name: Email.name, schema: EmailSchema },
      // { name: WorkflowExecution.name, schema: WorkflowExecutionSchema }
    ]),
    TypeOrmModule.forFeature([TenantConfig]), // Include shared entities
    // GrpcClientModule,
    S3Module,
    SQSModule,
    SharedDatabaseModule,
  ],
  controllers: [GmailController],
  providers: [GmailService, ImapConfigService],
  exports: [GmailService, ImapConfigService],
})
export class GmailModule {} 