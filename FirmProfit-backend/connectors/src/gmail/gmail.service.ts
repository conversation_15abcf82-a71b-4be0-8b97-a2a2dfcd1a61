import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SchedulerRegistry } from '@nestjs/schedule';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as Imap from 'node-imap';
import { simpleParser } from 'mailparser';
import * as crypto from 'crypto';
import * as fs from 'fs';
import * as path from 'path';
// import { GrpcClientService } from '../grpc-client/grpc-client.service';
import { Email, EmailDocument } from './schemas/email.schema';
import { ImapConfigService } from '../config/imap-config.service';
import { ImapConfig } from '../database/entities/tenant-config.entity';
import { S3Service } from '../common/s3/s3.service';
import { SQSService } from '../common/sqs/sqs.service';

export interface ParsedEmail {
  subject: string;
  from: string;
  body: string;
  receivedAt: string;
  messageId?: string;
  uniqueHash: string;
  uid?: number;
  seqno?: number;
  headers?: Record<string, any>;
  tenantId?: string;
  attachments?: AttachmentInfo[];
  isAiRpa?: boolean;
  clickHereUrl?: string; // Keep for backward compatibility
  clickHereLinks?: ClickHereLink[]; // New field for multiple links
}

export interface ClickHereLink {
  url: string;
  password?: string;
  linkText?: string;
  context?: string; // Additional context around the link
}

export interface AttachmentInfo {
  filename: string;
  contentType: string;
  size: number;
  savedPath?: string; // Local path (kept for backward compatibility)
  s3Key?: string; // S3 key for cloud storage
  s3Bucket?: string; // S3 bucket name
  s3Url?: string; // S3 presigned URL (temporary)
  contentId?: string;
  checksum: string;
  attachmentId: string; // Unique identifier for this attachment
  storageType: 'local' | 's3' | 'both'; // Where the file is stored
  uploadedAt: string; // ISO timestamp
}

@Injectable()
export class GmailService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(GmailService.name);
  private imap: Imap;
  private isConnected = false;
  private isMailboxOpen = false;
  private currentMailbox = '';
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectTimeout: NodeJS.Timeout;
  private markAsSeenRetryLimit = 3;
  private currentTenantId: string | null = null;
  private currentImapConfig: ImapConfig | null = null;
  private attachmentsBasePath = './attachments';

  constructor(
    private readonly configService: ConfigService,
    // private readonly grpcClientService: GrpcClientService,
    private readonly schedulerRegistry: SchedulerRegistry,
    private readonly imapConfigService: ImapConfigService,
    private readonly s3Service: S3Service,
    private readonly sqsService: SQSService,
    @InjectModel(Email.name) private readonly emailModel: Model<EmailDocument>,
  ) {}

  async onModuleInit() {
    this.logger.log('Initializing Gmail IMAP Service...');
    
    // Get default tenant ID from environment or use fallback
    const defaultTenantId = process.env.TENANT_ID;

    if (defaultTenantId) {
      this.logger.log(`Using default tenant: ${defaultTenantId}`);
      try {
        await this.initializeForTenant(defaultTenantId);
        this.logger.log(`✅ Successfully initialized for tenant: ${defaultTenantId}`);
      } catch (error) {
        this.logger.error(`❌ Failed to initialize for tenant ${defaultTenantId}:`, error.message);
        this.logger.warn('🔄 Falling back to environment configuration...');
        await this.initializeWithFallback();
      }
    } else {
      this.logger.warn('No default tenant ID specified, using fallback configuration');
      await this.initializeWithFallback();
    }
  }

  async onModuleDestroy() {
    this.logger.log('Destroying Gmail IMAP Service...');
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
    }
    await this.disconnectFromImap();
  }

  /**
   * Initialize IMAP connection for a specific tenant
   */
  async initializeForTenant(tenantId: string): Promise<void> {
    try {
      this.logger.log(`🔧 Initializing IMAP for tenant: ${tenantId}`);
      
      // Get IMAP configuration for tenant
      this.logger.log(`📡 Fetching IMAP configuration for tenant: ${tenantId}`);
      const imapConfig = await this.imapConfigService.getImapConfigWithFallback(tenantId);
      
      this.logger.log(`✅ Retrieved IMAP configuration for tenant: ${tenantId}`);
      this.logger.log(`📧 IMAP Host: ${imapConfig.host}:${imapConfig.port}`);
      this.logger.log(`👤 IMAP User: ${imapConfig.username ? imapConfig.username.substring(0, 3) + '***' : 'Not set'}`);
      
      this.currentTenantId = tenantId;
      this.currentImapConfig = imapConfig;
      
      // TODO: Uncomment when ready to test IMAP connection
      await this.connectToImap(imapConfig);
      
      this.logger.log(`🎯 Tenant ${tenantId} configuration loaded successfully (IMAP connection disabled for testing)`);
    } catch (error) {
      this.logger.error(`❌ Failed to initialize IMAP for tenant ${tenantId}:`, error);
      this.logger.error(`Error details:`, error.stack);
      throw error;
    }
  }

  /**
   * Initialize with fallback configuration
   */
  async initializeWithFallback(): Promise<void> {
    try {
      this.logger.log('Initializing IMAP with fallback configuration');
      
      const imapConfig = this.imapConfigService.getFallbackImapConfig();
      this.currentImapConfig = imapConfig;
      
      await this.connectToImap(imapConfig);
    } catch (error) {
      this.logger.error('Failed to initialize IMAP with fallback configuration:', error);
      throw error;
    }
  }

  /**
   * Switch to a different tenant's configuration
   */
  async switchToTenant(tenantId: string): Promise<void> {
    if (this.currentTenantId === tenantId) {
      this.logger.log(`Already connected to tenant: ${tenantId}`);
      return;
    }

    this.logger.log(`Switching to tenant: ${tenantId}`);
    
    // Disconnect from current IMAP connection
    await this.disconnectFromImap();
    
    // Initialize for new tenant
    await this.initializeForTenant(tenantId);
  }

  private async connectToImap(imapConfig: ImapConfig): Promise<void> {
    if (!imapConfig.username || !imapConfig.password) {
      throw new Error('IMAP username and password are required');
    }

    console.log('imapConfig', imapConfig);

    const nodeImapConfig = {
      user: imapConfig.username,
      password: imapConfig.password,
      host: imapConfig.host,
      port: imapConfig.port,
      tls: imapConfig.tls,
      tlsOptions: imapConfig.tlsOptions,
      keepalive: imapConfig.keepalive,
      autotls: imapConfig.autotls,
    };

    this.logger.log(`Connecting to IMAP server: ${imapConfig.host}:${imapConfig.port} for tenant: ${this.currentTenantId || 'fallback'}`);

    this.imap = new Imap(nodeImapConfig);
    this.maxReconnectAttempts = imapConfig.maxReconnectAttempts || 5;

    this.imap.once('ready', () => {
      this.logger.log(`Successfully connected to Gmail IMAP for tenant: ${this.currentTenantId || 'fallback'}`);
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.openInbox();
    });

    this.imap.once('error', (err) => { 
      this.logger.error('IMAP connection error:', err);
      this.isConnected = false;
      this.isMailboxOpen = false;
      this.handleConnectionError();
    });

    this.imap.once('end', () => {
      this.logger.warn('IMAP connection ended');
      this.isConnected = false;
      this.isMailboxOpen = false;
      this.handleConnectionError();
    });

    this.imap.once('close', () => {
      this.logger.warn('IMAP connection closed');
      this.isConnected = false;
      this.isMailboxOpen = false;
    });

    this.imap.connect();
  }

  private async disconnectFromImap(): Promise<void> {
    if (this.imap && this.isConnected) {
      this.imap.end();
      this.isConnected = false;
      this.isMailboxOpen = false;
      this.logger.log('Disconnected from Gmail IMAP');
    }
  }

  private handleConnectionError(): void {
    this.isConnected = false;
    this.isMailboxOpen = false;
    
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
      
      this.logger.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      this.reconnectTimeout = setTimeout(() => {
        if (this.currentImapConfig) {
          this.connectToImap(this.currentImapConfig);
        }
      }, delay);
    } else {
      this.logger.error('Max reconnection attempts reached. Giving up.');
    }
  }

  private openInbox(): void {
    this.imap.openBox('INBOX', false, (err, box) => {
      if (err) {
        this.logger.error('Failed to open inbox:', err);
        this.isMailboxOpen = false;
        return;
      }

      this.logger.log('Successfully opened inbox');
      this.logger.log(`Total messages: ${box.messages.total}, New messages: ${box.messages.new}`);
      this.isMailboxOpen = true;
      this.currentMailbox = 'INBOX';
      
      // Start listening for new messages
      this.startIdleListener();
    });
  }

  private async ensureMailboxOpen(): Promise<boolean> {
    if (!this.isConnected) {
      this.logger.warn('IMAP not connected, cannot ensure mailbox is open');
      return false;
    }

    if (this.isMailboxOpen && this.currentMailbox === 'INBOX') {
      return true;
    }

    this.logger.log('Mailbox not open, attempting to open INBOX...');
    
    return new Promise((resolve) => {
      this.imap.openBox('INBOX', false, (err, box) => {
        if (err) {
          this.logger.error('Failed to open inbox in ensureMailboxOpen:', err);
          this.isMailboxOpen = false;
          resolve(false);
        } else {
          this.logger.log('Successfully opened inbox in ensureMailboxOpen');
          this.isMailboxOpen = true;
          this.currentMailbox = 'INBOX';
          resolve(true);
        }
      });
    });
  }

  private startIdleListener(): void {
    this.logger.log('Starting IDLE listener for new emails...');
    
    // Set up mail event listener
    this.imap.on('mail', (numNewMsgs) => {
      this.logger.log(`${numNewMsgs} new message(s) received`);
      this.fetchNewMessages();
    });

    // Check if IDLE method exists and is supported
    if (typeof this.imap.idle === 'function') {
      this.logger.log('IDLE method available, attempting to enter IDLE mode');
      try {
        this.imap.idle();
        this.logger.log('Successfully entered IDLE mode');
      } catch (error) {
        this.logger.warn('Failed to enter IDLE mode, falling back to polling', error);
        this.startPollingMode();
      }
    } else {
      this.logger.warn('IDLE method not available, using polling mode');
      this.startPollingMode();
    }
  }

  private fetchNewMessages(): void {
    if (!this.isConnected || !this.isMailboxOpen) {
      this.logger.warn('Cannot fetch messages: connection or mailbox not ready');
      return;
    }
  
    this.logger.log('Fetching new unread messages...');
  
    // Example: labels may come from config
    const labels = this.currentImapConfig?.gmailLabel || ['Firmprofit Recieved','Firmprofit Recieved 2'];
  
    const searchQuery = `(${labels.map(label => `label:"${label}"`).join(' OR ')}) is:unread`;
    this.logger.log(`Using Gmail labels: ${labels.join(', ')} for tenant: ${this.currentTenantId || 'fallback'}`);
  
    const searchCriteria = [['X-GM-RAW', searchQuery]];
    const searchCriteria2 = ["UNSEEN"];
  
    const fetchOptions = {
      bodies: '',
      struct: true,
      envelope: true,
    };
  
    this.imap.search(searchCriteria, (err, results) => {
      if (err) {
        this.logger.error('Error searching for messages:', err);
        return;
      }
  
      if (!results || results.length === 0) {
        this.logger.log('No new unread messages found');
        return;
      }
  
      this.logger.log(`Found ${results.length} unread message(s)`);
      const fetch = this.imap.fetch(results, fetchOptions);
  
      fetch.on('message', (msg, seqno) => {
        this.logger.log(`Processing message ${seqno}`);
        this.processMessage(msg, seqno);
      });
  
      fetch.once('error', (err) => {
        this.logger.error('Fetch error:', err);
      });
  
      fetch.once('end', () => {
        this.logger.log('Finished fetching messages');
        this.resumeIdleMode();
      });
    });
  } 
  
  
  private startPollingMode(): void {
    if (!this.currentImapConfig) {
      this.logger.error('No IMAP configuration available for polling');
      return;
    }

    const pollingInterval = this.currentImapConfig.pollingInterval || 30000;
    this.logger.log(`Starting polling mode to check for new emails every ${pollingInterval}ms`);
    
    // Set up polling interval
    const interval = setInterval(() => {
      if (!this.isConnected || !this.isMailboxOpen) {
        this.logger.log('Connection or mailbox not ready, stopping polling');
        clearInterval(interval);
        return;
      }
      
      this.logger.log('Polling for new messages...');
      this.fetchNewMessages();
    }, pollingInterval);

    // Initial fetch
    this.fetchNewMessages();
  }

  private resumeIdleMode(): void {
    if (typeof this.imap.idle === 'function') {
      try {
        this.imap.idle();
        this.logger.log('Successfully resumed IDLE mode');
      } catch (error) {
        this.logger.warn('Failed to resume IDLE mode:', error);
      }
    }
  }

  /**
   * Process and save email attachments to S3 cloud storage
   */
  private async processAttachments(parsedEmail: any, tenantId: string | null, emailUniqueHash: string): Promise<AttachmentInfo[]> {
    const savedAttachments: AttachmentInfo[] = [];

    if (!parsedEmail.attachments || parsedEmail.attachments.length === 0) {
      return savedAttachments;
    }

    this.logger.log(`Processing ${parsedEmail.attachments.length} attachment(s) for S3 upload`);

    for (let i = 0; i < parsedEmail.attachments.length; i++) {
      const attachment = parsedEmail.attachments[i];
      try {
        // Generate unique attachment ID
        const attachmentId = `${emailUniqueHash}_${i}_${Date.now()}`;
        
        // Get original filename
        const originalFilename = attachment.filename || `attachment_${attachmentId}${this.getExtensionFromContentType(attachment.contentType)}`;
        
        // Calculate checksum
        const checksum = attachment.checksum || crypto.createHash('md5').update(attachment.content).digest('hex');

        this.logger.log(`Uploading attachment ${i + 1}/${parsedEmail.attachments.length}: ${originalFilename} (${attachment.content.length} bytes)`);

        // Upload to S3
        const s3Result = await this.s3Service.uploadEmailAttachment(
          attachment.content,
          originalFilename,
          attachment.contentType || 'application/octet-stream',
          tenantId,
          emailUniqueHash,
          attachmentId
        );

        if (s3Result.success) {
          // Generate presigned URL for immediate access (valid for 24 hours)
          const presignedUrl = await this.s3Service.generatePresignedUrl(s3Result.key!, 'get', 86400);

          const attachmentInfo: AttachmentInfo = {
            filename: originalFilename,
            contentType: attachment.contentType || 'application/octet-stream',
            size: attachment.size || attachment.content.length,
            s3Key: s3Result.key,
            s3Bucket: this.s3Service.getBucketName(),
            s3Url: presignedUrl,
            contentId: attachment.contentId || attachment.cid,
            checksum: checksum,
            attachmentId: attachmentId,
            storageType: 's3',
            uploadedAt: new Date().toISOString(),
          };

          savedAttachments.push(attachmentInfo);

          this.logger.log(`✅ Attachment uploaded successfully: ${originalFilename} -> S3:${s3Result.key}`);
        } else {
          this.logger.error(`❌ Failed to upload attachment to S3: ${s3Result.error}`);
          
          // Optionally fall back to local storage
          if (this.configService.get('ATTACHMENT_FALLBACK_LOCAL') === 'true') {
            const localResult = await this.saveAttachmentLocally(attachment, tenantId, attachmentId);
            if (localResult) {
              savedAttachments.push(localResult);
            }
          }
        }

      } catch (error) {
        this.logger.error(`Failed to process attachment ${i + 1}: ${attachment.filename}`, error);
        // Continue processing other attachments even if one fails
      }
    }

    this.logger.log(`✅ Successfully processed ${savedAttachments.length}/${parsedEmail.attachments.length} attachment(s)`);
    return savedAttachments;
  }

  /**
   * Fallback method to save attachment locally when S3 fails
   */
  private async saveAttachmentLocally(attachment: any, tenantId: string | null, attachmentId: string): Promise<AttachmentInfo | null> {
    try {
      const tenantDir = tenantId || 'fallback';
      const dateDir = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
      const attachmentDir = path.join(this.attachmentsBasePath, tenantDir, dateDir);

      // Ensure the directory exists
      await fs.promises.mkdir(attachmentDir, { recursive: true });

      // Generate safe filename
      const originalFilename = attachment.filename || `attachment_${attachmentId}${this.getExtensionFromContentType(attachment.contentType)}`;
      const safeFilename = this.generateSafeFilename(originalFilename, attachment.contentType);
      const timestamp = Date.now();
      const uniqueFilename = `${timestamp}_${safeFilename}`;
      const filePath = path.join(attachmentDir, uniqueFilename);

      // Save attachment to file
      await fs.promises.writeFile(filePath, attachment.content);

      const attachmentInfo: AttachmentInfo = {
        filename: originalFilename,
        contentType: attachment.contentType || 'application/octet-stream',
        size: attachment.size || attachment.content.length,
        savedPath: filePath,
        contentId: attachment.contentId || attachment.cid,
        checksum: attachment.checksum || crypto.createHash('md5').update(attachment.content).digest('hex'),
        attachmentId: attachmentId,
        storageType: 'local',
        uploadedAt: new Date().toISOString(),
      };

      this.logger.log(`📁 Attachment saved locally: ${originalFilename} -> ${filePath}`);
      return attachmentInfo;
    } catch (error) {
      this.logger.error(`Failed to save attachment locally: ${error.message}`);
      return null;
    }
  }

  /**
   * Generate a safe filename for saving attachments
   */
  private generateSafeFilename(originalFilename: string, contentType?: string): string {
    // Remove or replace unsafe characters
    let safeFilename = originalFilename
      .replace(/[<>:"/\\|?*]/g, '_')
      .replace(/\s+/g, '_')
      .replace(/_{2,}/g, '_')
      .trim();

    // Ensure filename is not empty
    if (!safeFilename || safeFilename === '_') {
      const extension = this.getExtensionFromContentType(contentType);
      safeFilename = `attachment${extension}`;
    }

    // Limit filename length
    if (safeFilename.length > 100) {
      const ext = path.extname(safeFilename);
      const name = path.basename(safeFilename, ext);
      safeFilename = name.substring(0, 100 - ext.length) + ext;
    }

    return safeFilename;
  }

  /**
   * Get file extension from content type
   */
  private getExtensionFromContentType(contentType?: string): string {
    if (!contentType) return '';

    const mimeToExt: { [key: string]: string } = {
      'application/pdf': '.pdf',
      'image/jpeg': '.jpg',
      'image/png': '.png',
      'image/gif': '.gif',
      'text/plain': '.txt',
      'text/html': '.html',
      'application/msword': '.doc',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
      'application/vnd.ms-excel': '.xls',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
      'application/zip': '.zip',
      'application/x-zip-compressed': '.zip',
    };

    return mimeToExt[contentType.toLowerCase()] || '';
  }

  private processMessage(msg: Imap.ImapMessage, seqno: number): void {
    let buffer = '';
    let attributes: any;

    msg.on('body', (stream, info) => {
      stream.on('data', (chunk) => {
        buffer += chunk.toString('utf8');
      });
    });

    msg.once('attributes', (attrs) => {
      attributes = attrs;
    });

    msg.once('end', async () => {
      try {
        const parsed = await simpleParser(buffer);
        
        // Extract Message-ID from headers
        const messageId = parsed.messageId || parsed.headers.get('message-id') || null;
        
        // Create unique hash for duplicate prevention
        const uniqueHash = this.generateUniqueHash(
          parsed.from?.text || 'Unknown Sender',
          parsed.subject || 'No Subject',
          parsed.date || new Date(),
          messageId,
          buffer
        );

        // Process attachments if they exist
        let savedAttachments: AttachmentInfo[] = [];
        try {
          savedAttachments = await this.processAttachments(parsed, this.currentTenantId, uniqueHash);
        } catch (error) {
          this.logger.error('Error processing attachments:', error);
          // Continue processing email even if attachments fail
        }

        // Extract email body for processing
        const emailBody = parsed.text || parsed.html || 'No Content';
        
        // Detect AI RPA emails
        const aiRpaResult = this.detectAiRpaEmail(emailBody);

        const email: ParsedEmail = {
          subject: parsed.subject || 'No Subject',
          from: parsed.from?.text || 'Unknown Sender',
          body: emailBody,
          receivedAt: (parsed.date || new Date()).toISOString(),
          messageId: messageId,
          uniqueHash: uniqueHash,
          uid: attributes?.uid,
          seqno: seqno,
          headers: parsed.headers ? Object.fromEntries(parsed.headers) : {},
          tenantId: this.currentTenantId,
          attachments: savedAttachments,
          isAiRpa: aiRpaResult.isAiRpa,
          clickHereUrl: aiRpaResult.clickHereUrl, // Backward compatibility
          clickHereLinks: aiRpaResult.clickHereLinks, // New multiple links support
        };

        const aiRpaInfo = email.isAiRpa ? 
          `YES (${email.clickHereLinks?.length || 0} link(s)${email.clickHereLinks?.some(link => link.password) ? ' with passwords' : ''})` : 
          'NO';
        
        this.logger.log(`Parsed email: ${email.subject} from ${email.from} (Hash: ${uniqueHash}, UID: ${attributes?.uid}, Tenant: ${this.currentTenantId || 'fallback'}, Attachments: ${savedAttachments.length}, AI RPA: ${aiRpaInfo})`);
        
        // Check for duplicates and process atomically
        const processed = await this.processEmailAtomically(email, attributes, seqno);
        
        if (processed) {
          let aiRpaStatus = '';
          if (email.isAiRpa) {
            const linkCount = email.clickHereLinks?.length || 0;
            const passwordCount = email.clickHereLinks?.filter(link => link.password).length || 0;
            aiRpaStatus = ` [AI RPA DETECTED: ${linkCount} link(s)${passwordCount > 0 ? `, ${passwordCount} with password(s)` : ''}]`;
          }
          this.logger.log(`Email processed successfully: ${email.subject} with ${savedAttachments.length} attachment(s)${aiRpaStatus}`);
        }
        
      } catch (error) {
        this.logger.error(`Error processing message ${seqno}:`, error);
        // Still try to mark as seen to prevent reprocessing
        if (attributes?.uid) {
          this.markMessageAsReadWithRetry(attributes.uid, seqno);
        }
      }
    });
  }

  /**
   * Generate a unique hash for the email to prevent duplicates
   */
  private generateUniqueHash(
    from: string,
    subject: string,
    date: Date,
    messageId: string | null,
    rawContent: string
  ): string {
    // Primary: Use Message-ID if available
    if (messageId) {
      return crypto.createHash('sha256').update(messageId).digest('hex');
    }

    // Fallback: Create hash from multiple fields
    const normalizedFrom = from.toLowerCase().trim();
    const normalizedSubject = subject.toLowerCase().trim();
    const dateStr = date.toISOString();
    
    // Include a portion of the raw content for uniqueness
    const contentHash = crypto.createHash('md5').update(rawContent).digest('hex').substring(0, 8);
    
    const combinedString = `${normalizedFrom}|${normalizedSubject}|${dateStr}|${contentHash}`;
    return crypto.createHash('sha256').update(combinedString).digest('hex');
  }

  /**
   * Detect AI RPA emails by finding ALL links and storing them in clickHereLinks array
   */
  private detectAiRpaEmail(emailBody: string): { isAiRpa: boolean; clickHereUrl?: string; clickHereLinks?: ClickHereLink[] } {
    if (!emailBody) {
      return { isAiRpa: false };
    }

    const bodyText = emailBody.toLowerCase();
    const clickHereLinks: ClickHereLink[] = [];
    const foundUrls = new Set<string>(); // Track unique URLs to avoid duplicates
    let firstUrl: string | undefined;
    
    // Check if email contains "click here" text
    const hasClickHereText = bodyText.includes('click here');
    
    // Check if email contains any href links
    const hrefLinkRegex = /<a[^>]+href=['"](https?:\/\/[^'"]+)['"][^>]*>/gi;
    const hasHrefLinks = hrefLinkRegex.test(emailBody);
    
    // Enhanced check for any URLs including complex S3 URLs with query parameters
    const enhancedUrlRegex = /(https?:\/\/[^\s\n\r<>"'\[\]{}|\\^`]+(?:\?[^\s\n\r<>"'\[\]{}|\\^`]*)?(?:#[^\s\n\r<>"'\[\]{}|\\^`]*)?|(?:www\.)?[a-zA-Z0-9-]+\.[a-zA-Z]{2,}(?:\/[^\s<>"']*)?)/gi;
    const hasDirectUrls = enhancedUrlRegex.test(emailBody);
    
    // If no links are found at all, return false
    if (!hasClickHereText && !hasHrefLinks && !hasDirectUrls) {
      return { isAiRpa: false };
    }

    this.logger.log(`AI RPA email detected: ${hasClickHereText ? 'contains "click here" text' : ''} ${hasClickHereText && (hasHrefLinks || hasDirectUrls) ? 'and ' : ''}${hasHrefLinks ? 'contains href links' : ''}${hasDirectUrls ? 'contains direct URLs' : ''}`);

    // Reset regex lastIndex since we used test() above
    hrefLinkRegex.lastIndex = 0;
    enhancedUrlRegex.lastIndex = 0;

    // Pattern 1: HTML links with anchor tags and content
    const allHrefRegex = /<a[^>]+href=['"](https?:\/\/[^'"]+)['"][^>]*>([^<]*(?:<[^/][^<]*>[^<]*)*[^<]*)<\/a>/gi;
    let match;
    
    while ((match = allHrefRegex.exec(emailBody)) !== null) {
      const url = match[1];
      
      if (!foundUrls.has(url)) {
        foundUrls.add(url);
        const linkText = match[2] ? match[2].replace(/<[^>]*>/g, '').trim() : 'link';
        const isClickHereLink = linkText.toLowerCase().includes('click here');
        const password = this.extractPasswordNearLink(emailBody, url, match.index);
        
        clickHereLinks.push({
          url,
          password,
          linkText: linkText || 'link',
          context: this.extractContextAroundMatch(emailBody, match.index, match[0].length)
        });

        if (!firstUrl) {
          firstUrl = url;
        }
        
        this.logger.log(`AI RPA HTML link found: ${url}${password ? ` with password: ${password}` : ''} ${isClickHereLink ? '(click here link)' : ''}`);
      }
    }

    // Pattern 2: Simple href links without closing tags or malformed HTML
    const simpleHrefRegex = /<a[^>]+href=['"](https?:\/\/[^'"]+)['"][^>]*>/gi;
    
    while ((match = simpleHrefRegex.exec(emailBody)) !== null) {
      const url = match[1];
      
      if (!foundUrls.has(url)) {
        foundUrls.add(url);
        const password = this.extractPasswordNearLink(emailBody, url, match.index);
        
        clickHereLinks.push({
          url,
          password,
          linkText: 'link',
          context: this.extractContextAroundMatch(emailBody, match.index, match[0].length)
        });

        if (!firstUrl) {
          firstUrl = url;
        }
        
        this.logger.log(`AI RPA HTML link (simple) found: ${url}${password ? ` with password: ${password}` : ''}`);
      }
    }

    // Pattern 3: Aggressive href extraction (fallback)
    const aggressiveHrefRegex = /href=['"](https?:\/\/[^'"]+)['"]/gi;
    
    while ((match = aggressiveHrefRegex.exec(emailBody)) !== null) {
      const url = match[1];
      
      if (!foundUrls.has(url)) {
        foundUrls.add(url);
        const password = this.extractPasswordNearLink(emailBody, url, match.index);
        
        clickHereLinks.push({
          url,
          password,
          linkText: 'link',
          context: this.extractContextAroundMatch(emailBody, match.index, match[0].length)
        });

        if (!firstUrl) {
          firstUrl = url;
        }
        
        this.logger.log(`AI RPA aggressive href found: ${url}${password ? ` with password: ${password}` : ''}`);
      }
    }

    // Pattern 4: Enhanced direct URL extraction (supports complex S3 URLs with query parameters)
    const complexUrlRegex = /(https?:\/\/[^\s\n\r<>"'\[\]{}|\\^`]+(?:\?[^\s\n\r<>"'\[\]{}|\\^`]*)?(?:#[^\s\n\r<>"'\[\]{}|\\^`]*)?)/gi;
    
    while ((match = complexUrlRegex.exec(emailBody)) !== null) {
      let url = match[1];
      
      // Clean up URL (remove trailing punctuation that might not be part of URL, but preserve query params)
      const cleanUrl = this.cleanComplexUrl(url);
      
      // Validate if it's a proper URL
      if (!this.isValidComplexUrl(cleanUrl)) {
        continue;
      }
      
      if (!foundUrls.has(cleanUrl)) {
        foundUrls.add(cleanUrl);
        const password = this.extractPasswordNearLink(emailBody, cleanUrl, match.index);
        
        const linkType = this.determineUrlType(cleanUrl);
        
        clickHereLinks.push({
          url: cleanUrl,
          password,
          linkText: linkType,
          context: this.extractContextAroundMatch(emailBody, match.index, match[0].length)
        });

        if (!firstUrl) {
          firstUrl = cleanUrl;
        }
        
        this.logger.log(`AI RPA ${linkType} found: ${cleanUrl}${password ? ` with password: ${password}` : ''}`);
      }
    }

    // Pattern 5: Simple domain extraction (for basic domains like google.com)
    const simpleDomainRegex = /(?:^|\s)((?:www\.)?[a-zA-Z0-9-]+\.[a-zA-Z]{2,}(?:\/[^\s<>"']*)?)/gi;
    
    while ((match = simpleDomainRegex.exec(emailBody)) !== null) {
      let domain = match[1];
      
      // Skip if it looks like an email address
      if (emailBody.charAt(match.index - 1) === '@') {
        continue;
      }
      
      const cleanDomain = domain.replace(/[.,;:!?)\]}]+$/, '');
      let normalizedUrl = cleanDomain;
      
      // Add protocol if missing
      if (!cleanDomain.startsWith('http://') && !cleanDomain.startsWith('https://')) {
        normalizedUrl = 'https://' + cleanDomain;
      }
      
      if (this.isValidComplexUrl(normalizedUrl) && !foundUrls.has(normalizedUrl)) {
        foundUrls.add(normalizedUrl);
        const password = this.extractPasswordNearLink(emailBody, cleanDomain, match.index);
        
        clickHereLinks.push({
          url: normalizedUrl,
          password,
          linkText: 'domain link',
          context: this.extractContextAroundMatch(emailBody, match.index, match[0].length)
        });

        if (!firstUrl) {
          firstUrl = normalizedUrl;
        }
        
        this.logger.log(`AI RPA domain link found: ${normalizedUrl}${password ? ` with password: ${password}` : ''}`);
      }
    }

    // Only proceed with text-based patterns if we found "click here" text
    if (hasClickHereText) {
      // Pattern 6: Text with "click here" followed by URL in angle brackets
      const textLinkRegex = /click\s+here\s*[^\w]*\s*<(https?:\/\/[^>]+)>/gi;
      
      while ((match = textLinkRegex.exec(emailBody)) !== null) {
        const url = match[1];
        
        if (!foundUrls.has(url)) {
          foundUrls.add(url);
          const password = this.extractPasswordNearLink(emailBody, url, match.index);
          
          clickHereLinks.push({
            url,
            password,
            linkText: 'click here',
            context: this.extractContextAroundMatch(emailBody, match.index, match[0].length)
          });

          if (!firstUrl) {
            firstUrl = url;
          }
          
          this.logger.log(`AI RPA text link found: ${url}${password ? ` with password: ${password}` : ''}`);
        }
      }

      // Pattern 7: "click here" followed by a URL on the same line or next line
      const proximityLinkRegex = /click\s+here.*?\n?\s*(https?:\/\/[^\s\n]+)/gi;
      
      while ((match = proximityLinkRegex.exec(emailBody)) !== null) {
        const url = match[1];
        const cleanUrl = this.cleanComplexUrl(url);
        
        if (this.isValidComplexUrl(cleanUrl) && !foundUrls.has(cleanUrl)) {
          foundUrls.add(cleanUrl);
          const password = this.extractPasswordNearLink(emailBody, cleanUrl, match.index);
          
          clickHereLinks.push({
            url: cleanUrl,
            password,
            linkText: 'click here',
            context: this.extractContextAroundMatch(emailBody, match.index, match[0].length)
          });

          if (!firstUrl) {
            firstUrl = cleanUrl;
          }
          
          this.logger.log(`AI RPA proximity link found: ${cleanUrl}${password ? ` with password: ${password}` : ''}`);
        }
      }

      // Pattern 8: Find "click here" instances and look for nearby URLs
      const clickHereRegex = /click\s+here/gi;
      const allUrls = this.extractAllUrls(emailBody); // Use existing method name
      
      while ((match = clickHereRegex.exec(emailBody)) !== null) {
        const clickHerePosition = match.index;
        
        // Find the closest URL to this "click here" instance
        const nearbyUrl = this.findClosestUrl(emailBody, clickHerePosition, allUrls);
        
        if (nearbyUrl && !foundUrls.has(nearbyUrl.url)) {
          foundUrls.add(nearbyUrl.url);
          const password = this.extractPasswordNearLink(emailBody, nearbyUrl.url, clickHerePosition);
          
          clickHereLinks.push({
            url: nearbyUrl.url,
            password,
            linkText: 'click here',
            context: this.extractContextAroundMatch(emailBody, clickHerePosition, match[0].length)
          });

          if (!firstUrl) {
            firstUrl = nearbyUrl.url;
          }
          
          this.logger.log(`AI RPA nearby link found: ${nearbyUrl.url}${password ? ` with password: ${password}` : ''}`);
        }
      }
    }

    if (clickHereLinks.length === 0) {
      // Found potential links but no URLs extracted
      this.logger.warn('AI RPA email detected but no URLs could be extracted');
      return { isAiRpa: true };
    }

    this.logger.log(`AI RPA email processed: found ${clickHereLinks.length} link(s) total`);
    
    return { 
      isAiRpa: true, 
      clickHereUrl: firstUrl, // For backward compatibility
      clickHereLinks 
    };
  }

  /**
   * Helper method to clean complex URLs while preserving query parameters
   */
  private cleanComplexUrl(url: string): string {
    // Remove trailing punctuation that's clearly not part of the URL
    return url.replace(/[.,;:!?)\]}]+$/, '');
  }

  /**
   * Enhanced URL validation for complex URLs including S3 presigned URLs
   */
  private isValidComplexUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      // Check if it has a valid protocol and host
      return (urlObj.protocol === 'http:' || urlObj.protocol === 'https:') && 
             urlObj.hostname.length > 0 &&
             (urlObj.hostname.includes('.') || urlObj.hostname === 'localhost');
    } catch (error) {
      return false;
    }
  }

  /**
   * Determine the type of URL for better categorization
   */
  private determineUrlType(url: string): string {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname.toLowerCase();
      
      if (hostname.includes('s3') || hostname.includes('amazonaws')) {
        return 'S3 link';
      } else if (hostname.includes('google')) {
        return 'Google link';
      } else if (hostname.includes('court') || hostname.includes('legal')) {
        return 'Court link';
      } else if (urlObj.search.length > 0) {
        return 'dynamic link';
      } else {
        return 'direct link';
      }
    } catch (error) {
      return 'direct link';
    }
  }

  /**
   * Enhanced method to extract all URLs including complex ones with query parameters
   * (Replace the existing extractAllUrls method)
   */
  private extractAllUrls(emailBody: string): Array<{ url: string; position: number }> {
    const complexUrlRegex = /(https?:\/\/[^\s\n\r<>"'\[\]{}|\\^`]+(?:\?[^\s\n\r<>"'\[\]{}|\\^`]*)?(?:#[^\s\n\r<>"'\[\]{}|\\^`]*)?)/gi;
    const urls: Array<{ url: string; position: number }> = [];
    let match;
    
    while ((match = complexUrlRegex.exec(emailBody)) !== null) {
      const cleanUrl = this.cleanComplexUrl(match[1]);
      
      if (this.isValidComplexUrl(cleanUrl)) {
        urls.push({
          url: cleanUrl,
          position: match.index
        });
      }
    }
    
    // Also include simple domains
    const simpleDomainRegex = /(?:^|\s)((?:www\.)?[a-zA-Z0-9-]+\.[a-zA-Z]{2,}(?:\/[^\s<>"']*)?)/gi;
    
    while ((match = simpleDomainRegex.exec(emailBody)) !== null) {
      const domain = match[1];
      const cleanDomain = domain.replace(/[.,;:!?)\]}]+$/, '');
      let normalizedUrl = cleanDomain;
      
      if (!cleanDomain.startsWith('http://') && !cleanDomain.startsWith('https://')) {
        normalizedUrl = 'https://' + cleanDomain;
      }
      
      if (this.isValidComplexUrl(normalizedUrl)) {
        urls.push({
          url: normalizedUrl,
          position: match.index
        });
      }
    }
    
    return urls;
  }

  /**
   * Extract password from text near a specific link or "click here" instance
   */
  private extractPasswordNearLink(emailBody: string, url: string, searchPosition: number): string | undefined {
    // Define the search radius around the link/click here position
    const searchRadius = 500; // characters before and after
    const startPos = Math.max(0, searchPosition - searchRadius);
    const endPos = Math.min(emailBody.length, searchPosition + searchRadius);
    const searchText = emailBody.substring(startPos, endPos);

    // Common password patterns
    const passwordPatterns = [
      // "Password: 123456" or "password: 123456"
      /(?:password|pwd|pass)\s*[:=]\s*([^\s\n\r,;.]+)/gi,
      // "Password is 123456"
      /(?:password|pwd|pass)\s+(?:is|=)\s+([^\s\n\r,;.]+)/gi,
      // "Use password 123456"
      /(?:use|with)\s+(?:password|pwd|pass)\s+([^\s\n\r,;.]+)/gi,
      // "Access code: 123456"
      /(?:access\s+code|code)\s*[:=]\s*([^\s\n\r,;.]+)/gi,
      // "PIN: 123456"
      /pin\s*[:=]\s*([^\s\n\r,;.]+)/gi,
      // In parentheses or brackets: "(password: 123456)" or "[pass: 123456]"
      /[\(\[](?:password|pwd|pass)\s*[:=]\s*([^\s\)\],;.]+)[\)\]]/gi,
      // "Password 123456" (without colon)
      /(?:password|pwd|pass)\s+([A-Za-z0-9]{4,})/gi
    ];

    for (const pattern of passwordPatterns) {
      pattern.lastIndex = 0; // Reset regex
      const match = pattern.exec(searchText);
      if (match && match[1]) {
        const password = match[1].trim();
        // Validate password format (at least 3 characters, alphanumeric)
        if (password.length >= 3 && /^[A-Za-z0-9@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~`!]+$/.test(password)) {
          this.logger.log(`Password extracted for link ${url}: ${password}`);
          return password;
        }
      }
    }

    // Look for standalone alphanumeric strings that might be passwords
    const standalonePasswordRegex = /\b([A-Za-z0-9]{6,})\b/g;
    const potentialPasswords = [];
    let passwordMatch;
    
    while ((passwordMatch = standalonePasswordRegex.exec(searchText)) !== null) {
      const candidate = passwordMatch[1];
      // Skip common words that are not passwords
      if (!/^(click|here|link|email|message|notification|court|document|view|access|http|https|www|com|org|net)$/i.test(candidate)) {
        potentialPasswords.push(candidate);
      }
    }

    if (potentialPasswords.length > 0) {
      const selectedPassword = potentialPasswords[0]; // Take the first potential password
      this.logger.log(`Potential password extracted for link ${url}: ${selectedPassword}`);
      return selectedPassword;
    }

    return undefined;
  }

  /**
   * Extract context around a match position
   */
  private extractContextAroundMatch(emailBody: string, matchIndex: number, matchLength: number): string {
    const contextRadius = 100;
    const startPos = Math.max(0, matchIndex - contextRadius);
    const endPos = Math.min(emailBody.length, matchIndex + matchLength + contextRadius);
    
    return emailBody.substring(startPos, endPos).trim();
  }

  /**
   * Process email atomically - check for duplicates and save/mark as seen together
   */
  private async processEmailAtomically(
    email: ParsedEmail,
    attributes: any,
    seqno: number
  ): Promise<boolean> {
    try {
      // Check if email already exists
      const existingEmail = await this.emailModel.findOne({ 
        uniqueHash: email.uniqueHash
      });

      if (existingEmail) {
        this.logger.log(`Duplicate email detected: ${email.subject} (Hash: ${email.uniqueHash})`);
        
        // Mark as seen to prevent reprocessing - use UID if available
        if (attributes?.uid) {
          await this.markMessageAsReadWithRetry(attributes.uid, seqno);
        }
        
        // Update existing record if it wasn't marked as seen before
        if (!existingEmail.isMarkedAsSeen) {
          await this.emailModel.updateOne(
            { _id: existingEmail._id },
            { $set: { isMarkedAsSeen: true, seqno: seqno, uid: attributes?.uid } }
          );
        }
        
        return false;
      }

      // Save email to MongoDB
      const saved = await this.saveEmailToMongoDB(email, attributes);
      
      if (saved) {
        // Send email data to SQS queue after successful database save
        try {
          await this.sendEmailToSQS(email);
        } catch (sqsError) {
          this.logger.error(`Failed to send email to SQS queue: ${sqsError.message}`, sqsError);
          // Continue processing even if SQS fails - email is already saved
        }

        // Mark message as read only after successful save - use UID if available
        const shouldMarkAsSeen = this.currentImapConfig?.enableMarkAsSeen !== false;
        
        if (shouldMarkAsSeen) {
          const marked = attributes?.uid 
            ? await this.markMessageAsReadWithRetry(attributes.uid, seqno)
            : await this.markMessageAsReadAsync(seqno);
          
          if (marked) {
            // Update the saved email to reflect it's been marked as seen
            await this.emailModel.updateOne(
              { uniqueHash: email.uniqueHash },
              { $set: { isMarkedAsSeen: true } }
            );
          }
        }
        
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error(`Error in atomic email processing: ${error.message}`, error);
      return false;
    }
  }

  /**
   * Send email data to SQS queue
   */
  private async sendEmailToSQS(email: ParsedEmail): Promise<void> {
    try {
      // Get queue URL for the current tenant
      const queueUrl = await this.imapConfigService.getQueueUrlWithFallback(this.currentTenantId);
      
      if (!queueUrl) {
        this.logger.warn(`No SQS queue URL configured for tenant: ${this.currentTenantId || 'fallback'}`);
        return;
      }

      // Get the full email document from database to include all metadata
      const emailDoc = await this.emailModel.findOne({ uniqueHash: email.uniqueHash }).lean();
      
      if (!emailDoc) {
        this.logger.error(`Email document not found for uniqueHash: ${email.uniqueHash}`);
        return;
      }

      // Prepare the message payload
      const messagePayload = {
        subject: emailDoc.subject,
        from: emailDoc.from,
        body: emailDoc.body,
        receivedAt: emailDoc.receivedAt,
        messageId: emailDoc.messageId,
        uniqueHash: emailDoc.uniqueHash,
        uid: emailDoc.uid,
        seqno: emailDoc.seqno,
        tenantId: emailDoc.tenantId,
        processedAt: emailDoc.processedAt,
        isProcessed: emailDoc.isProcessed,
        isMarkedAsSeen: emailDoc.isMarkedAsSeen,
        errorMessage: emailDoc.errorMessage,
        isAiRpa: emailDoc.isAiRpa || false,
        clickHereUrl: emailDoc.clickHereUrl,
        clickHereLinks: emailDoc.clickHereLinks || [],
        metadata: emailDoc.metadata,
        createdAt: (emailDoc as any).createdAt,
        updatedAt: (emailDoc as any).updatedAt,
        __v: emailDoc.__v
      };

      // Prepare message attributes for SQS
      const messageAttributes = {
        tenantId: {
          DataType: 'String',
          StringValue: this.currentTenantId || 'fallback'
        },
        messageType: {
          DataType: 'String',
          StringValue: 'email_processed'
        },
        uniqueHash: {
          DataType: 'String',
          StringValue: email.uniqueHash
        },
        hasAttachments: {
          DataType: 'String',
          StringValue: emailDoc.metadata?.hasAttachments ? 'true' : 'false'
        },
        isAiRpa: {
          DataType: 'String',
          StringValue: emailDoc.isAiRpa ? 'true' : 'false'
        }
      };

      // Send message to SQS
      const messageId = await this.sqsService.sendMessage(
        queueUrl,
        messagePayload,
        messageAttributes
      );

      this.logger.log(`✅ Email successfully sent to SQS queue: ${queueUrl}`);
      this.logger.log(`📨 SQS Message ID: ${messageId}`);
      this.logger.log(`📧 Email: ${email.subject} (Hash: ${email.uniqueHash}, Tenant: ${this.currentTenantId || 'fallback'})`);

    } catch (error) {
      this.logger.error(`❌ Failed to send email to SQS queue:`, error);
      throw error;
    }
  }

  private async saveEmailToMongoDB(email: ParsedEmail, attributes?: any): Promise<boolean> {
    try {
      const emailDoc = new this.emailModel({
        subject: email.subject,
        from: email.from,
        body: email.body,
        receivedAt: email.receivedAt,
        messageId: email.messageId,
        uniqueHash: email.uniqueHash,
        uid: email.uid,
        seqno: email.seqno,
        processedAt: new Date(),
        isProcessed: false,
        isMarkedAsSeen: false,
        tenantId: email.tenantId,
        isAiRpa: email.isAiRpa || false,
        clickHereUrl: email.clickHereUrl,
        clickHereLinks: email.clickHereLinks || [],
        metadata: {
          attributes: attributes,
          headers: email.headers,
          bodySize: email.body.length,
          hasAttachments: (email.attachments && email.attachments.length > 0) || false,
          attachmentCount: email.attachments ? email.attachments.length : 0,
          attachments: email.attachments || [],
          totalAttachmentSize: email.attachments ? email.attachments.reduce((sum, att) => sum + att.size, 0) : 0,
          tenantId: email.tenantId,
          aiRpaLinks: email.clickHereLinks || [],
          aiRpaLinkCount: email.clickHereLinks?.length || 0,
          aiRpaLinksWithPasswords: email.clickHereLinks?.filter(link => link.password).length || 0,
        },
      });

      const savedEmail = await emailDoc.save();
      this.logger.log(`Email saved to MongoDB with ID: ${savedEmail._id} for tenant: ${email.tenantId || 'fallback'}`);
      
      // Update processed status
      await this.emailModel.updateOne(
        { _id: savedEmail._id },
        { $set: { isProcessed: true } }
      );
      
      return true;
      
    } catch (error) {
      this.logger.error('Error saving email to MongoDB:', error);
      
      // Check if it's a duplicate key error
      if (error.code === 11000) {
        this.logger.warn('Duplicate email detected during save operation');
        return false;
      }
      
      // Try to save error information
      try {
        const errorDoc = new this.emailModel({
          subject: email.subject,
          from: email.from,
          body: email.body,
          receivedAt: email.receivedAt,
          messageId: email.messageId,
          uniqueHash: email.uniqueHash + '_error_' + Date.now(), // Avoid unique constraint
          uid: email.uid,
          seqno: email.seqno,
          processedAt: new Date(),
          isProcessed: false,
          isMarkedAsSeen: false,
          tenantId: email.tenantId,
          isAiRpa: email.isAiRpa || false,
          clickHereUrl: email.clickHereUrl,
          clickHereLinks: email.clickHereLinks || [],
          errorMessage: error.message || 'Unknown error occurred',
          metadata: {
            error: true,
            errorDetails: error.stack,
            originalHash: email.uniqueHash,
            tenantId: email.tenantId,
            aiRpaLinks: email.clickHereLinks || [],
            aiRpaLinkCount: email.clickHereLinks?.length || 0,
            aiRpaLinksWithPasswords: email.clickHereLinks?.filter(link => link.password).length || 0,
          },
        });
        
        await errorDoc.save();
        this.logger.log('Error email record saved to MongoDB');
      } catch (saveError) {
        this.logger.error('Failed to save error email record:', saveError);
      }
      
      return false;
    }
  }

  /**
   * Mark message as read with retry logic - preferred method using UID
   */
  private async markMessageAsReadWithRetry(uid: number, seqno: number, retryCount: number = 0): Promise<boolean> {
    if (retryCount >= this.markAsSeenRetryLimit) {
      this.logger.error(`Failed to mark message as read after ${this.markAsSeenRetryLimit} attempts (UID: ${uid})`);
      return false;
    }

    try {
      // Ensure mailbox is open before attempting to mark as read
      const mailboxReady = await this.ensureMailboxOpen();
      if (!mailboxReady) {
        this.logger.warn(`Mailbox not ready, cannot mark message as read (UID: ${uid})`);
        return false;
      }

      return new Promise((resolve) => {
        // Use UID-based operation which is more reliable
        this.imap.addFlags(uid, ['\\Seen'], (err) => {
          if (err) {
            this.logger.error(`Error marking message as read (UID: ${uid}, attempt ${retryCount + 1}):`, err);
            
            // Retry with exponential backoff
            setTimeout(() => {
              this.markMessageAsReadWithRetry(uid, seqno, retryCount + 1).then(resolve);
            }, 1000 * Math.pow(2, retryCount));
          } else {
            this.logger.log(`Message marked as read successfully (UID: ${uid})`);
            resolve(true);
          }
        });
      });
    } catch (error) {
      this.logger.error(`Exception marking message as read (UID: ${uid}):`, error);
      
      // Retry after a delay
      await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, retryCount)));
      return this.markMessageAsReadWithRetry(uid, seqno, retryCount + 1);
    }
  }

  /**
   * Async version of markMessageAsRead with better error handling - fallback using sequence number
   */
  private async markMessageAsReadAsync(seqno: number): Promise<boolean> {
    try {
      // Ensure mailbox is open before attempting to mark as read
      const mailboxReady = await this.ensureMailboxOpen();
      if (!mailboxReady) {
        this.logger.warn(`Mailbox not ready, cannot mark message as read (seqno: ${seqno})`);
        return false;
      }

      return new Promise((resolve) => {
        this.imap.addFlags(seqno, ['\\Seen'], (err) => {
          if (err) {
            this.logger.error(`Error marking message ${seqno} as read:`, err);
            resolve(false);
          } else {
            this.logger.log(`Message ${seqno} marked as read`);
            resolve(true);
          }
        });
      });
    } catch (error) {
      this.logger.error(`Exception marking message ${seqno} as read:`, error);
      return false;
    }
  }

  private async sendEmailToApiGateway(email: ParsedEmail): Promise<void> {
    // try {
    //   this.logger.log(`Sending email to API Gateway: ${email.subject}`);
      
    //   // const result = await this.grpcClientService.handleNewEmail({
    //   //   subject: email.subject,
    //   //   from: email.from,
    //   //   body: email.body,
    //   //   receivedAt: email.receivedAt,
    //   // });

    //   if (result.success) {
    //     this.logger.log(`Successfully sent email to API Gateway: ${result.message}`);
    //   } else {
    //     this.logger.warn(`Failed to send email to API Gateway: ${result.message}`);
    //   }
    // } catch (error) {
    //   this.logger.error('Error sending email to API Gateway:', error);
    // }
  }

  private markMessageAsRead(seqno: number): void {
    this.ensureMailboxOpen().then(mailboxReady => {
      if (!mailboxReady) {
        this.logger.warn(`Mailbox not ready, cannot mark message as read (seqno: ${seqno})`);
        return;
      }

      this.imap.addFlags(seqno, ['\\Seen'], (err) => {
        if (err) {
          this.logger.error(`Error marking message ${seqno} as read:`, err);
        } else {
          this.logger.log(`Message ${seqno} marked as read`);
        }
      });
    });
  }

  // Public method to get connection status
  getStatus(): { 
    connected: boolean; 
    reconnectAttempts: number; 
    mailboxOpen: boolean; 
    currentTenant: string | null;
    hasImapConfig: boolean;
  } {
    return {
      connected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      mailboxOpen: this.isMailboxOpen,
      currentTenant: this.currentTenantId,
      hasImapConfig: this.currentImapConfig !== null,
    };
  }

  // Public method to get email statistics from MongoDB
  async getEmailStats(): Promise<any> {
    try {
      const total = await this.emailModel.countDocuments();
      const processed = await this.emailModel.countDocuments({ isProcessed: true });
      const markedAsSeen = await this.emailModel.countDocuments({ isMarkedAsSeen: true });
      const errors = await this.emailModel.countDocuments({ errorMessage: { $ne: null } });
      const aiRpaEmails = await this.emailModel.countDocuments({ isAiRpa: true });
      const aiRpaWithUrls = await this.emailModel.countDocuments({ isAiRpa: true, clickHereUrl: { $ne: null } });
      const aiRpaWithMultipleLinks = await this.emailModel.countDocuments({ isAiRpa: true, 'clickHereLinks.1': { $exists: true } });
      const aiRpaWithPasswords = await this.emailModel.countDocuments({ isAiRpa: true, 'clickHereLinks.password': { $exists: true, $ne: null } });
      
      // Tenant-specific stats if current tenant is set
      const tenantStats = this.currentTenantId ? {
        totalForTenant: await this.emailModel.countDocuments({ tenantId: this.currentTenantId }),
        processedForTenant: await this.emailModel.countDocuments({ tenantId: this.currentTenantId, isProcessed: true }),
        errorsForTenant: await this.emailModel.countDocuments({ tenantId: this.currentTenantId, errorMessage: { $ne: null } }),
        aiRpaForTenant: await this.emailModel.countDocuments({ tenantId: this.currentTenantId, isAiRpa: true }),
        aiRpaWithUrlsForTenant: await this.emailModel.countDocuments({ tenantId: this.currentTenantId, isAiRpa: true, clickHereUrl: { $ne: null } }),
        aiRpaWithMultipleLinksForTenant: await this.emailModel.countDocuments({ tenantId: this.currentTenantId, isAiRpa: true, 'clickHereLinks.1': { $exists: true } }),
        aiRpaWithPasswordsForTenant: await this.emailModel.countDocuments({ tenantId: this.currentTenantId, isAiRpa: true, 'clickHereLinks.password': { $exists: true, $ne: null } }),
      } : {};
      
      return {
        total,
        processed,
        markedAsSeen,
        errors,
        pending: total - processed,
        duplicatesPreventedByHash: await this.emailModel.countDocuments({ uniqueHash: { $ne: null } }),
        aiRpaEmails,
        aiRpaWithUrls,
        aiRpaWithMultipleLinks,
        aiRpaWithPasswords,
        aiRpaPercentage: total > 0 ? Math.round((aiRpaEmails / total) * 100 * 100) / 100 : 0,
        aiRpaMultipleLinkPercentage: aiRpaEmails > 0 ? Math.round((aiRpaWithMultipleLinks / aiRpaEmails) * 100 * 100) / 100 : 0,
        aiRpaPasswordPercentage: aiRpaEmails > 0 ? Math.round((aiRpaWithPasswords / aiRpaEmails) * 100 * 100) / 100 : 0,
        currentTenant: this.currentTenantId,
        ...tenantStats,
      };
    } catch (error) {
      this.logger.error('Error getting email stats:', error);
      return { 
        total: 0, 
        processed: 0, 
        markedAsSeen: 0, 
        errors: 0, 
        pending: 0, 
        duplicatesPreventedByHash: 0,
        aiRpaEmails: 0,
        aiRpaWithUrls: 0,
        aiRpaWithMultipleLinks: 0,
        aiRpaWithPasswords: 0,
        aiRpaPercentage: 0,
        aiRpaMultipleLinkPercentage: 0,
        aiRpaPasswordPercentage: 0,
        currentTenant: this.currentTenantId,
      };
    }
  }

  // Public method to get recent emails from MongoDB
  async getRecentEmails(limit: number = 10): Promise<EmailDocument[]> {
    try {
      const filter = this.currentTenantId ? { tenantId: this.currentTenantId } : {};
      return await this.emailModel
        .find(filter)
        .sort({ createdAt: -1 })
        .limit(limit)
        .exec();
    } catch (error) {
      this.logger.error('Error getting recent emails:', error);
      return [];
    }
  }

  // Public method to get AI RPA emails from MongoDB
  async getAiRpaEmails(limit: number = 10): Promise<EmailDocument[]> {
    try {
      const filter = {
        isAiRpa: true,
        ...(this.currentTenantId ? { tenantId: this.currentTenantId } : {})
      };
      return await this.emailModel
        .find(filter)
        .sort({ createdAt: -1 })
        .limit(limit)
        .exec();
    } catch (error) {
      this.logger.error('Error getting AI RPA emails:', error);
      return [];
    }
  }

  // Public method to get detailed AI RPA link analysis
  async getAiRpaLinkAnalysis(limit: number = 20): Promise<any> {
    try {
      const filter = {
        isAiRpa: true,
        clickHereLinks: { $exists: true, $ne: [] },
        ...(this.currentTenantId ? { tenantId: this.currentTenantId } : {})
      };

      const emails = await this.emailModel
        .find(filter)
        .select('subject from receivedAt clickHereLinks tenantId')
        .sort({ createdAt: -1 })
        .limit(limit)
        .lean();

      const analysis = {
        totalEmailsAnalyzed: emails.length,
        totalLinks: 0,
        linksWithPasswords: 0,
        uniqueDomains: new Set(),
        passwordPatterns: {},
        linksByEmail: [],
        domainStats: {},
        currentTenant: this.currentTenantId,
      };

      for (const email of emails) {
        const emailLinks = email.clickHereLinks || [];
        analysis.totalLinks += emailLinks.length;

        const emailData = {
          subject: email.subject,
          from: email.from,
          receivedAt: email.receivedAt,
          linkCount: emailLinks.length,
          linksWithPasswords: emailLinks.filter(link => link.password).length,
          links: emailLinks.map(link => ({
            url: link.url,
            hasPassword: !!link.password,
            password: link.password ? '***' : undefined, // Masked for security
            domain: this.extractDomainFromUrl(link.url),
            linkText: link.linkText,
          }))
        };

        analysis.linksByEmail.push(emailData);

        // Analyze domains and passwords
        for (const link of emailLinks) {
          if (link.password) {
            analysis.linksWithPasswords++;
            
            // Analyze password patterns (length and type)
            const passwordType = this.categorizePassword(link.password);
            analysis.passwordPatterns[passwordType] = (analysis.passwordPatterns[passwordType] || 0) + 1;
          }

          const domain = this.extractDomainFromUrl(link.url);
          if (domain) {
            analysis.uniqueDomains.add(domain);
            analysis.domainStats[domain] = (analysis.domainStats[domain] || 0) + 1;
          }
        }
      }

      return {
        ...analysis,
        uniqueDomains: Array.from(analysis.uniqueDomains),
        passwordCoveragePercentage: analysis.totalLinks > 0 ? 
          Math.round((analysis.linksWithPasswords / analysis.totalLinks) * 100 * 100) / 100 : 0,
      };
    } catch (error) {
      this.logger.error('Error getting AI RPA link analysis:', error);
      throw error;
    }
  }

  // Helper method to extract domain from URL
  private extractDomainFromUrl(url: string): string | null {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (error) {
      return null;
    }
  }

  // Helper method to categorize password types
  private categorizePassword(password: string): string {
    if (!password) return 'none';
    
    const length = password.length;
    const hasNumbers = /\d/.test(password);
    const hasLetters = /[a-zA-Z]/.test(password);
    const hasSpecialChars = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~`]/.test(password);
    
    let category = '';
    
    if (length <= 6) category += 'short_';
    else if (length <= 10) category += 'medium_';
    else category += 'long_';
    
    if (hasNumbers && hasLetters && hasSpecialChars) category += 'complex';
    else if (hasNumbers && hasLetters) category += 'alphanumeric';
    else if (hasNumbers) category += 'numeric';
    else if (hasLetters) category += 'alphabetic';
    else category += 'other';
    
    return category;
  }

  // Public method to get emails with multiple AI RPA links
  async getEmailsWithMultipleAiRpaLinks(limit: number = 10): Promise<EmailDocument[]> {
    try {
      const filter = {
        isAiRpa: true,
        'clickHereLinks.1': { $exists: true }, // Has at least 2 links
        ...(this.currentTenantId ? { tenantId: this.currentTenantId } : {})
      };
      
      return await this.emailModel
        .find(filter)
        .sort({ createdAt: -1 })
        .limit(limit)
        .exec();
    } catch (error) {
      this.logger.error('Error getting emails with multiple AI RPA links:', error);
      return [];
    }
  }

  // Public method to get emails with AI RPA links that have passwords
  async getEmailsWithPasswordProtectedLinks(limit: number = 10): Promise<EmailDocument[]> {
    try {
      const filter = {
        isAiRpa: true,
        'clickHereLinks.password': { $exists: true, $ne: null },
        ...(this.currentTenantId ? { tenantId: this.currentTenantId } : {})
      };
      
      return await this.emailModel
        .find(filter)
        .sort({ createdAt: -1 })
        .limit(limit)
        .exec();
    } catch (error) {
      this.logger.error('Error getting emails with password-protected AI RPA links:', error);
      return [];
    }
  }

  // Public method to check for duplicates by hash
  async checkForDuplicates(uniqueHash: string): Promise<EmailDocument | null> {
    try {
      return await this.emailModel.findOne({ uniqueHash });
    } catch (error) {
      this.logger.error('Error checking for duplicates:', error);
      return null;
    }
  }

  // Public method to manually process unseen emails (for testing/debugging)
  async processUnseenEmails(): Promise<void> {
    if (!this.isConnected) {
      this.logger.warn('Not connected to IMAP server');
      return;
    }

    if (!this.isMailboxOpen) {
      this.logger.warn('Mailbox not open, attempting to open...');
      const opened = await this.ensureMailboxOpen();
      if (!opened) {
        this.logger.error('Failed to open mailbox for manual processing');
        return;
      }
    }

    this.logger.log('Manually processing unseen emails...');
    this.fetchNewMessages();
  }

  // Public method to test mark as seen functionality
  async testMarkAsSeen(): Promise<{ success: boolean; message: string }> {
    try {
      if (!this.isConnected) {
        return { success: false, message: 'Not connected to IMAP server' };
      }

      const mailboxReady = await this.ensureMailboxOpen();
      if (!mailboxReady) {
        return { success: false, message: 'Failed to open mailbox' };
      }

      return { success: true, message: 'Mailbox is ready for mark as seen operations' };
    } catch (error) {
      return { success: false, message: `Error testing mark as seen: ${error.message}` };
    }
  }

  // Public method to test AI RPA detection functionality
  async testAiRpaDetection(testEmailBody?: string): Promise<{ success: boolean; result: any; message: string }> {
    try {
      // Default test email body with multiple click here links and passwords
      const defaultTestBody = `
        DC-2025-CR-1169

        State of Texas vs. GONZALEZ MICHAEL

        Arraignment Hearing

        You have received new notifications from the court:

        1. First document - click here to view:
        <https://publicrecords.lubbockcounty.gov/PublicRequestHandlerTXLUBBOCKPROD/2c4eb35440d24b0ab9b5002c78f0fb72>
        Password: ABC123

        2. Second document - <a href="https://courtdocs.example.com/case/12345">click here</a>
        Access code: XYZ789

        3. Additional filing - click here https://filings.court.gov/view/67890
        Use password DEF456 to access

        Please review all documents promptly.
      `;

      const emailBody = testEmailBody || defaultTestBody;
      const result = this.detectAiRpaEmail(emailBody);

      return {
        success: true,
        result: {
          isAiRpa: result.isAiRpa,
          clickHereUrl: result.clickHereUrl, // First URL for backward compatibility
          clickHereLinks: result.clickHereLinks || [],
          linkCount: result.clickHereLinks?.length || 0,
          linksWithPasswords: result.clickHereLinks?.filter(link => link.password).length || 0,
          testEmailBody: emailBody,
          detectionReason: result.isAiRpa ? 'Contains "click here" text' : 'No "click here" text found'
        },
        message: `AI RPA detection test completed: ${result.isAiRpa ? 'DETECTED' : 'NOT DETECTED'} - Found ${result.clickHereLinks?.length || 0} link(s)`
      };
    } catch (error) {
      this.logger.error('Error testing AI RPA detection:', error);
      return {
        success: false,
        result: null,
        message: `Error testing AI RPA detection: ${error.message}`
      };
    }
  }

  // Public method to get current IMAP configuration
  getCurrentImapConfig(): ImapConfig | null {
    return this.currentImapConfig;
  }

  // Public method to reload configuration for current tenant
  async reloadConfiguration(): Promise<void> {
    if (this.currentTenantId) {
      this.logger.log(`Reloading configuration for tenant: ${this.currentTenantId}`);
      await this.switchToTenant(this.currentTenantId);
    } else {
      this.logger.log('Reloading fallback configuration');
      await this.initializeWithFallback();
    }
  }

  // Public method to get attachment statistics
  async getAttachmentStats(): Promise<any> {
    try {
      const totalEmailsWithAttachments = await this.emailModel.countDocuments({ 
        'metadata.hasAttachments': true 
      });
      
      const pipeline = [
        {
          $match: {
            'metadata.hasAttachments': true,
            ...(this.currentTenantId ? { tenantId: this.currentTenantId } : {})
          }
        },
        {
          $group: {
            _id: null,
            totalAttachments: { $sum: '$metadata.attachmentCount' },
            totalSize: { $sum: '$metadata.totalAttachmentSize' },
            avgAttachmentsPerEmail: { $avg: '$metadata.attachmentCount' },
            avgAttachmentSize: { $avg: '$metadata.totalAttachmentSize' }
          }
        }
      ];  

      const aggregateResult = await this.emailModel.aggregate(pipeline);
      const stats = aggregateResult[0] || {
        totalAttachments: 0,
        totalSize: 0,
        avgAttachmentsPerEmail: 0,
        avgAttachmentSize: 0
      };

      return {
        emailsWithAttachments: totalEmailsWithAttachments,
        totalAttachments: stats.totalAttachments,
        totalSizeBytes: stats.totalSize,
        totalSizeMB: Math.round((stats.totalSize / (1024 * 1024)) * 100) / 100,
        avgAttachmentsPerEmail: Math.round(stats.avgAttachmentsPerEmail * 100) / 100,
        avgAttachmentSizeKB: Math.round((stats.avgAttachmentSize / 1024) * 100) / 100,
        currentTenant: this.currentTenantId,
        attachmentBasePath: this.attachmentsBasePath,
      };
    } catch (error) {
      this.logger.error('Error getting attachment stats:', error);
      return {
        emailsWithAttachments: 0,
        totalAttachments: 0,
        totalSizeBytes: 0,
        totalSizeMB: 0,
        avgAttachmentsPerEmail: 0,
        avgAttachmentSizeKB: 0,
        currentTenant: this.currentTenantId,
        attachmentBasePath: this.attachmentsBasePath,
      };
    }
  }

  // Public method to get emails with attachments
  async getEmailsWithAttachments(limit: number = 10): Promise<EmailDocument[]> {
    try {
      const filter = {
        'metadata.hasAttachments': true,
        ...(this.currentTenantId ? { tenantId: this.currentTenantId } : {})
      };
      
      return await this.emailModel
        .find(filter)
        .sort({ createdAt: -1 })
        .limit(limit)
        .exec();
    } catch (error) {
      this.logger.error('Error getting emails with attachments:', error);
      return [];
    }
  }

  // Public method to clean up old attachments (optional utility)
  async cleanupOldAttachments(daysOld: number = 30): Promise<{ deletedFiles: number; errors: string[] }> {
    const deletedFiles: string[] = [];
    const errors: string[] = [];
    
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);
      
      this.logger.log(`Cleaning up attachments older than ${daysOld} days (before ${cutoffDate.toISOString()})`);
      
      const oldEmails = await this.emailModel.find({
        'metadata.hasAttachments': true,
        createdAt: { $lt: cutoffDate },
        ...(this.currentTenantId ? { tenantId: this.currentTenantId } : {})
      });

      for (const email of oldEmails) {
        if (email.metadata?.attachments) {
          for (const attachment of email.metadata.attachments) {
            try {
              if (fs.existsSync(attachment.savedPath)) {
                await fs.promises.unlink(attachment.savedPath);
                deletedFiles.push(attachment.savedPath);
                this.logger.log(`Deleted old attachment: ${attachment.savedPath}`);
              }
            } catch (error) {
              const errorMsg = `Failed to delete ${attachment.savedPath}: ${error.message}`;
              errors.push(errorMsg);
              this.logger.error(errorMsg);
            }
          }
        }
      }

      this.logger.log(`Cleanup completed: ${deletedFiles.length} files deleted, ${errors.length} errors`);
      
      return {
        deletedFiles: deletedFiles.length,
        errors
      };
    } catch (error) {
      this.logger.error('Error during attachment cleanup:', error);
      return {
        deletedFiles: 0,
        errors: [error.message]
      };
    }
  }

  // Public method to set custom attachments base path
  setAttachmentsBasePath(basePath: string): void {
    this.attachmentsBasePath = basePath;
    this.logger.log(`Attachments base path updated to: ${basePath}`);
  }

  // Public method to get S3 information
  async getS3Info(): Promise<any> {
    try {
      return {
        bucketName: this.s3Service.getBucketName(),
        basePrefix: 'attachments/',
        currentTenant: this.currentTenantId,
        supportedOperations: ['upload', 'download', 'delete', 'list'],
        presignedUrlExpiry: '1 hour (default)',
        attachmentPath: `attachments/{tenantId}/{emailId}/{attachmentId}_{filename}`,
      };
    } catch (error) {
      this.logger.error('Error getting S3 info:', error);
      throw error;
    }
  }

  // Public method to generate download URL for specific attachment
  async generateAttachmentDownloadUrl(
    emailId: string, 
    attachmentId: string, 
    expiresIn: number = 3600
  ): Promise<{ url: string; s3Key: string }> {
    try {
      // Find the email with the specific attachment
      const email = await this.emailModel.findOne({
        uniqueHash: emailId,
        'metadata.attachments.attachmentId': attachmentId
      });

      if (!email) {
        throw new Error(`Email with ID ${emailId} not found`);
      }

      const attachment = email.metadata?.attachments?.find(
        (att: any) => att.attachmentId === attachmentId
      );

      if (!attachment || !attachment.s3Key) {
        throw new Error(`Attachment ${attachmentId} not found or not stored in S3`);
      }

      const presignedUrl = await this.s3Service.generatePresignedUrl(
        attachment.s3Key,
        'get',
        expiresIn
      );

      return {
        url: presignedUrl,
        s3Key: attachment.s3Key,
      };
    } catch (error) {
      this.logger.error(`Error generating download URL: ${error.message}`);
      throw error;
    }
  }

  // Public method to delete all attachments for a specific email
  async deleteEmailAttachments(emailId: string): Promise<{ deletedCount: number; errors: string[] }> {
    try {
      const email = await this.emailModel.findOne({ uniqueHash: emailId });
      
      if (!email) {
        throw new Error(`Email with ID ${emailId} not found`);
      }

      const attachments = email.metadata?.attachments || [];
      const errors: string[] = [];
      let deletedCount = 0;

      for (const attachment of attachments) {
        try {
          if (attachment.s3Key) {
            await this.s3Service.deleteFile(attachment.s3Key);
            deletedCount++;
            this.logger.log(`Deleted S3 attachment: ${attachment.s3Key}`);
          }
          
          if (attachment.savedPath && fs.existsSync(attachment.savedPath)) {
            await fs.promises.unlink(attachment.savedPath);
            this.logger.log(`Deleted local attachment: ${attachment.savedPath}`);
          }
        } catch (error) {
          const errorMsg = `Failed to delete attachment ${attachment.attachmentId}: ${error.message}`;
          errors.push(errorMsg);
          this.logger.error(errorMsg);
        }
      }

      // Update email record to reflect deleted attachments
      await this.emailModel.updateOne(
        { uniqueHash: emailId },
        {
          $set: {
            'metadata.hasAttachments': false,
            'metadata.attachmentCount': 0,
            'metadata.attachments': [],
            'metadata.totalAttachmentSize': 0,
          }
        }
      );

      return { deletedCount, errors };
    } catch (error) {
      this.logger.error(`Error deleting email attachments: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get all emails with S3 attachments for a specific tenant
   */
  async getEmailsWithS3Attachments(tenantId?: string, limit: number = 50): Promise<any[]> {
    try {
      const query: any = {
        'metadata.hasAttachments': true,
        'metadata.attachments.storageType': 's3'
      };

      if (tenantId) {
        query.tenantId = tenantId;
      }

      const emails = await this.emailModel
        .find(query)
        .select('subject from receivedAt tenantId metadata.attachments metadata.attachmentCount')
        .sort({ createdAt: -1 })
        .limit(limit)
        .lean();

      return emails;
    } catch (error) {
      this.logger.error(`Error fetching emails with S3 attachments: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get S3 attachment information by attachment ID
   */
  async getS3AttachmentInfo(attachmentId: string): Promise<any> {
    try {
      const email = await this.emailModel
        .findOne({
          'metadata.attachments.attachmentId': attachmentId
        })
        .select('metadata.attachments')
        .lean();

      if (!email) {
        throw new Error(`Attachment with ID ${attachmentId} not found`);
      }

      const attachment = email.metadata.attachments.find(
        (att: any) => att.attachmentId === attachmentId
      );

      return attachment;
    } catch (error) {
      this.logger.error(`Error fetching S3 attachment info: ${error.message}`);
      throw error;
    }
  }

  /**
   * Generate fresh presigned URLs for email attachments
   */
  async regeneratePresignedUrls(emailId: string, expiresIn: number = 3600): Promise<any> {
    try {
      const email = await this.emailModel.findOne({ uniqueHash: emailId });
      
      if (!email) {
        throw new Error(`Email with ID ${emailId} not found`);
      }

      const attachments = email.metadata?.attachments || [];
      const updatedAttachments = [];

      for (const attachment of attachments) {
        if (attachment.s3Key) {
          try {
            // Generate new presigned URL
            const newPresignedUrl = await this.s3Service.generatePresignedUrl(
              attachment.s3Key, 
              'get', 
              expiresIn
            );

            const updatedAttachment = {
              ...attachment,
              s3Url: newPresignedUrl,
              urlGeneratedAt: new Date().toISOString(),
              urlExpiresIn: expiresIn
            };

            updatedAttachments.push(updatedAttachment);
          } catch (error) {
            this.logger.error(`Failed to generate presigned URL for ${attachment.s3Key}: ${error.message}`);
            updatedAttachments.push(attachment); // Keep original if generation fails
          }
        } else {
          updatedAttachments.push(attachment);
        }
      }

      // Update the email document with new presigned URLs
      await this.emailModel.updateOne(
        { uniqueHash: emailId },
        { $set: { 'metadata.attachments': updatedAttachments } }
      );

      return updatedAttachments;
    } catch (error) {
      this.logger.error(`Error regenerating presigned URLs: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get S3 storage statistics for a tenant
   */
  async getS3StorageStats(tenantId?: string): Promise<any> {
    try {
      const matchStage: any = {
        'metadata.hasAttachments': true,
        'metadata.attachments.storageType': 's3'
      };

      if (tenantId) {
        matchStage.tenantId = tenantId;
      }

      const stats = await this.emailModel.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: tenantId ? '$tenantId' : null,
            totalEmails: { $sum: 1 },
            totalAttachments: { $sum: '$metadata.attachmentCount' },
            totalStorageBytes: { $sum: '$metadata.totalAttachmentSize' },
            avgAttachmentsPerEmail: { $avg: '$metadata.attachmentCount' },
            avgStoragePerEmail: { $avg: '$metadata.totalAttachmentSize' }
          }
        }
      ]);

      const result = stats[0] || {
        totalEmails: 0,
        totalAttachments: 0,
        totalStorageBytes: 0,
        avgAttachmentsPerEmail: 0,
        avgStoragePerEmail: 0
      };

      // Convert bytes to human readable format
      result.totalStorageMB = Math.round(result.totalStorageBytes / (1024 * 1024) * 100) / 100;
      result.totalStorageGB = Math.round(result.totalStorageBytes / (1024 * 1024 * 1024) * 100) / 100;

      return result;
    } catch (error) {
      this.logger.error(`Error getting S3 storage stats: ${error.message}`);
      throw error;
    }
  }

  /**
   * Test SQS functionality by sending a test email to queue
   */
  async testSQSIntegration(emailUniqueHash?: string): Promise<{ success: boolean; message: string; messageId?: string }> {
    try {
      let emailToTest: EmailDocument;
      
      if (emailUniqueHash) {
        emailToTest = await this.emailModel.findOne({ uniqueHash: emailUniqueHash });
        if (!emailToTest) {
          return { success: false, message: `Email with hash ${emailUniqueHash} not found` };
        }
      } else {
        // Get the most recent email
        const filter = this.currentTenantId ? { tenantId: this.currentTenantId } : {};
        emailToTest = await this.emailModel.findOne(filter).sort({ createdAt: -1 });
        if (!emailToTest) {
          return { success: false, message: 'No emails found to test with' };
        }
      }

      // Create a ParsedEmail object from the database document
      const testEmail: ParsedEmail = {
        subject: emailToTest.subject,
        from: emailToTest.from,
        body: emailToTest.body,
        receivedAt: emailToTest.receivedAt,
        messageId: emailToTest.messageId,
        uniqueHash: emailToTest.uniqueHash,
        uid: emailToTest.uid,
        seqno: emailToTest.seqno,
        tenantId: emailToTest.tenantId,
        attachments: emailToTest.metadata?.attachments as any[] || []
      };

      // Test SQS sending
      await this.sendEmailToSQS(testEmail);
      
      return { 
        success: true, 
        message: `Successfully sent test email to SQS queue for tenant: ${this.currentTenantId || 'fallback'}`,
        messageId: emailToTest.uniqueHash
      };
    } catch (error) {
      this.logger.error(`SQS test failed: ${error.message}`);
      return { 
        success: false, 
        message: `SQS test failed: ${error.message}`
      };
    }
  }

  /**
   * Get SQS configuration information
   */
  async getSQSInfo(): Promise<any> {
    try {
      const queueUrl = await this.imapConfigService.getQueueUrlWithFallback(this.currentTenantId);
      const fallbackQueueUrl = this.imapConfigService.getFallbackQueueUrl();

      return {
        currentTenant: this.currentTenantId,
        queueUrl: queueUrl,
        fallbackQueueUrl: fallbackQueueUrl,
        hasQueueUrl: !!queueUrl,
        sqsServiceAvailable: !!this.sqsService,
        messageAttributes: [
          'tenantId',
          'messageType',
          'uniqueHash',
          'hasAttachments'
        ],
        messageType: 'email_processed'
      };
    } catch (error) {
      this.logger.error('Error getting SQS info:', error);
      throw error;
    }
  }

  /**
   * Resend specific email to SQS queue (useful for retry scenarios)
   */
  async resendEmailToSQS(emailUniqueHash: string): Promise<{ success: boolean; message: string; messageId?: string }> {
    try {
      const emailDoc = await this.emailModel.findOne({ uniqueHash: emailUniqueHash });
      
      if (!emailDoc) {
        return { success: false, message: `Email with hash ${emailUniqueHash} not found` };
      }

      // Create a ParsedEmail object from the database document
      const email: ParsedEmail = {
        subject: emailDoc.subject,
        from: emailDoc.from,
        body: emailDoc.body,
        receivedAt: emailDoc.receivedAt,
        messageId: emailDoc.messageId,
        uniqueHash: emailDoc.uniqueHash,
        uid: emailDoc.uid,
        seqno: emailDoc.seqno,
        tenantId: emailDoc.tenantId,
        attachments: emailDoc.metadata?.attachments as any[] || []
      };

      await this.sendEmailToSQS(email);
      
      return { 
        success: true, 
        message: `Successfully resent email to SQS queue`,
        messageId: emailUniqueHash
      };
    } catch (error) {
      this.logger.error(`Failed to resend email to SQS: ${error.message}`);
      return { 
        success: false, 
        message: `Failed to resend email to SQS: ${error.message}`
      };
    }
  }

  /**
   * Find the closest URL to a "click here" position (existing method - no changes needed)
   */
  private findClosestUrl(emailBody: string, clickHerePosition: number, allUrls: Array<{ url: string; position: number }>): { url: string; position: number } | undefined {
    if (allUrls.length === 0) {
      return undefined;
    }

    let closestUrl = allUrls[0];
    let minDistance = Math.abs(allUrls[0].position - clickHerePosition);

    for (const urlInfo of allUrls) {
      const distance = Math.abs(urlInfo.position - clickHerePosition);
      if (distance < minDistance) {
        minDistance = distance;
        closestUrl = urlInfo;
      }
    }

    // Only return if the URL is within reasonable proximity (e.g., 1000 characters)
    return minDistance <= 1000 ? closestUrl : undefined;
  }
}