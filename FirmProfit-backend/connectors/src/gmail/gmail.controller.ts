import { <PERSON>, Get, Post, Query, Param, Lo<PERSON>, BadRequestException, Body } from '@nestjs/common';
import { GmailService } from './gmail.service';

@Controller('gmail')
export class GmailController {
  private readonly logger = new Logger(GmailController.name);

  constructor(private readonly gmailService: GmailService) {}

  @Get('status')
  async getStatus() {
    try {
      const status = this.gmailService.getStatus();
      return {
        success: true,
        data: status,
      };
    } catch (error) {
      this.logger.error('Error getting Gmail status:', error);
      throw new BadRequestException('Failed to get Gmail status');
    }
  }

  @Get('stats')
  async getStats() {
    try {
      const stats = await this.gmailService.getEmailStats();
      return {
        success: true,
        data: stats,
      };
    } catch (error) {
      this.logger.error('Error getting Gmail stats:', error);
      throw new BadRequestException('Failed to get Gmail stats');
    }
  }

  @Get('recent')
  async getRecentEmails(@Query('limit') limit?: string) {
    try {
      const emailLimit = limit ? parseInt(limit, 10) : 10;
      const emails = await this.gmailService.getRecentEmails(emailLimit);
      return {
        success: true,
        data: emails,
      };
    } catch (error) {
      this.logger.error('Error getting recent emails:', error);
      throw new BadRequestException('Failed to get recent emails');
    }
  }

  @Get('ai-rpa')
  async getAiRpaEmails(@Query('limit') limit?: string) {
    try {
      const emailLimit = limit ? parseInt(limit, 10) : 10;
      const emails = await this.gmailService.getAiRpaEmails(emailLimit);
      return {
        success: true,
        data: {
          emails,
          count: emails.length,
          message: `Found ${emails.length} AI RPA emails`
        },
      };
    } catch (error) {
      this.logger.error('Error getting AI RPA emails:', error);
      throw new BadRequestException('Failed to get AI RPA emails');
    }
  }

  @Post('process-unseen')
  async processUnseenEmails() {
    try {
      await this.gmailService.processUnseenEmails();
      return {
        success: true,
        message: 'Processing unseen emails initiated',
      };
    } catch (error) {
      this.logger.error('Error processing unseen emails:', error);
      throw new BadRequestException('Failed to process unseen emails');
    }
  }

  @Get('duplicate-check/:hash')
  async checkDuplicate(@Param('hash') hash: string) {
    try {
      const duplicateEmail = await this.gmailService.checkForDuplicates(hash);
      const isDuplicate = !!duplicateEmail;
      return {
        success: true,
        data: {
          hash,
          isDuplicate,
          email: duplicateEmail
        },
      };
    } catch (error) {
      this.logger.error('Error checking duplicate:', error);
      throw new BadRequestException('Failed to check duplicate');
    }
  }

  /**
   * Get all emails with S3 attachments
   */
  @Get('attachments/s3')
  async getEmailsWithS3Attachments(
    @Query('tenantId') tenantId?: string,
    @Query('limit') limit?: string
  ) {
    try {
      const emailLimit = limit ? parseInt(limit, 10) : 50;
      const emails = await this.gmailService.getEmailsWithS3Attachments(tenantId, emailLimit);
      
      return {
        success: true,
        data: {
          emails,
          count: emails.length,
          tenantId: tenantId || 'all'
        },
      };
    } catch (error) {
      this.logger.error('Error getting emails with S3 attachments:', error);
      throw new BadRequestException('Failed to get emails with S3 attachments');
    }
  }

  /**
   * Get S3 attachment information by attachment ID
   */
  @Get('attachments/s3/:attachmentId')
  async getS3AttachmentInfo(@Param('attachmentId') attachmentId: string) {
    try {
      const attachment = await this.gmailService.getS3AttachmentInfo(attachmentId);
      
      return {
        success: true,
        data: attachment,
      };
    } catch (error) {
      this.logger.error('Error getting S3 attachment info:', error);
      throw new BadRequestException(`Failed to get S3 attachment info: ${error.message}`);
    }
  }

  /**
   * Regenerate presigned URLs for email attachments
   */
  @Post('attachments/regenerate-urls/:emailId')
  async regeneratePresignedUrls(
    @Param('emailId') emailId: string,
    @Query('expiresIn') expiresIn?: string
  ) {
    try {
      const expiry = expiresIn ? parseInt(expiresIn, 10) : 3600; // Default 1 hour
      const attachments = await this.gmailService.regeneratePresignedUrls(emailId, expiry);
      
      return {
        success: true,
        data: {
          emailId,
          attachments,
          expiresIn: expiry,
          regeneratedAt: new Date().toISOString()
        },
      };
    } catch (error) {
      this.logger.error('Error regenerating presigned URLs:', error);
      throw new BadRequestException(`Failed to regenerate presigned URLs: ${error.message}`);
    }
  }

  /**
   * Get S3 storage statistics
   */
  @Get('attachments/s3/stats')
  async getS3StorageStats(@Query('tenantId') tenantId?: string) {
    try {
      const stats = await this.gmailService.getS3StorageStats(tenantId);
      
      return {
        success: true,
        data: {
          ...stats,
          tenantId: tenantId || 'all',
          generatedAt: new Date().toISOString()
        },
      };
    } catch (error) {
      this.logger.error('Error getting S3 storage stats:', error);
      throw new BadRequestException('Failed to get S3 storage stats');
    }
  }

  /**
   * Delete email attachments
   */
  @Post('attachments/delete/:emailId')
  async deleteEmailAttachments(@Param('emailId') emailId: string) {
    try {
      const result = await this.gmailService.deleteEmailAttachments(emailId);
      
      return {
        success: true,
        data: {
          emailId,
          deletedCount: result.deletedCount,
          errors: result.errors,
          deletedAt: new Date().toISOString()
        },
      };
    } catch (error) {
      this.logger.error('Error deleting email attachments:', error);
      throw new BadRequestException(`Failed to delete email attachments: ${error.message}`);
    }
  }

  /**
   * Test SQS integration by sending a test email to queue
   */
  @Post('sqs/test')
  async testSQSIntegration(@Query('emailHash') emailHash?: string) {
    try {
      const result = await this.gmailService.testSQSIntegration(emailHash);
      
      return {
        success: result.success,
        message: result.message,
        data: {
          messageId: result.messageId,
          testedAt: new Date().toISOString(),
          emailHash: emailHash || 'most_recent'
        },
      };
    } catch (error) {
      this.logger.error('Error testing SQS integration:', error);
      throw new BadRequestException(`Failed to test SQS integration: ${error.message}`);
    }
  }

  /**
   * Get SQS configuration information
   */
  @Get('sqs/info')
  async getSQSInfo() {
    try {
      const sqsInfo = await this.gmailService.getSQSInfo();
      
      return {
        success: true,
        data: {
          ...sqsInfo,
          retrievedAt: new Date().toISOString()
        },
      };
    } catch (error) {
      this.logger.error('Error getting SQS info:', error);
      throw new BadRequestException('Failed to get SQS info');
    }
  }

  /**
   * Resend specific email to SQS queue
   */
  @Post('sqs/resend/:emailHash')
  async resendEmailToSQS(@Param('emailHash') emailHash: string) {
    try {
      const result = await this.gmailService.resendEmailToSQS(emailHash);
      
      return {
        success: result.success,
        message: result.message,
        data: {
          emailHash,
          messageId: result.messageId,
          resentAt: new Date().toISOString()
        },
      };
    } catch (error) {
      this.logger.error('Error resending email to SQS:', error);
      throw new BadRequestException(`Failed to resend email to SQS: ${error.message}`);
    }
  }

  /**
   * Switch to a different tenant configuration
   */
  @Post('tenant/switch/:tenantId')
  async switchTenant(@Param('tenantId') tenantId: string) {
    try {
      await this.gmailService.switchToTenant(tenantId);
      
      return {
        success: true,
        message: `Successfully switched to tenant: ${tenantId}`,
        data: {
          newTenantId: tenantId,
          switchedAt: new Date().toISOString()
        },
      };
    } catch (error) {
      this.logger.error('Error switching tenant:', error);
      throw new BadRequestException(`Failed to switch tenant: ${error.message}`);
    }
  }

  /**
   * Test AI RPA detection functionality
   */
  @Post('ai-rpa/test')
  async testAiRpaDetection(@Body() body?: { emailBody?: string }) {
    try {
      const testEmailBody = body?.emailBody;
      const result = await this.gmailService.testAiRpaDetection(testEmailBody);
      
      return {
        success: result.success,
        message: result.message,
        data: {
          ...result.result,
          testedAt: new Date().toISOString()
        },
      };
    } catch (error) {
      this.logger.error('Error testing AI RPA detection:', error);
      throw new BadRequestException(`Failed to test AI RPA detection: ${error.message}`);
    }
  }
} 