# Use official Node.js 20 image
FROM node:20-alpine AS builder
 
# Set working directory
WORKDIR /app
 
# Copy shared package
COPY ./shared/ ./shared/
 
RUN cd ./shared && npm install && npm run build
 
# Copy package.json and package-lock.json from connectors
COPY ./connectors/package.json ./
COPY ./connectors/package-lock.json* ./
 
# Install dependencies
RUN npm install
 
# Copy connectors source code (excluding shared to avoid overwriting built version)
COPY connectors/ ./connectors-src/
RUN cp -r ./connectors-src/* . && rm -rf ./connectors-src
 
# Build the application
RUN npm run build
 
# Production stage
FROM node:20-alpine
 
WORKDIR /app
 
# Copy built application and dependencies from builder stage
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/shared ./shared
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
 
# # Create non-root user
# RUN addgroup -g 1001 -S nodejs
# RUN adduser -S nestjs -u 1001
 
# # Set ownership of the app directory
# RUN chown -R nestjs:nodejs /app
 
# # Switch to non-root user
# USER nestjs
 
# Expose port
EXPOSE 3003
 
# Start the application
CMD ["node", "dist/src/main.js"]
 