# Gmail Connectors Service Integration Test

## Overview

This document describes how to test the end-to-end integration between the Gmail Connectors service and the API Gateway.

## Prerequisites

1. **Gmail Account Setup**:
   - Create a Gmail account or use an existing one
   - Enable 2-factor authentication
   - Generate an App Password (not your regular password)
   - Note: Regular Gmail passwords won't work due to security restrictions

2. **Services Running**:
   - Shared Service (port 3001)
   - API Gateway (port 3000 for HTTP, 3004 for gRPC)
   - Connectors Service (port 3003)

## Environment Configuration

### Connectors Service (.env)

```bash
# Application Configuration
PORT=3003
NODE_ENV=development

# Gmail IMAP Configuration
GMAIL_USER=<EMAIL>
GMAIL_PASSWORD=your-app-password
IMAP_HOST=imap.gmail.com
IMAP_PORT=993
IMAP_TLS=true

# API Gateway gRPC Configuration
API_GATEWAY_HOST=localhost
API_GATEWAY_GRPC_PORT=3004
```

### API Gateway (.env)

Add this to your existing API Gateway .env file:

```bash
# Email Service gRPC Configuration
GRPC_EMAIL_PORT=3004
```

## Testing Steps

### 1. Start All Services

```bash
# Terminal 1: Start Shared Service
cd shared-service
npm run start:dev

# Terminal 2: Start API Gateway
cd api-gateway
npm run start:dev

# Terminal 3: Start Connectors Service
cd connectors
npm run start:dev
```

### 2. Verify Services Are Running

```bash
# Check Connectors Service
curl http://localhost:3003/health

# Check Gmail Connection Status
curl http://localhost:3003/gmail/status

# Check API Gateway
curl http://localhost:3000
```

### 3. Send Test Email

1. Send an email to the configured Gmail account
2. Monitor the logs in the Connectors service terminal
3. Monitor the logs in the API Gateway terminal

### 4. Expected Log Output

**Connectors Service Logs:**
```
[GmailService] Initializing Gmail IMAP Service...
[GmailService] Connecting to IMAP server: imap.gmail.com:993
[GmailService] Successfully connected to Gmail IMAP
[GmailService] Successfully opened inbox
[GmailService] Starting IDLE listener for new emails...
[GmailService] 1 new message(s) received
[GmailService] Fetching new unread messages...
[GmailService] Found 1 unread message(s)
[GmailService] Processing message 1
[GmailService] Parsed email: Test <NAME_EMAIL>
[GmailService] Sending email to API Gateway: Test Subject
[GmailService] Successfully sent email to API Gateway: Email processed successfully
```

**API Gateway Logs:**
```
[EmailService] Received new email from connectors service
[EmailService] Subject: Test Subject
[EmailService] From: <EMAIL>
[EmailService] Received at: 2024-01-15T10:30:00.000Z
[EmailService] Email body preview: This is a test email...
```

## Troubleshooting

### Common Issues

1. **IMAP Connection Failed**:
   - Verify Gmail credentials
   - Ensure 2FA is enabled and app password is used
   - Check firewall settings

2. **gRPC Connection Failed**:
   - Verify API Gateway is running on port 3004
   - Check network connectivity
   - Ensure proto files are identical

3. **Email Not Processed**:
   - Check if email is marked as read
   - Verify IDLE listener is active
   - Check error logs for parsing issues

### Debug Commands

```bash
# Check if services are listening on correct ports
netstat -tlnp | grep 3003  # Connectors
netstat -tlnp | grep 3004  # API Gateway gRPC

# Test gRPC connection manually (requires grpcurl)
grpcurl -plaintext -proto proto/email.proto \
  -d '{"subject":"Test","from":"<EMAIL>","body":"Test body","receivedAt":"2024-01-15T10:30:00.000Z"}' \
  localhost:3004 email.EmailService/HandleNewEmail
```

## Success Criteria

✅ Connectors service connects to Gmail IMAP successfully
✅ IDLE listener detects new emails
✅ Emails are parsed correctly
✅ gRPC communication works between services
✅ API Gateway receives and processes emails
✅ No errors in service logs

## Next Steps

After successful integration testing:
1. Add email storage to database
2. Implement workflow triggers
3. Add email filtering/routing
4. Set up monitoring and alerting
5. Configure production deployment 