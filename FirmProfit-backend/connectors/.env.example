# Application Configuration
PORT=3003
NODE_ENV=development

# Gmail IMAP Configuration
GMAIL_USER=<EMAIL>
GMAIL_PASSWORD=your-app-password
IMAP_HOST=imap.gmail.com
IMAP_PORT=993
IMAP_TLS=true

# API Gateway gRPC Configuration
API_GATEWAY_HOST=localhost
API_GATEWAY_PORT=3004

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key

# SQS Configuration
COURT_NOTICE_RESULT_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/513981683228/court_notice_result
# SQS-specific AWS credentials (optional - will fallback to general A<PERSON> credentials)
AWS_SQS_ACCESS_KEY_ID=your-sqs-aws-access-key
AWS_SQS_SECRET_ACCESS_KEY=your-sqs-aws-secret-key
AWS_SQS_REGION=us-east-1

ATTACHMENT_PROCESSING_API_URL=
