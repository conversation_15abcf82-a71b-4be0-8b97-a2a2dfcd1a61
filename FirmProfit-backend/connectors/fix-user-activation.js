/**
 * <PERSON><PERSON>t to fix user activation status for round-robin assignment
 * This script updates users to ensure they can be properly assigned in round-robin
 */

const { MongoClient } = require('mongodb');

// MongoDB connection configuration
const MONGO_URI = process.env.MONGO_URI || 'mongodb://localhost:27017/your-database-name';
const DATABASE_NAME = process.env.DB_NAME || 'your-database-name';

async function fixUserActivationStatus() {
    let client;
    
    try {
        console.log('Connecting to MongoDB...');
        client = new MongoClient(MONGO_URI);
        await client.connect();
        
        const db = client.db(DATABASE_NAME);
        const usersCollection = db.collection('users');
        const userRoleCollection = db.collection('userrole');
        
        console.log('Connected to database successfully');
        
        // Step 1: Find all users who are assigned to roles but not active
        console.log('\n=== Finding users assigned to roles ===');
        const userRoles = await userRoleCollection.find({ status: true }).toArray();
        console.log(`Found ${userRoles.length} active user-role assignments`);
        
        const userIds = userRoles.map(ur => ur.userId);
        
        // Step 2: Check current status of these users
        console.log('\n=== Checking user statuses ===');
        const users = await usersCollection.find({ 
            _id: { $in: userIds }
        }).toArray();
        
        console.log(`Found ${users.length} users with role assignments`);
        
        const inactiveUsers = users.filter(user => !user.is_active);
        const deletedUsers = users.filter(user => user.is_deleted);
        const usersWithoutStaffId = users.filter(user => !user.my_case_staff_id);
        
        console.log(`\nUser Status Summary:`);
        console.log(`- Total users with roles: ${users.length}`);
        console.log(`- Inactive users: ${inactiveUsers.length}`);
        console.log(`- Deleted users: ${deletedUsers.length}`);
        console.log(`- Users without my_case_staff_id: ${usersWithoutStaffId.length}`);
        
        // Step 3: Show affected users details
        if (inactiveUsers.length > 0) {
            console.log('\n=== Inactive Users ===');
            inactiveUsers.forEach(user => {
                console.log(`- ${user.first_name} ${user.last_name} (${user.email}) - is_active: ${user.is_active}`);
            });
        }
        
        if (deletedUsers.length > 0) {
            console.log('\n=== Deleted Users ===');
            deletedUsers.forEach(user => {
                console.log(`- ${user.first_name} ${user.last_name} (${user.email}) - is_deleted: ${user.is_deleted}`);
            });
        }
        
        if (usersWithoutStaffId.length > 0) {
            console.log('\n=== Users without my_case_staff_id ===');
            usersWithoutStaffId.forEach(user => {
                console.log(`- ${user.first_name} ${user.last_name} (${user.email}) - my_case_staff_id: ${user.my_case_staff_id}`);
            });
        }
        
        // Step 4: Ask for confirmation before making changes
        console.log('\n=== Proposed Changes ===');
        
        const usersToActivate = users.filter(user => !user.is_active && !user.is_deleted);
        const usersToUndelete = users.filter(user => user.is_deleted);
        
        if (usersToActivate.length > 0) {
            console.log(`\nWill activate ${usersToActivate.length} users:`);
            usersToActivate.forEach(user => {
                console.log(`- ${user.first_name} ${user.last_name} (${user.email})`);
            });
        }
        
        if (usersToUndelete.length > 0) {
            console.log(`\nWill undelete ${usersToUndelete.length} users:`);
            usersToUndelete.forEach(user => {
                console.log(`- ${user.first_name} ${user.last_name} (${user.email})`);
            });
        }
        
        // Step 5: Execute the fixes (uncomment to actually run)
        console.log('\n=== Executing Fixes ===');
        
        // Activate inactive users
        if (usersToActivate.length > 0) {
            const activateResult = await usersCollection.updateMany(
                { 
                    _id: { $in: usersToActivate.map(u => u._id) },
                    is_deleted: { $ne: true }
                },
                { 
                    $set: { 
                        is_active: true,
                        updatedAt: new Date()
                    } 
                }
            );
            console.log(`✅ Activated ${activateResult.modifiedCount} users`);
        }
        
        // Undelete users (only if they're not intentionally deleted)
        // Note: Be careful with this - you might want to review each case manually
        if (usersToUndelete.length > 0) {
            console.log(`⚠️  Found ${usersToUndelete.length} deleted users - manual review recommended`);
            // Uncomment the following lines only after manual review:
            /*
            const undeleteResult = await usersCollection.updateMany(
                { _id: { $in: usersToUndelete.map(u => u._id) } },
                { 
                    $set: { 
                        is_deleted: false,
                        updatedAt: new Date()
                    } 
                }
            );
            console.log(`✅ Undeleted ${undeleteResult.modifiedCount} users`);
            */
        }
        
        // Step 6: Verify the changes
        console.log('\n=== Verification ===');
        const updatedUsers = await usersCollection.find({ 
            _id: { $in: userIds }
        }).toArray();
        
        const stillInactive = updatedUsers.filter(user => !user.is_active && !user.is_deleted);
        const stillDeleted = updatedUsers.filter(user => user.is_deleted);
        
        console.log(`After fixes:`);
        console.log(`- Still inactive (non-deleted): ${stillInactive.length}`);
        console.log(`- Still deleted: ${stillDeleted.length}`);
        console.log(`- Ready for round-robin: ${updatedUsers.filter(u => !u.is_deleted).length}`);
        
        console.log('\n✅ User activation fix completed successfully!');
        
    } catch (error) {
        console.error('❌ Error fixing user activation status:', error);
        throw error;
    } finally {
        if (client) {
            await client.close();
            console.log('Database connection closed');
        }
    }
}

// Run the script if called directly
if (require.main === module) {
    fixUserActivationStatus()
        .then(() => {
            console.log('Script completed successfully');
            process.exit(0);
        })
        .catch(error => {
            console.error('Script failed:', error);
            process.exit(1);
        });
}

module.exports = { fixUserActivationStatus };
