# MongoDB Integration for Gmail Connector

## Overview
The Gmail connector now integrates with MongoDB to store all incoming email data. This provides persistent storage and enables querying of email history.

## Environment Variables
Add these MongoDB configuration variables to your `.env` file:

```env
# MongoDB Configuration
MONGO_URI=mongodb://localhost:27017
MONGO_DB_NAME=gmail_connector
```

## Email Schema
Each email is stored with the following structure:

```typescript
{
  subject: string;           // Email subject
  from: string;             // Sender email address
  body: string;             // Email body content
  receivedAt: string;       // When email was received (ISO string)
  processedAt: Date;        // When email was processed by our system
  isProcessed: boolean;     // Processing status
  errorMessage: string;     // Error message if processing failed
  metadata: {               // Additional metadata
    attributes: any;        // IMAP attributes
    bodySize: number;       // Body size in characters
    hasAttachments: boolean; // Whether email has attachments
  };
  createdAt: Date;          // Auto-generated by MongoDB
  updatedAt: Date;          // Auto-generated by MongoDB
}
```

## API Endpoints

### Get Connection Status
```
GET /gmail/status
```
Returns Gmail IMAP connection status.

### Get Email Statistics
```
GET /gmail/stats
```
Returns email processing statistics:
- Total emails processed
- Successfully processed emails
- Emails with errors
- Pending emails

### Get Recent Emails
```
GET /gmail/recent?limit=10
```
Returns recent emails from MongoDB (default limit: 10).

## Features

1. **Automatic Storage**: All incoming emails are automatically saved to MongoDB
2. **Error Handling**: Failed email processing is logged with error details
3. **Metadata Storage**: Additional IMAP attributes and email metadata are preserved
4. **Processing Status**: Track which emails have been successfully processed
5. **Statistics**: Get insights into email processing performance
6. **Query Interface**: REST endpoints to access stored email data

## Database Connection
The service uses the same MongoDB connection configuration as the shared-service, ensuring consistency across the application.

## Error Handling
If an email fails to save to MongoDB, the service will:
1. Log the error
2. Attempt to save an error record with the email data
3. Continue processing other emails

This ensures no emails are lost even if there are database connectivity issues. 