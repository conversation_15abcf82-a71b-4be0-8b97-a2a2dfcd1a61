# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/gmail-connector
DB_NAME=gmail-connector

# PostgreSQL Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USERNAME=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DATABASE=firmprofit

# IMAP Configuration (Fallback - when no tenant config available)
GMAIL_USER=<EMAIL>
GMAIL_PASSWORD=your-app-password
IMAP_HOST=imap.gmail.com
IMAP_PORT=993
IMAP_TLS=true
IMAP_REJECT_UNAUTHORIZED=false
IMAP_MAX_RECONNECT_ATTEMPTS=5
IMAP_POLLING_INTERVAL=30000

# Processing Configuration
ENABLE_DUPLICATE_CHECKING=true
ENABLE_MARK_AS_SEEN=true

# Tenant Configuration
DEFAULT_TENANT_ID=11111111-1111-1111-1111-111111111111

# Security
ENCRYPTION_KEY=your-encryption-key-change-in-production-make-it-32-chars-long

# Application Configuration
NODE_ENV=development
PORT=3001

# gRPC Configuration
GRPC_PORT=50051
GRPC_PACKAGE=email
GRPC_PROTO_PATH=proto/email.proto

# API Gateway Configuration
API_GATEWAY_HOST=localhost
API_GATEWAY_PORT=3000

# Logging
LOG_LEVEL=debug 
