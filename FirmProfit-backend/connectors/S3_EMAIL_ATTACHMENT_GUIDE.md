# S3 Email Attachment Integration Guide

## Overview

Your email processing system **already saves S3 paths to the database** when processing emails with attachments! This document explains how it works and the new enhanced APIs available.

## 🎯 Current Implementation Status

✅ **FULLY IMPLEMENTED**: S3 paths are automatically saved to MongoDB when emails are processed

### What Happens Automatically

1. **Email Reception**: When an email arrives via IMAP
2. **Attachment Processing**: All attachments are automatically uploaded to S3
3. **Database Storage**: Complete S3 metadata is saved in `metadata.attachments[]`
4. **Path Storage**: S3 keys, bucket names, and presigned URLs are stored

## 📊 Database Schema

### Email Document Structure
```javascript
{
  _id: ObjectId,
  subject: "Invoice #12345",
  from: "<EMAIL>",
  body: "Email content...",
  tenantId: "tenant123",
  metadata: {
    hasAttachments: true,
    attachmentCount: 2,
    totalAttachmentSize: 2048576, // bytes
    attachments: [
      {
        filename: "invoice.pdf",
        contentType: "application/pdf",
        size: 1048576,
        s3Key: "attachments/tenant123/email_hash_123/att_1_1721123456789_invoice.pdf", // ✅ S3 PATH
        s3Bucket: "elite-assets-dev", // ✅ S3 BUCKET
        s3Url: "https://elite-assets-dev.s3.amazonaws.com/...", // ✅ PRESIGNED URL
        attachmentId: "att_1_1721123456789",
        storageType: "s3",
        uploadedAt: "2024-07-16T10:30:45.123Z",
        checksum: "abc123def456",
        contentId: null
      }
    ]
  }
}
```

## 🚀 Enhanced API Endpoints

### 1. Get Emails with S3 Attachments
```bash
GET /gmail/attachments/s3?tenantId=tenant123&limit=50
```

**Response:**
```json
{
  "success": true,
  "data": {
    "emails": [
      {
        "subject": "Invoice #12345",
        "from": "<EMAIL>",
        "receivedAt": "2024-07-16T10:30:45.123Z",
        "tenantId": "tenant123",
        "metadata": {
          "attachments": [
            {
              "filename": "invoice.pdf",
              "s3Key": "attachments/tenant123/email_hash_123/att_1_1721123456789_invoice.pdf",
              "s3Bucket": "elite-assets-dev",
              "s3Url": "https://...",
              "size": 1048576
            }
          ]
        }
      }
    ],
    "count": 25,
    "tenantId": "tenant123"
  }
}
```

### 2. Get S3 Attachment Info by ID
```bash
GET /gmail/attachments/s3/{attachmentId}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "filename": "invoice.pdf",
    "contentType": "application/pdf",
    "size": 1048576,
    "s3Key": "attachments/tenant123/email_hash_123/att_1_1721123456789_invoice.pdf",
    "s3Bucket": "elite-assets-dev",
    "s3Url": "https://elite-assets-dev.s3.amazonaws.com/...",
    "attachmentId": "att_1_1721123456789",
    "storageType": "s3",
    "uploadedAt": "2024-07-16T10:30:45.123Z",
    "checksum": "abc123def456"
  }
}
```

### 3. Regenerate Presigned URLs
```bash
POST /gmail/attachments/regenerate-urls/{emailId}?expiresIn=7200
```

**Response:**
```json
{
  "success": true,
  "data": {
    "emailId": "email_hash_123",
    "attachments": [
      {
        "filename": "invoice.pdf",
        "s3Url": "https://elite-assets-dev.s3.amazonaws.com/...", // NEW URL
        "urlGeneratedAt": "2024-07-16T15:30:45.123Z",
        "urlExpiresIn": 7200
      }
    ],
    "expiresIn": 7200,
    "regeneratedAt": "2024-07-16T15:30:45.123Z"
  }
}
```

### 4. Get S3 Storage Statistics
```bash
GET /gmail/attachments/s3/stats?tenantId=tenant123
```

**Response:**
```json
{
  "success": true,
  "data": {
    "totalEmails": 150,
    "totalAttachments": 347,
    "totalStorageBytes": 52428800,
    "totalStorageMB": 50.0,
    "totalStorageGB": 0.05,
    "avgAttachmentsPerEmail": 2.31,
    "avgStoragePerEmail": 349525.33,
    "tenantId": "tenant123",
    "generatedAt": "2024-07-16T15:30:45.123Z"
  }
}
```

### 5. Delete Email Attachments
```bash
POST /gmail/attachments/delete/{emailId}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "emailId": "email_hash_123",
    "deletedCount": 2,
    "errors": [],
    "deletedAt": "2024-07-16T15:30:45.123Z"
  }
}
```

## 🔧 S3 Key Structure

Attachments are stored with this organized structure:

```
s3://elite-assets-dev/
└── attachments/
    ├── tenant123/
    │   ├── email_hash_123/
    │   │   ├── att_1_1721123456789_invoice.pdf
    │   │   └── att_2_1721123456790_receipt.jpg
    │   └── email_hash_456/
    │       └── att_1_1721123456791_contract.docx
    └── fallback/
        └── email_hash_789/
            └── att_1_1721123456792_attachment.pdf
```

## 💡 How to Query S3 Paths from MongoDB

### Find all emails with S3 attachments:
```javascript
db.emails.find({
  "metadata.hasAttachments": true,
  "metadata.attachments.storageType": "s3"
})
```

### Find specific attachment by S3 key:
```javascript
db.emails.find({
  "metadata.attachments.s3Key": "attachments/tenant123/email_hash_123/att_1_1721123456789_invoice.pdf"
})
```

### Get attachment stats by tenant:
```javascript
db.emails.aggregate([
  {
    $match: {
      "tenantId": "tenant123",
      "metadata.hasAttachments": true
    }
  },
  {
    $group: {
      _id: "$tenantId",
      totalEmails: { $sum: 1 },
      totalAttachments: { $sum: "$metadata.attachmentCount" },
      totalSize: { $sum: "$metadata.totalAttachmentSize" }
    }
  }
])
```

## 🛠️ Integration Examples

### 1. Download Attachment from S3
```typescript
// Get attachment info
const attachment = await gmailService.getS3AttachmentInfo(attachmentId);

// Generate fresh presigned URL if needed
if (isUrlExpired(attachment.s3Url)) {
  const fresh = await gmailService.regeneratePresignedUrls(emailId);
  attachment.s3Url = fresh[0].s3Url;
}

// Download the file
const response = await fetch(attachment.s3Url);
const fileBuffer = await response.buffer();
```

### 2. Process Email with Attachments
```typescript
// Find emails with attachments
const emailsWithAttachments = await gmailService.getEmailsWithS3Attachments('tenant123', 10);

for (const email of emailsWithAttachments.data.emails) {
  for (const attachment of email.metadata.attachments) {
    console.log(`Processing: ${attachment.filename}`);
    console.log(`S3 Key: ${attachment.s3Key}`);
    console.log(`Size: ${attachment.size} bytes`);
    
    // Process the attachment...
  }
}
```

### 3. Monitor Storage Usage
```typescript
// Get storage statistics
const stats = await gmailService.getS3StorageStats('tenant123');

console.log(`Total storage used: ${stats.totalStorageMB} MB`);
console.log(`Average attachments per email: ${stats.avgAttachmentsPerEmail}`);

// Alert if storage is getting high
if (stats.totalStorageGB > 10) {
  console.warn('Storage usage is high, consider cleanup');
}
```

## 🔍 Troubleshooting

### Common Queries

1. **Find emails missing S3 data:**
```javascript
db.emails.find({
  "metadata.hasAttachments": true,
  "metadata.attachments.s3Key": { $exists: false }
})
```

2. **Find expired presigned URLs:**
```javascript
// URLs expire after 24 hours by default
const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000);
db.emails.find({
  "metadata.attachments.uploadedAt": { $lt: yesterday.toISOString() }
})
```

3. **Get storage by file type:**
```javascript
db.emails.aggregate([
  { $unwind: "$metadata.attachments" },
  {
    $group: {
      _id: "$metadata.attachments.contentType",
      count: { $sum: 1 },
      totalSize: { $sum: "$metadata.attachments.size" }
    }
  },
  { $sort: { totalSize: -1 } }
])
```

## ✅ Summary

Your system is **already saving S3 paths to the database**! The new enhancements provide:

1. **Better Type Safety**: Structured schemas for attachment metadata
2. **Rich APIs**: Query, manage, and analyze S3 attachment data
3. **Storage Management**: Statistics, cleanup, and URL regeneration
4. **Multi-tenant Support**: Tenant-specific storage and queries

The S3 integration is production-ready and automatically handles:
- ✅ File uploads to S3
- ✅ S3 path storage in MongoDB
- ✅ Presigned URL generation
- ✅ Error handling and fallbacks
- ✅ Tenant isolation
- ✅ Duplicate prevention
- ✅ Cleanup utilities

All email attachments are now stored in S3 with their paths properly saved in the database! 