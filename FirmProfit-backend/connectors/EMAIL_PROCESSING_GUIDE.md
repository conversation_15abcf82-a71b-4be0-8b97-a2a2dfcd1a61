# Email Processing Solution Guide

## Overview

This solution provides a robust email processing system that connects to Gmail via IMAP, processes incoming emails, and stores them in MongoDB while preventing duplicates and ensuring each email is processed only once.

## Features

### 1. Duplicate Prevention
- **Primary Method**: Uses email `Message-ID` header for unique identification
- **Fallback Method**: Creates SHA-256 hash from sender, subject, date, and content portion
- **MongoDB Unique Index**: Prevents duplicate storage at database level

### 2. Reliable Mark as Seen
- **Atomic Operations**: Email saving and marking as seen are coordinated
- **Async Mark as Seen**: Improved error handling for IMAP operations
- **Retry Logic**: Handles temporary connection issues

### 3. Error Handling
- **Graceful Degradation**: Continues processing even if individual emails fail
- **Error Logging**: Comprehensive logging for debugging
- **Error Storage**: Failed emails are stored with error details

### 4. Connection Management
- **Auto-Reconnection**: Exponential backoff for connection failures
- **IDLE Mode**: Efficient real-time email monitoring
- **Fallback Polling**: Falls back to polling if IDLE is not supported

## Configuration

### Environment Variables

Copy `config.example.env` to `.env` and configure:

```bash
# Gmail Configuration
GMAIL_USER=<EMAIL>
GMAIL_PASSWORD=your-app-password
IMAP_HOST=imap.gmail.com
IMAP_PORT=993
IMAP_TLS=true

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/gmail-connector
DB_NAME=gmail-connector

# Processing Settings
IMAP_MAX_RECONNECT_ATTEMPTS=5
IMAP_POLLING_INTERVAL=30000
ENABLE_DUPLICATE_CHECKING=true
ENABLE_MARK_AS_SEEN=true
```

### Gmail App Password Setup

1. Enable 2FA on your Gmail account
2. Go to Google Account settings → Security → App passwords
3. Generate an app password for "Mail"
4. Use this password in `GMAIL_PASSWORD` (not your regular password)

## API Endpoints

### Monitoring Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/gmail/health` | GET | Overall health status |
| `/gmail/status` | GET | Connection status |
| `/gmail/stats` | GET | Email processing statistics |
| `/gmail/detailed-stats` | GET | Comprehensive statistics |
| `/gmail/recent` | GET | Recent processed emails |

### Testing Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/gmail/test-connection` | GET | Test IMAP connection and mailbox status |
| `/gmail/test-mark-as-seen` | GET | Test mark as seen functionality |
| `/gmail/process-unseen` | POST | Manually process unseen emails |
| `/gmail/duplicate-check/:hash` | GET | Check if hash exists |
| `/gmail/simulate-email` | POST | Simulate email for testing |

## Testing the Solution

### 1. Basic Connection Test

```bash
curl http://localhost:3000/gmail/test-connection
```

Expected response:
```json
{
  "connected": true,
  "mailboxOpen": true,
  "reconnectAttempts": 0,
  "message": "Connection and mailbox are ready",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### 1.1. Mark as Seen Test

```bash
curl http://localhost:3000/gmail/test-mark-as-seen
```

Expected response:
```json
{
  "success": true,
  "message": "Mailbox is ready for mark as seen operations",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### 2. Health Check

```bash
curl http://localhost:3000/gmail/health
```

### 3. Processing Statistics

```bash
curl http://localhost:3000/gmail/detailed-stats
```

Expected response:
```json
{
  "connection": {
    "connected": true,
    "reconnectAttempts": 0
  },
  "statistics": {
    "total": 150,
    "processed": 148,
    "markedAsSeen": 148,
    "errors": 2,
    "pending": 2,
    "duplicatesPreventedByHash": 15
  },
  "recentEmails": [...],
  "health": {
    "isHealthy": true,
    "duplicatePreventionWorking": true,
    "markAsSeenWorking": true
  }
}
```

### 4. Manual Email Processing

```bash
curl -X POST http://localhost:3000/gmail/process-unseen
```

### 5. Duplicate Checking

```bash
curl http://localhost:3000/gmail/duplicate-check/abcd1234...
```

## Database Schema

### Email Collection

```javascript
{
  _id: ObjectId,
  subject: String,
  from: String,
  body: String,
  receivedAt: String,
  messageId: String,        // Email Message-ID header
  uniqueHash: String,       // SHA-256 unique identifier
  uid: Number,             // IMAP UID
  seqno: Number,           // IMAP sequence number
  processedAt: Date,
  isProcessed: Boolean,
  isMarkedAsSeen: Boolean,
  errorMessage: String,
  metadata: {
    attributes: Object,
    headers: Object,
    bodySize: Number,
    hasAttachments: Boolean
  },
  createdAt: Date,
  updatedAt: Date
}
```

### Indexes

```javascript
// Unique constraint on hash
{ uniqueHash: 1 } // unique: true

// Performance indexes
{ messageId: 1, uniqueHash: 1 }
{ from: 1, subject: 1, receivedAt: 1 }
{ isProcessed: 1, isMarkedAsSeen: 1 }
{ createdAt: -1 }
```

## How It Works

### 1. Email Processing Flow

```mermaid
graph TD
    A[New Email Arrives] --> B[Parse Email Content]
    B --> C[Extract Message-ID]
    C --> D{Message-ID Exists?}
    D -->|Yes| E[Use Message-ID for Hash]
    D -->|No| F[Generate Fallback Hash]
    E --> G[Check Database for Duplicate]
    F --> G
    G --> H{Duplicate Found?}
    H -->|Yes| I[Mark as Seen & Skip]
    H -->|No| J[Save to MongoDB]
    J --> K[Mark as Seen in IMAP]
    K --> L[Update isMarkedAsSeen Flag]
    I --> M[End]
    L --> M
```

### 2. Unique Hash Generation

```javascript
// Primary method (if Message-ID exists)
hash = SHA-256(messageId)

// Fallback method
combinedString = `${from.toLowerCase()}|${subject.toLowerCase()}|${date.toISOString()}|${contentHash}`
hash = SHA-256(combinedString)
```

### 3. Atomic Processing

The system ensures atomicity by:
1. Checking for duplicates before processing
2. Saving to MongoDB first
3. Marking as seen only after successful save
4. Updating the database record with seen status

## Monitoring and Logging

### Log Levels

- **INFO**: Connection status, email processing progress
- **WARN**: Fallback operations, non-critical issues
- **ERROR**: Processing failures, connection errors
- **DEBUG**: Detailed processing information

### Key Metrics to Monitor

1. **Connection Status**: `connected: true/false`
2. **Mailbox Status**: `mailboxOpen: true/false`
3. **Processing Rate**: `processed / total`
4. **Error Rate**: `errors / total`
5. **Duplicate Prevention**: `duplicatesPreventedByHash`
6. **Mark as Seen Rate**: `markedAsSeen / processed`

### Alerts to Set Up

1. Connection down for > 5 minutes
2. Mailbox not open for > 5 minutes
3. Error rate > 10%
4. Mark as seen rate < 90%
5. No new emails processed for > 1 hour (if expecting emails)
6. Duplicate prevention not working (duplicatesPreventedByHash = 0)

## Troubleshooting

### Common Issues

1. **Connection Failures**
   - Check Gmail app password
   - Verify IMAP settings
   - Check firewall/network connectivity

2. **Duplicate Emails**
   - Check `uniqueHash` generation
   - Verify MongoDB unique index
   - Monitor `duplicatesPreventedByHash` metric

3. **Emails Not Marked as Seen**
   - Check `isMarkedAsSeen` statistics
   - Test mark as seen functionality: `curl http://localhost:3000/gmail/test-mark-as-seen`
   - Verify IMAP write permissions
   - Check mailbox is open: `curl http://localhost:3000/gmail/test-connection`
   - Monitor mark as seen error logs
   - Look for "No mailbox is currently selected" errors

4. **High Error Rate**
   - Check MongoDB connection
   - Verify email parsing issues
   - Monitor specific error messages

### Debug Commands

```bash
# Check connection and mailbox status
curl http://localhost:3000/gmail/test-connection

# Test mark as seen functionality
curl http://localhost:3000/gmail/test-mark-as-seen

# Force process unseen emails
curl -X POST http://localhost:3000/gmail/process-unseen

# Check recent emails
curl http://localhost:3000/gmail/recent?limit=5

# Get detailed statistics
curl http://localhost:3000/gmail/detailed-stats
```

## Performance Considerations

1. **Database Indexing**: Proper indexes for query performance
2. **Connection Pooling**: MongoDB connection pooling
3. **Memory Management**: Large email handling
4. **Batch Processing**: Process multiple emails efficiently

## Security Considerations

1. **App Passwords**: Use Gmail app passwords, not regular passwords
2. **Environment Variables**: Keep credentials in environment files
3. **TLS**: Always use TLS for IMAP connections
4. **Access Control**: Limit API endpoint access in production

## Production Deployment

1. **Environment Setup**: Configure production environment variables
2. **Monitoring**: Set up health checks and alerts
3. **Logging**: Configure appropriate log levels
4. **Backup**: Regular MongoDB backups
5. **Scaling**: Consider multiple instances for high volume

## Testing Strategy

### Unit Tests
- Test unique hash generation
- Test duplicate detection logic
- Test email parsing

### Integration Tests
- Test IMAP connection
- Test MongoDB operations
- Test end-to-end email processing

### Manual Testing
- Send test emails
- Monitor processing via API endpoints
- Verify duplicate prevention
- Test connection recovery 