# Tenant-Aware IMAP Configuration System

This document describes the enhanced Gmail connector service that now supports tenant-specific IMAP configurations stored in the PostgreSQL database.

## Overview

The Gmail connector service has been upgraded to support multi-tenant IMAP configurations. Instead of using only environment variables, the service now fetches IMAP settings from the `tenant_config` table in PostgreSQL, enabling different tenants to have their own email configurations.

## Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    Gmail Connector Service                      │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   App Module    │  │  Gmail Service  │  │ Gmail Controller│  │
│  │                 │  │                 │  │                 │  │
│  │ - MongoDB       │  │ - Tenant-aware  │  │ - Tenant APIs   │  │
│  │ - PostgreSQL    │  │ - IMAP handling │  │ - Config APIs   │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
│                                │                                │
│  ┌─────────────────┐  ┌─────────────────┐                       │
│  │ImapConfigService│  │  TenantConfig   │                       │
│  │                 │  │     Entity      │                       │
│  │ - Encryption    │  │                 │                       │
│  │ - Fallback      │  │ - PostgreSQL    │                       │
│  └─────────────────┘  └─────────────────┘                       │
└─────────────────────────────────────────────────────────────────┘
```

## Database Schema

### TenantConfig Entity

The `tenant_config` table stores encrypted tenant-specific configurations:

```sql
CREATE TABLE tenant_config (
    tenant_id TEXT NOT NULL,
    key TEXT NOT NULL,
    config JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (tenant_id, key)
);
```

### IMAP Configuration Structure

```json
{
  "host": "imap.gmail.com",
  "port": 993,
  "username": "encrypted_username",
  "password": "encrypted_password",
  "tls": true,
  "tlsOptions": {
    "servername": "imap.gmail.com",
    "rejectUnauthorized": false
  },
  "keepalive": true,
  "autotls": "always",
  "maxReconnectAttempts": 5,
  "pollingInterval": 30000,
  "enableDuplicateChecking": true,
  "enableMarkAsSeen": true
}
```

## Setup Instructions

### 1. Environment Configuration

Copy the example environment file and configure your settings:

```bash
cp config.example.env .env
```

Update the `.env` file with your actual values:

```bash
# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/gmail-connector
DB_NAME=gmail-connector

# PostgreSQL Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USERNAME=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DATABASE=firmprofit

# Default Tenant (optional)
DEFAULT_TENANT_ID=11111111-1111-1111-1111-111111111111

# Security
ENCRYPTION_KEY=your-32-character-encryption-key-here
```

### 2. Database Setup

The service automatically connects to both MongoDB and PostgreSQL. Run the migrations to set up the tenant_config table:

```bash
# From the api-gateway directory
npm run migration:run
```

### 3. Seed Data

Run the seeds to populate sample IMAP configurations:

```bash
# From the api-gateway directory
npm run seed:run
```

### 4. Install Dependencies

```bash
npm install
```

### 5. Start the Service

```bash
npm run start:dev
```

## Usage

### Starting the Service

The service will automatically:

1. Connect to both MongoDB and PostgreSQL
2. Load IMAP configuration for the default tenant (if specified)
3. Fall back to environment variables if no tenant configuration is found
4. Start listening for emails

### Tenant-Specific IMAP Configuration

#### Save IMAP Configuration for a Tenant

```bash
POST /gmail/tenant/:tenantId/config
Content-Type: application/json

{
  "host": "imap.gmail.com",
  "port": 993,
  "username": "<EMAIL>",
  "password": "tenant_password",
  "tls": true,
  "tlsOptions": {
    "servername": "imap.gmail.com",
    "rejectUnauthorized": false
  },
  "keepalive": true,
  "autotls": "always",
  "maxReconnectAttempts": 5,
  "pollingInterval": 30000,
  "enableDuplicateChecking": true,
  "enableMarkAsSeen": true
}
```

#### Get IMAP Configuration for a Tenant

```bash
GET /gmail/tenant/:tenantId/config
```

#### Switch to Different Tenant

```bash
POST /gmail/tenant/:tenantId/switch
```

### Email Processing by Tenant

#### Get Tenant-Specific Email Statistics

```bash
GET /gmail/tenant/:tenantId/stats
```

#### Get Recent Emails for a Tenant

```bash
GET /gmail/tenant/:tenantId/recent?limit=10
```

#### Process Unseen Emails for a Tenant

```bash
POST /gmail/tenant/:tenantId/process-unseen
```

## Security Features

### Encryption

- **Automatic Encryption**: Username and password fields are automatically encrypted before storage
- **Decryption**: Sensitive fields are decrypted when retrieved from the database
- **Key Management**: Uses `ENCRYPTION_KEY` from environment variables

### Fallback Configuration

- **Environment Variables**: Falls back to environment variables if no tenant configuration is found
- **Graceful Degradation**: Service continues to work even if database connection fails
- **Error Handling**: Comprehensive error handling for configuration retrieval

## API Endpoints

### General Endpoints

- `GET /gmail/status` - Get connection status
- `GET /gmail/stats` - Get email statistics
- `GET /gmail/recent` - Get recent emails
- `GET /gmail/health` - Health check
- `POST /gmail/process-unseen` - Process unseen emails
- `POST /gmail/reload-config` - Reload current configuration
- `GET /gmail/current-config` - Get current IMAP configuration

### Tenant-Specific Endpoints

- `POST /gmail/tenant/:tenantId/switch` - Switch to tenant
- `GET /gmail/tenant/:tenantId/config` - Get tenant IMAP config
- `POST /gmail/tenant/:tenantId/config` - Save tenant IMAP config
- `GET /gmail/tenant/:tenantId/stats` - Get tenant email stats
- `GET /gmail/tenant/:tenantId/recent` - Get tenant recent emails
- `POST /gmail/tenant/:tenantId/process-unseen` - Process tenant unseen emails

## Monitoring and Debugging

### Connection Status

The service status includes tenant information:

```json
{
  "connected": true,
  "reconnectAttempts": 0,
  "mailboxOpen": true,
  "currentTenant": "11111111-1111-1111-1111-111111111111",
  "hasImapConfig": true
}
```

### Email Statistics

Statistics now include tenant-specific data:

```json
{
  "total": 150,
  "processed": 145,
  "markedAsSeen": 140,
  "errors": 5,
  "pending": 5,
  "duplicatesPreventedByHash": 25,
  "currentTenant": "11111111-1111-1111-1111-111111111111",
  "totalForTenant": 50,
  "processedForTenant": 48,
  "errorsForTenant": 2
}
```

### Logs

The service provides detailed logging for:

- Tenant switching operations
- Configuration loading and encryption/decryption
- Email processing with tenant identification
- Connection and authentication status

## Troubleshooting

### Common Issues

1. **PostgreSQL Connection Failed**
   - Check PostgreSQL connection details in `.env`
   - Ensure PostgreSQL is running and accessible
   - Verify database name and credentials

2. **IMAP Configuration Not Found**
   - Ensure tenant configuration exists in `tenant_config` table
   - Check tenant ID matches exactly
   - Verify fallback environment variables are set

3. **Decryption Errors**
   - Ensure `ENCRYPTION_KEY` is set and consistent
   - Check if data was encrypted with a different key
   - Verify encryption key length (should be 32 characters)

4. **Email Processing Issues**
   - Check IMAP credentials for the tenant
   - Verify email server settings (host, port, TLS)
   - Monitor reconnection attempts and error logs

### Debug Commands

```bash
# Check current configuration
curl http://localhost:3001/gmail/current-config

# Check service status
curl http://localhost:3001/gmail/status

# Get detailed statistics
curl http://localhost:3001/gmail/detailed-stats

# Test tenant switching
curl -X POST http://localhost:3001/gmail/tenant/your-tenant-id/switch
```

## Development

### Running Tests

```bash
npm run test
```

### Building for Production

```bash
npm run build
npm run start:prod
```

### Database Migrations

```bash
# Create new migration
npm run migration:create -- --name AddNewFeature

# Run migrations
npm run migration:run

# Revert migration
npm run migration:revert
```

## Contributing

1. Follow the existing code structure
2. Add appropriate error handling
3. Include comprehensive logging
4. Write tests for new features
5. Update documentation

## License

This project is licensed under the UNLICENSED license. 