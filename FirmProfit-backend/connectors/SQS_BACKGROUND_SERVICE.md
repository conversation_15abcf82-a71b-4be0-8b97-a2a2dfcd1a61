# SQS Background Service

This document describes the SQS background service that has been added to the connectors service to read from the court notice result queue.

## Overview

The SQS background service continuously polls the court notice result queue and logs all received messages to the console. It automatically starts when the application starts and runs in the background.

## Configuration

The service requires the following environment variables:

- `COURT_NOTICE_RESULT_QUEUE_URL`: The SQS queue URL for court notice results
- `AWS_ACCESS_KEY_ID`: AWS access key ID (or use AWS_SQS_ACCESS_KEY_ID for SQS-specific credentials)
- `AWS_SECRET_ACCESS_KEY`: AWS secret access key (or use AWS_SQS_SECRET_ACCESS_KEY for SQS-specific credentials)
- `AWS_REGION`: AWS region (defaults to us-east-1, or use AWS_SQS_REGION for SQS-specific region)

### SQS-Specific Credentials (Recommended)

For better security and to avoid permission conflicts, you can use SQS-specific credentials:

- `AWS_SQS_ACCESS_KEY_ID`: AWS access key ID specifically for SQS access
- `AWS_SQS_SECRET_ACCESS_KEY`: AWS secret access key specifically for SQS access  
- `AWS_SQS_REGION`: AWS region specifically for SQS (optional)

Example configuration in `.env`:
```
COURT_NOTICE_RESULT_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/513981683228/court_notice_result

# Option 1: Use general AWS credentials
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1

# Option 2: Use SQS-specific credentials (recommended)
AWS_SQS_ACCESS_KEY_ID=your-sqs-access-key
AWS_SQS_SECRET_ACCESS_KEY=your-sqs-secret-key
AWS_SQS_REGION=us-east-1
```

**Note**: The service will prioritize SQS-specific credentials over general AWS credentials if both are provided.

## Features

- **Automatic Startup**: Service starts automatically when the application starts
- **Long Polling**: Uses SQS long polling (20 seconds) for efficient message retrieval
- **Batch Processing**: Processes up to 10 messages at once
- **Message Deletion**: Automatically deletes processed messages from the queue
- **Error Handling**: Robust error handling with detailed logging
- **Status Monitoring**: REST endpoints for monitoring service status

## REST Endpoints

The service exposes the following endpoints for monitoring and control:

### Get Status
```
GET /sqs/status
```
Returns the current status of the SQS consumer service.

### Trigger Manual Poll
```
POST /sqs/trigger-poll
```
Manually triggers a poll of the SQS queue (useful for testing).

## Service Behavior

1. **Initialization**: Service initializes on module startup and connects to SQS
2. **Polling**: Continuously polls the queue every 5 seconds when no messages are available
3. **Processing**: When messages are received, they are logged to console with full details
4. **Cleanup**: Messages are deleted from queue after successful processing
5. **Shutdown**: Service gracefully stops polling when application shuts down

## Message Logging

When a message is received, the service logs:
- Message ID
- Receipt Handle  
- Message Body (parsed as JSON)
- Message Attributes
- Custom Message Attributes

Example log output:
```
[SqsConsumerService] === COURT NOTICE RESULT MESSAGE ===
[SqsConsumerService] Message ID: abc123-def456-ghi789
[SqsConsumerService] Receipt Handle: AQEBxxx...
[SqsConsumerService] Message Body:
{
  "courtNoticeId": "12345",
  "status": "processed",
  "result": { ... }
}
[SqsConsumerService] === END MESSAGE ===
[SqsConsumerService] Message deleted from queue successfully
```

## Error Handling

The service includes comprehensive error handling for:
- SQS connection issues
- Message parsing errors
- AWS credential problems
- Network connectivity issues

All errors are logged with detailed information for troubleshooting.

## Files Added/Modified

- `src/services/sqs-consumer.service.ts` - Main SQS consumer service
- `src/services/sqs-consumer.module.ts` - NestJS module definition
- `src/services/sqs-consumer.controller.ts` - REST endpoints for monitoring
- `src/app.module.ts` - Added SqsConsumerModule to imports
- `.env` - Added COURT_NOTICE_RESULT_QUEUE_URL
- `.env.example` - Added example SQS configuration 