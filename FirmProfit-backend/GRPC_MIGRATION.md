# gRPC Migration Guide

This document outlines the migration from TCP to gRPC for communication between the API Gateway and Shared Service.

## Overview

The API Gateway and Shared Service now communicate using gRPC instead of TCP for improved performance, type safety, and better error handling.

## Key Components

### Proto Files

Proto files define the contract between services:

- `auth.proto`: Authentication-related services
- `user.proto`: User-related services

Located in:
- `api-gateway/proto/`
- `shared-service/proto/`

### API Gateway Changes

1. **GrpcClientModule**: A module that configures the gRPC client connection.
   - Located at: `api-gateway/src/grpc-client/grpc-client.module.ts`
   - Important: Only exports GrpcClientService, not the ClientsModule providers directly

2. **GrpcClientService**: Service that provides typed methods for communicating with the gRPC services.
   - Located at: `api-gateway/src/grpc-client/grpc-client.service.ts`
   - Acts as a wrapper around the ClientGrpc interface

3. **Exception Handling**: GrpcClientExceptionFilter for translating gRPC errors to HTTP errors.
   - Located at: `api-gateway/src/exceptions/grpc-client-exception.filter.ts`

### Shared Service Changes

1. **Controllers**: Annotated with `@GrpcMethod` instead of `@MessagePattern`.
   - AuthController: `shared-service/src/auth/auth.controller.ts`
   - UsersController: `shared-service/src/users/users.controller.ts`

2. **Exception Handling**: GrpcExceptionFilter for handling errors in gRPC responses.
   - Located at: `shared-service/src/exceptions/grpc-exception.filter.ts`

## Environment Configuration

### API Gateway

```
SHARED_SERVICE_HOST=0.0.0.0
SHARED_SERVICE_PORT=3001
GRPC_URL=0.0.0.0:3001
```

### Shared Service

```
GRPC_URL=0.0.0.0:3001
```

## Testing the gRPC Connection

To test that gRPC is working correctly:

1. Start both services:
   ```
   cd shared-service && npm run start:dev
   cd api-gateway && npm run start:dev
   ```

2. Check the logs for successful connection:
   - Shared Service should log: "gRPC Microservice is listening on 0.0.0.0:3001"
   - API Gateway should log: "Shared Service gRPC Config: Host: 0.0.0.0, Port: 3001"

3. Test an endpoint, for example:
   ```
   curl -X POST http://localhost:3000/auth/register -H "Content-Type: application/json" -d '{"email":"<EMAIL>","password":"password123","username":"testuser"}'
   ```

## Troubleshooting

Common gRPC Issues:

1. **Connection Refused**: Ensure both services are running and can access each other.
2. **Missing Proto Definitions**: Ensure proto files are identical on both services and included in the dist folder.
3. **Deadline Exceeded**: Check for performance issues or timeouts.
4. **Invalid Argument**: Check the data types being passed match the proto definitions.
5. **Module Export Errors**: Ensure you're only exporting the GrpcClientService from GrpcClientModule, not the 'SHARED_SERVICE' token directly.

For more detailed diagnostics, use the gRPC CLI tools:
```
npm install -g grpc-tools
grpc_cli ls localhost:3001 --plaintext
``` 