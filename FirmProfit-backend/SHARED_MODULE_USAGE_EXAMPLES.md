# Shared Module Usage Examples

This document shows how to update each service to use the new shared database module.

## 1. API Gateway Service Update

### Before (api-gateway/src/app.module.ts):
```typescript
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { TenantModule } from './tenant/tenant.module';
import { DatabaseModule } from './database/database.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    TypeOrmModule.forRootAsync({
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('DB_HOST'),
        port: configService.get('DB_PORT'),
        username: configService.get('DB_USERNAME'),
        password: configService.get('DB_PASSWORD'),
        database: configService.get('DB_NAME'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: configService.get('NODE_ENV') !== 'production',
      }),
      inject: [ConfigService],
    }),
    DatabaseModule,
    AuthModule,
    TenantModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
```

### After (api-gateway/src/app.module.ts):
```typescript
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { TenantModule } from './tenant/tenant.module';
import { SharedDatabaseModule } from '@shared/database';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    SharedDatabaseModule, // Replace DatabaseModule and TypeOrmModule
    AuthModule,
    TenantModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
```

### Update Imports in Services:
```typescript
// Before
import { TenantConfig } from '../database/entities/tenant-config.entity';
import { Tenant } from '../tenant/entities/tenant.entity';

// After
import { TenantConfig, Tenant } from '@shared/database';
```

### Update DTOs:
```typescript
// Before
import { SignInDto } from '../dto/sign-in.dto';
import { CreateTenantDto } from '../tenant/dto/create-tenant.dto';

// After
import { SignInDto, CreateTenantDto } from '@shared/database';
```

## 2. Shared Service Update

### Before (shared-service/src/app.module.ts):
```typescript
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { User } from './users/entities/user.entity';
import { UserMfa } from './users/entities/mfa.entity';
import { WorkflowModule } from './workflow/workflow.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    TypeOrmModule.forRootAsync({
      // ... complex configuration
    }),
    MongooseModule.forRootAsync({
      // ... complex configuration
    }),
    WorkflowModule,
    UsersModule,
    AuthModule,
  ],
})
export class AppModule {}
```

### After (shared-service/src/app.module.ts):
```typescript
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { WorkflowModule } from './workflow/workflow.module';
import { UsersModule } from './users/users.module';
import { AuthModule } from './auth/auth.module';
import { SharedDatabaseModule } from '@shared/database';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    SharedDatabaseModule, // Replaces all database configuration
    WorkflowModule,
    UsersModule,
    AuthModule,
  ],
})
export class AppModule {}
```

### Update Workflow Module (shared-service/src/workflow/workflow.module.ts):
```typescript
// Before
import { MongooseModule } from '@nestjs/mongoose';
import { Template, TemplateSchema } from './entities/template.entity';
import { Category, CategorySchema } from './entities/category.entity';
import { Client, ClientSchema } from './entities/client.entity';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Template.name, schema: TemplateSchema },
      { name: Category.name, schema: CategorySchema },
      { name: Client.name, schema: ClientSchema },
      // ... many more
    ]),
  ],
})

// After
import { MongooseModule } from '@nestjs/mongoose';
import { 
  Template, 
  TemplateSchema,
  Category, 
  CategorySchema,
  Client, 
  ClientSchema 
} from '@shared/database';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Template.name, schema: TemplateSchema },
      { name: Category.name, schema: CategorySchema },
      { name: Client.name, schema: ClientSchema },
      // ... import from shared
    ]),
  ],
})
```

## 3. Connectors Service Update

### Before (connectors/src/app.module.ts):
```typescript
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TenantConfig } from './database/entities/tenant-config.entity';

@Module({
  imports: [
    MongooseModule.forRootAsync({
      // ... mongo config
    }),
    TypeOrmModule.forRootAsync({
      // ... postgres config
    }),
    TypeOrmModule.forFeature([TenantConfig]),
    GmailModule,
  ],
})
export class AppModule {}
```

### After (connectors/src/app.module.ts):
```typescript
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { SharedDatabaseModule } from '@shared/database';
import { GmailModule } from './gmail/gmail.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    SharedDatabaseModule, // Handles both MongoDB and PostgreSQL
    GmailModule,
  ],
})
export class AppModule {}
```

### Update Gmail Module (connectors/src/gmail/gmail.module.ts):
```typescript
// Before
import { MongooseModule } from '@nestjs/mongoose';
import { Email, EmailSchema } from './schemas/email.schema';

// After
import { MongooseModule } from '@nestjs/mongoose';
import { Email, EmailSchema } from '@shared/database';
```

## 4. Package.json Updates

Add the shared module to each service's package.json:

### api-gateway/package.json:
```json
{
  "dependencies": {
    "@shared/database": "file:../shared",
    // ... other dependencies
  }
}
```

### shared-service/package.json:
```json
{
  "dependencies": {
    "@shared/database": "file:../shared",
    // ... other dependencies
  }
}
```

### connectors/package.json:
```json
{
  "dependencies": {
    "@shared/database": "file:../shared",
    // ... other dependencies
  }
}
```

## 5. Environment Configuration

Update your environment files to use the expected configuration format:

### .env (All Services):
```env
# PostgreSQL Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_NAME=firmprofit
DB_SCHEMA=public
DB_SYNC=false
DB_LOGGING=false

# MongoDB Configuration
MONGO_URI=mongodb://localhost:27017
MONGO_DB_NAME=firmprofit

# Other environment variables...
```

## 6. Installation Commands

After updating the files, run these commands in each service:

```bash
# Install the shared module
npm install

# Remove old database-related files (optional cleanup)
rm -rf src/database/entities/
rm -rf src/dto/sign-*.dto.ts
rm -rf src/auth/dto/

# Build and test
npm run build
npm run test
```

## 7. Import Examples in Controllers and Services

### User Service Example:
```typescript
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, UserMfa, SignUpDto, JwtPayload } from '@shared/database';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    
    @InjectRepository(UserMfa)
    private mfaRepository: Repository<UserMfa>,
  ) {}

  async createUser(signUpDto: SignUpDto): Promise<User> {
    // Implementation using shared DTO and entity
  }

  validateToken(token: string): JwtPayload {
    // Implementation using shared type
  }
}
```

### Workflow Service Example:
```typescript
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { 
  Client, 
  ClientDocument, 
  Matter, 
  MatterDocument,
  MyCaseEvent,
  ApiResponse 
} from '@shared/database';

@Injectable()
export class WorkflowService {
  constructor(
    @InjectModel(Client.name)
    private clientModel: Model<ClientDocument>,
    
    @InjectModel(Matter.name)
    private matterModel: Model<MatterDocument>,
  ) {}

  async getClients(): Promise<ApiResponse<Client[]>> {
    // Implementation using shared schemas and types
  }
}
```

## 8. Testing the Migration

After completing the migration:

1. **Start each service individually** to ensure they can connect to databases
2. **Test API endpoints** to verify DTOs work correctly
3. **Check database operations** to ensure entities and schemas function
4. **Verify type safety** by running TypeScript compilation
5. **Run existing tests** to ensure no regressions

## Benefits After Migration

- ✅ **No duplicate code**: Single source of truth for entities and DTOs
- ✅ **Type safety**: Consistent types across all services
- ✅ **Easy maintenance**: Update once, used everywhere
- ✅ **Better DX**: Improved import organization and IDE support
- ✅ **Reduced bundle size**: No duplicate dependencies
- ✅ **Consistent configuration**: Unified database setup across services 