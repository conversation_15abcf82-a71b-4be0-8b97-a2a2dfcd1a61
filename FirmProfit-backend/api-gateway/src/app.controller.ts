import { Controller, Get, Post, HttpCode, Req, Body, Logger } from '@nestjs/common';
import { Request } from 'express';
import { AppService } from './app.service';
import { GrpcClientService } from './grpc-client/grpc-client.service';
import { MyCaseAuthService } from './mycase/services/mycase-auth.service';
import { 
  MyCaseWebhookData, 
  MyCaseWebhookAuthenticationData, 
  MyCaseEnrichedResourceBody, 
  MyCaseWebhookProcessResult 
} from './mycase/interfaces/mycase.interface';

@Controller()
export class AppController {
  private readonly logger = new Logger(AppController.name);
  
  constructor(
    private readonly appService: AppService,
    private readonly grpcClient: GrpcClientService,
    private readonly myCaseAuthService: MyCaseAuthService,
  ) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('mycase/callback')
  async handleMycaseOAuthCallback() {
    return true;
  }

  @Post('mycase/webhooks/subscriptions')
  @HttpCode(200)
  async handleMyCaseWebhook(@Req() req: Request, @Body() body: any): Promise<MyCaseWebhookProcessResult> {
    this.logger.log('=== MyCase post Webhook Received ===');
    this.logger.log(`Request Headers: ${JSON.stringify(req.headers, null, 2)}`);
    this.logger.log(`Request Body: ${JSON.stringify(body, null, 2)}`);
    this.logger.log(`Request URL: ${req.url}`);
    this.logger.log(`Request Method: ${req.method}`);
    this.logger.log(`Request IP: ${req.ip}`);
    this.logger.log(`User-Agent: ${req.get('User-Agent')}`);
    
    // Log specific webhook details if available
    if (body?.event_type) {
      this.logger.log(`Event Type: ${body.event_type}`);
    }
    if (body?.id) {
      this.logger.log(`Event ID: ${body.id}`);
    }
    if (body?.timestamp) {
      this.logger.log(`Event Timestamp: ${body.timestamp}`);
    }

    // Log the nested resource_body structure
    if (body?.resource_body) {
      this.logger.log(`Resource Body Type: ${typeof body.resource_body}`);
      this.logger.log(`Resource Body: ${JSON.stringify(body.resource_body, null, 2)}`);
    }
    
    this.logger.log('=== End MyCase Webhook ===');
    
    try {
      // Process authentication and enrich webhook data
      const authResult = await this.processWebhookAuthentication('1');
      const enrichedWebhookData = this.enrichWebhookData(body, authResult);
      
      // Save to database
      const result = await this.grpcClient.saveMyCaseWebhookData(enrichedWebhookData);
      
      this.logger.log(`MyCase webhook data saved successfully: ${result.message}`);
      
      return { 
        received: true, 
        saved: true, 
        record_id: result.id,
        authentication_status: authResult.status,
        access_token_included: !!authResult.access_token,
      };
    } catch (error) {
      this.logger.error(`Error saving MyCase webhook data: ${error.message}`);
      this.logger.error(`Error stack: ${error.stack}`);
      
      // Still return success to prevent webhook retries, but log the error
      return { 
        received: true, 
        saved: false, 
        authentication_status: 'unknown',
        access_token_included: false,
        error: error.message 
      };
    }
  }

  /**
   * Process authentication for webhook data
   */
  private async processWebhookAuthentication(firmUuid: string): Promise<MyCaseWebhookAuthenticationData> {
    let accessToken: string | null = null;
    let authenticationStatus: MyCaseWebhookAuthenticationData['status'] = 'not_attempted';
    
    if (firmUuid) {
      try {
        this.logger.log(`Attempting to authenticate for tenant: ${firmUuid}`);
        const tokenResponse = await this.myCaseAuthService.authenticateWithTenantConfig(firmUuid);
        accessToken = tokenResponse.access_token;
        authenticationStatus = 'success';
        this.logger.log(`Successfully authenticated for tenant: ${firmUuid}`);
      } catch (authError) {
        this.logger.warn(`Authentication failed for tenant ${firmUuid}: ${authError.message}`);
        authenticationStatus = 'failed';
        // Continue processing even if authentication fails
      }
    } else {
      this.logger.warn('No firm_uuid provided in webhook data, skipping authentication');
      authenticationStatus = 'no_tenant_id';
    }

    return {
      status: authenticationStatus,
      access_token: accessToken,
      authenticated_at: accessToken ? new Date().toISOString() : null,
    };
  }

  /**
   * Enrich webhook data with authentication information
   */
  private enrichWebhookData(body: any, authData: MyCaseWebhookAuthenticationData): MyCaseWebhookData {
    // Prepare the resource body with authentication details
    const resourceBodyData = body.resource_body || {};
    
    // Add authentication metadata to resource body
    const enrichedResourceBody: MyCaseEnrichedResourceBody = {
      ...resourceBodyData,
      _authentication: authData,
    };

    // Convert resource_body to JSON string to prevent data loss in gRPC transmission
    const resourceBodyString = JSON.stringify(enrichedResourceBody);

    const webhookData: MyCaseWebhookData = {
      firm_uuid: body.firm_uuid,
      action: body.action,
      resource: body.resource,
      resource_body: resourceBodyString,
      timestamp: body.timestamp,
    };

    // Log truncated data for debugging
    this.logger.log(`Sending to gRPC service: ${JSON.stringify({
      ...webhookData,
      resource_body: '... (truncated for logging, contains authentication data)',
    }, null, 2)}`);

    return webhookData;
  }

  @Get('grpc-status')
  async checkGrpcStatus() {
    try {
      // Just a simple call to see if gRPC connection is alive
      // This doesn't need to actually return data, just test the connection
      await this.grpcClient.getUserById({ id: 0 }).catch(() => {
        // Even an error response means the connection is working
        return {
          status: 'connected',
          message: 'gRPC is working but user not found',
        };
      });

      return {
        status: 'connected',
        message: 'gRPC connection is working properly',
      };
    } catch (error) {
      return {
        status: 'error',
        message: 'Failed to connect to gRPC service',
        error: error.message,
      };
    }
  }

  // @Get('shared/login')
  // emitLoginSuccess() {
  //   this.appService.emitLoginSuccess({ userId: 123, timestamp: new Date() });
  // }

  // @Post('shared/signup')
  // async emitSignUpSuccess(@Body() signUpDto: SignUpDto) {
  //   this.appService.emitSignup(signUpDto);
  // }
}
