import { Module, MiddlewareConsumer, RequestMethod } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TenantService } from './tenant.service';
import { TenantController } from './tenant.controller';
import { TenantContextService } from './tenant-context.service';
import { TenantContextMiddleware } from './tenant-context.middleware';
import { Tenant, TenantConfig } from '@shared/database';
import { TenantConfigService } from './services/tenant-config.service';
import { TenantConfigController } from './controllers/tenant-config.controller';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { TenantContextInterceptor } from './tenant-context.interceptor';
import { MyCaseAuthService } from '../mycase/services/mycase-auth.service';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [
    TypeOrmModule.forFeature([Tenant, TenantConfig]),
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
  ],
  controllers: [TenantController, TenantConfigController],
  providers: [
    TenantService,
    TenantContextService,
    TenantConfigService,
    MyCaseAuthService,
    {
      provide: APP_INTERCEPTOR,
      useClass: TenantContextInterceptor,
    },
  ],
  exports: [TenantService, TenantContextService, TenantConfigService],
})
export class TenantModule {
  configure(consumer: MiddlewareConsumer) {
    // Apply tenant context middleware to all routes except tenant management routes
    consumer
      .apply(TenantContextMiddleware)
      .exclude(
        { path: 'tenants', method: RequestMethod.ALL },
        { path: 'tenants/(.*)', method: RequestMethod.ALL },
        { path: 'tenant-config', method: RequestMethod.ALL },
        { path: 'tenant-config/(.*)', method: RequestMethod.ALL },
      )
      .forRoutes('*');
  }
}
