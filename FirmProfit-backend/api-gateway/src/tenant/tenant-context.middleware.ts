import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { TenantService } from './tenant.service';
import { TenantContextService } from './tenant-context.service';

@Injectable()
export class TenantContextMiddleware implements NestMiddleware {
  constructor(
    private readonly tenantService: TenantService,
    private readonly tenantContextService: TenantContextService,
  ) {}

  async use(req: Request, res: Response, next: NextFunction) {
    // Get tenant identifier from request headers
    const tenantId = req.headers['x-tenant-id'] as any;

    if (tenantId) {
      try {
        // Find the tenant by ID
        const tenant = await this.tenantService.findOne(tenantId);

        // Set the tenant context for this request
        this.tenantContextService.setCurrentTenant(tenant);
      } catch (error) {
        throw error;
        // If tenant not found, proceed without setting a tenant context
      }
    }

    next();
  }
}
