import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Tenant } from '@shared/database';
import { CreateTenantDto } from './dto/create-tenant.dto';
import { UpdateTenantDto } from './dto/update-tenant.dto';

@Injectable()
export class TenantService {
  constructor(
    @InjectRepository(Tenant)
    private readonly tenantRepository: Repository<Tenant>,
    private readonly dataSource: DataSource,
  ) {}

  async create(createTenantDto: CreateTenantDto): Promise<Tenant> {
    try {
      const existingTenant = await this.tenantRepository.findOne({
        where: [
          { name: createTenantDto.name },
          { schema: createTenantDto.schema },
        ],
      });

      if (existingTenant) {
        throw new BadRequestException(
          'Tenant with this name or schema already exists',
        );
      }

      // Create the new schema in PostgreSQL
      await this.dataSource.query(
        `CREATE SCHEMA IF NOT EXISTS "${createTenantDto.schema}"`,
      );

      // Create the new tenant record
      const tenant = this.tenantRepository.create(createTenantDto);
      return await this.tenantRepository.save(tenant);
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to create tenant: ${error.message}`,
      );
    }
  }

  async findAll(): Promise<Tenant[]> {
    return await this.tenantRepository.find();
  }

  async findOne(id: number): Promise<Tenant> {
    const tenant = await this.tenantRepository.findOne({ where: { id } });
    if (!tenant) {
      throw new NotFoundException(`Tenant with ID ${id} not found`);
    }
    return tenant;
  }

  async findBySchema(schema: string): Promise<Tenant> {
    const tenant = await this.tenantRepository.findOne({ where: { schema } });
    if (!tenant) {
      throw new NotFoundException(`Tenant with schema ${schema} not found`);
    }
    return tenant;
  }

  async update(id: number, updateTenantDto: UpdateTenantDto): Promise<Tenant> {
    const tenant = await this.findOne(id);

    // If schema is being updated, we need to rename the schema in PostgreSQL
    if (updateTenantDto.schema && updateTenantDto.schema !== tenant.schema) {
      await this.dataSource.query(
        `ALTER SCHEMA "${tenant.schema}" RENAME TO "${updateTenantDto.schema}"`,
      );
    }

    Object.assign(tenant, updateTenantDto);
    return await this.tenantRepository.save(tenant);
  }

  async remove(id: number): Promise<void> {
    const tenant = await this.findOne(id);

    // Drop the schema and all objects within it
    await this.dataSource.query(
      `DROP SCHEMA IF EXISTS "${tenant.schema}" CASCADE`,
    );

    await this.tenantRepository.remove(tenant);
  }

  async getCurrentTenantSchema(): Promise<string | null> {
    // This would be implemented based on your tenant identification strategy
    // For example, getting it from a request header or from the current user
    return null;
  }

  async createTenantSchemas(): Promise<void> {
    // This method can be used during application bootstrap to ensure all tenant schemas exist
    const tenants = await this.findAll();

    for (const tenant of tenants) {
      await this.dataSource.query(
        `CREATE SCHEMA IF NOT EXISTS "${tenant.schema}"`,
      );
    }
  }
}
