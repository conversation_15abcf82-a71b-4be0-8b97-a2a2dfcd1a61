import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { TenantContextService } from './tenant-context.service';
import { tenantContext } from './tenant-naming-strategy';

@Injectable()
export class TenantContextInterceptor implements NestInterceptor {
  constructor(private readonly tenantContextService: TenantContextService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const schema = this.tenantContextService.getCurrentTenantSchema();

    if (!schema) {
      // If no tenant context, just proceed with the request
      return next.handle();
    }

    // Use AsyncLocalStorage to set the tenant context for this request
    return new Observable((subscriber) => {
      try {
        tenantContext.run({ schema }, () => {
          next.handle().subscribe({
            next: (value) => subscriber.next(value),
            error: (err) => subscriber.error(err),
            complete: () => subscriber.complete(),
          });
        });
      } catch (error) {
        subscriber.error(error);
      }
    });
  }
}
