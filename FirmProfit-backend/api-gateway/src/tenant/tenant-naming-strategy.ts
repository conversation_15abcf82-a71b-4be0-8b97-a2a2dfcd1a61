import { DefaultNamingStrategy, NamingStrategyInterface, Table } from 'typeorm';
import { snakeCase } from 'typeorm/util/StringUtils';
import { AsyncLocalStorage } from 'async_hooks';

interface TenantContext {
  schema: string;
}

// This is a global AsyncLocalStorage instance to store the current tenant schema
export const tenantContext = new AsyncLocalStorage<TenantContext>();

export class TenantNamingStrategy
  extends DefaultNamingStrategy
  implements NamingStrategyInterface
{
  tableName(targetName: string, userSpecifiedName: string | undefined): string {
    return userSpecifiedName || snakeCase(targetName);
  }

  columnName(
    propertyName: string,
    customName: string,
    embeddedPrefixes: string[],
  ): string {
    return (
      customName || snakeCase(embeddedPrefixes.concat(propertyName).join('_'))
    );
  }

  relationName(propertyName: string): string {
    return snakeCase(propertyName);
  }

  joinColumnName(relationName: string, referencedColumnName: string): string {
    return snakeCase(`${relationName}_${referencedColumnName}`);
  }

  joinTableName(firstTableName: string, secondTableName: string): string {
    return snakeCase(`${firstTableName}_${secondTableName}`);
  }

  joinTableColumnName(
    tableName: string,
    propertyName: string,
    columnName: string,
  ): string {
    return snakeCase(`${tableName}_${columnName || propertyName}`);
  }

  classTableInheritanceParentColumnName(
    parentTableName: string,
    parentTableIdPropertyName: string,
  ): string {
    return snakeCase(`${parentTableName}_${parentTableIdPropertyName}`);
  }

  // Override the schema name based on the current tenant context
  schemaName(
    target: Table | string,
    givenSchemaName: string | undefined,
  ): string | undefined {
    const currentTenantContext = tenantContext.getStore();

    // If there is a current tenant context, use its schema
    if (currentTenantContext && currentTenantContext.schema) {
      return currentTenantContext.schema;
    }

    // Otherwise, fallback to the given schema name or 'public'
    return givenSchemaName || 'public';
  }
}
