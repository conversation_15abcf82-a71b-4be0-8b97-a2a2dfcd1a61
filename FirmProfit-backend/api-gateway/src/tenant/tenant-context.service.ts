import { Injectable } from '@nestjs/common';
import { Tenant } from '@shared/database';

@Injectable()
export class TenantContextService {
  private currentTenant: Tenant | null = null;

  setCurrentTenant(tenant: Tenant): void {
    this.currentTenant = tenant;
  }

  getCurrentTenant(): Tenant | null {
    return this.currentTenant;
  }

  getCurrentTenantSchema(): string | null {
    return this.currentTenant?.schema || null;
  }
}
