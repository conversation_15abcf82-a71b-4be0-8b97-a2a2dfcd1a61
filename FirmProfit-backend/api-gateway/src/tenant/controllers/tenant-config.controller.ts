import {
  Controller,
  Get,
  Post,
  Delete,
  Param,
  Body,
  HttpCode,
  HttpStatus,
  ValidationPipe,
} from '@nestjs/common';
import { TenantConfigService } from '../services/tenant-config.service';
import { MyCaseAuthService } from '../../mycase/services/mycase-auth.service';
import { ResponseMessage } from '../../auth/decorators/response.decorator';
import { IsString, IsOptional, IsBoolean, IsNotEmpty } from 'class-validator';

export class SaveMyCaseConfigDto {
  api_key: string;
  refresh_key: string;
  client_id?: string;
  client_secret?: string;
}

export class SaveTimezoneConfigDto {
  @IsString()
  @IsNotEmpty()
  timezone: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsBoolean()
  enabled?: boolean;
}

@Controller('tenant-config')
export class TenantConfigController {
  constructor(
    private readonly tenantConfigService: TenantConfigService,
    private readonly myCaseAuthService: MyCaseAuthService,
  ) { }

  @Get(':tenantId/mycase')
  @ResponseMessage('MyCase configuration retrieved successfully')
  async getMyCaseConfig(@Param('tenantId') tenantId: string) {
    return this.tenantConfigService.getMyCaseConfig(tenantId);
  }

  @Post(':tenantId/mycase')
  @HttpCode(HttpStatus.CREATED)
  @ResponseMessage('MyCase configuration saved successfully')
  async saveMyCaseConfig(
    @Param('tenantId') tenantId: string,
    @Body(ValidationPipe) configDto: SaveMyCaseConfigDto,
  ) {
    return this.tenantConfigService.saveMyCaseConfig(tenantId, configDto);
  }

  @Post(':tenantId/mycase/authenticate')
  @HttpCode(HttpStatus.OK)
  @ResponseMessage('MyCase authentication successful')
  async authenticateMyCase(@Param('tenantId') tenantId: string) {
    const tokenResponse =
      await this.myCaseAuthService.authenticateWithTenantConfig(tenantId);

    return {
      access_token: tokenResponse.access_token,
      expires_in: tokenResponse.expires_in,
      token_type: tokenResponse.token_type,
      scope: tokenResponse.scope,
    };
  }

  @Get(':tenantId/configs')
  @ResponseMessage('Tenant configurations retrieved successfully')
  async getTenantConfigs(@Param('tenantId') tenantId: string) {
    return this.tenantConfigService.getTenantConfigs(tenantId);
  }

  @Delete(':tenantId/config/:key')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ResponseMessage('Configuration deleted successfully')
  async deleteConfig(
    @Param('tenantId') tenantId: string,
    @Param('key') key: string,
  ) {
    await this.tenantConfigService.deleteConfig(tenantId, key);
    return { message: 'Configuration deleted successfully' };
  }
} 