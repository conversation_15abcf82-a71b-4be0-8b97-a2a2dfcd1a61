import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import { TenantConfig } from '../../database/entities/tenant-config.entity';

export interface MyCaseConfig {
  api_key: string;
  refresh_key: string;
  client_id?: string;
  client_secret?: string;
  access_token?: string;
  refresh_token?: string;
  access_token_expires_at?: string;
  username?: string;
  password?: string;
  expires_in?: number;
  timezone?: string; // Add timezone configuration
}


@Injectable()
export class TenantConfigService {
  private readonly logger = new Logger(TenantConfigService.name);
  private readonly encryptionKey: string;

  constructor(
    @InjectRepository(TenantConfig)
    private readonly tenantConfigRepository: Repository<TenantConfig>,
    private readonly configService: ConfigService,
  ) {
    this.encryptionKey = this.configService.get<string>(
      'ENCRYPTION_KEY',
      'default-encryption-key-change-in-production',
    );
  }

  /**
   * Encrypt sensitive data
   */
  private encrypt(text: string): string {
    const algorithm = 'aes-256-cbc';
    const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv(algorithm, key, iv);

    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    return iv.toString('hex') + ':' + encrypted;
  }

  /**
   * Decrypt sensitive data
   */
  private decrypt(encryptedText: string): string {
    const algorithm = 'aes-256-cbc';
    const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);

    const textParts = encryptedText.split(':');

    // Handle legacy format (no IV)
    if (textParts.length === 1) {
      try {
        const decipher = crypto.createDecipher(algorithm, this.encryptionKey);
        let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return decrypted;
      } catch {
        throw new Error('Failed to decrypt data with legacy method');
      }
    }

    // Handle new format (with IV)
    try {
      const iv = Buffer.from(textParts[0], 'hex');
      const encryptedData = textParts[1];
      const decipher = crypto.createDecipheriv(algorithm, key, iv);
      let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      return decrypted;
    } catch {
      throw new Error('Failed to decrypt data with IV method');
    }
  }

  /**
   * Get MyCase configuration for a tenant
   */
  async getMyCaseConfig(tenantId: string): Promise<MyCaseConfig> {
    try {
      const config = await this.tenantConfigRepository.findOne({
        where: {
          tenant_id: tenantId,
          key: 'my_case_config',
        },
      });

      if (!config) {
        throw new NotFoundException(
          `MyCase configuration not found for tenant: ${tenantId}`,
        );
      }

      // Decrypt sensitive fields
      const decryptedConfig = { ...config.config };
      if (decryptedConfig.api_key) {
        decryptedConfig.api_key = this.decrypt(decryptedConfig.api_key);
      }

      if (decryptedConfig.refresh_key) {
        decryptedConfig.refresh_key = this.decrypt(decryptedConfig.refresh_key);
      }
      if (decryptedConfig.client_secret) {
        decryptedConfig.client_secret = this.decrypt(
          decryptedConfig.client_secret,
        );
      }

      this.logger.log(`Retrieved MyCase config for tenant: ${tenantId}`);
      return decryptedConfig as MyCaseConfig;
    } catch (error) {
      this.logger.error(
        `Failed to get MyCase config for tenant ${tenantId}:`,
        error.message,
      );
      throw error;
    }
  }

  /**
   * Save or update MyCase configuration for a tenant
   */
  async saveMyCaseConfig(
    tenantId: string,
    config: MyCaseConfig,
  ): Promise<TenantConfig> {
    try {
      // Encrypt sensitive fields
      const encryptedConfig = { ...config };
      if (encryptedConfig.api_key) {
        encryptedConfig.api_key = this.encrypt(encryptedConfig.api_key);
      }
      if (encryptedConfig.refresh_key) {
        encryptedConfig.refresh_key = this.encrypt(encryptedConfig.refresh_key);
      }
      if (encryptedConfig.client_secret) {
        encryptedConfig.client_secret = this.encrypt(
          encryptedConfig.client_secret,
        );
      }

      const expiresAt = new Date(
        Date.now() + encryptedConfig.expires_in * 1000,
      );

      encryptedConfig.access_token_expires_at = expiresAt.toString();

      const existingConfig = await this.tenantConfigRepository.findOne({
        where: {
          tenant_id: tenantId,
          key: 'my_case_config',
        },
      });

      if (existingConfig) {
        existingConfig.config = encryptedConfig;
        const savedConfig =
          await this.tenantConfigRepository.save(existingConfig);
        this.logger.log(`Updated MyCase config for tenant: ${tenantId}`);
        return savedConfig;
      } else {
        const newConfig = this.tenantConfigRepository.create({
          tenant_id: tenantId,
          key: 'my_case_config',
          config: encryptedConfig,
        });
        const savedConfig = await this.tenantConfigRepository.save(newConfig);
        this.logger.log(`Created MyCase config for tenant: ${tenantId}`);
        return savedConfig;
      }
    } catch (error) {
      this.logger.error(
        `Failed to save MyCase config for tenant ${tenantId}:`,
        error.message,
      );
      throw error;
    }
  }


  /**
   * Get all configurations for a tenant
   */
  async getTenantConfigs(tenantId: string): Promise<TenantConfig[]> {
    try {
      const configs = await this.tenantConfigRepository.find({
        where: { tenant_id: tenantId },
      });

      this.logger.log(
        `Retrieved ${configs.length} configs for tenant: ${tenantId}`,
      );
      return configs;
    } catch (error) {
      this.logger.error(
        `Failed to get configs for tenant ${tenantId}:`,
        error.message,
      );
      throw error;
    }
  }

  /**
   * Delete a configuration for a tenant
   */
  async deleteConfig(tenantId: string, key: string): Promise<void> {
    try {
      const result = await this.tenantConfigRepository.delete({
        tenant_id: tenantId,
        key,
      });

      if (result.affected === 0) {
        throw new NotFoundException(
          `Configuration with key '${key}' not found for tenant: ${tenantId}`,
        );
      }

      this.logger.log(`Deleted config '${key}' for tenant: ${tenantId}`);
    } catch (error) {
      this.logger.error(
        `Failed to delete config '${key}' for tenant ${tenantId}:`,
        error.message,
      );
      throw error;
    }
  }
}
