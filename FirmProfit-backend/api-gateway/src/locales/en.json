{"LOGIN_SUCCESS": "Login Successfully", "MFA_ENABLE_SUCCESS": "MFA enable Successfully", "MFA_VERIFY_SUCCESS": "MFA verify Successfully", "MFA_VALIDATE_SUCCESS": "MFA validate Successfully", "TOKEN_VALIDATE_SUCCESS": "Validate Token successfully", "RESET_PASS_SUCCESS": "Reset password successfully", "EMAIL_ALREADY_EXISTS_ERROR": "User with this email already exists", "ALREADY_EXISTS_ERROR": "already exists", "USER_API_ERROR": "Error registering user", "USER_NOT_FOUND_ERROR": "User not found", "INVALID_CRED": "Invalid credentials", "NOT_FOUND": "Not found", "INACTIVE": "Inactive", "INCORRECT_EMAIL_PASSWORD": "Incorrect email or password.", "MULTIPLE_CRED": "Multiple credentials", "MULTIPLE_FAILED_ATTAMP": "Multiple failed sign-in attempts on your account", "MULTIPLE_FAILED_ATTAMP_MFA": "Multiple failed attempts to enter the authentication code for your account. For security reasons, MFA verification is temporarily locked for", "SOMETHING_WENT_WRONG": "Something went wrong", "INCORRECT_MFA_LOCKED": "Incorrect MFA Locked", "INVALID_MFA_CODE": "Incorrect/Invalid verification code. Please try again.", "MULTIPLE_LOGIN": "Multiple Time Login", "MFA_VERIFIED_SUCC": "MFA verified successfully", "INVALID_TOKEN": "Invalid token", "EMAIL_NOT_IN_TOKEN": "Email not found in token", "TOKEN_EXPIRED": "Token expired", "TASK_EXECUTION_NOT_FOUND": "Task Execution not found", "WORK_FLOW_EXECUTION_NOT_FOUND": "Workflow Execution not found", "INVALID_TASK_ID": "Invalid Task id", "INCORRECT_MFA_CODE_ERROR": "Incorrect MFA Locked"}