import {
  En<PERSON>ty,
  Column,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('tenant_config')
export class TenantConfig {
  @PrimaryColumn('text')
  tenant_id: string;

  @PrimaryColumn('text')
  key: string;

  // @PrimaryColumn('text')
  // queue_url: string;

  @Column('jsonb')
  config: Record<string, any>;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
