import { Injectable } from '@nestjs/common';
import { GrpcClientService } from './grpc-client/grpc-client.service';

@Injectable()
export class AppService {
  constructor(private readonly grpcClient?: GrpcClientService) {}

  getHello(): string {
    return 'Hello World!';
  }

  // If you want to reactivate these methods, uncomment them and use grpcClient
  /*
  emitLoginSuccess(userData: any): void {
    if (this.grpcClient) {
      this.grpcClient.handleMfa({
        action: 'login_success',
        userId: userData.userId,
      });
    }
  }

  emitSignup(userData: any): void {
    if (this.grpcClient) {
      this.grpcClient.signUp(userData);
    }
  }

  emitMFA(userData: any): void {
    if (this.grpcClient) {
      this.grpcClient.handleMfa(userData);
    }
  }
  */
}
