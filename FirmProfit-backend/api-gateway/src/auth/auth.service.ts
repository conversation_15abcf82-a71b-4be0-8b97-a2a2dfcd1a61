import {
  Injectable,
  UnauthorizedException,
  ConflictException,
  InternalServerErrorException,
  GoneException,
  BadRequestException,
} from '@nestjs/common';
import { JwtService, TokenExpiredError } from '@nestjs/jwt';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import {
  ResetPasswordDto,
  ValidateTokenDto,
  VerifyMfaDto,
} from './dto/verify-mfa.dto';
import { JwtPayload } from './interfaces/jwt-payload.interface';
import * as bcrypt from 'bcrypt';
import * as MESSAGE from '../locales/en.json';
import { GrpcClientService } from '../grpc-client/grpc-client.service';
import { throwError } from 'rxjs';
import { ErrorExceptions } from 'src/exceptions/error.exception';

@Injectable()
export class AuthService {
  constructor(
    private readonly jwtService: JwtService,
    private readonly grpcClient: GrpcClientService,
  ) {
  }

  async register(registerDto: RegisterDto) {
    try {
      // Hash the password
      const hashedPassword = await bcrypt.hash(registerDto.password, 10);

      // Send user data to shared service for registration
      const user = await this.grpcClient
        .register({
          ...registerDto,
          password: hashedPassword,
          isActive: true,
          isDeleted: false,
        })
        .catch((error) => {
          if (error.message?.includes(MESSAGE.ALREADY_EXISTS_ERROR)) {
            throw new ConflictException(MESSAGE.EMAIL_ALREADY_EXISTS_ERROR);
          }
          throw new InternalServerErrorException(MESSAGE.USER_API_ERROR);
        });

      // Generate JWT token
      const payload: JwtPayload = {
        email: user.email,
        sub: user.id,
        roleId: user.roleId,
      };

      return {
        access_token: this.jwtService.sign(payload),
        user: {
          id: user.id,
          email: user.email,
          roleId: user.roleId,
        },
      };
    } catch (error) {
      throw error instanceof ConflictException
        ? error
        : new InternalServerErrorException(MESSAGE.USER_API_ERROR);
    }
  }

  async login(loginDto: LoginDto) {
    try {

      // Request user verification from shared service
      const response = await this.grpcClient.login(loginDto);

      const { user, accessToken, requiresMfa } = response;

      // If MFA is required, return a partial auth response
      if (requiresMfa) {
        return {
          user: {
            id: user.id,
            email: user.email,
            roleId: user.roleId,
            loginAttemptCount: user.loginAttemptCount,
            lockedAccount: user.lockedAccount,
            isMFAEnable: requiresMfa,
            access_token: user.access_token,
          },
        };
      }

      // Return full auth response
      return {
        access_token: accessToken,
        user: {
          id: user.id,
          email: user.email,
          roleId: user.roleId,
          loginAttemptCount: user.loginAttemptCount,
          lockedAccount: user.lockedAccount,
          isMFAEnable: requiresMfa || false,
          access_token: accessToken,
        },
      };
    } catch (error) {
      if (
        error.message?.includes(MESSAGE.INVALID_CRED) ||
        error.message?.includes(MESSAGE.USER_NOT_FOUND_ERROR) ||
        error.message?.includes(MESSAGE.INACTIVE)
      ) {
        return throwError(() => ErrorExceptions.INCORRECT_CRED());
      }

      if (error.message?.includes(MESSAGE.MULTIPLE_CRED)) {
        return throwError(() => ErrorExceptions.MultipleFailedAttempt());
      }
      if (error.message?.includes(MESSAGE.INCORRECT_MFA_LOCKED)) {
        return throwError(() => ErrorExceptions.MultipleFailedAttemptMFA());
      }
      return throwError(() => ErrorExceptions.InternalServerError());
    }
  }

  async enableMfa(userId: number) {
    try {
      const response = await this.grpcClient.enableMfa({ userId });

      return {
        secret: response.secret,
        qrCode: response.qrCode,
      };
    } catch (error) {
      return throwError(() => ErrorExceptions.InternalServerError());
    }
  }

  async verifyMfa(verifyMfaDto: VerifyMfaDto) {
    try {
      const response = await this.grpcClient.verifyMfa(verifyMfaDto);

      return {
        success: true,
        message: 'MFA verified successfully',
        access_token: response.accessToken || response.access_token,
      };
    } catch (error) {
      if (error.message?.includes(MESSAGE.INVALID_MFA_CODE)) {
        return throwError(
          () => new UnauthorizedException(MESSAGE.INVALID_MFA_CODE),
        );
      }
      if (error.message?.includes(MESSAGE.MULTIPLE_LOGIN)) {
        return throwError(() => ErrorExceptions.MultipleFailedAttempt());
      }
      return throwError(() => ErrorExceptions.InternalServerError());
    }
  }

  async validateMfa(verifyMfaDto: VerifyMfaDto) {
    try {
      const response = await this.grpcClient.validateMfa({
        userId: verifyMfaDto.userId,
        otp: verifyMfaDto.otp,
      });

      return {
        access_token: response.accessToken || response.access_token,
        user: {
          id: response.user.id,
          email: response.user.email,
          roleId: response.user.roleId,
        },
      };
    } catch (error) {
      if (error.message?.includes(MESSAGE.INVALID_MFA_CODE)) {
        return throwError(
          () => new UnauthorizedException(MESSAGE.INVALID_MFA_CODE),
        );
      }

      if (error.message?.includes(MESSAGE.MULTIPLE_LOGIN)) {
        return throwError(() => ErrorExceptions.MultipleFailedAttempt());
      }
      if (error.message?.includes(MESSAGE.INCORRECT_MFA_CODE_ERROR)) {
        return throwError(() => ErrorExceptions.MultipleFailedAttemptMFA());
      }

      if (
        error.message?.includes(
          '13 INTERNAL: Incorrect/Invalid verification code. Please try again.',
        )
      ) {
        // Extract just the actual message without the gRPC error code prefix
        const errorMessage = error.message.replace(/^\d+\s+INTERNAL:\s+/, '');
        return throwError(() => new UnauthorizedException(errorMessage));
      }

      if (error.message) {
        // Extract message without gRPC error code prefix if it exists
        const errorMessage = error.message.replace(/^\d+\s+\w+:\s+/, '');
        return throwError(() => new UnauthorizedException(errorMessage));
      }

      return throwError(() => ErrorExceptions.InternalServerError());
    }
  }

  validateToken(token: string) {
    try {
      return this.jwtService.verify<JwtPayload>(token);
    } catch (error) {
      throw new UnauthorizedException(MESSAGE.INVALID_TOKEN);
    }
  }

  async validateUserToken(validateTokenDto: ValidateTokenDto) {
    try {
      await this.validateAndGetUserToken(validateTokenDto.token);
    } catch (error) {
      throw error;
    }
  }

  async validateAndGetUserToken(token: string) {
    try {
      const decoded = this.jwtService.verify(token, {
        secret: process.env.JWT_SECRET,
        algorithms: ['HS256'],
      });

      if (!decoded.email) {
        throw new BadRequestException('Email not found in token');
      }

      await this.grpcClient.checkAuthTokenInDB({
        token: token,
        email: decoded.email,
      });

      return decoded;
    } catch (error) {
      if (error instanceof TokenExpiredError) {
        throw new GoneException(MESSAGE.TOKEN_EXPIRED);
      }
      throw new BadRequestException(MESSAGE.INVALID_TOKEN);
    }
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto) {
    try {
      const decodeValue = await this.validateAndGetUserToken(
        resetPasswordDto.token,
      );

      await this.grpcClient.resetPassword({
        email: decodeValue?.email,
        password: resetPasswordDto.password,
      });

      return decodeValue;
    } catch (error) {
      console.log('🚀 ~ AuthService ~ resetPassword ~ error:', error);
      if (error instanceof TokenExpiredError) {
        throw new GoneException(MESSAGE.TOKEN_EXPIRED);
      }
      if (error instanceof GoneException) {
        throw new GoneException(MESSAGE.TOKEN_EXPIRED);
      }
      if (error instanceof BadRequestException) {
        throw error;
      }
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      return throwError(() => ErrorExceptions.InternalServerError());
    }
  }
}
