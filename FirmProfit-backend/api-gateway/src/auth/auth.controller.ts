import {
  Body,
  Controller,
  Get,
  Post,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import {
  ResetPasswordDto,
  ValidateTokenDto,
  VerifyMfaDto,
} from './dto/verify-mfa.dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { Roles } from './decorators/roles.decorator';
import { RolesGuard } from './guards/roles.guard';
import { ResponseMessage } from './decorators/response.decorator';
import * as MESSAGE from '../locales/en.json';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  /**
   * Register a new user
   * @param registerDto User registration data
   * @returns JWT token and user info
   */
  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  async register(@Body() registerDto: RegisterDto) {
    return this.authService.register(registerDto);
  }

  /**
   * Login an existing user
   * @param loginDto User login credentials
   * @returns JWT token and user info or MFA challenge
   */
  @Post('login')
  @ResponseMessage(MESSAGE.LOGIN_SUCCESS)
  @HttpCode(HttpStatus.OK)
  async login(@Body() loginDto: LoginDto) {
    return this.authService.login(loginDto);
  }

  /**
   * Enable MFA for a user
   * @param req Request object containing authenticated user
   * @returns MFA setup data including QR code
   */
  // @UseGuards(JwtAuthGuard)

  @Post('mfa/enable')
  @HttpCode(HttpStatus.OK)
  @ResponseMessage(MESSAGE.MFA_ENABLE_SUCCESS)
  async enableMfa(@Request() req) {
    return this.authService.enableMfa(req.body.userId);
  }

  /**
   * Verify MFA setup
   * @param verifyMfaDto MFA verification data
   * @returns Success message
   */
  @Post('mfa/verify')
  @HttpCode(HttpStatus.OK)
  @ResponseMessage(MESSAGE.MFA_VERIFY_SUCCESS)
  async verifyMfa(@Body() verifyMfaDto: VerifyMfaDto) {
    return this.authService.verifyMfa(verifyMfaDto);
  }

  /**
   * Validate MFA during login
   * @param verifyMfaDto MFA verification data
   * @returns JWT token and user info
   */
  @Post('mfa/validate')
  @HttpCode(HttpStatus.OK)
  @ResponseMessage(MESSAGE.MFA_VALIDATE_SUCCESS)
  async validateMfa(@Body() verifyMfaDto: VerifyMfaDto) {
    return this.authService.validateMfa(verifyMfaDto);
  }

  /**
   * Get current user profile
   * @param req Request object containing authenticated user
   * @returns User profile data
   */
  @UseGuards(JwtAuthGuard)
  @Get('profile')
  getProfile(@Request() req) {
    return req.user;
  }

  /**
   * Protected route for admin users only
   * @returns Admin data
   */
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(1)
  @Get('admin')
  getAdminData() {
    return { message: 'This is admin data' };
  }

  @Post('validate-token')
  @ResponseMessage(MESSAGE.TOKEN_VALIDATE_SUCCESS)
  @HttpCode(HttpStatus.OK)
  async validateUserToken(@Body() validateTokenDto: ValidateTokenDto) {
    return this.authService.validateUserToken(validateTokenDto);
  }

  @Post('reset-password')
  @ResponseMessage(MESSAGE.RESET_PASS_SUCCESS)
  @HttpCode(HttpStatus.OK)
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return await this.authService.resetPassword(resetPasswordDto);
  }
}
