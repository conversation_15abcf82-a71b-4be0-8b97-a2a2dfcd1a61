import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { GrpcClientModule } from '../grpc-client/grpc-client.module';

@Module({
  imports: [
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const jwtSecret = process.env.JWT_SECRET;
        const jwtExpiresIn = configService.get<string>('JWT_EXPIRES_IN', '1d');
        return {
          secret: jwtSecret,
          signOptions: {
            expiresIn: jwtExpiresIn,
          },
        };
      },
      inject: [ConfigService],
    }),
    GrpcClientModule,
  ],
  controllers: [AuthController],
  providers: [AuthService],
  exports: [AuthService, PassportModule],
})
export class AuthModule {}
