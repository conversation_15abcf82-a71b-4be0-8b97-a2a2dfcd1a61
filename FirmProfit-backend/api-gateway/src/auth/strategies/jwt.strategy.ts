import { Injectable, UnauthorizedException, Inject } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '../auth.service';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';
import { JwtPayload } from '../interfaces/jwt-payload.interface';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly configService: ConfigService,
    private readonly authService: AuthService,
    @Inject('SHARED_SERVICE') private readonly sharedServiceClient: ClientProxy,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET'),
    });
  }

  async validate(payload: JwtPayload) {
    try {
      // Check if user has completed MFA if required
      if (payload.requiresMfa && !payload.isMfaAuthenticated) {
        throw new UnauthorizedException('MFA required');
      }

      // Validate user exists in the shared service
      const user = await firstValueFrom(
        this.sharedServiceClient.send('user_get_by_id', payload.sub),
      );

      if (!user) {
        throw new UnauthorizedException('User no longer exists');
      }

      // Return user data to be injected in the request
      return {
        userId: payload.sub,
        email: payload.email,
        roleId: payload.roleId,
        isMfaAuthenticated: payload.isMfaAuthenticated,
      };
    } catch (error) {
      throw new UnauthorizedException('Invalid token or user');
    }
  }
}
