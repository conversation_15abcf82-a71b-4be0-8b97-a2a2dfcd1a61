import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { TenantModule } from './tenant/tenant.module';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { TransformInterceptor } from './auth/interceptors/transform.interceptor';
import { GrpcClientModule } from './grpc-client/grpc-client.module';
import { WorkflowModule } from './workflow/workflow.module';
import { MyCaseModule } from './mycase/mycase.module';
import { CronModule } from './cron/cron.module';
import { EmailModule } from './email/email.module';
import { EventSummaryModule } from './event-summary/event-summary.module';
import { User, UserMfa, Tenant, TenantConfig, Example } from '@shared/database';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    // Configure TypeORM with shared entities directly
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const dbConfig = configService.get('database') || configService.get('db')?.postgres;
        
        return {
          type: 'postgres',
          host: dbConfig?.host || configService.get('DB_HOST') || 'localhost',
          port: dbConfig?.port || configService.get('DB_PORT') || 5432,
          username: dbConfig?.username || configService.get('DB_USERNAME') || 'postgres',
          password: dbConfig?.password || configService.get('DB_PASSWORD') || 'postgres',
          database: dbConfig?.database || configService.get('DB_NAME') || 'firmprofit',
          entities: [User, UserMfa, Tenant, TenantConfig, Example],
          synchronize: configService.get('NODE_ENV') !== 'production' && (dbConfig?.synchronize || configService.get('DB_SYNC') === 'true'),
          logging: configService.get('NODE_ENV') !== 'production' && (dbConfig?.logging || configService.get('DB_LOGGING') === 'true'),
          schema: dbConfig?.schema || configService.get('DB_SCHEMA') || 'public',
          maxQueryExecutionTime: 10000,
          cache: {
            duration: 1000,
          },
        };
      },
    }),
    // Register shared entities for dependency injection
    TypeOrmModule.forFeature([User, UserMfa, Tenant, TenantConfig, Example]),
    GrpcClientModule,
    AuthModule,
    WorkflowModule,
    TenantModule,
    MyCaseModule,
    CronModule,
    EventSummaryModule,
    EmailModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_INTERCEPTOR,
      useClass: TransformInterceptor,
    },
  ],
})
export class AppModule { }
