// src/common/error-exceptions.ts
import {
  HttpException,
  HttpStatus,
  InternalServerErrorException,
} from '@nestjs/common';
import * as MESSAGE from '../locales/en.json'; // Ensure correct path

export const ErrorExceptions = {
  INCORRECT_CRED(): HttpException {
    return new HttpException(
      {
        message: MESSAGE.INCORRECT_EMAIL_PASSWORD,
        error: 'UnauthorizationError',
        statusCode: HttpStatus.UNAUTHORIZED,
      },
      HttpStatus.UNAUTHORIZED,
    );
  },

  MultipleFailedAttempt(): HttpException {
    return new HttpException(
      {
        message: MESSAGE.MULTIPLE_FAILED_ATTAMP,
        error: 'ConflictError',
        statusCode: HttpStatus.CONFLICT,
      },
      HttpStatus.CONFLICT,
    );
  },

  MultipleFailedAttemptMFA(): HttpException {
    return new HttpException(
      {
        message: MESSAGE.MULTIPLE_FAILED_ATTAMP_MFA,
        error: 'ConflictError',
        statusCode: HttpStatus.CONFLICT,
      },
      HttpStatus.CONFLICT,
    );
  },

  TASK_NOT_FOUND(): HttpException {
    return new HttpException(
      {
        message: MESSAGE.TASK_EXECUTION_NOT_FOUND,
        error: 'NotFoundError',
        statusCode: HttpStatus.NOT_FOUND,
      },
      HttpStatus.NOT_FOUND,
    );
  },
  InternalServerError(): InternalServerErrorException {
    return new InternalServerErrorException(MESSAGE.SOMETHING_WENT_WRONG);
  },

  WORK_FLOW_EXECUTION_NOT_FOUND(): HttpException {
    return new HttpException(
      {
        message: MESSAGE.WORK_FLOW_EXECUTION_NOT_FOUND,
        error: 'NotFoundError',
        statusCode: HttpStatus.NOT_FOUND,
      },
      HttpStatus.NOT_FOUND,
    );
  },

  INVALID_TASK_ID(): HttpException {
    return new HttpException(
      {
        message: MESSAGE.INVALID_TASK_ID,
        error: 'NotFoundError',
        statusCode: HttpStatus.NOT_FOUND,
      },
      HttpStatus.NOT_FOUND,
    );
  },

  //   InternalServerError(): HttpException {
  //     return new HttpException(
  //       {
  //         message: MESSAGE.INTERNAL_SERVER_ERROR,
  //         error: 'InternalServerError',
  //         statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
  //       },
  //       HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   },
};
