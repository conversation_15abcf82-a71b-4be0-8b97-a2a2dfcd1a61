import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';
import { Request, Response } from 'express';

@Catch(HttpException, RpcException)
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  catch(exception: any, host: ArgumentsHost) {
    const contextType = host.getType();

    if (contextType === 'http') {
      this.handleHttpException(exception, host);
    } else if (contextType === 'rpc') {
      this.handleRpcException(exception, host);
    }
  }

  // 🟢 Handle HTTP Errors
  private handleHttpException(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const statusCode = this.getStatus(exception);
    const message = this.extractMessage(exception);

    // Handle validation errors (e.g., BadRequestException)
    if (
      exception?.response?.message &&
      exception?.response?.error === 'Bad Request'
    ) {
      response.status(HttpStatus.BAD_REQUEST).json({
        statusCode: HttpStatus.BAD_REQUEST,
        message: exception.response.message,
        timestamp: new Date().toISOString(),
        endpoint: request.url,
      });
      return;
    }

    const errorResponse = {
      statusCode,
      message,
      timestamp: new Date().toISOString(),
      endpoint: request.url,
    };

    this.logger.warn(
      `${statusCode} - ${message} - ${request.method} ${request.url}`,
    );

    response.status(statusCode).json(errorResponse);
  }

  // 🟢 Handle RPC/TCP Errors
  private handleRpcException(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToRpc();
    let statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';
    const error = 'RpcException';

    if (exception instanceof RpcException) {
      message = exception.message;
      statusCode = HttpStatus.BAD_REQUEST;
    } else if (exception instanceof HttpException) {
      message = exception.message;
      statusCode = exception.getStatus();
    }

    this.logger.warn(`RPC Error - ${statusCode} - ${message}`);

    return {
      statusCode,
      message,
      error,
    };
  }

  // 🟢 Determine Status Code
  private getStatus(exception: any): number {
    if (exception instanceof HttpException) {
      return exception.getStatus();
    }
    return HttpStatus.INTERNAL_SERVER_ERROR;
  }

  // 🟢 Extract Error Message
  private extractMessage(exception: any): string {
    if (
      exception instanceof HttpException ||
      exception instanceof RpcException
    ) {
      return exception.message;
    }
    return 'Unexpected error occurred';
  }
}
