import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { Response } from 'express';
import { status } from '@grpc/grpc-js';

@Catch()
export class GrpcClientExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GrpcClientExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();

    // Handle gRPC specific errors
    if (this.isGrpcError(exception)) {
      const statusCode = this.mapGrpcCodeToHttpStatus(exception['code']);
      const errorResponse = {
        statusCode,
        message: exception['message'] || 'Unknown gRPC error',
        error: this.getErrorType(statusCode),
        timestamp: new Date().toISOString(),
      };

      this.logger.error(`gRPC client error: ${JSON.stringify(errorResponse)}`);
      response.status(statusCode).json(errorResponse);
      return;
    }

    // If it's already an HTTP exception, use its status code
    if (exception instanceof HttpException) {
      const status = exception.getStatus();
      const errorResponse = {
        statusCode: status,
        message: exception.message,
        error: this.getErrorType(status),
        timestamp: new Date().toISOString(),
      };

      this.logger.error(`HTTP error: ${JSON.stringify(errorResponse)}`);
      response.status(status).json(errorResponse);
      return;
    }

    // For unknown errors, return a generic 500 error
    const internalError = new InternalServerErrorException(
      'Internal server error',
    );
    const status = internalError.getStatus();
    const errorResponse = {
      statusCode: status,
      message: 'Internal server error',
      error: this.getErrorType(status),
      timestamp: new Date().toISOString(),
    };

    this.logger.error('Unhandled exception', exception);
    response.status(status).json(errorResponse);
  }

  private isGrpcError(error: any): boolean {
    return (
      error &&
      typeof error === 'object' &&
      'code' in error &&
      typeof error['code'] === 'number'
    );
  }

  private mapGrpcCodeToHttpStatus(grpcCode: number): number {
    switch (grpcCode) {
      case status.OK:
        return HttpStatus.OK;
      case status.CANCELLED:
        return HttpStatus.BAD_REQUEST;
      case status.UNKNOWN:
        return HttpStatus.INTERNAL_SERVER_ERROR;
      case status.INVALID_ARGUMENT:
        return HttpStatus.BAD_REQUEST;
      case status.DEADLINE_EXCEEDED:
        return HttpStatus.REQUEST_TIMEOUT;
      case status.NOT_FOUND:
        return HttpStatus.NOT_FOUND;
      case status.ALREADY_EXISTS:
        return HttpStatus.CONFLICT;
      case status.PERMISSION_DENIED:
        return HttpStatus.FORBIDDEN;
      case status.UNAUTHENTICATED:
        return HttpStatus.UNAUTHORIZED;
      case status.RESOURCE_EXHAUSTED:
        return HttpStatus.TOO_MANY_REQUESTS;
      case status.FAILED_PRECONDITION:
        return HttpStatus.PRECONDITION_FAILED;
      case status.ABORTED:
        return HttpStatus.CONFLICT;
      default:
        return HttpStatus.INTERNAL_SERVER_ERROR;
    }
  }

  private getErrorType(httpStatus: number): string {
    switch (httpStatus) {
      case HttpStatus.BAD_REQUEST:
        return 'Bad Request';
      case HttpStatus.UNAUTHORIZED:
        return 'Unauthorized';
      case HttpStatus.FORBIDDEN:
        return 'Forbidden';
      case HttpStatus.NOT_FOUND:
        return 'Not Found';
      case HttpStatus.REQUEST_TIMEOUT:
        return 'Request Timeout';
      case HttpStatus.CONFLICT:
        return 'Conflict';
      case HttpStatus.PRECONDITION_FAILED:
        return 'Precondition Failed';
      case HttpStatus.TOO_MANY_REQUESTS:
        return 'Too Many Requests';
      case HttpStatus.INTERNAL_SERVER_ERROR:
        return 'Internal Server Error';
      default:
        return 'Error';
    }
  }
}
