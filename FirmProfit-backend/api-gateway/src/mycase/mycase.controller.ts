import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpCode,
  HttpStatus,
  ValidationPipe,
  ParseIntPipe,
} from '@nestjs/common';
import { MyCaseService } from './mycase.service';
import { MyCaseAuthService } from './services/mycase-auth.service';
import { GrpcClientService } from '../grpc-client/grpc-client.service';
import {
  MyCaseAuthDto,
  MyCaseRefreshTokenDto,
  CreateMyCaseEventDto,
  UpdateMyCaseEventDto,
  GetMyCaseEventsDto,
} from './dto/mycase.dto';
import { ResponseMessage } from '../auth/decorators/response.decorator';

@Controller('mycase')
export class MyCaseController {
  constructor(
    private readonly myCaseService: MyCaseService,
    private readonly myCaseAuthService: MyCaseAuthService,
    private readonly grpcClientService: GrpcClientService,
  ) {}

  // Authentication endpoints
  @Post('auth/login')
  @HttpCode(HttpStatus.OK)
  @ResponseMessage('Authentication successful')
  async authenticate(@Body(ValidationPipe) authDto: MyCaseAuthDto) {
    return this.myCaseService.authenticate(authDto);
  }

  @Post('auth/refresh')
  @HttpCode(HttpStatus.OK)
  @ResponseMessage('Token refreshed successfully')
  async refreshToken(
    @Body(ValidationPipe) refreshTokenDto: MyCaseRefreshTokenDto,
  ) {
    return this.myCaseService.refreshToken(refreshTokenDto);
  }

  @Post('auth/revoke')
  @HttpCode(HttpStatus.OK)
  @ResponseMessage('Token revoked successfully')
  async revokeToken() {
    await this.myCaseService.revokeToken();
    return { message: 'Token revoked successfully' };
  }

  @Get('auth/status')
  @ResponseMessage('Authentication status retrieved')
  async getAuthStatus() {
    return this.myCaseService.getAuthStatus();
  }

  // Event endpoints
  @Post('events')
  @HttpCode(HttpStatus.CREATED)
  @ResponseMessage('Event created successfully')
  async createEvent(
    @Body(ValidationPipe) createEventDto: CreateMyCaseEventDto,
  ) {
    return this.myCaseService.createEvent(createEventDto);
  }

  @Get('events')
  @ResponseMessage('Events retrieved successfully')
  async getEvents(@Query(ValidationPipe) filtersDto: GetMyCaseEventsDto) {
    return this.myCaseService.getEvents(filtersDto);
  }

  @Get('events/:id')
  @ResponseMessage('Event retrieved successfully')
  async getEventById(@Param('id', ParseIntPipe) id: number) {
    return this.myCaseService.getEventById(id);
  }

  @Put('events/:id')
  @ResponseMessage('Event updated successfully')
  async updateEvent(
    @Param('id', ParseIntPipe) id: number,
    @Body(ValidationPipe) updateEventDto: UpdateMyCaseEventDto,
  ) {
    return this.myCaseService.updateEvent(id, updateEventDto);
  }

  @Delete('events/:id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ResponseMessage('Event deleted successfully')
  async deleteEvent(@Param('id', ParseIntPipe) id: number) {
    await this.myCaseService.deleteEvent(id);
    return { message: 'Event deleted successfully' };
  }

  // Specialized event endpoints
  @Get('events/date-range')
  @ResponseMessage('Events retrieved by date range')
  async getEventsByDateRange(
    @Query('start_date') startDate: string,
    @Query('end_date') endDate: string,
  ) {
    return this.myCaseService.getEventsByDateRange(startDate, endDate);
  }

  @Get('events/matter/:matterId')
  @ResponseMessage('Events retrieved by matter')
  async getEventsByMatter(@Param('matterId', ParseIntPipe) matterId: number) {
    return this.myCaseService.getEventsByMatter(matterId);
  }

  @Get('events/contact/:contactId')
  @ResponseMessage('Events retrieved by contact')
  async getEventsByContact(
    @Param('contactId', ParseIntPipe) contactId: number,
  ) {
    return this.myCaseService.getEventsByContact(contactId);
  }

  @Get('events/upcoming')
  @ResponseMessage('Upcoming events retrieved')
  async getUpcomingEvents() {
    return this.myCaseService.getUpcomingEvents();
  }

  @Get('events/search')
  @ResponseMessage('Events search completed')
  async searchEvents(@Query('q') searchTerm: string) {
    return this.myCaseService.searchEvents(searchTerm);
  }

  // Utility endpoints
  @Get('health')
  @ResponseMessage('Health check completed')
  async healthCheck() {
    return this.myCaseService.healthCheck();
  }

  // Sync endpoints
  @Post('sync/manual/:tenantId')
  @HttpCode(HttpStatus.OK)
  @ResponseMessage('Manual MyCase sync triggered successfully')
  async triggerManualSync(@Param('tenantId') tenantId: string) {
    try {
      // Authenticate with tenant config to get access token
      const tokenResponse =
        await this.myCaseAuthService.authenticateWithTenantConfig(tenantId);

      // Call shared service with access token
      const result = await this.grpcClientService.triggerManualMyCaseSync({
        access_token: tokenResponse.access_token,
      });

      return {
        success: true,
        message: 'Manual MyCase sync completed successfully',
        tenant_id: tenantId,
        data: result,
      };
    } catch (error) {
      return {
        success: false,
        message: `Manual MyCase sync failed: ${error.message}`,
        tenant_id: tenantId,
        error: error.message,
      };
    }
  }

  @Get('sync/status')
  @ResponseMessage('MyCase sync status retrieved successfully')
  async getSyncStatus() {
    try {
      const result = await this.grpcClientService.getMyCaseSyncStatus();

      return {
        success: true,
        message: 'MyCase sync status retrieved successfully',
        data: result,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to retrieve MyCase sync status: ${error.message}`,
        error: error.message,
      };
    }
  }
}
