import {
  Injectable,
  Logger,
  UnauthorizedException,
  BadRequestException,
} from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import {
  MyCaseAuthCredentials,
  MyCaseTokenResponse,
  MyCaseRefreshTokenRequest,
  MyCaseAuthState,
} from '../interfaces/mycase.interface';
import { TenantConfigService } from '../../tenant/services/tenant-config.service';

@Injectable()
export class MyCaseAuthService {
  private readonly logger = new Logger(MyCaseAuthService.name);
  private readonly baseUrl: string;
  private authState: MyCaseAuthState | null = null;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly tenantConfigService: TenantConfigService,
  ) {
    this.baseUrl = this.configService.get<string>(
      'MYCASE_API_BASE_URL',
      'https://auth.mycase.com',
    );
  }

  /**
   * Authenticate with MyCase API using tenant configuration
   */
  async authenticateWithTenantConfig(
    tenantId: string,
  ): Promise<MyCaseTokenResponse> {
    try {
      const tenantConfig =
        await this.tenantConfigService.getMyCaseConfig(tenantId);

      // First, check if we have existing valid tokens
      if (tenantConfig.api_key && tenantConfig.refresh_key) {
        // Check if access token is still valid
        const tokenExpiresAt = tenantConfig.access_token_expires_at
          ? new Date(tenantConfig.access_token_expires_at)
          : new Date(Date.now() - 1); // Assume expired if no expiry date

        const expirationBuffer = 5 * 60 * 1000; // 5 minutes buffer
        const willExpireSoon =
          new Date(Date.now() + expirationBuffer) >= tokenExpiresAt;

        if (!willExpireSoon) {
          // Token is still valid, use it
          this.logger.log(
            `Using existing valid access token for tenant: ${tenantId}`,
          );
          const tokenResponse: MyCaseTokenResponse = {
            access_token: tenantConfig.api_key,
            refresh_token: tenantConfig.refresh_key,
            expires_in: Math.floor(
              (tokenExpiresAt.getTime() - Date.now()) / 1000,
            ),
            token_type: 'Bearer',
            scope: 'read write', // Default scope
          };
          this.updateAuthState(tokenResponse);
          return tokenResponse;
        } else {
          // Token expired or will expire soon, try to refresh
          this.logger.log(
            `Access token expired for tenant ${tenantId}, attempting refresh`,
          );
          try {
            const refreshedToken = await this.refreshToken({
              refresh_token: tenantConfig.refresh_key,
              client_id:
                tenantConfig.client_id ||
                this.configService.get<string>('MYCASE_CLIENT_ID'),
              client_secret:
                tenantConfig.client_secret ||
                this.configService.get<string>('MYCASE_CLIENT_SECRET'),
              grant_type: 'refresh_token',
            });

            // Save the refreshed token back to tenant config
            await this.saveTenantTokens(tenantId, refreshedToken);
            return refreshedToken;
          } catch (refreshError) {
            this.logger.warn(
              `Token refresh failed for tenant ${tenantId}, will need new authentication`,
            );
            // Continue to new authentication flow
          }
        }
      }

      // No valid tokens available, need new authentication
      throw new UnauthorizedException(
        'No valid tokens available. Please authenticate using the authorization code flow or provide 2FA code.',
      );
    } catch (error) {
      // this.logger.error(`Failed to authenticate with tenant config for ${tenantId}:`, error.message);
      throw error;
    }
  }

  /**
   * Authenticate with MyCase API using authorization code (OAuth2 flow)
   */
  async authenticateWithAuthCode(
    tenantId: string,
    authCode: string,
    redirectUri?: string,
  ): Promise<MyCaseTokenResponse> {
    try {
      const tenantConfig =
        await this.tenantConfigService.getMyCaseConfig(tenantId);

      const response = await firstValueFrom(
        this.httpService.post(
          `${this.baseUrl}/oauth/token`,
          {
            grant_type: 'authorization_code',
            code: authCode,
            redirect_uri:
              redirectUri ||
              this.configService.get<string>('MYCASE_REDIRECT_URI'),
            client_id:
              tenantConfig.client_id ||
              this.configService.get<string>('MYCASE_CLIENT_ID'),
            client_secret:
              tenantConfig.client_secret ||
              this.configService.get<string>('MYCASE_CLIENT_SECRET'),
          },
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
              Accept: 'application/json',
            },
          },
        ),
      );

      const tokenData: MyCaseTokenResponse = response.data;
      this.updateAuthState(tokenData);

      // Save tokens to tenant configuration
      await this.saveTenantTokens(tenantId, tokenData);

      this.logger.log(
        'Successfully authenticated with MyCase API using authorization code',
      );
      return tokenData;
    } catch (error) {
      this.logger.error(
        'Authorization code authentication failed:',
        error.response?.data || error.message,
      );

      if (error.response?.status === 401) {
        throw new UnauthorizedException(
          'Invalid authorization code or credentials',
        );
      }

      throw new BadRequestException('Authorization code authentication failed');
    }
  }

  /**
   * Authenticate with MyCase API using username and password
   */
  async authenticate(
    credentials: MyCaseAuthCredentials,
  ): Promise<MyCaseTokenResponse> {
    try {
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.baseUrl}/tokens`,
          {
            grant_type: 'password',
            username: credentials.username,
            password: credentials.password,
            client_id: credentials.client_id,
            client_secret: credentials.client_secret,
          },
          {
            headers: {
              'Content-Type': 'application/json',
              Accept: 'application/json',
            },
          },
        ),
      );

      const tokenData: MyCaseTokenResponse = response.data;
      this.updateAuthState(tokenData);

      this.logger.log('Successfully authenticated with MyCase API');
      return tokenData;
    } catch (error) {
      this.logger.error(
        'Authentication failed:',
        error.response?.data || error.message,
      );

      if (error.response?.status === 401) {
        throw new UnauthorizedException('Invalid credentials');
      }

      if (error.response?.data?.error === 'two_factor_required') {
        throw new BadRequestException('Two-factor authentication required');
      }

      throw new BadRequestException('Authentication failed');
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(
    request: MyCaseRefreshTokenRequest,
  ): Promise<MyCaseTokenResponse> {
    try {
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.baseUrl}/tokens`,
          {
            grant_type: 'refresh_token',
            refresh_token: request.refresh_token,
            client_id: request.client_id,
            client_secret: request.client_secret,
          },
          {
            headers: {
              'Content-Type': 'application/json',
              Accept: 'application/json',
            },
          },
        ),
      );

      const tokenData: MyCaseTokenResponse = response.data;
      this.updateAuthState(tokenData);

      this.logger.log('Successfully refreshed MyCase API token');
      return tokenData;
    } catch (error) {
      this.logger.error(
        'Token refresh failed:',
        error.response?.data || error.message,
      );

      if (error.response?.status === 401) {
        this.clearAuthState();
        throw new UnauthorizedException('Invalid refresh token');
      }

      throw new BadRequestException('Token refresh failed');
    }
  }

  /**
   * Get current authentication state
   */
  getAuthState(): MyCaseAuthState | null {
    return this.authState;
  }

  /**
   * Check if current token is valid and not expired
   */
  isAuthenticated(): boolean {
    if (!this.authState) {
      return false;
    }

    return (
      this.authState.is_authenticated && new Date() < this.authState.expires_at
    );
  }

  /**
   * Get valid access token, refresh if necessary
   */
  async getValidAccessToken(): Promise<string> {
    if (!this.authState) {
      throw new UnauthorizedException('Not authenticated');
    }

    // Check if token is expired or will expire in the next 5 minutes
    const expirationBuffer = 5 * 60 * 1000; // 5 minutes in milliseconds
    const willExpireSoon =
      new Date(Date.now() + expirationBuffer) >= this.authState.expires_at;

    if (willExpireSoon) {
      this.logger.log(
        'Access token expired or will expire soon, refreshing...',
      );

      try {
        await this.refreshToken({
          refresh_token: this.authState.refresh_token,
          client_id: this.configService.get<string>('MYCASE_CLIENT_ID'),
          client_secret: this.configService.get<string>('MYCASE_CLIENT_SECRET'),
          grant_type: 'refresh_token',
        });
      } catch (error) {
        this.logger.error('Failed to refresh token:', error.message);
        throw new UnauthorizedException('Token refresh failed');
      }
    }

    return this.authState.access_token;
  }

  /**
   * Get authorization headers for API requests
   */
  async getAuthHeaders(): Promise<{ Authorization: string }> {
    const accessToken = await this.getValidAccessToken();
    return {
      Authorization: `Bearer ${accessToken}`,
    };
  }

  /**
   * Clear authentication state
   */
  clearAuthState(): void {
    this.authState = null;
    this.logger.log('Authentication state cleared');
  }

  /**
   * Update internal authentication state
   */
  private updateAuthState(tokenData: MyCaseTokenResponse): void {
    const expiresAt = new Date(Date.now() + tokenData.expires_in * 1000);

    this.authState = {
      access_token: tokenData.access_token,
      refresh_token: tokenData.refresh_token,
      expires_at: expiresAt,
      is_authenticated: true,
    };

    this.logger.log(`Token expires at: ${expiresAt.toISOString()}`);
  }

  /**
   * Revoke current access token
   */
  async revokeToken(): Promise<void> {
    if (!this.authState) {
      return;
    }

    try {
      await firstValueFrom(
        this.httpService.post(
          `${this.baseUrl}/oauth/revoke`,
          {
            token: this.authState.access_token,
            client_id: this.configService.get<string>('MYCASE_CLIENT_ID'),
            client_secret: this.configService.get<string>(
              'MYCASE_CLIENT_SECRET',
            ),
          },
          {
            headers: {
              'Content-Type': 'application/json',
              Accept: 'application/json',
            },
          },
        ),
      );

      this.logger.log('Successfully revoked MyCase API token');
    } catch (error) {
      this.logger.error(
        'Token revocation failed:',
        error.response?.data || error.message,
      );
    } finally {
      this.clearAuthState();
    }
  }

  /**
   * Save tokens to tenant configuration
   */
  private async saveTenantTokens(
    tenantId: string,
    tokenData: MyCaseTokenResponse,
  ): Promise<void> {
    try {
      const tenantConfig =
        await this.tenantConfigService.getMyCaseConfig(tenantId);
      const expiresAt = new Date(Date.now() + tokenData.expires_in * 1000);

      const updatedConfig = {
        ...tenantConfig,
        api_key: tokenData.access_token,
        refresh_key: tokenData.refresh_token,
        access_token_expires_at: expiresAt.toISOString(),
      };

      await this.tenantConfigService.saveMyCaseConfig(tenantId, updatedConfig);
      this.logger.log(`Saved tokens to tenant config for: ${tenantId}`);
    } catch (error) {
      this.logger.error(
        `Failed to save tokens for tenant ${tenantId}:`,
        error.message,
      );
      // Don't throw here, as the authentication was successful
    }
  }
}
