import {
  Injectable,
  Logger,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { MyCaseAuthService } from './mycase-auth.service';
import {
  MyCaseEvent,
  MyCaseEventResponse,
  MyCaseApiResponse,
  MyCaseEventFilters,
} from '../interfaces/mycase.interface';

@Injectable()
export class MyCaseEventService {
  private readonly logger = new Logger(MyCaseEventService.name);
  private readonly baseUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly authService: MyCaseAuthService,
  ) {
    this.baseUrl = this.configService.get<string>(
      'MYCASE_API_BASE_URL',
      'https://api.mycase.com',
    );
  }

  /**
   * Create a new event in MyCase
   */
  async createEvent(eventData: MyCaseEvent): Promise<MyCaseEventResponse> {
    try {
      const headers = await this.authService.getAuthHeaders();

      const response = await firstValueFrom(
        this.httpService.post(`${this.baseUrl}/v1/events`, eventData, {
          headers: {
            ...headers,
            'Content-Type': 'application/json',
            Accept: 'application/json',
          },
        }),
      );

      const apiResponse: MyCaseApiResponse<MyCaseEventResponse> = response.data;

      if (apiResponse.errors && apiResponse.errors.length > 0) {
        throw new BadRequestException(
          `Event creation failed: ${apiResponse.errors.map((e) => e.message).join(', ')}`,
        );
      }

      this.logger.log(`Successfully created event: ${apiResponse.data.id}`);
      return apiResponse.data;
    } catch (error) {
      this.logger.error(
        'Event creation failed:',
        error.response?.data || error.message,
      );

      if (error.response?.status === 401) {
        throw new BadRequestException('Authentication required');
      }

      if (error.response?.status === 422) {
        throw new BadRequestException('Invalid event data provided');
      }

      throw new BadRequestException('Failed to create event');
    }
  }

  /**
   * Get events from MyCase with optional filters
   */
  async getEvents(
    filters?: MyCaseEventFilters,
  ): Promise<MyCaseApiResponse<MyCaseEventResponse[]>> {
    try {
      const headers = await this.authService.getAuthHeaders();

      // Build query parameters
      const params = new URLSearchParams();
      if (filters?.start_date) params.append('start_date', filters.start_date);
      if (filters?.end_date) params.append('end_date', filters.end_date);
      if (filters?.matter_id)
        params.append('matter_id', filters.matter_id.toString());
      if (filters?.contact_id)
        params.append('contact_id', filters.contact_id.toString());
      if (filters?.event_type) params.append('event_type', filters.event_type);
      if (filters?.page) params.append('page', filters.page.toString());
      if (filters?.per_page)
        params.append('per_page', filters.per_page.toString());

      const queryString = params.toString();
      const url = `${this.baseUrl}/v1/events${queryString ? `?${queryString}` : ''}`;

      const response = await firstValueFrom(
        this.httpService.get(url, {
          headers: {
            ...headers,
            Accept: 'application/json',
          },
        }),
      );

      const apiResponse: MyCaseApiResponse<MyCaseEventResponse[]> =
        response.data;

      this.logger.log(
        `Successfully retrieved ${apiResponse.data.length} events`,
      );
      return apiResponse;
    } catch (error) {
      this.logger.error(
        'Failed to retrieve events:',
        error.response?.data || error.message,
      );

      if (error.response?.status === 401) {
        throw new BadRequestException('Authentication required');
      }

      throw new BadRequestException('Failed to retrieve events');
    }
  }

  /**
   * Get a specific event by ID
   */
  async getEventById(eventId: number): Promise<MyCaseEventResponse> {
    try {
      const headers = await this.authService.getAuthHeaders();

      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/v1/events/${eventId}`, {
          headers: {
            ...headers,
            Accept: 'application/json',
          },
        }),
      );

      const apiResponse: MyCaseApiResponse<MyCaseEventResponse> = response.data;

      this.logger.log(`Successfully retrieved event: ${eventId}`);
      return apiResponse.data;
    } catch (error) {
      this.logger.error(
        `Failed to retrieve event ${eventId}:`,
        error.response?.data || error.message,
      );

      if (error.response?.status === 401) {
        throw new BadRequestException('Authentication required');
      }

      if (error.response?.status === 404) {
        throw new NotFoundException(`Event with ID ${eventId} not found`);
      }

      throw new BadRequestException('Failed to retrieve event');
    }
  }

  /**
   * Update an existing event
   */
  async updateEvent(
    eventId: number,
    eventData: Partial<MyCaseEvent>,
  ): Promise<MyCaseEventResponse> {
    try {
      const headers = await this.authService.getAuthHeaders();

      const response = await firstValueFrom(
        this.httpService.put(
          `${this.baseUrl}/v1/events/${eventId}`,
          eventData,
          {
            headers: {
              ...headers,
              'Content-Type': 'application/json',
              Accept: 'application/json',
            },
          },
        ),
      );

      const apiResponse: MyCaseApiResponse<MyCaseEventResponse> = response.data;

      if (apiResponse.errors && apiResponse.errors.length > 0) {
        throw new BadRequestException(
          `Event update failed: ${apiResponse.errors.map((e) => e.message).join(', ')}`,
        );
      }

      this.logger.log(`Successfully updated event: ${eventId}`);
      return apiResponse.data;
    } catch (error) {
      this.logger.error(
        `Failed to update event ${eventId}:`,
        error.response?.data || error.message,
      );

      if (error.response?.status === 401) {
        throw new BadRequestException('Authentication required');
      }

      if (error.response?.status === 404) {
        throw new NotFoundException(`Event with ID ${eventId} not found`);
      }

      if (error.response?.status === 422) {
        throw new BadRequestException('Invalid event data provided');
      }

      throw new BadRequestException('Failed to update event');
    }
  }

  /**
   * Delete an event
   */
  async deleteEvent(eventId: number): Promise<void> {
    try {
      const headers = await this.authService.getAuthHeaders();

      await firstValueFrom(
        this.httpService.delete(`${this.baseUrl}/v1/events/${eventId}`, {
          headers: {
            ...headers,
            Accept: 'application/json',
          },
        }),
      );

      this.logger.log(`Successfully deleted event: ${eventId}`);
    } catch (error) {
      this.logger.error(
        `Failed to delete event ${eventId}:`,
        error.response?.data || error.message,
      );

      if (error.response?.status === 401) {
        throw new BadRequestException('Authentication required');
      }

      if (error.response?.status === 404) {
        throw new NotFoundException(`Event with ID ${eventId} not found`);
      }

      throw new BadRequestException('Failed to delete event');
    }
  }

  /**
   * Get events for a specific date range
   */
  async getEventsByDateRange(
    startDate: string,
    endDate: string,
  ): Promise<MyCaseEventResponse[]> {
    const filters: MyCaseEventFilters = {
      start_date: startDate,
      end_date: endDate,
    };

    const response = await this.getEvents(filters);
    return response.data;
  }

  /**
   * Get events for a specific matter
   */
  async getEventsByMatter(matterId: number): Promise<MyCaseEventResponse[]> {
    const filters: MyCaseEventFilters = {
      matter_id: matterId,
    };

    const response = await this.getEvents(filters);
    return response.data;
  }

  /**
   * Get events for a specific contact
   */
  async getEventsByContact(contactId: number): Promise<MyCaseEventResponse[]> {
    const filters: MyCaseEventFilters = {
      contact_id: contactId,
    };

    const response = await this.getEvents(filters);
    return response.data;
  }

  /**
   * Get upcoming events (next 30 days)
   */
  async getUpcomingEvents(): Promise<MyCaseEventResponse[]> {
    const today = new Date();
    const thirtyDaysFromNow = new Date(
      today.getTime() + 30 * 24 * 60 * 60 * 1000,
    );

    const filters: MyCaseEventFilters = {
      start_date: today.toISOString().split('T')[0],
      end_date: thirtyDaysFromNow.toISOString().split('T')[0],
    };

    const response = await this.getEvents(filters);
    return response.data;
  }

  /**
   * Search events by title or description
   */
  async searchEvents(searchTerm: string): Promise<MyCaseEventResponse[]> {
    try {
      const allEvents = await this.getEvents();

      // Filter events by search term (case-insensitive)
      const filteredEvents = allEvents.data.filter(
        (event) =>
          event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (event.description &&
            event.description.toLowerCase().includes(searchTerm.toLowerCase())),
      );

      this.logger.log(
        `Found ${filteredEvents.length} events matching search term: ${searchTerm}`,
      );
      return filteredEvents;
    } catch (error) {
      this.logger.error('Event search failed:', error.message);
      throw new BadRequestException('Failed to search events');
    }
  }
}
