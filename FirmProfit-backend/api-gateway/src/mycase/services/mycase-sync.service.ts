import { Injectable, Logger } from '@nestjs/common';
import { MyCaseAuthService } from './mycase-auth.service';
import { GrpcClientService } from '../../grpc-client/grpc-client.service';

@Injectable()
export class MyCaseSyncService {
  private readonly logger = new Logger(MyCaseSyncService.name);

  constructor(
    private readonly myCaseAuthService: MyCaseAuthService,
    private readonly grpcClientService: GrpcClientService,
  ) {}

  async triggerManualSync(tenantId: string) {
    try {
      this.logger.log(`Starting manual MyCase sync for tenant ${tenantId}`);

      // Authenticate with tenant config to get access token
      const tokenResponse =
        await this.myCaseAuthService.authenticateWithTenantConfig(tenantId);

      // Call shared service with access token
      const result = await this.grpcClientService.triggerManualMyCaseSync({
        access_token: tokenResponse.access_token,
      });

      this.logger.log(
        `Manual MyCase sync completed successfully for tenant ${tenantId}`,
      );

      return {
        success: true,
        message: 'Manual MyCase sync completed successfully',
        tenant_id: tenantId,
        data: result,
      };
    } catch (error) {
      this.logger.error(
        `Manual MyCase sync failed for tenant ${tenantId}: ${error.message}`,
      );

      return {
        success: false,
        message: `Manual MyCase sync failed: ${error.message}`,
        tenant_id: tenantId,
        error: error.message,
      };
    }
  }

  async healthCheck() {
    try {
      this.logger.log('Performing MyCase health check');

      // Simple health check - could be expanded to check API connectivity
      return {
        success: true,
        message: 'MyCase service is healthy',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(`MyCase health check failed: ${error.message}`);

      return {
        success: false,
        message: `MyCase health check failed: ${error.message}`,
        timestamp: new Date().toISOString(),
      };
    }
  }
}
