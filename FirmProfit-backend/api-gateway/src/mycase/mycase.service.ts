import { Injectable, Logger } from '@nestjs/common';
import { MyCaseAuthService } from './services/mycase-auth.service';
import { MyCaseEventService } from './services/mycase-event.service';
import {
  MyCaseAuthCredentials,
  MyCase2FARequest,
  MyCaseRefreshTokenRequest,
  MyCaseTokenResponse,
  MyCaseEvent,
  MyCaseEventResponse,
  MyCaseEventFilters,
  MyCaseAuthState,
} from './interfaces/mycase.interface';

@Injectable()
export class MyCaseService {
  private readonly logger = new Logger(MyCaseService.name);

  constructor(
    private readonly authService: MyCaseAuthService,
    private readonly eventService: MyCaseEventService,
  ) {}

  // Authentication methods
  async authenticate(
    credentials: MyCaseAuthCredentials,
  ): Promise<MyCaseTokenResponse> {
    return this.authService.authenticate(credentials);
  }

  async refreshToken(
    request: MyCaseRefreshTokenRequest,
  ): Promise<MyCaseTokenResponse> {
    return this.authService.refreshToken(request);
  }

  async revokeToken(): Promise<void> {
    return this.authService.revokeToken();
  }

  getAuthState(): MyCaseAuthState | null {
    return this.authService.getAuthState();
  }

  isAuthenticated(): boolean {
    return this.authService.isAuthenticated();
  }

  // Event methods
  async createEvent(eventData: MyCaseEvent): Promise<MyCaseEventResponse> {
    return this.eventService.createEvent(eventData);
  }

  async getEvents(
    filters?: MyCaseEventFilters,
  ): Promise<MyCaseEventResponse[]> {
    const response = await this.eventService.getEvents(filters);
    return response.data;
  }

  async getEventById(eventId: number): Promise<MyCaseEventResponse> {
    return this.eventService.getEventById(eventId);
  }

  async updateEvent(
    eventId: number,
    eventData: Partial<MyCaseEvent>,
  ): Promise<MyCaseEventResponse> {
    return this.eventService.updateEvent(eventId, eventData);
  }

  async deleteEvent(eventId: number): Promise<void> {
    return this.eventService.deleteEvent(eventId);
  }

  async getEventsByDateRange(
    startDate: string,
    endDate: string,
  ): Promise<MyCaseEventResponse[]> {
    return this.eventService.getEventsByDateRange(startDate, endDate);
  }

  async getEventsByMatter(matterId: number): Promise<MyCaseEventResponse[]> {
    return this.eventService.getEventsByMatter(matterId);
  }

  async getEventsByContact(contactId: number): Promise<MyCaseEventResponse[]> {
    return this.eventService.getEventsByContact(contactId);
  }

  async getUpcomingEvents(): Promise<MyCaseEventResponse[]> {
    return this.eventService.getUpcomingEvents();
  }

  async searchEvents(searchTerm: string): Promise<MyCaseEventResponse[]> {
    return this.eventService.searchEvents(searchTerm);
  }

  // Utility methods
  async getAuthStatus(): Promise<{
    isAuthenticated: boolean;
    authState: MyCaseAuthState | null;
  }> {
    return {
      isAuthenticated: this.isAuthenticated(),
      authState: this.getAuthState(),
    };
  }

  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    this.logger.log('MyCase service health check');
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
    };
  }
} 