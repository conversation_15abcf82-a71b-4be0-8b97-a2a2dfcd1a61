export interface MyCaseAuthCredentials {
  username: string;
  password: string;
  client_id: string;
  client_secret: string;
}

export interface MyCaseTokenResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  scope: string;
}

export interface MyCase2FARequest {
  username: string;
  password: string;
  two_factor_code: string;
  client_id: string;
  client_secret: string;
}

export interface MyCaseRefreshTokenRequest {
  refresh_token: string;
  client_id: string;
  client_secret: string;
  grant_type: 'refresh_token';
}

export interface MyCaseEvent {
  id?: number;
  title: string;
  description?: string;
  start_date: string;
  end_date?: string;
  start_time?: string;
  end_time?: string;
  all_day?: boolean;
  location?: string;
  matter_id?: number;
  contact_id?: number;
  event_type?: string;
  reminder_minutes?: number;
  created_at?: string;
  updated_at?: string;
}

export interface MyCaseEventResponse {
  id: number;
  title: string;
  description: string;
  start_date: string;
  end_date: string;
  start_time: string;
  end_time: string;
  all_day: boolean;
  location: string;
  matter_id: number;
  contact_id: number;
  event_type: string;
  reminder_minutes: number;
  created_at: string;
  updated_at: string;
}

export interface MyCaseApiResponse<T> {
  data: T;
  meta?: {
    total_count: number;
    page: number;
    per_page: number;
  };
  errors?: Array<{
    field: string;
    message: string;
  }>;
}

export interface MyCaseClient {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  company: string;
  created_at: string;
  updated_at: string;
}

export interface MyCaseEventFilters {
  start_date?: string;
  end_date?: string;
  matter_id?: number;
  contact_id?: number;
  event_type?: string;
  page?: number;
  per_page?: number;
}

export interface MyCaseAuthState {
  access_token: string;
  refresh_token: string;
  expires_at: Date;
  is_authenticated: boolean;
}

// Webhook related interfaces
export interface MyCaseWebhookData {
  firm_uuid: string;
  action: string;
  resource: string;
  resource_body: any;
  timestamp: string;
}

export interface MyCaseWebhookAuthenticationData {
  status: 'success' | 'failed' | 'not_attempted' | 'no_tenant_id';
  access_token: string | null;
  authenticated_at: string | null;
}

export interface MyCaseEnrichedResourceBody {
  [key: string]: any;
  _authentication: MyCaseWebhookAuthenticationData;
}

export interface MyCaseWebhookProcessResult {
  received: boolean;
  saved: boolean;
  record_id?: string;
  authentication_status: string;
  access_token_included: boolean;
  error?: string;
} 