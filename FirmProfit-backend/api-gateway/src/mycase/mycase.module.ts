import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { MyCaseController } from './mycase.controller';
import { MyCaseService } from './mycase.service';
import { MyCaseAuthService } from './services/mycase-auth.service';
import { MyCaseEventService } from './services/mycase-event.service';
import { MyCaseSyncService } from './services/mycase-sync.service';
import { TenantModule } from '../tenant/tenant.module';
import { GrpcClientModule } from '../grpc-client/grpc-client.module';

@Module({
  imports: [
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
    ConfigModule,
    TenantModule,
    GrpcClientModule,
  ],
  controllers: [MyCaseController],
  providers: [
    MyCaseService,
    MyCaseAuthService,
    MyCaseEventService,
    MyCaseSyncService,
  ],
  exports: [
    MyCaseService,
    MyCaseAuthService,
    MyCaseEventService,
    MyCaseSyncService,
  ],
})
export class MyCaseModule {}
