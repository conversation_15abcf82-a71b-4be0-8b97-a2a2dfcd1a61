import {
  IsString,
  IsOptional,
  IsBoolean,
  IsNumber,
  IsDateString,
  IsEmail,
} from 'class-validator';

export class MyCaseAuthDto {
  @IsString()
  username: string;

  @IsString()
  password: string;

  @IsString()
  client_id: string;

  @IsString()
  client_secret: string;
}

export class MyCase2FADto extends MyCaseAuthDto {
  @IsString()
  two_factor_code: string;
}

export class MyCaseRefreshTokenDto {
  @IsString()
  refresh_token: string;

  @IsString()
  client_id: string;

  @IsString()
  client_secret: string;

  @IsString()
  grant_type = 'refresh_token' as const;
}

export class CreateMyCaseEventDto {
  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsDateString()
  start_date: string;

  @IsOptional()
  @IsDateString()
  end_date?: string;

  @IsOptional()
  @IsString()
  start_time?: string;

  @IsOptional()
  @IsString()
  end_time?: string;

  @IsOptional()
  @IsBoolean()
  all_day?: boolean;

  @IsOptional()
  @IsString()
  location?: string;

  @IsOptional()
  @IsNumber()
  matter_id?: number;

  @IsOptional()
  @IsNumber()
  contact_id?: number;

  @IsOptional()
  @IsString()
  event_type?: string;

  @IsOptional()
  @IsNumber()
  reminder_minutes?: number;
}

export class UpdateMyCaseEventDto extends CreateMyCaseEventDto {
  @IsNumber()
  id: number;
}

export class GetMyCaseEventsDto {
  @IsOptional()
  @IsDateString()
  start_date?: string;

  @IsOptional()
  @IsDateString()
  end_date?: string;

  @IsOptional()
  @IsNumber()
  matter_id?: number;

  @IsOptional()
  @IsNumber()
  contact_id?: number;

  @IsOptional()
  @IsString()
  event_type?: string;

  @IsOptional()
  @IsNumber()
  page?: number;

  @IsOptional()
  @IsNumber()
  per_page?: number;
}

export class MyCaseClientDto {
  @IsString()
  first_name: string;

  @IsString()
  last_name: string;

  @IsEmail()
  email: string;

  @IsOptional()
  @IsString()
  phone?: string;

  @IsOptional()
  @IsString()
  company?: string;
} 