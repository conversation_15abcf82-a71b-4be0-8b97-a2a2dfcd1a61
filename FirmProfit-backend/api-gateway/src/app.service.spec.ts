import { Test, TestingModule } from '@nestjs/testing';
import { AppService } from './app.service';
import { GrpcClientService } from './grpc-client/grpc-client.service';

describe('AppService', () => {
  let service: AppService;

  beforeEach(async () => {
    const mockGrpcClientService = {
      getUserById: jest.fn(),
      handleMfa: jest.fn(),
      signUp: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AppService,
        {
          provide: GrpcClientService,
          useValue: mockGrpcClientService,
        },
      ],
    }).compile();

    service = module.get<AppService>(AppService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should return "Hello World!"', () => {
    expect(service.getHello()).toBe('Hello World!');
  });
});
