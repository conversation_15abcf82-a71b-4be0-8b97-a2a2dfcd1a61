import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventSummaryController } from './event-summary.controller';
import { EventSummaryService } from './event-summary.service';

@Module({
  imports: [ConfigModule],
  controllers: [EventSummaryController],
  providers: [EventSummaryService],
  exports: [EventSummaryService],
})
export class EventSummaryModule {} 