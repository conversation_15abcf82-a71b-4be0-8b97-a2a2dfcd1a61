import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { EventSummaryService } from './event-summary.service';

describe('EventSummaryService', () => {
  let service: EventSummaryService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EventSummaryService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              const config = {
                AWS_ACCESS_KEY_ID: 'test-key',
                AWS_SECRET_ACCESS_KEY: 'test-secret',
                AWS_REGION: 'us-east-1',
                BEDROCK_MODEL_ID: 'us.anthropic.claude-3-7-sonnet-20250219-v1:0',
                BEDROCK_MAX_TOKENS: '5000',
              };
              return config[key];
            }),
          },
        },
      ],
    }).compile();

    service = module.get<EventSummaryService>(EventSummaryService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should have health check method', async () => {
    const healthCheck = await service.healthCheck();
    expect(healthCheck).toHaveProperty('status');
    expect(healthCheck).toHaveProperty('model');
    expect(healthCheck).toHaveProperty('region');
    expect(healthCheck.status).toBe('healthy');
  });
}); 