import { 
  Controller, 
  Post, 
  Body, 
  Get, 
  HttpStatus, 
  HttpCode,
  ValidationPipe,
  UsePipes,
  Logger 
} from '@nestjs/common';
import { EventSummaryService } from './event-summary.service';
import { EventSummaryRequestDto } from './dto/event-summary-request.dto';
import { EventSummaryResponseDto } from './dto/event-summary-response.dto';

@Controller('event-summary')
export class EventSummaryController {
  private readonly logger = new Logger(EventSummaryController.name);

  constructor(private readonly eventSummaryService: EventSummaryService) {}

  @Post('generate')
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe({ 
    transform: true, 
    whitelist: true,
    forbidNonWhitelisted: true 
  }))
  async generateSummary(
    @Body() request: EventSummaryRequestDto,
  ): Promise<EventSummaryResponseDto> {

    console.log(request, 'request in generateSummary');
    this.logger.log(`Received request to generate summary for ${request.event.length} events`);
    
    const response = await this.eventSummaryService.generateEventSummary(request);
    
    this.logger.log(`Successfully generated summary for ${response.processedEvents} events`);
    
    return response;
  }

  @Post('generate-mock')
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe({ 
    transform: true, 
    whitelist: true,
    forbidNonWhitelisted: true 
  }))
  async generateMockSummary(
    @Body() request: EventSummaryRequestDto,
  ): Promise<EventSummaryResponseDto> {
    this.logger.log(`Received mock request to generate summary for ${request.event.length} events`);
    
    // Generate mock summary based on event data
    let mockSummary = `Summary of ${request.event.length} legal event(s): `;
    
    request.event.forEach((event, index) => {
      const update = event.updateData;
      mockSummary += `Event ${index + 1}: ${update.clientName} has a court proceeding`;
      
      if (update.courtNoticeType) {
        mockSummary += ` (${update.courtNoticeType})`;
      }
      
      mockSummary += ` scheduled for ${update.startDate} from ${update.startTime} to ${update.endTime}`;
      
      if (update.courtLocation) {
        mockSummary += ` at ${update.courtLocation}`;
      }
      
      mockSummary += `. Client attendance: ${update.clientAttendance}`;
      
      if (index < request.event.length - 1) {
        mockSummary += '. ';
      }
    });
    
    mockSummary += ". If anything is to change of this, I'll be sure to let you know. Please let me know that you have received this message. Thank you!!";
    
    const response = new EventSummaryResponseDto(
      mockSummary,
      request.event.length,
      'mock-model-for-testing'
    );
    
    this.logger.log(`Successfully generated mock summary for ${response.processedEvents} events`);
    
    return response;
  }

  @Get('health')
  @HttpCode(HttpStatus.OK)
  async healthCheck(): Promise<{ status: string; model: string; region: string }> {
    return this.eventSummaryService.healthCheck();
  }
} 