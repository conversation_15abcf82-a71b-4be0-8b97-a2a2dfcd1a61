import { IsArray, ValidateNested, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { EventDto } from './event-data.dto';

export class EventSummaryRequestDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EventDto)
  event: EventDto[];

  @IsString()
  @IsOptional()
  customPrompt?: string;

  @IsString()
  @IsOptional()
  additionalInstructions?: string;
} 