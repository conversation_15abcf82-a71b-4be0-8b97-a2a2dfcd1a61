import { IsString, IsBoolean, IsOptional, IsArray, IsNotEmpty, IsDateString } from 'class-validator';

export class EventUpdateDataDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsString()
  @IsOptional()
  caseNumber?: string;

  @IsString()
  @IsNotEmpty()
  clientName: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  subject?: string;

  @IsString()
  @IsOptional()
  courtNoticeType?: string;

  @IsString()
  @IsOptional()
  appointmentAction?: string;

  @IsString()
  @IsOptional()
  charge?: string;

  @IsString()
  @IsOptional()
  county?: string;

  @IsString()
  @IsOptional()
  courtLocation?: string;

  @IsDateString()
  @IsNotEmpty()
  startDate: string;

  @IsDateString()
  @IsNotEmpty()
  endDate: string;

  @IsString()
  @IsOptional()
  date?: string;

  @IsString()
  @IsNotEmpty()
  startTime: string;

  @IsString()
  @IsNotEmpty()
  endTime: string;

  @IsBoolean()
  @IsOptional()
  allDay?: boolean;

  @IsString()
  @IsOptional()
  requiredAttendees?: string;

  @IsString()
  @IsOptional()
  optionalAttendees?: string;

  @IsString()
  @IsNotEmpty()
  clientAttendance: string;

  @IsString()
  @IsOptional()
  meetingLocation?: string;

  @IsBoolean()
  @IsOptional()
  isCompleted?: boolean;

  @IsString()
  @IsOptional()
  courtNoticeActions?: string;

  @IsString()
  @IsOptional()
  selectedMatterId?: string;

  @IsString()
  @IsOptional()
  rescheduleAppointment?: string;

  @IsString()
  @IsOptional()
  eventStatus?: string;

  @IsString()
  @IsOptional()
  court_notice_date?: string;

  @IsArray()
  @IsOptional()
  files?: any[];

  @IsArray()
  @IsOptional()
  deleteFiles?: any[];

  @IsArray()
  @IsOptional()
  renameFiles?: any[];
}

export class EventDto {
  @IsString()
  @IsNotEmpty()
  derivedFieldId: string;

  @IsBoolean()
  @IsOptional()
  newClient?: boolean;

  @IsString()
  @IsOptional()
  clientMatterId?: string;

  @IsString()
  @IsOptional()
  clientIdentifier?: string;

  @IsString()
  @IsOptional()
  matterId?: string;

  @IsNotEmpty()
  updateData: EventUpdateDataDto;
} 