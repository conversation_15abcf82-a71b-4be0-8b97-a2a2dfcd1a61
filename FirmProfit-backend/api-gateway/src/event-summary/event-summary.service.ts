import { Injectable, Logger, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BedrockRuntime } from '@aws-sdk/client-bedrock-runtime';
import { 
  BedrockConfig, 
  BedrockRequestBody, 
  BedrockResponseChunk 
} from './interfaces/aws-bedrock.interface';
import { EventSummaryRequestDto } from './dto/event-summary-request.dto';
import { EventSummaryResponseDto } from './dto/event-summary-response.dto';
import { EventDto } from './dto/event-data.dto';

@Injectable()
export class EventSummaryService {
  private readonly logger = new Logger(EventSummaryService.name);
  private readonly bedrockClient: BedrockRuntime;
  private readonly bedrockConfig: BedrockConfig;

  constructor(private readonly configService: ConfigService) {
    this.bedrockConfig = {
      accessKeyId: this.configService.get<string>('AWS_ACCESS_KEY_ID') || '********************',
      secretAccessKey: this.configService.get<string>('AWS_SECRET_ACCESS_KEY') || '/e8MLDE9g5LwhPnXCPoRNF39mRw3XKIxPg9JWGcE',
      region: this.configService.get<string>('AWS_REGION') || 'us-east-1',
      modelId: this.configService.get<string>('BEDROCK_MODEL_ID') || 'us.anthropic.claude-3-7-sonnet-20250219-v1:0',
      maxTokens: parseInt(this.configService.get<string>('BEDROCK_MAX_TOKENS') || '5000'),
    };

    this.bedrockClient = new BedrockRuntime({
      region: this.bedrockConfig.region,
      credentials: {
        accessKeyId: this.bedrockConfig.accessKeyId,
        secretAccessKey: this.bedrockConfig.secretAccessKey,
      },
    });
  }

  async generateEventSummary(request: EventSummaryRequestDto): Promise<EventSummaryResponseDto> {
    try {
      this.logger.log(`Generating summary for ${request.event.length} events`);

      // Validate input
      if (!request.event || request.event.length === 0) {
        throw new BadRequestException('No events provided for summary generation');
      }

      // Extract and format event data
      const formattedEventData = this.extractEventData(request.event);
      
      // Generate prompt
      const prompt = this.buildPrompt(formattedEventData, request.customPrompt, request.additionalInstructions);
      
      // Call AWS Bedrock
      const summary = await this.callBedrockAPI(prompt);
      
      // Add closing message
      const finalSummary = this.appendClosingMessage(summary);

      return new EventSummaryResponseDto(
        finalSummary,
        request.event.length,
        this.bedrockConfig.modelId
      );

    } catch (error) {
      this.logger.error('Error generating event summary:', error);
      
      if (error instanceof BadRequestException) {
        throw error;
      }
      
      throw new InternalServerErrorException('Failed to generate event summary');
    }
  }

  private extractEventData(events: EventDto[]): string {
    let result = '';
    
    events.forEach((event, index) => {
      const update = event.updateData;
      result += `Event ${index + 1}:\n`;
      result += `  clientName: ${update.clientName}\n`;
      result += `  startDate: ${update.startDate}\n`;
      result += `  endDate: ${update.endDate}\n`;
      result += `  startTime: ${update.startTime}\n`;
      result += `  endTime: ${update.endTime}\n`;
      result += `  clientAttendance: ${update.clientAttendance}\n`;
      
      // Include additional fields if available
      if (update.courtNoticeType) {
        result += `  caseType: ${update.courtNoticeType}\n`;
      }
      if (update.charge) {
        result += `  charge: ${update.charge}\n`;
      }
      if (update.caseNumber) {
        result += `  caseNumber: ${update.caseNumber}\n`;
      }
      if (update.courtLocation) {
        result += `  courtLocation: ${update.courtLocation}\n`;
      }
      
      result += '\n';
    });

    return result;
  }

  private buildPrompt(eventData: string, customPrompt?: string, additionalInstructions?: string): string {
    const defaultPrompt = `Summarize the following list of events. Each event contains the following fields: clientName, startDate, endDate, startTime, endTime, clientAttendance, and possibly caseType. For each event, include in the summary:

The client name

The case type (if provided)

The date and time range (in a readable format, combining date and time)

Whether the client's appearance is required (based on clientAttendance)

Ignore duplicate events unless they are relevant to the summary. Present the information as a single paragraph that clearly and concisely describes all the events.`;

    let prompt = customPrompt || defaultPrompt;
    
    if (additionalInstructions) {
      prompt += `\n\nAdditional Instructions: ${additionalInstructions}`;
    }
    
    prompt += `\n\n###event data : ${eventData}`;
    
    return prompt;
  }

  private async callBedrockAPI(prompt: string): Promise<string> {
    try {
      const body: BedrockRequestBody = {
        anthropic_version: 'bedrock-2023-05-31',
        max_tokens: this.bedrockConfig.maxTokens,
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: prompt,
              },
            ],
          },
        ],
      };

      const response = await this.bedrockClient.invokeModelWithResponseStream({
        body: JSON.stringify(body),
        modelId: this.bedrockConfig.modelId,
      });

      let extractedText = '';

      if (response.body) {
        for await (const chunk of response.body) {
          if (chunk.chunk?.bytes) {
            const chunkData: BedrockResponseChunk = JSON.parse(
              new TextDecoder().decode(chunk.chunk.bytes)
            );

            if (
              chunkData.type === 'content_block_delta' &&
              chunkData.delta?.type === 'text_delta'
            ) {
              extractedText += chunkData.delta.text;
            }
          }
        }
      }

      if (!extractedText.trim()) {
        throw new Error('No response received from Bedrock API');
      }

      return extractedText.trim();

    } catch (error) {
      this.logger.error('Bedrock API call failed:', error);
      throw new InternalServerErrorException('Failed to generate summary using AI service');
    }
  }

  private appendClosingMessage(summary: string): string {
    const closingMessage = " If anything is to change of this, I'll be sure to let you know. Please let me know that you have received this message. Thank you!!";
    return summary + closingMessage;
  }

  async healthCheck(): Promise<{ status: string; model: string; region: string }> {
    return {
      status: 'healthy',
      model: this.bedrockConfig.modelId,
      region: this.bedrockConfig.region,
    };
  }
} 