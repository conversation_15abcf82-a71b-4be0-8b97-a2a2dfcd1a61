export interface BedrockConfig {
  accessKeyId: string;
  secretAccessKey: string;
  region: string;
  modelId: string;
  maxTokens: number;
}

export interface BedrockMessage {
  role: 'user' | 'assistant';
  content: Array<{
    type: 'text';
    text: string;
  }>;
}

export interface BedrockRequestBody {
  anthropic_version: string;
  max_tokens: number;
  messages: BedrockMessage[];
}

export interface BedrockResponseChunk {
  type: string;
  delta?: {
    type: string;
    text: string;
  };
} 