import { Is<PERSON>ptional, IsString, IsBoolean, IsN<PERSON>ber } from 'class-validator';

export class GetLocationDto {
  @IsOptional()
  @IsString()
  case_id?: string;

  @IsOptional()
  @IsString()
  access_token?: string;
}

export class LocationResponseDto {
  @IsString()
  name: string;

  @IsBoolean()
  is_active: boolean;

  createdAt?: Date;
  updatedAt?: Date;
}

export class SyncLocationsResponseDto {
  locations: LocationResponseDto[];
  synced_count: number;
  total_count: number;
  message: string;
}
