import { IsString, <PERSON>NotEmpty, IsOptional, IsEnum, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class AssigneeDto {
    @IsString()
    @IsNotEmpty()
    id: string;

    @IsEnum(['user', 'role'])
    type: 'user' | 'role';
}

export class AssignToTaskDto {
    @IsString()
    @IsNotEmpty()
    task_execution_id: string;

    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => AssigneeDto)
    assignees: AssigneeDto[];

    @IsString()
    @IsNotEmpty()
    assigned_by: string;

    @IsString()
    @IsOptional()
    notes?: string;
}

// New DTOs for single user assignment
export class AssignUserToTaskDto {
    @IsString()
    @IsNotEmpty()
    task_execution_id: string;

    @IsString()
    @IsNotEmpty()
    user_id: string;

    @IsString()
    @IsNotEmpty()
    work_flow_id: string;

    // Alternative field names that might be sent from frontend
    @IsOptional()
    @IsString()
    workflow_id?: string;

    @IsOptional()
    @IsString()
    workflow_execution_id?: string;

    @IsString()
    @IsNotEmpty()
    assigned_by: string;

    @IsOptional()
    @IsEnum(['user', 'role'])
    assignee_type?: 'user' | 'role';
}

export class RemoveUserFromTaskDto {
    @IsString()
    @IsNotEmpty()
    task_execution_id: string;

    @IsString()
    @IsNotEmpty()
    user_id: string;

    @IsString()
    @IsNotEmpty()
    work_flow_id: string;

    // Alternative field names that might be sent from frontend
    @IsOptional()
    @IsString()
    workflow_id?: string;

    @IsOptional()
    @IsString()
    workflow_execution_id?: string;

    @IsOptional()
    @IsEnum(['user', 'role'])
    assignee_type?: 'user' | 'role';
}

export class RemoveAssignmentDto {
    @IsString()
    @IsNotEmpty()
    task_execution_id: string;

    @IsString()
    @IsNotEmpty()
    assignee_id: string;

    @IsEnum(['user', 'role'])
    assignment_type: 'user' | 'role';
}

export class GetTaskAssignmentsDto {
    @IsString()
    @IsNotEmpty()
    task_execution_id: string;
}

export class CheckUserTaskAssignmentDto {
    @IsString()
    @IsNotEmpty()
    taskId: string;

    @IsString()
    @IsNotEmpty()
    work_flow_id: string;

    @IsString()
    @IsNotEmpty()
    user_id: string;
} 