import { Type } from 'class-transformer';
import {
  IsOptional,
  Min,
  IsString,
  IsNotEmpty,
  IsArray,
  ValidateNested,
  IsBoolean,
  IsNotEmptyObject,
  IsMongoId,
  IsNumber,
  Max,
} from 'class-validator';
import { FileDto } from './file.dto';
import { RenameFileDto } from './rename.file.dto';

export class FilterCriteriaDto {
  @IsString()
  @IsNotEmpty()
  fieldName: string;

  @IsString()
  @IsNotEmpty()
  criteria: string;

  @IsOptional()
  value?: string | string[] | { from: string; to: string };
}

export class GetWorkflowDto {
  @IsOptional()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Min(1)
  limit?: number = 10;

  @IsOptional()
  @IsString()
  type?: string;

  @IsOptional()
  @IsString()
  userId?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FilterCriteriaDto)
  filters?: FilterCriteriaDto[];

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsString()
  sortBy?: string;

  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc';
}

export class WorkflowRenderDto {
  @IsOptional()
  work_flow_id?: string;
}

export class FormValueDto {
  @IsOptional()
  @IsString()
  id: string | null;

  @IsString()
  value: string; // allow empty string
}

export class FormDto {
  @IsNotEmpty()
  @IsString()
  form_component_id: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FormValueDto)
  value: FormValueDto[];
}

class EventUpdateDataDto {
  @IsOptional() @IsString() caseNumber?: string;
  @IsOptional() @IsString() clientName?: string;
  @IsOptional() @IsString() description?: string;
  @IsOptional() @IsString() date?: string;
  @IsOptional() @IsString() startTime?: string;
  @IsOptional() @IsString() endTime?: string;
  @IsOptional() @IsBoolean() isCompleted?: boolean;
  @IsOptional() @IsString() subject?: string;
  @IsOptional() @IsString() courtNoticeType?: string;
  @IsOptional() @IsString() courtNoticeActions?: string;
  @IsOptional() @IsString() appointmentAction?: string;
  @IsOptional() @IsString() charge?: string;
  @IsOptional() @IsString() county?: string;
  @IsOptional() @IsString() courtLocation?: string;
  @IsOptional() @IsString() optionalAttendees?: string;
  @IsOptional() @IsString() requiredAttendees?: string;
  @IsOptional() @IsString() clientAttendance?: string;
  @IsOptional() @IsString() meetingLocation?: string;
  @IsOptional() @IsString() startDate?: string;
  @IsOptional() @IsString() endDate?: string;
  @IsOptional() @IsBoolean() allDay?: boolean;
  @IsOptional() @IsString() matterName?: string;
  @IsOptional() @IsString() appointmentToReschedule?: string;
  @IsOptional() @IsString() meetingLink?: string;
  @IsOptional() @IsString() court_notice_date?: string;
  @IsOptional() @IsString() phoneDetails?: string;
  @IsOptional() @IsString() meetingAddress?: string;
  @IsOptional() @IsString() id?: string;
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FileDto)
  files?: FileDto[];
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FileDto)
  deleteFiles?: FileDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RenameFileDto)
  renameFiles?: RenameFileDto[];
}

export class UpdateEventDto {
  @IsString()
  @IsNotEmpty()
  derivedFieldId: string;

  @IsString()
  @IsNotEmpty()
  clientIdentifier: string;

  @IsString()
  @IsNotEmpty()
  clientMatterId: string;

  @IsString()
  @IsNotEmpty()
  matterId: string;

  @IsOptional()
  @IsString()
  eventId?: string; // Now optional - if not provided, a new event will be created

  @IsOptional()
  @IsBoolean()
  createEvent?: boolean; // Flag to explicitly indicate we're creating a new event

  @IsOptional()
  @IsBoolean()
  deleteEvent?: boolean; // Flag to indicate we're deleting an event

  @IsOptional()
  @IsBoolean()
  newClient?: boolean; // Flag to indicate if this is a new client

  @IsNotEmptyObject()
  @ValidateNested()
  @Type(() => EventUpdateDataDto)
  updateData: EventUpdateDataDto;
}

export class UpdateWorkflowDto {
  @IsNotEmpty()
  @IsString()
  workflow_id: string;

  @IsNotEmpty()
  @IsString()
  task_id: string;

  @IsOptional()
  @IsBoolean()
  is_completed?: boolean;

  @IsOptional()
  @IsBoolean()
  deleteMatter?: boolean;

  @IsOptional()
  @IsString()
  clientMatterId?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FormDto)
  forms: FormDto[];

  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateEventDto)
  event?: UpdateEventDto;

  @IsOptional()
  @IsBoolean()
  isChild?: boolean;

  @IsOptional()
  @IsBoolean()
  isChildWorkflow?: boolean;
}

export class ArchiveWorkFlowDto {
  @IsNotEmpty()
  work_flow_execution_id: string;

  @IsNotEmpty()
  type: string;

  @IsOptional()
  tenant_id?: string;

  @IsOptional()
  task_id?: string;

  @IsOptional()
  archive_by?: string;

  @IsOptional()
  archive_at?: string;

  @IsOptional()
  mycase_access_token?: string;

  @IsOptional()
  error_message?: string;

  @IsOptional()
  increment_retry?: boolean;
}

export class GetNextTaskIdDto {
  @IsString()
  @IsNotEmpty()
  taskId: string;

  @IsString()
  work_flow_id: string;
}

export class UserListDto {
  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsString()
  client_id?: string;

  @IsOptional()
  @IsString()
  user_group_id?: string;
}

export class CreateDemoWorkflowDto {
  @IsString()
  @IsNotEmpty()
  @IsMongoId()
  template_id: string;

  @IsOptional()
  @IsString()
  notes?: string;

  @IsOptional()
  @IsArray()
  assigns?: string[];

  @IsOptional()
  @IsString()
  user_group_id?: string;
}

export class UpdateWorkflowEndDateDto {
  @IsString()
  @IsNotEmpty()
  workflow_id: string;

  @IsString()
  @IsNotEmpty()
  task_id: string;

  @IsString()
  @IsNotEmpty()
  end_date: string;
}

export class SearchMatterDto {
  @IsOptional()
  @IsString()
  search?: string;
}

export class GetPresignedUrlDto {
  @IsString()
  key: string;

  @IsString()
  operation: string;

  @IsOptional()
  @IsString()
  contentType?: string;
}

export class UpdateWorkflowStatusDto {
  @IsNotEmpty()
  @IsString()
  workflow_id: string;

  @IsOptional()
  @IsBoolean()
  isChild?: boolean;
}

export class GetEventListDto {
  @IsOptional()
  @IsString()
  workflow_id?: string;

  @IsOptional()
  @IsString()
  client_matter_id?: string;
}

export class GetEventByIdDto {
  @IsNotEmpty()
  @IsString()
  event_id: string;
}

export class CleanupFilesDto {
  @IsArray()
  @IsString({ each: true })
  fileKeys: string[];
}

export class CheckClientAvailabilityDto {
  @IsNotEmpty()
  @IsString()
  client_id: string;

  @IsOptional()
  @IsString()
  tenant_id?: string;

  @IsOptional()
  @IsString()
  access_token?: string;
}
