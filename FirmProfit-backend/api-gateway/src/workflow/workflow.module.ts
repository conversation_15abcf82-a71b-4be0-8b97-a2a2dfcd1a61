import { Module } from '@nestjs/common';
import { WorkflowService } from './workflow.service';
import { WorkflowController } from './workflow.controller';
import { GrpcClientModule } from '../grpc-client/grpc-client.module';
import { MyCaseModule } from '../mycase/mycase.module';
import { TenantModule } from '../tenant/tenant.module';

@Module({
  imports: [GrpcClientModule, MyCaseModule, TenantModule],
  controllers: [WorkflowController],
  providers: [WorkflowService],
  exports: [WorkflowService],
})
export class WorkflowModule {}
