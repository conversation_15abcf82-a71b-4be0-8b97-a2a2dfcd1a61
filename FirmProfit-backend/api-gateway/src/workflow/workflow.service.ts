import { Injectable, OnModuleInit } from '@nestjs/common';
import {
  GetWorkflowDto,
  UpdateWorkflowDto,
  UpdateWorkflowStatusDto,
  GetNextTaskIdDto,
  CreateDemoWorkflowDto,
  UpdateWorkflowEndDateDto,
  GetPresignedUrlDto,
  ArchiveWorkFlowDto,
  GetEventListDto,
  UserListDto,
  CleanupFilesDto,
} from './dto/workflow.dto';
import { GetLocationDto } from './dto/location.dto';
import { ErrorExceptions } from 'src/exceptions/error.exception';
import { GrpcClientService } from 'src/grpc-client/grpc-client.service';
import * as MESSAGE from '../locales/en.json';
import { throwError } from 'rxjs';
import { Logger } from '@nestjs/common';

@Injectable()
export class WorkflowService implements OnModuleInit {
  private readonly logger = new Logger(WorkflowService.name);

  constructor(private readonly grpcClient: GrpcClientService) { }

  // Add this method to implement the OnModuleInit interface
  async onModuleInit() {
    // Initialize anything you need when the module starts
  }

  async myWorkFLow(getWorkflowDto: GetWorkflowDto) {
    try {
      const response = await this.grpcClient.myWorkflows(getWorkflowDto);
      return response;
    } catch (error) {
      console.error('Error in myWorkFLow service:', error);
      throw ErrorExceptions.InternalServerError();
    }
  }

  async workFlowRender(data) {
    const response = await this.grpcClient.workFlowRenderShared(data);
    // console.log("🚀 ~ WorkflowService ~ workFlowRender ~ response:", response);

    return JSON.parse(response.data || '[]');
  }

  async handleOAuthCallback() {
    return await this.grpcClient.handleOAuthCallbackShared();
  }

  async updateWorkflow(updateWorkflowDto: UpdateWorkflowDto) {
    try {
      // Ensure each form's values are strings and structure matches proto format
      const formattedForms = updateWorkflowDto.forms.map((form) => ({
        form_component_id: form.form_component_id,
        value: form.value.map((v) => ({
          id: v.id ?? null, // ensure null if not present
          value: v.value ? String(v.value) : '', // convert value to string
        })),
      }));

      // Determine if this update is for a child workflow (frontend may send either isChild or isChildWorkflow)
      const isChildWorkflow = Boolean(
        (updateWorkflowDto as any).isChildWorkflow ?? updateWorkflowDto.isChild,
      );

      // Format event data if present
      let formattedEvent = null;
      if (updateWorkflowDto.event) {
        // If child workflow, prevent reschedule/cancel/delete operations.
        // Only allow updating the event fields. We explicitly strip flags
        // that would trigger deletion or rescheduling.
        const sanitizedEvent = { ...updateWorkflowDto.event } as any;
        if (isChildWorkflow) {
          // Remove delete flag at event level
          if (sanitizedEvent.deleteEvent) {
            delete sanitizedEvent.deleteEvent;
          }
          // Remove reschedule/cancel hints inside updateData
          if (sanitizedEvent.updateData) {
            const blockedKeys = [
              'appointmentAction',
              'courtNoticeActions',
              'appointmentToReschedule',
            ];
            blockedKeys.forEach((k) => {
              if (k in sanitizedEvent.updateData) {
                delete sanitizedEvent.updateData[k];
              }
            });
          }
        }

        formattedEvent = {
          ...sanitizedEvent,
          updateData: {
            ...sanitizedEvent?.updateData,
            // Ensure files array is properly formatted
            files: Array.isArray(sanitizedEvent?.updateData?.files)
              ? sanitizedEvent.updateData.files
                  .filter((file) => file && typeof file.size === 'number')
                  .map((file) => ({
                    name: String(file.name || ''),
                    url: String(file.url || ''),
                    type: String(file.type || ''),
                    size: Number(file.size),
                    key: String(file.key || ''),
                    uniqueId: String(file.uniqueId || ''),
                  }))
              : [],
          },
        };
      }

      const formattedDto = {
        workflow_id: updateWorkflowDto.workflow_id,
        task_id: updateWorkflowDto.task_id,
        forms: isChildWorkflow ? [] : formattedForms,
        is_completed: updateWorkflowDto.is_completed ?? false,
        deleteMatter: isChildWorkflow ? false : (updateWorkflowDto.deleteMatter ?? false),
        clientMatterId: updateWorkflowDto.clientMatterId ?? null,
        event: formattedEvent,
        // Do not forward any child flags to gRPC unless backend requires it; keep for server-side logic only
      };

      return await this.grpcClient.updateWorkflow(formattedDto);
    } catch (err) {
      if (err.message?.includes(MESSAGE.TASK_EXECUTION_NOT_FOUND)) {
        return throwError(() => ErrorExceptions.TASK_NOT_FOUND());
      }
      return throwError(() => ErrorExceptions.InternalServerError());
    }
  }

  async getCourtNoticeList(getWorkflowDto: GetWorkflowDto) {
    try {
      return await this.grpcClient.getCourtNoticeList(getWorkflowDto);
    } catch (err) {
      console.error('Error in getCourtNoticeList service:', err);
      throw ErrorExceptions.InternalServerError();
    }
  }

  async updateWorkflowStatus(updateWorkflowStatusDto: UpdateWorkflowStatusDto) {
    try {
      // Ensure each form's values are strings and structure matches proto format
      return await this.grpcClient.updateWorkflowStatus(
        updateWorkflowStatusDto,
      );
    } catch (err) {
      if (err.message?.includes(MESSAGE.WORK_FLOW_EXECUTION_NOT_FOUND)) {
        return throwError(() =>
          ErrorExceptions.WORK_FLOW_EXECUTION_NOT_FOUND(),
        );
      }
      return throwError(() => ErrorExceptions.InternalServerError());
    }
  }

  async getCountyList() {
    try {
      return await this.grpcClient.getCountyList();
    } catch (err) {
      console.error('Error in getCourtNoticeList service:', err);
      throw ErrorExceptions.InternalServerError();
    }
  }

  async getCourtLocationList() {
    try {
      return await this.grpcClient.getCourtLocationList();
    } catch (err) {
      console.error('Error in getCourtNoticeList service:', err);
      throw ErrorExceptions.InternalServerError();
    }
  }

  async getUserList(userListDto: UserListDto) {
    try {
      console.log('🚀 ~ getUserList ~ API  userListDto:', userListDto);
      return await this.grpcClient.getUserList(userListDto);
    } catch (err) {
      console.error('Error in getUserList service:', err);
      throw ErrorExceptions.InternalServerError();
    }
  }

  async getAllUsersAndRoles(query: { search?: string }) {
    try {
      return await this.grpcClient.getAllUsersAndRoles(query);
    } catch (err) {
      console.error('Error in getAllUsersAndRoles service:', err);
      throw ErrorExceptions.InternalServerError();
    }
  }


  async getCourtNoticeType() {
    try {
      return await this.grpcClient.getCourtNoticeType();
    } catch (err) {
      console.error('Error in getCourtNoticeType service:', err);
      throw ErrorExceptions.InternalServerError();
    }
  }

  async getMatterList() {
    try {
      return await this.grpcClient.getMatterList();
    } catch (err) {
      console.error('Error in getMatterList service:', err);
      throw ErrorExceptions.InternalServerError();
    }
  }

  /**
   * Search matters by name
   * @param search Search string to filter matters
   * @returns Filtered list of matters matching the search string
   */
  async searchMatterList(search: string) {
    try {
      return await this.grpcClient.searchMatterList(search);
    } catch (error) {
      console.error('Error in searchMatterList service:', error);
      throw ErrorExceptions.InternalServerError();
    }
  }

  async getNextTaskId(getNextTaskIdDto: GetNextTaskIdDto) {
    try {
      return await this.grpcClient.getNextTaskId(getNextTaskIdDto);
    } catch (err) {
      console.error('Error in getNextTaskId service:', err);
      return throwError(() => ErrorExceptions.InternalServerError());
    }
  }

  /**
   * Create a demo workflow for testing purposes
   * @param createDemoWorkflowDto Data for creating a demo workflow
   * @returns Creation result
   */
  async createDemoWorkflow(createDemoWorkflowDto: CreateDemoWorkflowDto) {
    try {
      return await this.grpcClient.createDemoWorkflow(createDemoWorkflowDto);
    } catch (err) {
      if (err.message?.includes('Template not found')) {
        return throwError(() => ErrorExceptions.TASK_NOT_FOUND());
      }
      if (err.message?.includes('Invalid template id')) {
        return throwError(() => ErrorExceptions.INVALID_TASK_ID());
      }
      console.error('Error in createDemoWorkflow service:', err);
      return throwError(() => ErrorExceptions.InternalServerError());
    }
  }

  /**
   * Update the end date of a workflow task
   * @param updateWorkflowEndDateDto Data containing workflow_id, task_id, and end_date
   * @returns Result of the update operation
   */
  async updateWorkflowEndDate(
    updateWorkflowEndDateDto: UpdateWorkflowEndDateDto,
  ) {
    try {
      return await this.grpcClient.updateWorkflowEndDate(
        updateWorkflowEndDateDto,
      );
    } catch (err) {
      if (err.message?.includes(MESSAGE.WORK_FLOW_EXECUTION_NOT_FOUND)) {
        return throwError(() =>
          ErrorExceptions.WORK_FLOW_EXECUTION_NOT_FOUND(),
        );
      }
      if (err.message?.includes(MESSAGE.TASK_EXECUTION_NOT_FOUND)) {
        return throwError(() => ErrorExceptions.TASK_NOT_FOUND());
      }
      console.error('Error in updateWorkflowEndDate service:', err);
      return throwError(() => ErrorExceptions.InternalServerError());
    }
  }

  async getPresignedUrl(getPresignedUrlDto: GetPresignedUrlDto) {
    try {
      return await this.grpcClient.getPresignedUrl(getPresignedUrlDto);
    } catch (err) {
      console.error('Error in getPresignedUrl service:', err);
      throw ErrorExceptions.InternalServerError();
    }
  }

  async archiveWorkFlow(archiveWorkFlowDto: ArchiveWorkFlowDto) {
    try {
      return await this.grpcClient.archiveWorkFlow(archiveWorkFlowDto);
    } catch (err) {
      console.error('Error in archiveWorkFlow service:', err);
      throw ErrorExceptions.InternalServerError();
    }
  }

  async getAppointmentEvents(getEventListDto: GetEventListDto) {
    try {
      const response: any =
        await this.grpcClient.getAppointmentEvents(getEventListDto);
      return JSON.parse(response.data || '[]');
    } catch (err) {
      console.error('Error in getAppointmentEvents service:', err);
      return throwError(() => ErrorExceptions.InternalServerError());
    }
  }

  async getEventById(getEventByIdDto: { event_id: string }) {
    try {
      return await this.grpcClient.getEventById(getEventByIdDto);
    } catch (err) {
      console.error('Error in getEventById service:', err);
      return throwError(() => ErrorExceptions.InternalServerError());
    }
  }

  // Location-related methods
  async syncLocationsFromMyCase(getLocationDto: GetLocationDto) {
    try {
      return await this.grpcClient.syncLocationsFromMyCase(getLocationDto);
    } catch (err) {
      console.error('Error in syncLocationsFromMyCase service:', err);
      throw ErrorExceptions.InternalServerError();
    }
  }

  async getLocationList() {
    try {
      return await this.grpcClient.getLocationList();
    } catch (err) {
      console.error('Error in getLocationList service:', err);
      throw ErrorExceptions.InternalServerError();
    }
  }

  async getLocationById(data: { id: string }) {
    try {
      this.logger.log(`Calling getLocationById with id: ${data.id}`);
      const result = await this.grpcClient.getLocationById(data);
      return result;
    } catch (error) {
      this.logger.error(`Error in getLocationById: ${error.message}`);
      throw error;
    }
  }

  async cleanupUploadedFiles(cleanupFilesDto: CleanupFilesDto) {
    try {
      return await this.grpcClient.cleanupUploadedFiles(cleanupFilesDto);
    } catch (err) {
      console.error('Error in cleanupUploadedFiles service:', err);
      throw ErrorExceptions.InternalServerError();
    }
  }

  async checkClientAvailability(data: {
    client_id: string;
    tenant_id?: string;
    access_token?: string;
  }) {
    try {
      this.logger.log(
        `Calling checkClientAvailability with client_id: ${data.client_id}`,
      );
      const result = await this.grpcClient.checkClientAvailability(data);
      return result;
    } catch (error) {
      this.logger.error(`Error in checkClientAvailability: ${error.message}`);
      throw error;
    }
  }

  async testSuccess(req: any) {
    try {
      this.logger.log(
        `Service layer - Processing request: ${JSON.stringify(req)}`,
      );
      const grpcResponse = await this.grpcClient.testSuccess(req);

      // Parse the received_params_json back to an object for the final response
      let receivedParams = {};
      if (grpcResponse.received_params_json) {
        try {
          // Check if it's already a string that needs parsing
          if (typeof grpcResponse.received_params_json === 'string') {
            receivedParams = JSON.parse(grpcResponse.received_params_json);
          } else {
            // If it's already an object, use it directly
            receivedParams = grpcResponse.received_params_json;
          }
        } catch (parseError) {
          this.logger.error(
            `Error parsing received_params_json: ${parseError.message}`,
          );
          this.logger.error(
            `Raw received_params_json value: ${grpcResponse.received_params_json}`,
          );
          this.logger.error(
            `Type of received_params_json: ${typeof grpcResponse.received_params_json}`,
          );
          // Fallback to the original request data if parsing fails
          receivedParams = req;
        }
      }

      return {
        success: grpcResponse.success,
        message: grpcResponse.message,
        received_params: receivedParams,
      };
    } catch (error) {
      this.logger.error(`Error in testSuccess service: ${error.message}`);
      throw ErrorExceptions.InternalServerError();
    }
  }
  async removeAssignment(data: any): Promise<any> {
    return this.grpcClient.removeAssignment(data);
  }

  async assignUserToTask(data: any): Promise<any> {
    this.logger.log(`API Gateway received assignUserToTask data: ${JSON.stringify(data)}`);

    // Validate required fields
    if (!data.task_execution_id) {
      throw new Error('task_execution_id is required');
    }
    if (!data.work_flow_id) {
      throw new Error('work_flow_id is required');
    }
    if (!data.user_id) {
      throw new Error('user_id is required');
    }
    if (!data.assigned_by) {
      throw new Error('assigned_by is required');
    }

    // Set default assignee_type if not provided
    if (!data.assignee_type) {
      data.assignee_type = 'user';
    }

    return this.grpcClient.assignUserToTask(data);
  }

  async removeUserFromTask(data: any): Promise<any> {
    this.logger.log(`API Gateway received removeUserFromTask data: ${JSON.stringify(data)}`);

    // Validate required fields
    if (!data.task_execution_id) {
      throw new Error('task_execution_id is required');
    }
    if (!data.work_flow_id) {
      throw new Error('work_flow_id is required');
    }
    if (!data.user_id) {
      throw new Error('user_id is required');
    }

    // Set default assignee_type if not provided
    if (!data.assignee_type) {
      data.assignee_type = 'user';
    }

    return this.grpcClient.removeUserFromTask(data);
  }

  async checkUserTaskAssignment(data: any): Promise<any> {
    this.logger.log(`API Gateway received checkUserTaskAssignment data: ${JSON.stringify(data)}`);

    // Validate required fields
    if (!data.taskId) {
      throw new Error('taskId is required');
    }
    if (!data.work_flow_id) {
      throw new Error('work_flow_id is required');
    }
    if (!data.user_id) {
      throw new Error('user_id is required');
    }

    return this.grpcClient.checkUserTaskAssignment(data);
  }

  async getTaskAssignedUsers(data: any): Promise<any> {
    this.logger.log(`API Gateway received getTaskAssignedUsers data: ${JSON.stringify(data)}`);

    // Validate required fields with better error messages
    if (!data.task_id) {
      this.logger.error(`task_id is missing. Received data: ${JSON.stringify(data)}`);
      throw new Error('task_id is required');
    }
    if (!data.workflow_execution_id) {
      this.logger.error(`workflow_execution_id is missing. Received data: ${JSON.stringify(data)}`);
      throw new Error('workflow_execution_id is required');
    }

    this.logger.log(`Calling gRPC service with task_id: ${data.task_id}, workflow_execution_id: ${data.workflow_execution_id}`);
    return this.grpcClient.getTaskAssignedUsers(data);
  }
}
