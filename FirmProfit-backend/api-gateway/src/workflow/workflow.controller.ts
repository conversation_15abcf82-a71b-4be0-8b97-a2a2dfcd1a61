import {
  Body,
  Controller,
  Get,
  Post,
  Put,
  Query,
  Param,
  Logger,
} from '@nestjs/common';
import {
  GetWorkflowDto,
  UpdateWorkflowDto,
  UpdateWorkflowStatusDto,
  WorkflowRenderDto,
  GetNextTaskIdDto,
  CreateDemoWorkflowDto,
  UpdateWorkflowEndDateDto,
  SearchMatterDto,
  GetPresignedUrlDto,
  ArchiveWorkFlowDto,
  GetEventListDto,
  UserListDto,
  CleanupFilesDto,
  CheckClientAvailabilityDto,
} from './dto/workflow.dto';
import { GetLocationDto } from './dto/location.dto';
import {
  AssignUserToTaskDto,
  RemoveUserFromTaskDto,
  CheckUserTaskAssignmentDto,
} from './dto/assignment.dto';
import { WorkflowService } from './workflow.service';
import { ResponseMessage } from 'src/auth/decorators/response.decorator';
import { MESSAGES } from '@nestjs/core/constants';
import { GrpcClientService } from '../grpc-client/grpc-client.service';
import { MyCaseAuthService } from '../mycase/services/mycase-auth.service';
import { MyCaseEventService } from '../mycase/services/mycase-event.service';
import { TenantConfigService } from '../tenant/services/tenant-config.service';

@Controller('workflow')
export class WorkflowController {
  private readonly logger = new Logger(WorkflowController.name);

  constructor(
    private readonly workflowService: WorkflowService,
    private readonly grpcClientService: GrpcClientService,
    private readonly myCaseAuthService: MyCaseAuthService,
    private readonly myCaseEventService: MyCaseEventService,
    private readonly tenantConfigService: TenantConfigService,
  ) { }

  /**
   * show my workFlow
   * @param getWorkflowDto User registration data
   */
  @Get('my-workflow')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async myWorkFLow(@Query() getWorkflowDto: GetWorkflowDto) {
    return this.workflowService.myWorkFLow(getWorkflowDto);
  }

  /**
   * this api use for workflow render
   * @param getWorkflowDto User registration data
   */
  @Get('workflow-render')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async workFlowRender(@Query() workflowRenderDto: WorkflowRenderDto) {
    return this.workflowService.workFlowRender(workflowRenderDto);
  }

  /**
   * this api use for workflow render
   * @param getWorkflowDto User registration data
   */
  @Get('callback')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async handleOAuthCallback() {
    return this.workflowService.handleOAuthCallback();
  }

  /**
   * this api use to update workflow
   * @param updateWorkflowDto Workflow update data
   */
  @Put('workflow-update')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async updateWorkflow(@Body() updateWorkflowDto: UpdateWorkflowDto) {
    return this.workflowService.updateWorkflow(updateWorkflowDto);
  }

  /**
   * Get court notice list
   * @param getWorkflowDto Pagination data
   */
  @Get('court-notice-list')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async getCourtNoticeList(@Query() getWorkflowDto: GetWorkflowDto) {
    return this.workflowService.getCourtNoticeList(getWorkflowDto);
  }

  /**
   * this api use to update workflow
   * @param UpdateWorkflowStatusDto Workflow update data
   */
  @Put('workflow-update-status')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async updateWorkflowStatus(
    @Body() updateWorkflowStatusDto: UpdateWorkflowStatusDto,
  ) {
    return this.workflowService.updateWorkflowStatus(updateWorkflowStatusDto);
  }

  /**
   * Get county list
   * @param  Pagination data
   */
  @Get('county-list')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async getCountyList() {
    return this.workflowService.getCountyList();
  }

  /**
   * Get court_location list
   * @param  Pagination data
   */
  @Get('court-location-list')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async getCourtLocationList() {
    return this.workflowService.getCourtLocationList();
  }

  /**
   * Get users list
   */
  @Get('user-list')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async getUsersList(@Query() userListDto: UserListDto) {
    return this.workflowService.getUserList(userListDto);
  }

  /**
   * Get all users and roles combined
   */
  @Get('users-and-roles')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async getAllUsersAndRoles(@Query() query: { search?: string }) {
    return this.workflowService.getAllUsersAndRoles(query);
  }


  /**
   * Get client attendance options list
   */
  @Get('court-notice-type')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async getCourtNoticeType() {
    return this.workflowService.getCourtNoticeType();
  }

  /**
   * Get matter list
   */
  @Get('matter-list')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async getMatterList(@Query() searchMatterDto: SearchMatterDto) {
    // If search parameter is provided, use the search function
    if (searchMatterDto.search) {
      return this.workflowService.searchMatterList(searchMatterDto.search);
    }
    // Otherwise return all matters
    return this.workflowService.getMatterList();
  }

  /**
   * Get next task ID for a given task
   * @param getNextTaskIdDto Task ID to get the next task for
   */
  @Get('next-task')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async getNextTaskId(@Query() getNextTaskIdDto: GetNextTaskIdDto) {
    return this.workflowService.getNextTaskId(getNextTaskIdDto);
  }

  /**
   * Create a demo workflow for testing
   * @param createDemoWorkflowDto Data required to create a demo workflow
   */
  @Post('demo-workflow')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async createDemoWorkflow(
    @Body() createDemoWorkflowDto: CreateDemoWorkflowDto,
  ) {
    this.logger.log(
      `Received createDemoWorkflow request with data: ${JSON.stringify(createDemoWorkflowDto)}`,
    );
    this.logger.log(
      `user_group_id in request: ${createDemoWorkflowDto.user_group_id}`,
    );

    // Debug log to help troubleshoot the dev server issue
    if (!createDemoWorkflowDto.user_group_id) {
      this.logger.warn(
        'user_group_id is missing from request body - will use default value in shared service',
      );
    }

    return this.workflowService.createDemoWorkflow(createDemoWorkflowDto);
  }

  /**
   * Update the end date of a workflow task
   * @param updateWorkflowEndDateDto Data to update the end date
   */
  @Put('update-end-date')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async updateWorkflowEndDate(
    @Body() updateWorkflowEndDateDto: UpdateWorkflowEndDateDto,
  ) {
    return this.workflowService.updateWorkflowEndDate(updateWorkflowEndDateDto);
  }

  @Get('get-presigned-url')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async getPresignedUrl(@Query() query: GetPresignedUrlDto) {
    return this.workflowService.getPresignedUrl(query);
  }

  /**
   * Cleanup uploaded files from S3 when user cancels or closes modal without saving
   * @param cleanupFilesDto Data containing file keys to delete
   */
  @Post('cleanup-files')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async cleanupUploadedFiles(@Body() cleanupFilesDto: CleanupFilesDto) {
    return this.workflowService.cleanupUploadedFiles(cleanupFilesDto);
  }

  /**
   * this api use to update workflow
   * @param ArchiveWorkFlowDto Workflow update data
   */
  @Put('archive-work-flow')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async archiveWorkFlow(@Body() archiveWorkFlowDto: ArchiveWorkFlowDto) {
    // If type is "updatecase" and tenant_id is provided, handle MyCase integration
    if (
      archiveWorkFlowDto.type === 'updatecase' &&
      archiveWorkFlowDto.tenant_id &&
      archiveWorkFlowDto.work_flow_execution_id
    ) {
      return this.handleMyCaseWorkflowArchive(archiveWorkFlowDto);
    }

    // Otherwise, use the standard workflow service
    return this.workflowService.archiveWorkFlow(archiveWorkFlowDto);
  }

  /**
   * Handle MyCase integration for workflow archiving
   */
  private async handleMyCaseWorkflowArchive(
    archiveWorkFlowDto: ArchiveWorkFlowDto,
  ) {
    try {
      const { tenant_id, work_flow_execution_id } = archiveWorkFlowDto;

      // Step 1: Check tenant configuration
      const tenantConfig =
        await this.tenantConfigService.getMyCaseConfig(tenant_id);
      if (!tenantConfig) {
        // Update retry count and status through workflow service
        const failureResponse = await this.workflowService.archiveWorkFlow({
          ...archiveWorkFlowDto,
          error_message: `MyCase configuration not found for tenant: ${tenant_id}`,
          increment_retry: true,
        });

        return {
          success: false,
          message: `MyCase configuration not found for tenant: ${tenant_id}`,
          tenant_id: archiveWorkFlowDto.tenant_id,
          work_flow_execution_id: archiveWorkFlowDto.work_flow_execution_id,
          mycase_archive_retry_count:
            failureResponse.mycase_archive_retry_count,
        };
      }

      // Step 2: Authenticate with MyCase (check existing tokens first)
      let authResult;
      try {
        authResult =
          await this.myCaseAuthService.authenticateWithTenantConfig(tenant_id);
      } catch (error) {
        if (error.message.includes('No valid tokens available')) {
          // Return a response indicating 2FA is required
          return {
            success: false,
            requires_2fa: true,
            message:
              'Authentication required. Please provide 2FA code or complete OAuth flow.',
            tenant_id,
            work_flow_execution_id,
          };
        }

        // Update retry count and status through workflow service for auth failure
        const failureResponse = await this.workflowService.archiveWorkFlow({
          ...archiveWorkFlowDto,
          error_message: error.message,
          increment_retry: true,
        });

        return {
          success: false,
          message: `MyCase authentication failed: ${error.message}`,
          tenant_id: archiveWorkFlowDto.tenant_id,
          work_flow_execution_id: archiveWorkFlowDto.work_flow_execution_id,
          error: error.message,
          mycase_archive_retry_count:
            failureResponse.mycase_archive_retry_count,
        };
      }

      // Step 3: Pass access token to workflow service
      const archiveWorkFlowDtoWithToken = {
        ...archiveWorkFlowDto,
        mycase_access_token: authResult.access_token,
      };

      return this.workflowService.archiveWorkFlow(archiveWorkFlowDtoWithToken);
    } catch (error) {
      // Log the error and return failure status
      console.error('MyCase workflow archive failed:', error);

      // Update retry count and status through workflow service
      const failureResponse = await this.workflowService.archiveWorkFlow({
        ...archiveWorkFlowDto,
        error_message: error.message,
        increment_retry: true,
      });

      return {
        success: false,
        message: `MyCase integration failed: ${error.message}`,
        tenant_id: archiveWorkFlowDto.tenant_id,
        work_flow_execution_id: archiveWorkFlowDto.work_flow_execution_id,
        error: error.message,
        mycase_archive_retry_count: failureResponse.mycase_archive_retry_count,
      };
    }
  }

  private async scheduleRetry(
    archiveWorkFlowDto: ArchiveWorkFlowDto,
    currentRetryCount: number,
    errorMessage: string,
    delayMs: number,
  ) {
    const nextRetryCount = currentRetryCount + 1;
    const MAX_RETRIES = 3;

    this.logger.warn(
      `Scheduling retry ${nextRetryCount}/${MAX_RETRIES} for workflow ${archiveWorkFlowDto.work_flow_execution_id} after ${delayMs}ms. Error: ${errorMessage}`,
    );

    // Update workflow execution with current failure info
    await this.updateWorkflowArchiveStatus(
      archiveWorkFlowDto.work_flow_execution_id,
      'PENDING',
      nextRetryCount,
      errorMessage,
    );

    if (nextRetryCount >= MAX_RETRIES) {
      // Final attempt failed
      await this.updateWorkflowArchiveStatus(
        archiveWorkFlowDto.work_flow_execution_id,
        'FAILED',
        nextRetryCount,
        `Final retry failed: ${errorMessage}`,
      );

      return {
        success: false,
        message: `All retry attempts exhausted. Last error: ${errorMessage}`,
        tenant_id: archiveWorkFlowDto.tenant_id,
        work_flow_execution_id: archiveWorkFlowDto.work_flow_execution_id,
        retry_count: nextRetryCount,
        max_retries_exceeded: true,
        last_error: errorMessage,
      };
    }

    // Schedule the retry after delay
    setTimeout(async () => {
      try {
        const retryDto = {
          ...archiveWorkFlowDto,
          retry_count: nextRetryCount,
          last_error: errorMessage,
        };

        this.logger.log(
          `Executing retry ${nextRetryCount} for workflow ${archiveWorkFlowDto.work_flow_execution_id}`,
        );
        await this.handleMyCaseWorkflowArchive(retryDto);
      } catch (retryError) {
        this.logger.error(
          `Error during retry execution: ${retryError.message}`,
        );
      }
    }, delayMs);

    return {
      success: false,
      message: `Retry ${nextRetryCount}/${MAX_RETRIES} scheduled. Error: ${errorMessage}`,
      tenant_id: archiveWorkFlowDto.tenant_id,
      work_flow_execution_id: archiveWorkFlowDto.work_flow_execution_id,
      retry_count: nextRetryCount,
      retry_scheduled: true,
      retry_delay_ms: delayMs,
      last_error: errorMessage,
    };
  }

  private async updateWorkflowArchiveStatus(
    workflowExecutionId: string,
    status: 'PENDING' | 'IN_PROGRESS' | 'SUCCESS' | 'FAILED',
    retryCount: number,
    errorMessage?: string,
  ) {
    try {
      this.logger.log(
        `Updating workflow ${workflowExecutionId} - Status: ${status}, Retry Count: ${retryCount}, Error: ${errorMessage || 'None'}`,
      );

      // Call the shared service to update the workflow execution retry info
      const updateData = {
        workflow_id: workflowExecutionId,
        status: status,
        retry_count: retryCount,
        error_message: errorMessage,
      };

      const result =
        await this.grpcClientService.updateWorkflowRetryInfo(updateData);

      if (result.success) {
        this.logger.log(
          `Successfully updated workflow retry info for ${workflowExecutionId}`,
        );
      } else {
        this.logger.warn(
          `Failed to update workflow retry info for ${workflowExecutionId}: ${result.message}`,
        );
      }

      return result.success;
    } catch (updateError) {
      this.logger.error(
        `Failed to update workflow archive status: ${updateError.message}`,
      );
      return false;
    }
  }

  @Get('appointments')
  async getAppointmentEvents(@Query() query: GetEventListDto) {
    return this.workflowService.getAppointmentEvents(query);
  }

  /**
   * Get event by ID from MycaseClientMatter collection
   * @param event_id Event ID from route parameter
   */
  @Get('event/:event_id')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async getEventById(@Param('event_id') event_id: string) {
    return this.workflowService.getEventById({ event_id });
  }

  /**
   * Sync locations from MyCase
   * @param getLocationDto Location sync parameters
   */
  @Get('sync-mycase-locations')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async syncLocationsFromMyCase(@Query() getLocationDto: GetLocationDto) {
    return this.workflowService.syncLocationsFromMyCase(getLocationDto);
  }

  /**
   * Get all locations
   */
  @Get('locations')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async getLocationList() {
    return this.workflowService.getLocationList();
  }

  /**
   * Get location by ID
   * @param id Location ID
   */
  @Get('location/:id')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async getLocationById(@Query('id') id: string) {
    return this.workflowService.getLocationById({ id });
  }

  @Post('check-client-availability')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async checkClientAvailability(
    @Body() checkClientAvailabilityDto: CheckClientAvailabilityDto,
  ) {
    try {
      this.logger.log(
        `Checking client availability for client_id: ${checkClientAvailabilityDto.client_id}`,
      );

      // If tenant_id is provided, generate access token
      if (checkClientAvailabilityDto.tenant_id) {
        try {
          const authResult =
            await this.myCaseAuthService.authenticateWithTenantConfig(
              checkClientAvailabilityDto.tenant_id,
            );
          checkClientAvailabilityDto.access_token = authResult.access_token;
        } catch (authError) {
          this.logger.error(
            `Authentication failed for tenant ${checkClientAvailabilityDto.tenant_id}: ${authError.message}`,
          );
          return {
            available: false,
            message: `Authentication failed: ${authError.message}`,
            client_name: '',
            client_email: '',
            my_case_client_id: '',
          };
        }
      }

      // Call the shared service
      const result = await this.workflowService.checkClientAvailability(
        checkClientAvailabilityDto,
      );

      this.logger.log(
        `Client availability check completed: ${result.available}`,
      );
      return result;
    } catch (error) {
      this.logger.error(`Error in checkClientAvailability: ${error.message}`);
      throw error;
    }
  }

  /**
   * Test endpoint that accepts any data, logs request params and returns them
   */
  @Post('webhook-test')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async testSuccess(@Body() req: any) {
    this.logger.log(
      `Received webhook test request with params: ${JSON.stringify(req)}`,
    );
    return this.workflowService.testSuccess(req);
  }

  /**
   * Remove an assignment from a task
   * @param removeAssignmentDto Assignment data
   */
  @Post('remove-assignment')
  async removeAssignment(@Body() removeAssignmentDto: any) {
    return this.workflowService.removeAssignment(removeAssignmentDto);
  }

  /**
   * Assign a single user to a task
   * @param assignUserToTaskDto Assignment data
   */
  @Post('assign-user-to-task')
  @ResponseMessage('User assigned to task successfully')
  async assignUserToTask(@Body() assignUserToTaskDto: AssignUserToTaskDto, @Query() query: any) {
    this.logger.log(`Controller received assignUserToTask data: ${JSON.stringify(assignUserToTaskDto)}`);
    this.logger.log(`Controller received query params: ${JSON.stringify(query)}`);

    // Handle alternative field names
    if (!assignUserToTaskDto.work_flow_id && assignUserToTaskDto.workflow_id) {
      assignUserToTaskDto.work_flow_id = assignUserToTaskDto.workflow_id;
      this.logger.log(`Mapped workflow_id to work_flow_id: ${assignUserToTaskDto.work_flow_id}`);
    }

    if (!assignUserToTaskDto.work_flow_id && assignUserToTaskDto.workflow_execution_id) {
      assignUserToTaskDto.work_flow_id = assignUserToTaskDto.workflow_execution_id;
      this.logger.log(`Mapped workflow_execution_id to work_flow_id: ${assignUserToTaskDto.work_flow_id}`);
    }

    // Try to get work_flow_id from query parameters if not in body
    if (!assignUserToTaskDto.work_flow_id && query.work_flow_id) {
      assignUserToTaskDto.work_flow_id = query.work_flow_id;
      this.logger.log(`Extracted work_flow_id from query params: ${assignUserToTaskDto.work_flow_id}`);
    }

    if (!assignUserToTaskDto.work_flow_id && query.workflow_id) {
      assignUserToTaskDto.work_flow_id = query.workflow_id;
      this.logger.log(`Extracted workflow_id from query params: ${assignUserToTaskDto.work_flow_id}`);
    }

    // Handle assignee_type from query parameters if not provided in body
    if (!assignUserToTaskDto.assignee_type && query.assignee_type) {
      assignUserToTaskDto.assignee_type = query.assignee_type;
      this.logger.log(`Extracted assignee_type from query params: ${assignUserToTaskDto.assignee_type}`);
    }

    this.logger.log(`work_flow_id type: ${typeof assignUserToTaskDto.work_flow_id}, value: ${assignUserToTaskDto.work_flow_id}`);
    this.logger.log(`task_execution_id type: ${typeof assignUserToTaskDto.task_execution_id}, value: ${assignUserToTaskDto.task_execution_id}`);
    this.logger.log(`user_id type: ${typeof assignUserToTaskDto.user_id}, value: ${assignUserToTaskDto.user_id}`);
    this.logger.log(`assigned_by type: ${typeof assignUserToTaskDto.assigned_by}, value: ${assignUserToTaskDto.assigned_by}`);
    this.logger.log(`assignee_type type: ${typeof assignUserToTaskDto.assignee_type}, value: ${assignUserToTaskDto.assignee_type}`);

    // Check if work_flow_id is undefined or null
    if (!assignUserToTaskDto.work_flow_id) {
      this.logger.error('work_flow_id is missing or undefined in the request body and query params');
      this.logger.error(`Full request body: ${JSON.stringify(assignUserToTaskDto)}`);
      this.logger.error(`Full query params: ${JSON.stringify(query)}`);
      throw new Error('work_flow_id is required in the request body or query parameters');
    }

    return this.workflowService.assignUserToTask(assignUserToTaskDto);
  }

  /**
   * Remove a single user from a task
   * @param removeUserFromTaskDto Removal data
   */
  @Post('remove-user-from-task')
  @ResponseMessage('User removed from task successfully')
  async removeUserFromTask(@Body() removeUserFromTaskDto: RemoveUserFromTaskDto, @Query() query: any) {
    this.logger.log(`Controller received removeUserFromTask data: ${JSON.stringify(removeUserFromTaskDto)}`);
    this.logger.log(`Controller received query params: ${JSON.stringify(query)}`);

    // Handle alternative field names
    if (!removeUserFromTaskDto.work_flow_id && removeUserFromTaskDto.workflow_id) {
      removeUserFromTaskDto.work_flow_id = removeUserFromTaskDto.workflow_id;
      this.logger.log(`Mapped workflow_id to work_flow_id: ${removeUserFromTaskDto.work_flow_id}`);
    }

    if (!removeUserFromTaskDto.work_flow_id && removeUserFromTaskDto.workflow_execution_id) {
      removeUserFromTaskDto.work_flow_id = removeUserFromTaskDto.workflow_execution_id;
      this.logger.log(`Mapped workflow_execution_id to work_flow_id: ${removeUserFromTaskDto.work_flow_id}`);
    }

    if (!removeUserFromTaskDto.work_flow_id && query.work_flow_id) {
      removeUserFromTaskDto.work_flow_id = query.work_flow_id;
      this.logger.log(`Extracted work_flow_id from query params: ${removeUserFromTaskDto.work_flow_id}`);
    }

    if (!removeUserFromTaskDto.work_flow_id && query.workflow_id) {
      removeUserFromTaskDto.work_flow_id = query.workflow_id;
      this.logger.log(`Extracted workflow_id from query params: ${removeUserFromTaskDto.work_flow_id}`);
    }

    // Handle assignee_type from query parameters if not provided in body
    if (!removeUserFromTaskDto.assignee_type && query.assignee_type) {
      removeUserFromTaskDto.assignee_type = query.assignee_type;
      this.logger.log(`Extracted assignee_type from query params: ${removeUserFromTaskDto.assignee_type}`);
    }

    this.logger.log(`Final removeUserFromTask data: ${JSON.stringify(removeUserFromTaskDto)}`);

    return this.workflowService.removeUserFromTask(removeUserFromTaskDto);
  }

  /**
   * Check if a user is assigned to a specific task
   * @param checkUserTaskAssignmentDto Assignment check data
   */
  @Get('check-user-task-assignment')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async checkUserTaskAssignment(@Query() checkUserTaskAssignmentDto: CheckUserTaskAssignmentDto) {
    return this.workflowService.checkUserTaskAssignment(checkUserTaskAssignmentDto);
  }

  @Get('task-assigned-users')
  @ResponseMessage(MESSAGES.APPLICATION_READY)
  async getTaskAssignedUsers(@Query() query: any) {
    this.logger.log(`Controller received getTaskAssignedUsers query: ${JSON.stringify(query)}`);

    // Handle different possible parameter names
    const taskId = query.task_id || query.taskId || query.task_execution_id;
    const workflowExecutionId = query.workflow_execution_id || query.workflow_execution_id || query.work_flow_id || query.workflow_id;

    if (!taskId) {
      this.logger.error(`task_id is missing. Available query parameters: ${JSON.stringify(query)}`);
      throw new Error('task_id is required. Please provide task_id, taskId, or task_execution_id');
    }

    if (!workflowExecutionId) {
      this.logger.error(`workflow_execution_id is missing. Available query parameters: ${JSON.stringify(query)}`);
      throw new Error('workflow_execution_id is required. Please provide workflow_execution_id, work_flow_id, or workflow_id');
    }

    const normalizedQuery = {
      task_id: taskId,
      workflow_execution_id: workflowExecutionId
    };

    this.logger.log(`Normalized query parameters: ${JSON.stringify(normalizedQuery)}`);

    return this.workflowService.getTaskAssignedUsers(normalizedQuery);
  }
}
