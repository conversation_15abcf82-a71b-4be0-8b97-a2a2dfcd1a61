import 'reflect-metadata'; // Make sure this is imported if using TypeORM decorators
import { DataSource } from 'typeorm';
import { seedTenants } from './tenant.seed';
import { seedImapConfigs, seedImapConfigsWithEncryption } from './imap-config.seed';

// Initialize your DataSource
const dataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT),
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  entities: ['src/**/*.entity{.ts,.js}'],
  synchronize: false, // Always keep this false in production
  logging: true, // Enable to check SQL logs
});

// Run the seed
export const runSeeds = async (): Promise<void> => {
  try {
    await dataSource.initialize(); // Initialize the data source
    console.log('DataSource initialized successfully');

    await seedTenants(dataSource); // Pass the dataSource to the seed function
    
    // Seed IMAP configurations
    console.log('Seeding IMAP configurations...');
    if (process.env.NODE_ENV === 'development') {
      // Use unencrypted configs for development
      await seedImapConfigs(dataSource);
    } else {
      // Use encrypted configs for production
      await seedImapConfigsWithEncryption(dataSource, process.env.ENCRYPTION_KEY);
    }

    console.log('All seeds completed successfully');
  } catch (error) {
    console.error('Error during DataSource initialization or seeding:', error);
  } finally {
    await dataSource.destroy(); // Close the connection
  }
};

// Run the seeds
runSeeds();
