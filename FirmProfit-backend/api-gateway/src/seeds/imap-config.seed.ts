import { DataSource } from 'typeorm';
import { TenantConfig } from '../database/entities/tenant-config.entity';

export const seedImapConfigs = async (
  dataSource: DataSource,
): Promise<void> => {
  const tenantConfigRepository = dataSource.getRepository(TenantConfig);

  // Check if IMAP configurations already exist
  const count = await tenantConfigRepository.count({
    where: { key: 'imap_config' },
  });

  if (count > 0) {
    console.log('IMAP configurations already seeded');
    return;
  }

  // Sample IMAP configurations for different tenants
  const imapConfigs = [
    {
      tenant_id: '11111111-1111-1111-1111-111111111111',
      key: 'imap_config',
      config: {
        host: 'imap.gmail.com',
        port: 993,
        username: '<EMAIL>',
        password: 'demo_password',
        // queue_url:
        //   'https://sqs.us-east-1.amazonaws.com/513981683228/courtNotice',
        tls: true,
        tlsOptions: {
          servername: 'imap.gmail.com',
          rejectUnauthorized: false,
        },
        keepalive: true,
        autotls: 'always',
        maxReconnectAttempts: 5,
        pollingInterval: 30000,
        enableDuplicateChecking: true,
        enableMarkAsSeen: true,
      },
    },
    {
      tenant_id: '22222222-2222-2222-2222-222222222222',
      key: 'imap_config',
      // queue_url: 'https://sqs.us-east-1.amazonaws.com/513981683228/courtNotice',
      config: {
        host: 'imap.gmail.com',
        port: 993,
        username: '<EMAIL>',
        password: 'demo_password2',
        tls: true,
        tlsOptions: {
          servername: 'imap.gmail.com',
          rejectUnauthorized: false,
        },
        keepalive: true,
        autotls: 'always',
        maxReconnectAttempts: 3,
        pollingInterval: 45000,
        enableDuplicateChecking: true,
        enableMarkAsSeen: false, // Different configuration for testing
      },
    },
    {
      tenant_id: 'stephen-tenant-id',
      key: 'imap_config',
      // queue_url: 'https://sqs.us-east-1.amazonaws.com/513981683228/courtNotice',

      config: {
        host: 'imap.gmail.com',
        port: 993,
        username: '<EMAIL>',
        password: 'stephen_password',
        tls: true,
        tlsOptions: {
          servername: 'imap.gmail.com',
          rejectUnauthorized: false,
        },
        keepalive: true,
        autotls: 'always',
        maxReconnectAttempts: 5,
        pollingInterval: 30000,
        enableDuplicateChecking: true,
        enableMarkAsSeen: true,
      },
    },
    {
      tenant_id: 'moskovich-tenant-id',
      key: 'imap_config',
      // queue_url: 'https://sqs.us-east-1.amazonaws.com/513981683228/courtNotice',

      config: {
        host: 'imap.gmail.com',
        port: 993,
        username: '<EMAIL>',
        password: 'moskovich_password',
        tls: true,
        tlsOptions: {
          servername: 'imap.gmail.com',
          rejectUnauthorized: false,
        },
        keepalive: true,
        autotls: 'always',
        maxReconnectAttempts: 5,
        pollingInterval: 30000,
        enableDuplicateChecking: true,
        enableMarkAsSeen: true,
      },
    },
  ];

  // Insert IMAP configurations
  await tenantConfigRepository.save(imapConfigs);
  console.log('IMAP configurations seeded successfully');
};

export const seedImapConfigsWithEncryption = async (
  dataSource: DataSource,
  encryptionKey: string = 'default-encryption-key-change-in-production',
): Promise<void> => {
  const crypto = require('crypto');
  const algorithm = 'aes-256-cbc';

  const encrypt = (text: string): string => {
    try {
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipher(algorithm, encryptionKey);
      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      return iv.toString('hex') + ':' + encrypted;
    } catch (error) {
      console.error('Error encrypting data:', error);
      return text; // Return original text if encryption fails
    }
  };

  const tenantConfigRepository = dataSource.getRepository(TenantConfig);

  // Check if IMAP configurations already exist
  const count = await tenantConfigRepository.count({
    where: { key: 'imap_config' },
  });

  if (count > 0) {
    console.log('Encrypted IMAP configurations already seeded');
    return;
  }

  // Sample IMAP configurations with encrypted sensitive data
  const encryptedImapConfigs = [
    {
      tenant_id: '11111111-1111-1111-1111-111111111111',
      // queue_url:
      //     'https://sqs.us-east-1.amazonaws.com/513981683228/courtNotice',
      key: 'imap_config',
      config: {
        host: 'imap.gmail.com',
        port: 993,
        username: encrypt('<EMAIL>'),
        password: encrypt('demo_password'),
        tls: true,
        tlsOptions: {
          servername: 'imap.gmail.com',
          rejectUnauthorized: false,
        },
        keepalive: true,
        autotls: 'always',
        maxReconnectAttempts: 5,
        pollingInterval: 30000,
        enableDuplicateChecking: true,
        enableMarkAsSeen: true,
      },
    },
    {
      tenant_id: '22222222-2222-2222-2222-222222222222',
        //  queue_url:
        //   'https://sqs.us-east-1.amazonaws.com/513981683228/courtNotice',
      key: 'imap_config',
      config: {
        host: 'imap.gmail.com',
        port: 993,
        username: encrypt('<EMAIL>'),
        password: encrypt('demo_password2'),
        tls: true,
        tlsOptions: {
          servername: 'imap.gmail.com',
          rejectUnauthorized: false,
        },
        keepalive: true,
        autotls: 'always',
        maxReconnectAttempts: 3,
        pollingInterval: 45000,
        enableDuplicateChecking: true,
        enableMarkAsSeen: false,
      },
    },
  ];

  // Insert encrypted IMAP configurations
  await tenantConfigRepository.save(encryptedImapConfigs);
  console.log('Encrypted IMAP configurations seeded successfully');
};
