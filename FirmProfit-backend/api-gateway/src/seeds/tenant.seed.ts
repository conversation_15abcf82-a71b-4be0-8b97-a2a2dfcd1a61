import { DataSource } from 'typeorm';
import { Tenant } from '@shared/database';

export const seedTenants = async (dataSource: DataSource): Promise<void> => {
  const tenantRepository = dataSource.getRepository(Tenant);

  // Check if tenants already exist
  const count = await tenantRepository.count();
  if (count > 0) {
    console.log('Tenants already seeded');
    return;
  }

  // Sample tenants
  const tenants = [
    {
      name: 'stephen',
      schema: 'stephen_schema',
      logo: 'https://example.com/logo1.png',
      isActive: true,
      description: 'This is the first tenant.',
      domain: 'stephen.firmprofitai.com',
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      name: 'moskovich',
      schema: 'moskovich_schema',
      logo: 'https://example.com/logo2.png',
      isActive: true,
      description: 'This is the second tenant.',
      domain: 'moskovich.firmprofitai.com',
      created_at: new Date(),
      updated_at: new Date(),
    },
  ];

  // Insert tenants
  await tenantRepository.save(tenants);
  console.log('Tenants seeded successfully');
};
