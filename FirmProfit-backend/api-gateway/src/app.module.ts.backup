import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { TenantModule } from './tenant/tenant.module';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { TransformInterceptor } from './auth/interceptors/transform.interceptor';
import { GrpcClientModule } from './grpc-client/grpc-client.module';
import { WorkflowModule } from './workflow/workflow.module';
import { MyCaseModule } from './mycase/mycase.module';
import { CronModule } from './cron/cron.module';
import { EmailModule } from './email/email.module';
import { SharedDatabaseModule } from '@shared/database';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    // Use the shared database module instead of local database configuration
    SharedDatabaseModule.forRoot(),
    GrpcClientModule,
    AuthModule,
    WorkflowModule,
    TenantModule,
    MyCaseModule,
    CronModule,
    EmailModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_INTERCEPTOR,
      useClass: TransformInterceptor,
    },
  ],
})
export class AppModule {}
