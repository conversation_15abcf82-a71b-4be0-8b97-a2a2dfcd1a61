import { <PERSON>, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { EmailService, EmailMessage, EmailAck } from './email.service';

@Controller()
export class EmailController {
  private readonly logger = new Logger(EmailController.name);

  constructor(private readonly emailService: EmailService) {}

  @GrpcMethod('EmailService', 'HandleNewEmail')
  async handleNewEmail(data: EmailMessage): Promise<EmailAck> {
    this.logger.log(`gRPC HandleNewEmail called with subject: ${data.subject}`);
    return this.emailService.handleNewEmail(data);
  }
} 