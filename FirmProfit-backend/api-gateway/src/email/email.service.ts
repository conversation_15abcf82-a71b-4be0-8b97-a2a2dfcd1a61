import { Injectable, Logger } from '@nestjs/common';

export interface EmailMessage {
  subject: string;
  from: string;
  body: string;
  receivedAt: string;
}

export interface EmailAck {
  success: boolean;
  message: string;
}

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  async handleNewEmail(data: EmailMessage): Promise<EmailAck> {
    try {
      this.logger.log(`Received new email from connectors service`);
      this.logger.log(`Subject: ${data.subject}`);
      this.logger.log(`From: ${data.from}`);
      this.logger.log(`Received at: ${data.receivedAt}`);
      
      // TODO: Process the email data here
      // - Store in database
      // - Trigger workflows
      // - Send notifications
      // - etc.
      
      // For now, just log the email content
      this.logger.log(`Email body preview: ${data.body.substring(0, 100)}...`);
      
      return {
        success: true,
        message: '<PERSON><PERSON> processed successfully',
      };
    } catch (error) {
      this.logger.error('Error processing email:', error);
      return {
        success: false,
        message: `Error processing email: ${error.message}`,
      };
    }
  }
} 