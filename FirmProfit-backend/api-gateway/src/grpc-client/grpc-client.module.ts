import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { GrpcClientService } from './/grpc-client.service';
import { join } from 'path';

@Module({
  imports: [
    ConfigModule,
    ClientsModule.registerAsync([
      {
        name: 'SHARED_SERVICE',
        imports: [ConfigModule],
        useFactory: () => {
          const sharedServiceHost = process.env.SHARED_SERVICE_HOST;
          const sharedServicePort = process.env.SHARED_SERVICE_PORT;

          console.log(
            'Shared Service gRPC Config:',
            process.env.SHARED_SERVICE_PORT,
          );
          console.log('Host:', sharedServiceHost);
          console.log('Port:', sharedServicePort);

          return {
            transport: Transport.GRPC,
            options: {
              package: ['auth', 'user', 'workflowapi', 'workflowshared'],
              protoPath: [
                join(__dirname, '../../proto/auth.proto'),
                join(__dirname, '../../proto/user.proto'),
                join(__dirname, '../../proto/workflow-api.proto'),
                join(__dirname, '../../proto/workflow-shared.proto'),
              ],
              url: `${sharedServiceHost}:${sharedServicePort}`,
              loader: {
                keepCase: true,
                longs: String,
                enums: String,
                defaults: true,
                oneofs: true,
              },
              maxSendMessageLength: 1024 * 1024 * 50, // 50MB
              maxReceiveMessageLength: 1024 * 1024 * 50, // 50MB
            },
          };
        },
        inject: [ConfigService],
      },
    ]),
  ],
  providers: [GrpcClientService],
  exports: [GrpcClientService],
})
export class GrpcClientModule {}
