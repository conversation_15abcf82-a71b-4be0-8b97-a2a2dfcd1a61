import { Injectable, Inject, OnModuleInit } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { Observable, lastValueFrom } from 'rxjs';
import { Empty } from 'google-protobuf/google/protobuf/empty_pb';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

interface AuthServiceClient {
  register(data: any): Observable<any>;
  login(data: any): Observable<any>;
  enableMfa(data: any): Observable<any>;
  verifyMfa(data: any): Observable<any>;
  validateMfa(data: any): Observable<any>;
  validateToken(data: any): Observable<any>;
  resetPassword(data: any): Observable<any>;
}

interface UserServiceClient {
  signUp(data: any): Observable<any>;
  signIn(data: any): Observable<any>;
  verifyUser(data: any): Observable<any>;
  validateUserToken(data: any): Observable<any>;
  getUserById(data: any): Observable<any>;
  enableMfa(data: any): Observable<any>;
  verifyMfa(data: any): Observable<any>;
  validateMfa(data: any): Observable<any>;
  handleMfa(data: any): Observable<any>;
  resetPassword(data: any): Observable<any>;
  checkAuthTokenInDB(data: any): Observable<any>;
}

interface WorkFlowApiServiceClient {
  myWorkFlow(data: any): Observable<any>;
  workFlowRender(data: any): Observable<any>;
  handleOAuthCallback(data: Empty): Observable<any>;
}

interface WorkFlowSharedServiceClient {
  myWorkflows(data: any): Observable<any>;
  workFlowRender(data: any): Observable<any>;
  handleOAuthCallbackApi(data: Empty): Observable<any>;
  updateWorkflow(data: any): Observable<any>;
  listCourtNotice(data: any): Observable<any>;
  updateWorkflowStatus(data: any): Observable<any>;
  listCounty(data: Empty): Observable<any>;
  listCourtLocation(data: Empty): Observable<any>;
  listUsers(data: any): Observable<any>;
  GetAllUsersAndRoles(data: any): Observable<any>;
  listCourtNoticeType(data: Empty): Observable<any>;
  listMatter(data: Empty): Observable<any>;
  searchMatter(data: any): Observable<any>;
  getNextTaskId(data: any): Observable<any>;
  createDemoWorkflow(data: any): Observable<any>;
  updateWorkflowEndDate(data: any): Observable<any>;
  getPresignedUrl(data: any): Observable<any>;
  CleanupUploadedFiles(data: any): Observable<any>;
  ArchiveWorkFlow(data: any): Observable<any>;
  getAppointmentEvents(data: any): Observable<any>;
  getEventById(data: any): Observable<any>;
  syncLocationsFromMyCase(data: any): Observable<any>;
  getLocationList(data: Empty): Observable<any>;
  getLocationById(data: any): Observable<any>;
  updateWorkflowRetryInfo(data: any): Observable<any>;
  checkClientAvailability(data: {
    client_id: string;
    tenant_id?: string;
    access_token?: string;
  }): Observable<any>;
  triggerManualMyCaseSync(data: { access_token?: string }): Observable<any>;
  getMyCaseSyncStatus(data: Empty): Observable<any>;
  saveMyCaseWebhookData(data: any): Observable<any>;
  testSuccess(data: any): Observable<any>;
  assignToTask(data: any): Observable<any>;
  getTaskAssignments(data: any): Observable<any>;
  removeAssignment(data: any): Observable<any>;
  assignUserToTask(data: any): Observable<any>;
  removeUserFromTask(data: any): Observable<any>;
  checkUserTaskAssignment(data: any): Observable<any>;
  getTaskAssignedUsers(data: any): Observable<any>;
}

interface AssignUserToTaskRequest {
  task_execution_id: string;
  user_id: string;
  assigned_by: string;
  work_flow_id: string;
  assignee_type?: string;
  assignToTask(data: any): Observable<any>;
  getTaskAssignments(data: any): Observable<any>;
  removeAssignment(data: any): Observable<any>;
  assignUserToTask(data: any): Observable<any>;
  removeUserFromTask(data: any): Observable<any>;
  checkUserTaskAssignment(data: any): Observable<any>;
  getTaskAssignedUsers(data: any): Observable<any>;
}

export class ArchiveWorkFlowDto {
  @IsNotEmpty()
  @IsString()
  work_flow_execution_id: string;

  @IsOptional()
  @IsString()
  archive_at?: string;

  @IsOptional()
  @IsString()
  archive_by?: string;

  @IsOptional()
  @IsString()
  work_child_flow_execution_id?: string;
}

@Injectable()
export class GrpcClientService implements OnModuleInit {
  private authService: AuthServiceClient;
  private userService: UserServiceClient;
  private workFlowApiService: WorkFlowApiServiceClient;
  private workFlowSharedService: WorkFlowSharedServiceClient;

  constructor(@Inject('SHARED_SERVICE') private client: ClientGrpc) { }

  onModuleInit() {
    this.authService = this.client.getService<AuthServiceClient>('AuthService');
    this.userService = this.client.getService<UserServiceClient>('UserService');
    this.workFlowApiService =
      this.client.getService<WorkFlowApiServiceClient>('WorkflowService');
    this.workFlowSharedService =
      this.client.getService<WorkFlowSharedServiceClient>(
        'WorkflowSharedService',
      );
  }

  // Auth Service Methods
  async register(data: any): Promise<any> {
    return lastValueFrom(this.authService.register(data));
  }

  async login(data: any): Promise<any> {
    return lastValueFrom(this.authService.login(data));
  }

  async enableMfa(data: any): Promise<any> {
    return lastValueFrom(this.authService.enableMfa(data));
  }

  async verifyMfa(data: any): Promise<any> {
    return lastValueFrom(this.authService.verifyMfa(data));
  }

  async validateMfa(data: any): Promise<any> {
    return lastValueFrom(this.authService.validateMfa(data));
  }

  async validateToken(data: any): Promise<any> {
    return lastValueFrom(this.authService.validateToken(data));
  }

  async resetPassword(data: any): Promise<any> {
    return lastValueFrom(this.authService.resetPassword(data));
  }

  // User Service Methods
  async signUp(data: any): Promise<any> {
    return lastValueFrom(this.userService.signUp(data));
  }

  async signIn(data: any): Promise<any> {
    return lastValueFrom(this.userService.signIn(data));
  }

  async verifyUser(data: any): Promise<any> {
    return lastValueFrom(this.userService.verifyUser(data));
  }

  async validateUserToken(data: any): Promise<any> {
    return lastValueFrom(this.userService.validateUserToken(data));
  }

  async getUserById(data: any): Promise<any> {
    return lastValueFrom(this.userService.getUserById(data));
  }

  async userEnableMfa(data: any): Promise<any> {
    return lastValueFrom(this.userService.enableMfa(data));
  }

  async userVerifyMfa(data: any): Promise<any> {
    return lastValueFrom(this.userService.verifyMfa(data));
  }

  async userValidateMfa(data: any): Promise<any> {
    return lastValueFrom(this.userService.validateMfa(data));
  }

  async handleMfa(data: any): Promise<any> {
    return lastValueFrom(this.userService.handleMfa(data));
  }

  async userResetPassword(data: any): Promise<any> {
    return lastValueFrom(this.userService.resetPassword(data));
  }

  async checkAuthTokenInDB(data: any): Promise<any> {
    return lastValueFrom(this.userService.checkAuthTokenInDB(data));
  }

  async myWorkFLow(data: any): Promise<any> {
    return lastValueFrom(this.workFlowApiService.myWorkFlow(data));
  }

  async myWorkflows(data: any): Promise<any> {
    return lastValueFrom(this.workFlowSharedService.myWorkflows(data));
  }

  async workFlowRenderApi(data: any): Promise<any> {
    try {
      return lastValueFrom(this.workFlowApiService.workFlowRender(data));
    } catch (error) {
      throw error;
    }
  }

  async workFlowRenderShared(data: any): Promise<any> {
    return lastValueFrom(this.workFlowSharedService.workFlowRender(data));
  }

  async handleOAuthCallbackShared(): Promise<any> {
    return lastValueFrom(
      this.workFlowSharedService.handleOAuthCallbackApi(new Empty()),
    );
  }

  async handleOAuthCallbackApi(): Promise<any> {
    return lastValueFrom(
      this.workFlowApiService.handleOAuthCallback(new Empty()),
    );
  }

  async updateWorkflow(data: any): Promise<any> {
    return lastValueFrom(this.workFlowSharedService.updateWorkflow(data));
  }

  async getCourtNoticeList(data: any) {
    return lastValueFrom(this.workFlowSharedService.listCourtNotice(data));
  }

  async updateWorkflowStatus(data: any) {
    return lastValueFrom(this.workFlowSharedService.updateWorkflowStatus(data));
  }

  async getCountyList() {
    return lastValueFrom(this.workFlowSharedService.listCounty(new Empty()));
  }

  async getCourtLocationList() {
    return lastValueFrom(
      this.workFlowSharedService.listCourtLocation(new Empty()),
    );
  }

  async getUserList(data: any) {
    return lastValueFrom(this.workFlowSharedService.listUsers(data));
  }

  async getAllUsersAndRoles(data: any): Promise<any> {
    return lastValueFrom(this.workFlowSharedService.GetAllUsersAndRoles(data));
  }

  async getCourtNoticeType() {
    return lastValueFrom(
      this.workFlowSharedService.listCourtNoticeType(new Empty()),
    );
  }

  async getMatterList() {
    return lastValueFrom(this.workFlowSharedService.listMatter(new Empty()));
  }

  /**
   * Search matters by name
   * @param search Search term to filter by
   * @returns Filtered list of matters
   */
  async searchMatterList(search: string) {
    return lastValueFrom(this.workFlowSharedService.searchMatter({ search }));
  }

  async getNextTaskId(data: any): Promise<any> {
    return lastValueFrom(this.workFlowSharedService.getNextTaskId(data));
  }

  async createDemoWorkflow(data: any): Promise<any> {
    return lastValueFrom(this.workFlowSharedService.createDemoWorkflow(data));
  }

  /**
   * Update the end date of a workflow task
   * @param data Object containing workflow_id, task_id, and end_date
   * @returns Result of the update operation
   */
  async updateWorkflowEndDate(data: any) {
    return lastValueFrom(
      this.workFlowSharedService.updateWorkflowEndDate(data),
    );
  }

  async getPresignedUrl(data: any) {
    return lastValueFrom(this.workFlowSharedService.getPresignedUrl(data));
  }

  async cleanupUploadedFiles(data: any) {
    return lastValueFrom(this.workFlowSharedService.CleanupUploadedFiles(data));
  }

  async archiveWorkFlow(data: any) {
    return lastValueFrom(this.workFlowSharedService.ArchiveWorkFlow(data));
  }

  getAppointmentEvents(data: any) {
    return lastValueFrom(this.workFlowSharedService.getAppointmentEvents(data));
  }

  getEventById(data: any) {
    return lastValueFrom(this.workFlowSharedService.getEventById(data));
  }

  // Location-related methods
  async syncLocationsFromMyCase(data: any): Promise<any> {
    return lastValueFrom(
      this.workFlowSharedService.syncLocationsFromMyCase(data),
    );
  }

  async getLocationList(): Promise<any> {
    return lastValueFrom(
      this.workFlowSharedService.getLocationList(new Empty()),
    );
  }

  async getLocationById(data: any): Promise<any> {
    return lastValueFrom(this.workFlowSharedService.getLocationById(data));
  }

  async updateWorkflowRetryInfo(data: any): Promise<any> {
    return lastValueFrom(
      this.workFlowSharedService.updateWorkflowRetryInfo(data),
    );
  }

  async checkClientAvailability(data: {
    client_id: string;
    tenant_id?: string;
    access_token?: string;
  }): Promise<any> {
    return lastValueFrom(
      this.workFlowSharedService.checkClientAvailability(data),
    );
  }

  // MyCase sync methods
  async triggerManualMyCaseSync(data: { access_token?: string }): Promise<any> {
    return lastValueFrom(
      this.workFlowSharedService.triggerManualMyCaseSync(data),
    );
  }

  async getMyCaseSyncStatus(): Promise<any> {
    return lastValueFrom(
      this.workFlowSharedService.getMyCaseSyncStatus(new Empty()),
    );
  }

  async saveMyCaseWebhookData(data: any): Promise<any> {
    return lastValueFrom(
      this.workFlowSharedService.saveMyCaseWebhookData(data),
    );
  }

  async testSuccess(req: any): Promise<any> {
    const requestData = {
      params_json: JSON.stringify(req || {}),
    };
    return lastValueFrom(this.workFlowSharedService.testSuccess(requestData));
  }

  async assignToTask(data: any): Promise<any> {
    return lastValueFrom(this.workFlowSharedService.assignToTask(data));
  }

  async getTaskAssignments(data: any): Promise<any> {
    return lastValueFrom(this.workFlowSharedService.getTaskAssignments(data));
  }

  async removeAssignment(data: any): Promise<any> {
    return lastValueFrom(this.workFlowSharedService.removeAssignment(data));
  }

  async assignUserToTask(data: any): Promise<any> {
    return lastValueFrom(this.workFlowSharedService.assignUserToTask(data));
  }

  async removeUserFromTask(data: any): Promise<any> {
    return lastValueFrom(this.workFlowSharedService.removeUserFromTask(data));
  }

  async checkUserTaskAssignment(data: any): Promise<any> {
    return lastValueFrom(this.workFlowSharedService.checkUserTaskAssignment(data));
  }

  async getTaskAssignedUsers(data: any): Promise<any> {
    return lastValueFrom(this.workFlowSharedService.getTaskAssignedUsers(data));
  }
}
