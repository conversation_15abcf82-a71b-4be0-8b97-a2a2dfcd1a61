// (global as any).crypto = require('crypto'); 
import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { AppModule } from './app.module';
import { HttpExceptionFilter } from './exceptions/http-exception.filter';
import { GrpcClientExceptionFilter } from './exceptions/grpc-client-exception.filter';
import { TenantService } from './tenant/tenant.service';
import { join } from 'path';

async function bootstrap() {
  // Create HTTP application
  const app = await NestFactory.create(AppModule);
  
  // Initialize tenant schemas after the app is created
  const tenantService = app.get(TenantService);
  await tenantService.createTenantSchemas();
  app.enableCors();
  app.useGlobalFilters(
    new HttpExceptionFilter(),
    new GrpcClientExceptionFilter(),
  );
  
  // Create gRPC microservice for email handling
  const grpcPort = process.env.GRPC_EMAIL_PORT || 3004;
  const grpcApp = await NestFactory.createMicroservice<MicroserviceOptions>(
    AppModule,
    {
      transport: Transport.GRPC,
      options: {
        package: 'email',
        protoPath: join(__dirname, '../proto/email.proto'),
        url: `0.0.0.0:${grpcPort}`,
        loader: {
          keepCase: true,
          longs: String,
          enums: String,
          defaults: true,
          oneofs: true,
        },
      },
    },
  );

  // Start both servers
  const httpPort = process.env.PORT || 3000;
  await app.listen(httpPort, '0.0.0.0');
  await grpcApp.listen();
  
  console.log(`API Gateway HTTP is running on: 0.0.0.0:${httpPort}`);
  console.log(`API Gateway gRPC (Email) is running on: 0.0.0.0:${grpcPort}`);
}
bootstrap();
