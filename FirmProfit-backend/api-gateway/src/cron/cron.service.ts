import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ConfigService } from '@nestjs/config';
import { MyCaseSyncService } from '../mycase/services/mycase-sync.service';

@Injectable()
export class CronService {
  private readonly logger = new Logger(CronService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly myCaseSyncService: MyCaseSyncService,
  ) {}

  // Run every Saturday at 2:00 AM
  @Cron('1 12 * * *', {
    name: 'weekly-mycase-sync',
    timeZone: 'America/New_York', // Adjust timezone as needed
  })
  async weeklyMyCaseSync() {
    this.logger.log('Starting weekly MyCase sync cron job...');

    try {
      // Default tenant ID - you can modify this or make it configurable
      const tenantId = '1';

      this.logger.log(`Calling MyCase sync for tenant ${tenantId}...`);

      // Directly call the service method
      const result = await this.myCaseSyncService.triggerManualSync(tenantId);

      if (result.success) {
        this.logger.log(
          `Weekly MyCase sync completed successfully for tenant ${tenantId}: ${result.message}`,
        );
      } else {
        this.logger.error(
          `Weekly MyCase sync failed for tenant ${tenantId}: ${result.message}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error during weekly MyCase sync cron job: ${error.message}`,
      );
    }
  }

  // Optional: Add a method to sync multiple tenants if needed
  // This runs at 3:00 AM on Saturday (1 hour after the single tenant sync)
  // Comment out if you only need single tenant sync
  /*
  @Cron('0 3 * * 6', {
    name: 'weekly-mycase-sync-all-tenants',
    timeZone: 'America/New_York',
  })
  async weeklyMyCaseSyncAllTenants() {
    this.logger.log('Starting weekly MyCase sync for all tenants...');

    try {
      // List of tenant IDs to sync - you can make this dynamic by fetching from database
      const tenantIds = ['1', '2', '3']; // Add more tenant IDs as needed

      for (const tenantId of tenantIds) {
        try {
          this.logger.log(`Syncing tenant ${tenantId}...`);

          // Directly call the service method
          const result = await this.myCaseSyncService.triggerManualSync(tenantId);

          if (result.success) {
            this.logger.log(
              `Sync completed successfully for tenant ${tenantId}: ${result.message}`,
            );
          } else {
            this.logger.error(
              `Sync failed for tenant ${tenantId}: ${result.message}`,
            );
          }

          // Add a small delay between tenant syncs to avoid overwhelming the system
          await new Promise((resolve) => setTimeout(resolve, 5000)); // 5 second delay
        } catch (tenantError) {
          this.logger.error(
            `Error syncing tenant ${tenantId}: ${tenantError.message}`,
          );
          // Continue with next tenant even if one fails
        }
      }

      this.logger.log('Completed weekly MyCase sync for all tenants');
    } catch (error) {
      this.logger.error(
        `Error during weekly MyCase sync for all tenants: ${error.message}`,
      );
    }
  }
  */

  // Optional: Add a health check cron job
  @Cron(CronExpression.EVERY_HOUR)
  async healthCheck() {
    this.logger.log('Performing hourly health check...');

    try {
      // Directly call the service method
      const result = await this.myCaseSyncService.healthCheck();

      this.logger.log('Health check completed successfully');
      return result;
    } catch (error) {
      this.logger.error(`Health check failed: ${error.message}`);
    }
  }
}
