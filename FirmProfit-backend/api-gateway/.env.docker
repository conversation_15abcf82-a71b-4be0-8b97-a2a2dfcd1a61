# App
PORT=3000
 
# Database
DB_HOST=postgres
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_NAME=firmprofit
DB_SYNC=true
DB_LOGGING=true
 
# API Gateway Configuration
PORT=3000
 
# JWT Configuration
JWT_SECRET=your_super_secret_key_should_be_changed_in_production
JWT_EXPIRES_IN=1d
 
# Shared Service Configuration
SHARED_SERVICE_HOST=shared-service
SHARED_SERVICE_PORT=3001
GRPC_URL=shared-service:3001
JWT_SECRET=super_secret_key 