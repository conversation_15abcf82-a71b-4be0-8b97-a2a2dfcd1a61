syntax = "proto3";

package workflowshared;

import "google/protobuf/timestamp.proto";
import "google/protobuf/any.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/struct.proto";  // For google.protobuf.Value

service WorkflowSharedService {
  rpc MyWorkflows              (GetWorkflowRequest)          returns (WorkflowResponse);
  rpc WorkFlowRender           (GetWorkflowRenderDto)        returns (WorkflowRenderResponse);
  rpc handleOAuthCallbackApi   (google.protobuf.Empty)       returns (handleOAuthCallbackResponse);
  rpc UpdateWorkflow           (UpdateWorkflowRequest)       returns (UpdateWorkflowResponse);
  rpc ListCourtNotice          (ListCourtNoticeRequest)       returns (ListCourtNoticeResponse);
  rpc UpdateWorkflowStatus     (UpdateWorkflowStatusRequest)  returns (UpdateWorkflowStatusResponse);
  rpc ListCounty               (google.protobuf.Empty)            returns (ListCountyResponse);
  rpc ListCourtLocation        (google.protobuf.Empty)            returns (ListCourtLocationResponse);
  rpc ListUsers                (ListUsersRequest)                 returns (ListUsersResponse);
  rpc GetAllUsersAndRoles      (GetAllUsersAndRolesRequest)       returns (GetAllUsersAndRolesResponse);
  rpc listCourtNoticeType      (google.protobuf.Empty)            returns (listCourtNoticeTypeResponse);
  rpc ListMatter               (google.protobuf.Empty)            returns (ListMatterResponse);
  rpc SearchMatter             (SearchMatterRequest)              returns (ListMatterResponse);
  rpc GetNextTaskId            (GetNextTaskIdRequest)            returns (GetNextTaskIdResponse);
  rpc CreateDemoWorkflow       (CreateDemoWorkflowRequest)      returns (CreateDemoWorkflowResponse);
  rpc UpdateWorkflowEndDate    (UpdateWorkflowEndDateRequest)   returns (UpdateWorkflowEndDateResponse);
  rpc GetPresignedUrl          (GetPresignedUrlRequest)   returns (GetPresignedUrlResponse);
  rpc CleanupUploadedFiles     (CleanupFilesRequest)   returns (CleanupFilesResponse);
  rpc ArchiveWorkFlow          (ArchiveWorkFlowRequest)   returns (ArchiveWorkFlowResponse);
  rpc GetAppointmentEvents (GetAppointmentEventsRequest) returns (GetAppointmentEventsResponse);
  rpc GetEventById             (GetEventByIdRequest)             returns (GetEventByIdResponse);
  rpc SyncLocationsFromMyCase (SyncLocationsFromMyCaseRequest) returns (SyncLocationsFromMyCaseResponse);
  rpc GetLocationList        (google.protobuf.Empty)   returns (GetLocationListResponse);
  rpc GetLocationById        (GetLocationByIdRequest)  returns (GetLocationByIdResponse);
  rpc UpdateWorkflowRetryInfo (UpdateWorkflowRetryInfoRequest) returns (UpdateWorkflowRetryInfoResponse);
  rpc CheckClientAvailability (CheckClientAvailabilityRequest) returns (CheckClientAvailabilityResponse);
  rpc TriggerManualMyCaseSync (TriggerManualMyCaseSyncRequest) returns (TriggerManualMyCaseSyncResponse);
  rpc GetMyCaseSyncStatus     (google.protobuf.Empty) returns (GetMyCaseSyncStatusResponse);
  rpc SaveMyCaseWebhookData    (SaveMyCaseWebhookDataRequest)  returns (SaveMyCaseWebhookDataResponse);
  rpc TestSuccess              (TestSuccessRequest)            returns (TestSuccessResponse);
  
  rpc GetAvailableAssignees    (GetAssigneesRequest)            returns (GetAssigneesResponse);
  rpc GetAssigneesByCategory   (GetAssigneesRequest)            returns (GetAssigneesResponse);
  rpc GetRoleMembers           (GetRoleMembersRequest)          returns (GetRoleMembersResponse);
  rpc AssignToTask             (AssignToTaskRequest)            returns (AssignToTaskResponse);
  rpc GetTaskAssignments       (GetTaskAssignmentsRequest)      returns (GetTaskAssignmentsResponse);
  rpc RemoveAssignment         (RemoveAssignmentRequest)        returns (RemoveAssignmentResponse);
  rpc AssignUserToTask         (AssignUserToTaskRequest)        returns (AssignUserToTaskResponse);
  rpc RemoveUserFromTask       (RemoveUserFromTaskRequest)      returns (RemoveUserFromTaskResponse);
  rpc CheckUserTaskAssignment  (CheckUserTaskAssignmentRequest) returns (CheckUserTaskAssignmentResponse);
  rpc GetTaskAssignedUsers     (GetTaskAssignedUsersRequest)    returns (GetTaskAssignedUsersResponse);
}

message GetPresignedUrlRequest {
  string key = 1;
  string operation = 2;
  string contentType = 3;

}


message ArchiveWorkFlowRequest {
  string work_flow_execution_id = 1;
  string type = 2;
  string archive_at = 3;
  string archive_by = 4;
  string task_id = 5;
  string tenant_id = 6;
  string mycase_access_token = 7;
  int32 retry_count = 8;
  string last_error = 9;
  string work_child_flow_execution_id = 10;
}

message ArchiveWorkFlowResponse {
  bool success = 1;
  int32 mycase_archive_retry_count = 2;
}


// The response message containing the presigned URL.
message GetPresignedUrlResponse {
  string url = 1;
  string message = 2; // Optional message like "Application Ready"
}

message GetWorkflowRenderDto {
  string work_flow_id = 1;
}

message GetWorkflowRequest {
  int32 page  = 1;
  int32 limit = 2;
}

message ListUsersRequest {
  string search = 1;
  string client_id = 2;
  string user_group_id = 3;
}

message WorkflowRenderResponse {
  string data = 1;

}

message handleOAuthCallbackResponse {
  string result = 1;
}

message RenderWorkflow {
  string          id          = 1;
  string          name        = 2;
  string          description = 3;
  repeated RenderTask tasks   = 4;
}

message RenderTask {
  string          id                 = 1;
  string          name               = 2;
  string          description        = 3;
  string          icon               = 4;
  string          status             = 5;
  repeated FormField formFields      = 6;
  string          work_flow_id       = 7;
  bool            default_task       = 8;
  bool            selected_task      = 9;
  ConditionTask   condition_task     = 10;
  bool            is_reviewed       = 11;


  message ConditionTask {
    string value = 1;
    string id    = 2;
  }
}

message Condition {
  string            field      = 1;
  string            value      = 2;
  string            type       = 3;
  repeated Condition conditions = 4;
}

message EventItem {
  string id                 = 1;
  string caseNumber         = 2;
  string clientName         = 3;
  string description        = 4;
  string date               = 5;
  string startTime          = 6;
  string endTime            = 7;
  bool   isCompleted        = 8;
  string subject            = 9;
  string courtNoticeType    = 10;
  string appointmentAction  = 11;
  string charge             = 12;
  string exCountyOfArrest   = 13;
  string courtLocation      = 14;
  string optionalAttendees  = 15;
  string requiredAttendees  = 16;
  string clientAttendees    = 17;
  string meetingLocation    = 18;
  string startDate          = 19;
  string endDate            = 20;
  bool   allDay            = 21;
  string courtNoticeActions    = 22;
  string appointmentToReschedule    = 23;
   string county    = 24;
  string meetingLink    = 25;
  string clientAttendance    = 26;
  string court_notice_date    = 27;
  string phoneDetails    = 28;
  string meetingAddress    = 29;






}

message Matter {
  string        _id    = 1;
  string        name   = 2;
  repeated EventItem events = 3;
}

message Client {
  string         client_name = 1;
  repeated Matter matter      = 2;
}

message DerivedField {
  string id       = 1;
  string type     = 2;
  string label    = 3;
  int32  sequence = 4;
  repeated Client client = 5;
}

message FormField {
  string                          id                    = 1;
  string                          type                  = 2;
  string                          title                 = 3;
  bool                            required              = 4;
  bool                            dynamic_fields        = 5;
  repeated Field                  fields                = 6;
  bool                            selected_task         = 7;
  bool                            dynamic_selected_task = 8;
  Condition                       condition             = 9;
  string                          label                 = 10;
  int32                           sequence              = 11;
  repeated Client                 client                = 12;
  map<string, EventItems>         event                = 13;  // Map of matter IDs to list of events
  string                          api_end_point                 = 14;

}

message EventItems {
  repeated EventItem event_items = 1;
}

message FieldValue {
  string id    = 1;
  string value = 2;
  string _id   = 3;
}

message Field {
  string               type        = 1;
  string               placeholder = 2;
  string               id          = 3;
  string               label       = 4;
  repeated Option      options     = 5;
  string               _id         = 6;
  repeated FieldValue  value       = 7;
  map<string, string>  validation  = 8;
  bool                 required    = 9;
  string               api_end_point       = 10;
}

message Option {
  string value = 1;
  string text  = 2;
  string _id   = 3;
}

message WorkflowResponse {
  int32 total         = 1;
  int32 page          = 2;
  int32 limit         = 3;
  repeated Workflow workflows = 4;
}

message Workflow {
  string id             = 1;
  string work_flow_name = 2;
  string start_date     = 3;
  string end_date       = 4;
  int32  task_complete  = 5;
  string status         = 6;
  string last_activity  = 7;
  repeated AssignedUser assign = 8;
  string notes          = 9;
  string latest_task_id          = 10;
}

message AssignedUser {
  string id   = 1;
  string name = 2;
}

message WorkflowData {
  int32 page      = 1;
  int32 limit     = 2;
  bool  processed = 3;
  string timestamp = 4;
}

message FormValue {
  string id    = 1;
  string value = 2;
}

message Form {
  string form_component_id = 1;
  repeated FormValue value = 2;
}

message File {
  string name = 1;
  string url = 2;
  string type = 3;
  int64 size = 4;
  string key = 5;
  string uniqueId = 6;
}

message RenameFile {
  string old_fileName = 1;
  string new_fileName = 2;
  string key = 3;
  string uniqueId = 4;
  string new_s3Key = 5;
  string old_s3Key = 6;
}

message EventUpdateData {
  string caseNumber         = 1;
  string clientName         = 2;
  string description        = 3;
  string date               = 4;
  string startTime          = 5;
  string endTime            = 6;
  bool   isCompleted        = 7;
  string subject            = 8;
  string courtNoticeType    = 9;
  string appointmentAction  = 10;
  string charge             = 11;
  string exCountyOfArrest   = 12;
  string courtLocation      = 13;
  string optionalAttendees  = 14;
  string requiredAttendees  = 15;
  string clientAttendees    = 16;
  string meetingLocation    = 17;
  string startDate          = 18;
  string endDate            = 19;
  bool   allDay            = 20;
  string courtNoticeActions = 21;

  repeated File files        = 22;
  repeated File deleteFiles  = 23;

  repeated RenameFile renameFiles = 24;
  string appointmentToReschedule    = 25;
   string county    = 26;
  string meetingLink    = 27;
  string clientAttendance    = 28;
  string id    = 29;
  string court_notice_date    = 30;
  string phoneDetails    = 31;
  string meetingAddress    = 32;




}


message UpdateEvent {
  string derivedFieldId     = 1;
  string clientIdentifier   = 2;
  string clientMatterId     = 3;
  string matterId           = 4;
  string eventId            = 5;
  bool createEvent          = 6;
  EventUpdateData updateData = 7;
  bool deleteEvent          = 8;
  bool newClient            = 9;
}

message UpdateWorkflowRequest {
  string workflow_id  = 1;
  string task_id      = 2;
  bool   is_completed = 3;
  repeated Form forms = 4;
  UpdateEvent event   = 5;
  bool deleteMatter = 6;
  string clientMatterId = 7;
}

message UpdateWorkflowResponse {
  bool success = 1;
  string eventId = 2;
}

message ListCourtNoticeRequest {
  int32 page  = 1;
  int32 limit = 2;
  string type=3;
  string userId=4;
}

message CourtNotice {
  string id = 1;
  string template_name = 2;
  string start_date = 3;        // formatted as MM/DD/YYYY
  string end_date = 4;          // formatted as MM/DD/YYYY
  string last_activity = 5;   
  string status = 6;
  string assigned_users = 7;
  string assign_by = 8;
  string run_by = 9;
  string notes = 10;
  string work_flow_runner = 11;
  // New fields for follow-up type with matter information
  string client_name = 12;
  string matter_name = 13;
  string matter_id = 14;
  string matter = 15;         // formatted as "client_name (matter_name) SA"
  bool follow_up = 16;        // indicates if this is a follow-up entry
  string attorney  = 18;
  string _id = 19;
  string last_task_id =20;
  string isChild = 21;
  string completed_tasks =22;
}

message ListCourtNoticeResponse {
  int32 total = 1;
  int32 page = 2;
  int32 limit = 3;
  repeated CourtNotice fields = 4;
}

message UpdateWorkflowStatusRequest {
  string workflow_id  = 1;
  bool isChild = 2;        
}

message UpdateWorkflowStatusResponse {
  bool success = 1;
}

message County {
  string _id = 1;
  string value = 2;
  string text = 3;
  bool is_active = 4;
}

message ListCountyResponse {
  repeated County counties = 1;
}

message Court_location {
  string _id = 1;
  string value = 2;
  string text = 3;
  bool is_active = 4;
}

message ListCourtLocationResponse {
    repeated Court_location court_location = 1;
}

message UserItem {
  string _id = 1;
  string value = 2;
  string text = 3;
  bool is_active = 4;
}

message ListUsersResponse {
  repeated UserItem users = 1;
}

message court_notice_type {
  string _id = 1;
  string value = 2;
  string text = 3;
  bool is_active = 4;
}

message listCourtNoticeTypeResponse {
  repeated court_notice_type court_notice_type = 1;
}

message ListMatterResponse {
  repeated MatterItem matters = 1;
}


message MatterItem {
  string _id = 1;
  string name = 2;
  string client_id = 3;
  bool is_active = 4;
  string client_name = 5;
  string text = 6;
  string id = 7;
  string ex_county_of_arrest = 8;
  string case_number = 9;
  string my_case_matter_id = 10;
}

message GetNextTaskIdRequest {
  string task_id = 1;
  string work_flow_id = 2;
}

message GetNextTaskIdResponse {
  string next_task_id = 1;
}

message CreateDemoWorkflowRequest {
  string template_id = 1;
  string notes = 2;
  repeated string assigns = 3;
}

message CreateDemoWorkflowResponse {
  bool success = 1;
  string workflow_id = 2;
  string message = 3;
}

message UpdateWorkflowEndDateRequest {
  string workflow_id = 1;
  string task_id = 2;
  string end_date = 3;
}

message UpdateWorkflowEndDateResponse {
  bool success = 1;
  string message = 2;
}

message SearchMatterRequest {
  string search = 1;
}

message GetAppointmentEventsRequest {
  string workflow_id = 1;
  string client_matter_id = 2;
}


message GetAppointmentEventsResponse {
  string data = 1;
}

message GetEventByIdRequest {
  string event_id = 1;
}

// Top-level response
message GetEventByIdResponse {
string id = 1;
  string caseNumber = 2;
  string clientName = 3;
  string description = 4;
  string date = 5;
  string startTime = 6;
  string endTime = 7;
  bool isCompleted = 8;
  string subject = 9;
  string courtNoticeType = 10;
  string appointmentAction = 11;
  string charge = 12;
  string exCountyOfArrest = 13;
  string courtLocation = 14;
  string optionalAttendees = 15;
  string requiredAttendees = 16;
  string meetingLocation = 17;
  string startDate = 18;
  string endDate = 19;
  bool allDay = 20;
  string courtNoticeActions = 21;
  string appointmentToReschedule = 22;
  string county = 23;
  string meetingLink = 24;
  string clientAttendance = 25;
  string court_notice_date = 26;
  string phoneDetails = 27;
  string meetingAddress = 28;
}

message SyncLocationsFromMyCaseRequest {
  string case_id = 1;
  string access_token = 2;
}

message SyncLocationsFromMyCaseResponse {
  repeated LocationItem locations = 1;
  int32 synced_count = 2;
  int32 total_count = 3;
  string message = 4;
}

message LocationItem {
  string id = 1;
  string name = 2;
  bool is_active = 3;
  string created_at = 4;
  string updated_at = 5;
}

message GetLocationListResponse {
  repeated LocationItem locations = 1;
}

message GetLocationByIdRequest {
  string id = 1;
}

message GetLocationByIdResponse {
  LocationItem location = 1;
}

message CleanupFilesRequest {
  repeated string fileKeys = 1;
}

message CleanupFilesResponse {
  bool success = 1;
  string message = 2;
}

message SaveMyCaseWebhookDataRequest {
  string resource_type = 1;
  string resource_id = 2;
  string action = 3;
  // resource_body as JSON string to preserve nested objects and arrays
  // This prevents data loss during gRPC transmission
  // Example: "{\"id\":338308378,\"staff\":[{\"id\":61054170}]}"
  string resource_body = 4;
  string timestamp = 5;
}

message SaveMyCaseWebhookDataResponse {
  bool success = 1;
  string message = 2;
  string id = 3;
}

message UpdateWorkflowRetryInfoRequest {
  string workflow_id = 1;
  string task_id = 2;
  int32 retry_count = 3;
  string last_error = 4;
}

message UpdateWorkflowRetryInfoResponse {
  bool success = 1;
  string message = 2;
}

message CheckClientAvailabilityRequest {
  string client_id = 1;
  string tenant_id = 2;
  string access_token = 3;
}

message CheckClientAvailabilityResponse {
  bool available = 1;
  string message = 2;
  string client_name = 3;
  string client_email = 4;
  string my_case_client_id = 5;
}

message TriggerManualMyCaseSyncRequest {
  string client_id = 1;
  string tenant_id = 2;
  string access_token = 3;
}

message TriggerManualMyCaseSyncResponse {
  bool success = 1;
  string message = 2;
}

message GetMyCaseSyncStatusResponse {
  bool success = 1;
  string message = 2;
  string last_sync_time = 3;
  string sync_status = 4;
}

// Assignment-related messages
message GetAssigneesRequest {
  string search = 1;
  string category = 2;
}

message AssigneeOption {
  string id = 1;
  string name = 2;
  string type = 3; // "user" or "group"
  string category = 4;
  string email = 5;
  string description = 6;
}

message GetAssigneesResponse {
  repeated AssigneeOption assignees = 1;
}

message GetRoleMembersRequest {
  string role_id = 1;
}

message GetRoleMembersResponse {
  repeated AssigneeOption members = 1;
}

message AssignToTaskRequest {
  string task_execution_id = 1;
  repeated Assignee assignees = 2;
  string assigned_by = 3;
  string notes = 4;
}

message AssignToTaskResponse {
  bool success = 1;
  string message = 2;
}

message AssignUserToTaskRequest {
  string task_execution_id = 1;
  string user_id = 2;
  string assigned_by = 3;
  string work_flow_id = 4;
  string assignee_type = 5;
  bool isdeSelected = 6;
}

message AssignUserToTaskResponse {
  bool success = 1;
  string assigneeType = 2;
  string assigned_user_ids = 3;
  bool isdeSelected =4;
}

message RemoveUserFromTaskRequest {
  string task_execution_id = 1;
  string user_id = 2;
  string work_flow_id = 3;
  bool isdeSelected =4;
}

message RemoveUserFromTaskResponse {
  bool success = 1;
  string message = 2;
}

message Assignee {
  string id = 1;
  string type = 2; // "user" or "role"
}

message GetTaskAssignmentsRequest {
  string task_execution_id = 1;
}

message GetTaskAssignmentsResponse {
  repeated AssigneeOption assignees = 1;
}

message RemoveAssignmentRequest {
  string task_execution_id = 1;
  string assignee_id = 2;
  string assignment_type = 3; // "user" or "role"
}

message RemoveAssignmentResponse {
  bool success = 1;
  string message = 2;
}

message GetAllUsersAndRolesRequest {
  string search = 1;
}

message UserAndRoleItem {
  string id = 1;
  string name = 2;
  string email = 3;
  string type = 4;
  string roleId = 5;
  bool is_active = 6;
  string displayName = 7;
  string category = 8;
  string _id = 9;
  bool isdeSelected = 10;
}

message GetAllUsersAndRolesResponse {
  repeated UserAndRoleItem combined = 1;
}

message TestSuccessRequest {
  string params_json = 1;
}

message TestSuccessResponse {
  bool success = 1;
  string message = 2;
  string received_params_json = 3;
}
message CheckUserTaskAssignmentRequest {
  string taskId = 1;
  string work_flow_id = 2;
  string user_id = 3;
}

message CheckUserTaskAssignmentResponse {
  bool is_assigned = 1;
  string message = 2;
  bool success = 3;
}

message GetTaskAssignedUsersRequest {
  string task_id = 1;
  string workflow_execution_id = 2;
}

message GetTaskAssignedUsersResponse {
  bool success = 1;
  repeated UserAndRoleItem assigned_users = 2;
  int32 total_assigned = 3;
  string message = 4;
}
