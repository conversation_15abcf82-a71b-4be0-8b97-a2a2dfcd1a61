syntax = "proto3";

package auth;

service AuthService {
  // User authentication
  rpc Register (RegisterRequest) returns (AuthResponse) {}
  rpc Login (LoginRequest) returns (AuthResponse) {}
  
  // MFA Operations
  rpc EnableMfa (MfaEnableRequest) returns (MfaEnableResponse) {}
  rpc VerifyMfa (MfaVerifyRequest) returns (MfaVerifyResponse) {}
  rpc ValidateMfa (MfaValidateRequest) returns (AuthResponse) {}
  
  // Token Operations
  rpc ValidateToken (ValidateTokenRequest) returns (ValidateTokenResponse) {}
  rpc ResetPassword (ResetPasswordRequest) returns (ResetPasswordResponse) {}
}

// User authentication messages
message RegisterRequest {
  string email = 1;
  string password = 2;
  string username = 3;
  string firstName = 4;
  string lastName = 5;
  int32 roleId = 6;
}

message LoginRequest {
  string email = 1;
  string password = 2;
  string timezone = 3;
}

message AuthResponse {
  string access_token = 1;
  UserInfo user = 2;
  bool requiresMfa = 3;
}

message UserInfo {
  int32 id = 1;
  string email = 2;
  int32 roleId = 3;
  optional int32 loginAttemptCount = 4;
  optional bool lockedAccount = 5;
  optional bool isMFAEnable = 6;
  string access_token = 7;
}

// MFA messages
message MfaEnableRequest {
  int32 userId = 1;
}

message MfaEnableResponse {
  string secret = 1;
  string qrCode = 2;
}

message MfaVerifyRequest {
  int32 userId = 1;
  string otp = 2;
}

message MfaVerifyResponse {
  bool success = 1;
  string message = 2;
  string access_token = 3;
}

message MfaValidateRequest {
  int32 userId = 1;
  string otp = 2;
}

// Token messages
message ValidateTokenRequest {
  string token = 1;
}

message ValidateTokenResponse {
  bool valid = 1;
  string message = 2;
}

message ResetPasswordRequest {
  string token = 1;
  string password = 2;
}

message ResetPasswordResponse {
  bool success = 1;
  string message = 2;
  string email = 3;
} 