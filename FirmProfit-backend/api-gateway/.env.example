# App
PORT=3000

# Database
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=root
DB_NAME=firmprofit
DB_SYNC=true
DB_LOGGING=true 

# API Gateway Configuration
PORT=3000

# JWT Configuration
JWT_SECRET=your_super_secret_key_should_be_changed_in_production
JWT_EXPIRES_IN=1d

# Shared Service Configuration
SHARED_SERVICE_HOST=0.0.0.0
SHARED_SERVICE_PORT=3001
GRPC_URL=0.0.0.0:3001

# MyCase API Configuration
MYCASE_API_BASE_URL=https://api.mycase.com
MYCASE_CLIENT_ID=your_mycase_client_id
MYCASE_CLIENT_SECRET=your_mycase_client_secret

# Encryption Configuration
ENCRYPTION_KEY=your_encryption_key_change_in_production_32_chars