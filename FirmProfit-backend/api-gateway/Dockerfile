# Use official Node.js 20 image
FROM node:20-alpine AS builder
 
# Set working directory
WORKDIR /app

# Copy shared package
COPY ./shared/ ./shared/

RUN cd ./shared && npm install && npm run build

# Copy package.json and package-lock.json from shared-service
COPY ./api-gateway/package.json ./
COPY ./api-gateway/package-lock.json* ./

# Install dependencies
RUN npm install

# Copy shared-service source code (excluding shared to avoid overwriting built version)
COPY ./api-gateway/src/ ./src/
COPY ./api-gateway/nest-cli.json ./
COPY ./api-gateway/tsconfig.json ./
COPY ./api-gateway/tsconfig.build.json ./
COPY ./api-gateway/proto/ ./proto/

# Build the application
RUN npm run build
 
# Production stage
FROM node:20-alpine
 
WORKDIR /app
 
# Copy built application and dependencies from builder stage
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/shared ./shared
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/proto ./dist/proto

# Expose the port the app runs on
EXPOSE 3001

# Start the application
CMD ["node", "dist/src/main.js"]