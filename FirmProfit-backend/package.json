{"name": "@shared/database", "version": "1.0.0", "description": "Shared database module for FirmProfit microservices", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/"], "scripts": {"build": "tsc", "lint": "eslint . --ext .ts", "type-check": "tsc --noEmit", "install-deps": "npm install"}, "dependencies": {"@nestjs/common": "^11.0.12", "@nestjs/config": "^4.0.1", "@nestjs/typeorm": "^11.0.0", "@nestjs/mongoose": "^11.0.3", "typeorm": "^0.3.25", "mongoose": "^8.16.3", "class-validator": "^0.14.1", "class-transformer": "^0.5.1", "reflect-metadata": "^0.2.0"}, "devDependencies": {"@types/node": "^20.3.1", "typescript": "^5.1.3", "eslint": "^8.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0"}, "peerDependencies": {"pg": "^8.8.0"}, "resolutions": {"mongoose": "8.16.3", "bson": "6.10.4"}, "keywords": ["<PERSON><PERSON><PERSON>", "database", "typeorm", "mongoose", "shared", "microservices"], "author": "FirmProfit Team", "license": "MIT"}