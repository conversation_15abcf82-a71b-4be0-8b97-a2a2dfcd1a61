import fs from 'fs';

// Create .eslintrc.json
const eslintConfig = {
  root: true,
  env: {
    browser: true,
    es2021: true,
    node: true,
    jest: true,
  },
  extends: [
    'eslint:recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
    'plugin:@typescript-eslint/recommended',
    'prettier',
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaFeatures: {
      jsx: true,
    },
    ecmaVersion: 12,
    sourceType: 'module',
  },
  plugins: ['react', 'react-hooks', '@typescript-eslint'],
  rules: {
    'react/react-in-jsx-scope': 'off',
    'react/prop-types': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    'no-console': ['warn', { allow: ['warn', 'error'] }],
  },
  settings: {
    react: {
      version: 'detect',
    },
  },
};

// Create .prettierrc
const prettierConfig = {
  semi: true,
  trailingComma: 'es5',
  singleQuote: true,
  printWidth: 100,
  tabWidth: 2,
  useTabs: false,
  arrowParens: 'avoid',
  endOfLine: 'lf',
};

// Create .editorconfig
const editorConfig = `root = true

[*]
indent_style = space
indent_size = 2
end_of_line = lf
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true

[*.md]
trim_trailing_whitespace = false
`;

// Create .nvmrc
const nvmrc = '18.17.0\n';

// Create .dockerignore
const dockerignore = `node_modules
.next
.git
.github
npm-debug.log
yarn-debug.log
yarn-error.log
README.md
.env.local
.env.development.local
.env.test.local
.env.production.local
coverage
`;

// Save all files
fs.writeFileSync('.eslintrc.json', JSON.stringify(eslintConfig, null, 2));
fs.writeFileSync('.prettierrc', JSON.stringify(prettierConfig, null, 2));
fs.writeFileSync('.editorconfig', editorConfig);
fs.writeFileSync('.nvmrc', nvmrc);
fs.writeFileSync('.dockerignore', dockerignore);
