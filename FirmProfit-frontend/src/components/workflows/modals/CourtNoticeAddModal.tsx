import React, { useEffect, useState } from 'react';
import { EventType, ClientMatter } from '../CourtNotice';
import { v4 as uuidv4 } from 'uuid';
import CourtNoticeFormModal from './CourtNoticeFormModal';

// Timezone utility functions
const getUserTimezone = (): string => {
  if (typeof window === 'undefined') return 'UTC';
  return localStorage.getItem('timezone') || Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC';
};

// Convert time from user timezone to UTC for storage
const convertTimeToUTCReliable = (timeString: string, dateString: string, userTimezone?: string): string => {
  try {
    if (!timeString || !dateString) return timeString;

    const timezone = userTimezone || getUserTimezone();

    // Create date in user's timezone
    const dateTime = new Date(`${dateString}T${timeString}:00`);

    // Get the timezone offset for this specific date/time
    const userDate = new Date(dateTime.toLocaleString('en-US', { timeZone: timezone }));
    const utcDate = new Date(dateTime.toLocaleString('en-US', { timeZone: 'UTC' }));
    const offset = userDate.getTime() - utcDate.getTime();

    // Apply offset to get UTC time
    const utcDateTime = new Date(dateTime.getTime() - offset);

    const utcHours = utcDateTime.getHours().toString().padStart(2, '0');
    const utcMinutes = utcDateTime.getMinutes().toString().padStart(2, '0');

    return `${utcHours}:${utcMinutes}`;
  } catch (error) {
    console.error('Error converting time to UTC (reliable):', error);
    return timeString;
  }
};

const getCurrentDateInUserTimezone = (timezone?: string): string => {
  const userTimezone = timezone || getUserTimezone();
  const now = new Date();

  // Create a date object in the user's timezone
  const userDate = new Date(now.toLocaleString('en-US', { timeZone: userTimezone }));

  // Format as YYYY-MM-DD
  const year = userDate.getFullYear();
  const month = String(userDate.getMonth() + 1).padStart(2, '0');
  const day = String(userDate.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
};

interface Matter {
  _id: string;
  name: string;
  client_id: string;
  is_active: boolean;
  client_name: string;
  text: string;
  id: string;
  ex_county_of_arrest: string;
  case_number: string;
}

interface CourtNoticeAddModalProps {
  isOpen: boolean;
  onClose: () => void;
  clientId: string;
  client: ClientMatter | null;
  onAdd: (clientId: string, newEvent: EventType) => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  options?: any;
  workflow_id?: string;
  clientName?: string;
  isChildWorkflow?: boolean;
  titleOfEvent: string;
  selectedMatter?: Matter;
  user_group_id?: string;
}

const CourtNoticeAddModal: React.FC<CourtNoticeAddModalProps> = ({
  isOpen,
  onClose,
  clientId,
  client,
  onAdd,
  options,
  workflow_id,
  clientName,
  isChildWorkflow,
  titleOfEvent,
  selectedMatter,
  user_group_id
}) => {
  console.log("🚀 ~ client:", client)
  console.log("🚀 ~ workflow_id:", workflow_id)
  const [eventTemplate, setEventTemplate] = useState<EventType | null>(null);

  // Create default template on mount or when client changes
  useEffect(() => {
    if (client) {
      // Find the selected matter - client should already have the selected matter from the parent component
      const selectedMatter = client.matters && client.matters.length > 0 ? client.matters[0] : null;

      console.log("🚀 ~ selectedMatter:", selectedMatter)

      // Format client name with matter if available
      const clientMatterLabel = selectedMatter ? `${selectedMatter.name}` : client.name;

      // Get the client_matter_id from the selected matter (prioritize matter_id, then _id)
      const clientMatterId = selectedMatter ? (selectedMatter.matter_id || selectedMatter._id) : undefined;

      // Default template for a new event
      const template: EventType = {
        _id: `evt-${uuidv4()}`,
        id: `evt-${uuidv4()}`,
        clientName: clientMatterLabel || '',
        description: '',
        subject: ``,
        courtNoticeType: '',
        appointmentAction: '',
        charge: '',
        county: selectedMatter?.ex_county_of_arrest || '',
        caseNumber: selectedMatter?.case_number || '',
        courtLocation: '',
        startDate: getCurrentDateInUserTimezone(),
        endDate: getCurrentDateInUserTimezone(),
        date: (() => {
          const userTimezone = getUserTimezone();
          const now = new Date();
          const userDate = new Date(now.toLocaleString('en-US', { timeZone: userTimezone }));
          return userDate.toLocaleDateString('en-US', {
            month: '2-digit',
            day: '2-digit',
            year: 'numeric',
          });
        })(),
        // Convert default times from local timezone to UTC for storage
        startTime: convertTimeToUTCReliable('10:00', getCurrentDateInUserTimezone()),
        endTime: convertTimeToUTCReliable('11:00', getCurrentDateInUserTimezone()),
        allDay: false,
        requiredAttendees: '',
        optionalAttendees: '',
        clientAttendance: '',
        meetingLocation: '',
        isCompleted: false,
        courtNoticeActions: '',
        selectedMatterId: selectedMatter?._id,
        // Add client_matter_id for attendees search to work
        client_matter_id: clientMatterId,
      };

      setEventTemplate(template);
    }
  }, [client]);

  if (!eventTemplate) return null;

  return (
    <CourtNoticeFormModal
      isOpen={isOpen}
      onClose={onClose}
      event={eventTemplate}
      clientId={clientId}
      onSave={onAdd}
      options={options}
      isNew={true}
      title="Add a new event"
      workflow_id={workflow_id}
      isChildWorkflow={isChildWorkflow}
      titleOfEvent={titleOfEvent}
      clientName={clientName || ''}
      selectedMatter={selectedMatter}
      user_group_id={user_group_id}
    />
  );
};

export default CourtNoticeAddModal;

// NEXT_PUBLIC_BASE_URL=http://localhost:3000
