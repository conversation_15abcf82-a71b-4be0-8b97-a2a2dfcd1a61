/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { X, ChevronDown } from 'lucide-react';
import { EventType } from '../CourtNotice';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import Image from 'next/image';
import { isTbdValue } from '@/types/courtNotice';
import FileUpload, { UploadedFile } from '@/components/common/FileUpload';
import axios from 'axios';
import { VALIDATIONS } from '@/constants/workflow';
import { createPortal } from 'react-dom';
import { Poppins } from 'next/font/google';
import ClientEmailAlertModal from '@/components/ui/alertModal';

const poppins = Poppins({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
});

// Timezone utility functions
const getUserTimezone = (): string => {
  if (typeof window === 'undefined') return 'UTC';
  return localStorage.getItem('timezone') || Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC';
};

// Convert time from user timezone to UTC for storage
const convertTimeToUTC = (timeString: string, dateString: string, userTimezone?: string): string => {
  try {
    if (!timeString || !dateString) return timeString;

    const timezone = userTimezone || getUserTimezone();
    const [hours, minutes] = timeString.split(':').map(Number);

    // Create a date object in the user's timezone
    const date = new Date(dateString);
    date.setHours(hours, minutes, 0, 0);

    // Convert to UTC by getting the timezone offset
    const tempDate = new Date(date.toLocaleString('en-US', { timeZone: timezone }));
    const utcDate = new Date(date.getTime() + (date.getTime() - tempDate.getTime()));

    // Format as HH:MM
    const utcHours = utcDate.getHours().toString().padStart(2, '0');
    const utcMinutes = utcDate.getMinutes().toString().padStart(2, '0');

    return `${utcHours}:${utcMinutes}`;
  } catch (error) {
    console.error('Error converting time to UTC:', error);
    return timeString;
  }
};

// Convert time from UTC to user timezone for display
const convertTimeFromUTC = (utcTimeString: string, dateString: string, userTimezone?: string): string => {
  try {
    if (!utcTimeString || !dateString) return utcTimeString;

    const timezone = userTimezone || getUserTimezone();
    const [hours, minutes] = utcTimeString.split(':').map(Number);

    // Create a UTC date object
    const utcDate = new Date(dateString);
    utcDate.setUTCHours(hours, minutes, 0, 0);

    // Convert to user's timezone
    const localDate = new Date(utcDate.toLocaleString('en-US', { timeZone: timezone }));

    // Format as HH:MM
    const localHours = localDate.getHours().toString().padStart(2, '0');
    const localMinutes = localDate.getMinutes().toString().padStart(2, '0');

    return `${localHours}:${localMinutes}`;
  } catch (error) {
    console.error('Error converting time from UTC:', error);
    return utcTimeString;
  }
};

// Alternative more reliable timezone conversion using Intl.DateTimeFormat
const convertTimeToUTCReliable = (timeString: string, dateString: string, userTimezone?: string): string => {
  try {
    if (!timeString || !dateString) return timeString;

    const timezone = userTimezone || getUserTimezone();
    const [hours, minutes] = timeString.split(':').map(Number);

    // Create date in user's timezone
    const dateTime = new Date(`${dateString}T${timeString}:00`);

    // Get the timezone offset for this specific date/time
    const userDate = new Date(dateTime.toLocaleString('en-US', { timeZone: timezone }));
    const utcDate = new Date(dateTime.toLocaleString('en-US', { timeZone: 'UTC' }));
    const offset = userDate.getTime() - utcDate.getTime();

    // Apply offset to get UTC time
    const utcDateTime = new Date(dateTime.getTime() - offset);

    const utcHours = utcDateTime.getHours().toString().padStart(2, '0');
    const utcMinutes = utcDateTime.getMinutes().toString().padStart(2, '0');

    return `${utcHours}:${utcMinutes}`;
  } catch (error) {
    console.error('Error converting time to UTC (reliable):', error);
    return timeString;
  }
};

const convertTimeFromUTCReliable = (utcTimeString: string, dateString: string, userTimezone?: string): string => {
  try {
    if (!utcTimeString || !dateString) return utcTimeString;

    const timezone = userTimezone || getUserTimezone();

    // Create UTC datetime
    const utcDateTime = new Date(`${dateString}T${utcTimeString}:00Z`);

    // Convert to user's timezone
    const userDateTime = new Date(utcDateTime.toLocaleString('en-US', { timeZone: timezone }));

    const localHours = userDateTime.getHours().toString().padStart(2, '0');
    const localMinutes = userDateTime.getMinutes().toString().padStart(2, '0');

    return `${localHours}:${localMinutes}`;
  } catch (error) {
    console.error('Error converting time from UTC (reliable):', error);
    return utcTimeString;
  }
};

// Add new utility function to get current date in user's timezone
const getCurrentDateInUserTimezone = (timezone?: string): string => {
  const userTimezone = timezone || getUserTimezone();
  const now = new Date();

  // Create a date object in the user's timezone
  const userDate = new Date(now.toLocaleString('en-US', { timeZone: userTimezone }));

  // Format as YYYY-MM-DD
  const year = userDate.getFullYear();
  const month = String(userDate.getMonth() + 1).padStart(2, '0');
  const day = String(userDate.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
};

// Add utility function to get current Date object in user's timezone for DatePicker
const getCurrentDateObjectInUserTimezone = (timezone?: string): Date => {
  const userTimezone = timezone || getUserTimezone();
  const now = new Date();

  // Create a date object in the user's timezone
  return new Date(now.toLocaleString('en-US', { timeZone: userTimezone }));
};

const convertDateToUserTimezone = (date: Date | string, timezone?: string): Date => {
  const userTimezone = timezone || getUserTimezone();
  const inputDate = typeof date === 'string' ? new Date(date) : date;

  // Convert to user's timezone
  const timezonedDate = new Date(inputDate.toLocaleString('en-US', { timeZone: userTimezone }));
  return timezonedDate;
};

const formatDateInUserTimezone = (dateString: string, timezone?: string): string => {
  try {
    if (!dateString || typeof dateString !== 'string') {
      return '';
    }

    const userTimezone = timezone || getUserTimezone();
    const date = new Date(dateString);

    // Check if the date is valid
    if (isNaN(date.getTime())) {
      console.warn('Invalid date for timezone formatting:', dateString);
      return '';
    }

    // Convert to user's timezone and format
    const options: Intl.DateTimeFormatOptions = {
      timeZone: userTimezone,
      month: '2-digit',
      day: '2-digit',
      year: 'numeric'
    };

    const formatter = new Intl.DateTimeFormat('en-US', options);
    return formatter.format(date);
  } catch (e) {
    console.error('Error formatting date with timezone:', e, dateString);
    return '';
  }
};

const parseDateInUserTimezone = (dateString: string, timezone?: string): Date | null => {
  try {
    if (!dateString || typeof dateString !== 'string') {
      return null;
    }

    const userTimezone = timezone || getUserTimezone();

    // Create date in user's timezone
    const date = new Date(dateString);

    // Check if the date is valid
    if (isNaN(date.getTime())) {
      console.warn('Invalid date string for timezone parsing:', dateString);
      return null;
    }

    // Convert to user's timezone
    return convertDateToUserTimezone(date, userTimezone);
  } catch (e) {
    console.error('Error parsing date with timezone:', e, dateString);
    return null;
  }
};

// Define a type for the option items
export interface OptionItem {
  value: string;
  text: string;
  _id?: string;
  is_active?: boolean;
  workflow_id?: string;
}

// Define interface for user API response
interface UserItem {
  _id: string;
  value: string;
  text: string;
  is_active?: boolean;
}

interface Events {
  id?: string;
  _id?: string;
  clientName?: string;
  charge?: string;
  county?: string;
  exCountyOfArrest?: string;
  caseNumber?: string;
  courtNoticeType?: string;
  clientAttendance?: string;
  startDate?: string;
  date?: string;
  startTime?: string;
  endTime?: string;
  endDate?: string;
  subject?: string;
}

interface Matter {
  _id: string;
  name: string;
  client_id: string;
  is_active: boolean;
  client_name: string;
  text: string;
  id: string;
  ex_county_of_arrest: string;
  case_number: string;
  matter_id?: string;
}

export interface CourtNoticeFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  event: EventType | null;
  clientId: string;
  onSave: (clientId: string, updatedEvent: EventType) => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  options?: any;
  isNew?: boolean;
  title?: string;
  isDisabled?: boolean;
  workflow_id?: string;
  isChildWorkflow?: boolean;
  titleOfEvent?: string;
  clientName?: string; // Add clientName prop
  isEditMode?: boolean;
  isTaskReviewed?: boolean;
  selectedMatter?: Matter;
  user_group_id?: string;
}

// Define interface for API options
export interface WorkflowOptions {
  courtNoticeTypes: OptionItem[];
  appointmentActions: OptionItem[];
  counties: OptionItem[];
  courtLocations: OptionItem[];
  attendees: OptionItem[];
  clientAttendanceOptions: OptionItem[];
  meetingLocations: OptionItem[];
  optionalAttendees: OptionItem[];
  courtNoticeActions: OptionItem[];
}

// Utility functions for text generation
const formatChargeCountyPart = (charge?: string, county?: string): string => {
  const chargePart = charge?.trim();
  const countyPart = county?.trim();

  if (!chargePart && !countyPart) {
    return '';
  }

  if (chargePart && countyPart) {
    return `(${chargePart}-${countyPart})`;
  }

  if (chargePart) {
    return `(${chargePart})`;
  }

  if (countyPart) {
    return `(${countyPart})`;
  }

  return '';
};

const generateSubject = (formData: EventType): string => {
  // Handle special cases first
  if (formData.courtNoticeActions === 'Save court notice only') {
    return 'Save court notice only';
  }

  // Generate client name part (first and last name)
  const clientNamePart =
    formData.clientName?.split(' ')[0] + ' ' + formData.clientName?.split(' ')[1] || '';

  const chargeCountyPart = formatChargeCountyPart(formData.charge, formData.county);
  const caseNumberPart = formData.caseNumber?.trim() || '';
  const clientAttendancePart = formData.clientAttendance?.trim() || '';

  // Handle court notice type unless appointment is Cancel
  let courtNoticeTypePart = '';
  if (formData.appointmentAction !== 'Cancel') {
    courtNoticeTypePart = formData.courtNoticeType?.trim()
      ? `${formData.courtNoticeType.trim()};`
      : '';

    // Append 'DEADLINE' if needed
    if (formData.courtNoticeActions === 'Calendar DEADLINE and save court notice') {
      courtNoticeTypePart = courtNoticeTypePart ? `${courtNoticeTypePart} DEADLINE` : 'DEADLINE';
    }
  }

  const parts = [
    courtNoticeTypePart,
    clientNamePart,
    chargeCountyPart,
    caseNumberPart,
    clientAttendancePart,
  ].filter(part => part && part.trim() !== '');

  const subject = parts.join(' ').replace(/\s+/g, ' ').trim();

  return subject;
};

// Async function to handle Cancel appointment subject generation
const generateCancelSubject = async (formData: EventType): Promise<string> => {
  // Handle Cancel appointment action - fetch the rescheduled event's subject
  if (formData.appointmentAction === 'Cancel' && formData.appointmentToReschedule) {
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/event/${formData.appointmentToReschedule}`
      );

      if (response.data?.statusCode === 200 && response.data?.data?.subject) {
        console.log('✅ Retrieved rescheduled event subject:', response.data.data.subject);
        return response.data.data.subject;
      }
    } catch (error) {
      console.error('❌ Error fetching rescheduled event subject:', error);
      // Fall back to normal subject generation if API call fails
    }
  }

  // Fall back to normal subject generation
  return generateSubject(formData);
};

const generateDescription = (formData: EventType): string => {
  // Handle special case
  if (formData.courtNoticeActions === 'Save court notice only') {
    return 'Save court notice only';
  }

  // Generate base description
  const clientNamePart =
    formData.clientName?.split(' ')[0] + ' ' + formData.clientName?.split(' ')[1] || '';
  const chargeCountyPart = formatChargeCountyPart(formData.charge, formData.county);
  const caseNumberPart = formData.caseNumber?.trim() || '';
  const courtNoticeTypePart = formData.courtNoticeType?.trim()
    ? `${formData.courtNoticeType.trim()};`
    : '';
  const clientAttendancePart = formData.clientAttendance?.trim() || '';

  const baseParts = [
    courtNoticeTypePart,
    clientNamePart,
    chargeCountyPart,
    caseNumberPart,
    clientAttendancePart,
  ].filter(part => part && part.trim() !== '');

  let description = baseParts.join(' ').replace(/\s+/g, ' ').trim();
  let meetingDetails = '';

  // Only add meeting location details if there's actually a meeting location selected
  // and it's not empty/default and not a TBD value and has actual details to show
  if (
    formData.meetingLocation &&
    formData.meetingLocation.trim() !== '' &&
    !formData.meetingLocation.toLowerCase().includes('select')
  ) {
    let hasDetails = false;

    if (
      formData.meetingLocation.includes('Virtual meeting link') &&
      formData.meetingLink &&
      formData.meetingLink.trim()
    ) {
      meetingDetails += formData.meetingLink;
      hasDetails = true;
    }

    if (
      formData.meetingLocation.includes('Phone') &&
      formData.phoneDetails &&
      formData.phoneDetails.trim()
    ) {
      meetingDetails += formData.phoneDetails;
      hasDetails = true;
    }

    if (
      formData.meetingLocation.includes('Meeting in person') &&
      formData.meetingAddress &&
      formData.meetingAddress.trim()
    ) {
      meetingDetails += formData.meetingAddress;
      hasDetails = true;
    }

    if (
      formData.meetingLocation.includes('Virtual meeting link - Link TBD') ||
      formData.meetingLocation.includes('Phone - Phone TBD') ||
      formData.meetingLocation.includes('Meeting in person - Location TBD')
    ) {
      meetingDetails = '';
      meetingDetails += formData.meetingLocation;
      hasDetails = true;
    }

    // Only add the meeting location section if there are actual details
    if (hasDetails) {
      description += '\n\nMeeting Location:\n' + meetingDetails;
    }
  }

  return description.trim();
};

// Utility function to format date strings
// const formatDate = (dateStr: string | undefined): string => {
//   if (!dateStr) return '';

//   try {
//     // Check if date is already in MM/DD/YYYY format
//     if (/^\d{2}\/\d{2}\/\d{4}$/.test(dateStr)) {
//       return dateStr;
//     }

//     // Otherwise try to parse and format
//     const date = new Date(dateStr);
//     return date.toLocaleDateString('en-US', {
//       month: '2-digit',
//       day: '2-digit',
//       year: 'numeric',
//     });
//   } catch (e) {
//     console.error('Error formatting date:', e);
//     return dateStr; // If parsing fails, return original
//   }
// };

// Utility function to format events to options for dropdowns
// const formatEventsToOptions = (responseData: Events[]): { text: string; value: string; workflow_id?: string }[] => {
//   // Extract the data array from the response
//   const events = responseData || [];

//   // Create options array by iterating through events
//   // eslint-disable-next-line @typescript-eslint/no-explicit-any
//   const options = events.map((event: any) => {
//     // Extract client name part (before the first |)
//     const clientNamePart = event.clientName?.split('|')[0]?.trim() || '';

//     // Extract other parts
//     const chargePart = event.charge || '';
//     const countyPart = event.county || ''; // Using exCountyOfArrest as county
//     const caseNumberPart = event.caseNumber || '';
//     const courtNoticeTypePart = event.courtNoticeType === '' ? '' : event.courtNoticeType + '; ';
//     const clientAttendancePart = event.clientAttendance || '';

//     // Create charge/county part with proper formatting
//     const chargeCountyPart = formatChargeCountyPart(chargePart, countyPart);

//     const formattedDate: string = `${formatDate(event.startDate || event.date)} ${event.startTime} - ${event.endTime}`;

//     // Create formatted subject
//     const formattedSubject =
//       `${courtNoticeTypePart}${clientNamePart} ${chargeCountyPart} ${caseNumberPart} ${clientAttendancePart}`.trim();

//     // Return option object with text and value
//     return {
//       text: formattedSubject + ' ' + formattedDate,
//       value: event.id || event._id || '', // Use id if available, otherwise use _id
//       workflow_id: '', // Add empty workflow_id to match OptionItem interface
//     };
//   });

//   return options;
// };

// Utility function to ensure options are in the right format
const ensureOptionFormat = (options: OptionItem[] | undefined): OptionItem[] => {
  if (!options || !Array.isArray(options)) {
    // Return a basic "Select" option if no options are provided
    return [{ value: '', text: 'Select a location' }];
  }
  return options.map(option => ({
    ...option,
    value: option.text,
  }));
};

/**
 * CourtNoticeFormModal Component
 *
 * This component renders a modal form for court notices.
 * It can be used with options from the API by passing them through the options prop.
 *
 * Example usage with API data:
 * ```
 * // In the parent component that gets API response:
 * const response = await axios.get(`${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-render`);
 * const apiOptions = response?.data?.data?.options;
 *
 * <CourtNoticeFormModal
 *   options={apiOptions}
 *   // other props.. ..
 * />
 * ```
 */
const CourtNoticeFormModal: React.FC<CourtNoticeFormModalProps> = ({
  isOpen,
  onClose,
  event,
  clientId,
  onSave,
  options,
  isNew = false,
  // title = 'Edit event',
  workflow_id,
  isChildWorkflow = false,
  titleOfEvent,
  clientName, // Add clientName prop
  isEditMode,
  isTaskReviewed,
  selectedMatter,
  user_group_id,
}) => {
  const [formData, setFormData] = useState<EventType | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [allDay, setAllDay] = useState(false);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [allDayDeadLine, setAllDayDeadLine] = useState(false);
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [startTime, setStartTime] = useState<Date | null>(null);
  const [endTime, setEndTime] = useState<Date | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [originalUploadedFiles, setOriginalUploadedFiles] = useState<UploadedFile[]>([]);
  const isInitializedRef = useRef(false);
  const isFirstRender = useRef(0);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [intialEventDescription, setIntialEventDescription] = useState<string | null>(
    event?.description || null
  );
  const [intialEventSubject, setIntialEventSubject] = useState<string | null>(
    event?.subject || null
  );
  const [isFieldManuallyEdited, setIsFieldManuallyEdited] = useState<{
    subject: boolean;
    description: boolean;
  }>({
    subject: false,
    description: false,
  });
  interface AppointmentOption {
    text: string;
    value: string | undefined;
  }

  const [rescheduleAppointments, setRescheduleAppointments] = useState<AppointmentOption[]>([]);

  // Debounce function for API calls
  const useDebounce = (value: string, delay: number) => {
    const [debouncedValue, setDebouncedValue] = useState(value);

    useEffect(() => {
      const handler = setTimeout(() => {
        setDebouncedValue(value);
      }, delay);

      return () => {
        clearTimeout(handler);
      };
    }, [value, delay]);

    return debouncedValue;
  };

  // Self-contained SearchableMultiSelectWithAPI component for optional attendees
  const SearchableMultiSelectWithAPI = ({
    name,
    value,
    onChange,
    hasError = false,
    disabled = false,
    placeholder = 'Search and select...',
  }: {
    name: string;
    value: string;
    onChange: (name: string, value: string) => void;
    hasError?: boolean;
    disabled?: boolean;
    placeholder?: string;
  }) => {
    const [isOpen, setIsOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [highlightedIndex, setHighlightedIndex] = useState<number>(-1);
    const [apiOptions, setApiOptions] = useState<OptionItem[]>([]);
    const [allOptions, setAllOptions] = useState<OptionItem[]>([]); // Store all options
    const [isLoading, setIsLoading] = useState(false);
    const [isInitialLoad, setIsInitialLoad] = useState(false);
    const dropdownRef = React.useRef<HTMLDivElement>(null);
    const searchInputRef = React.useRef<HTMLInputElement>(null);

    // Modal state for client selection
    const [showClientModal, setShowClientModal] = useState(false);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [pendingClientOption, setPendingClientOption] = useState<OptionItem | null>(null);

    // Debounce the search term
    const debouncedSearchTerm = useDebounce(searchTerm, 300);

    // Convert comma-separated string to array of selected values
    const selectedValues = value ? value.split(',').map(val => val.trim()) : [];

    // Function to fetch users from API
    const fetchUsers = useCallback(
      async (searchTerm: string = '') => {
        setIsLoading(true);
        try {
          // Build the API URL with search parameter and client_id if available
          const clientId = event?.client_matter_id;
          let apiUrl = `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/user-list`;

          // Add search parameter only if searchTerm is provided
          const params = new URLSearchParams();
          if (searchTerm.trim()) {
            params.append('search', searchTerm);
          }
          params.append('client_id', clientId || '');
          params.append('user_group_id', user_group_id || '');

          if (params.toString()) {
            apiUrl += `?${params.toString()}`;
          }

          const response = await axios.get(apiUrl);

          if (response.data && response.data.data && Array.isArray(response.data.data.users)) {
            const formattedUsers: OptionItem[] = response.data.data.users
              .filter((user: UserItem) => user.is_active) // Only include active users
              .map((user: UserItem) => ({
                value: user.value,
                text: user.text || user.value,
                _id: user._id,
                is_active: user.is_active,
              }));

            // If this is the initial load (no search term), store all options
            if (!searchTerm.trim()) {
              setAllOptions(formattedUsers);
              setApiOptions(formattedUsers);
            } else {
              // For search results, update apiOptions but keep allOptions unchanged
              setApiOptions(formattedUsers);
            }
          } else {
            if (!searchTerm.trim()) {
              setAllOptions([]);
            }
            setApiOptions([]);
          }
        } catch (error) {
          console.error('Error fetching users:', error);
          if (!searchTerm.trim()) {
            setAllOptions([]);
          }
          setApiOptions([]);
        } finally {
          setIsLoading(false);
          if (!searchTerm.trim()) {
            setIsInitialLoad(true);
          }
        }
      },
      [event?.selectedMatterId]
    );

    // Load all options when dropdown opens for the first time
    useEffect(() => {
      if (isOpen && !isInitialLoad && allOptions.length === 0) {
        fetchUsers(''); // Fetch all users without search term
      }
    }, [isOpen, isInitialLoad, allOptions.length, fetchUsers]);

    // Handle search functionality
    useEffect(() => {
      if (!isInitialLoad) return; // Don't search until initial load is complete

      if (debouncedSearchTerm.trim()) {
        // If there's a search term, fetch filtered results from API
        fetchUsers(debouncedSearchTerm);
      } else {
        // If no search term, show all options
        setApiOptions(allOptions);
      }
    }, [debouncedSearchTerm, fetchUsers, allOptions, isInitialLoad]);

    // Handle outside click to close dropdown
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
          setIsOpen(false);
          setSearchTerm('');
          setHighlightedIndex(-1);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, []);

    // Focus search input when dropdown opens
    useEffect(() => {
      if (isOpen && searchInputRef.current) {
        searchInputRef.current.focus();
        setHighlightedIndex(0);
      }
    }, [isOpen]);

    // Reset highlighted index when filtered options change
    useEffect(() => {
      if (apiOptions.length > 0) {
        setHighlightedIndex(0);
      } else {
        setHighlightedIndex(-1);
      }
    }, [apiOptions.length]);

    // Handle keyboard navigation
    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (!isOpen) {
        if (e.key === 'Enter' || e.key === ' ' || e.key === 'ArrowDown' || e.key === 'ArrowUp') {
          e.preventDefault();
          setIsOpen(true);
        }
        return;
      }

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setHighlightedIndex(prev => {
            const nextIndex = prev < apiOptions.length - 1 ? prev + 1 : 0;
            setTimeout(() => {
              const highlightedElement = document.querySelector(
                `[data-api-option-index="${nextIndex}"]`
              );
              if (highlightedElement) {
                highlightedElement.scrollIntoView({
                  block: 'nearest',
                  behavior: 'smooth',
                });
              }
            }, 0);
            return nextIndex;
          });
          break;

        case 'ArrowUp':
          e.preventDefault();
          setHighlightedIndex(prev => {
            const nextIndex = prev > 0 ? prev - 1 : apiOptions.length - 1;
            setTimeout(() => {
              const highlightedElement = document.querySelector(
                `[data-api-option-index="${nextIndex}"]`
              );
              if (highlightedElement) {
                highlightedElement.scrollIntoView({
                  block: 'nearest',
                  behavior: 'smooth',
                });
              }
            }, 0);
            return nextIndex;
          });
          break;

        case 'Enter':
          e.preventDefault();
          if (highlightedIndex >= 0 && highlightedIndex < apiOptions.length) {
            const selectedOption = apiOptions[highlightedIndex];
            toggleOption(selectedOption.text, selectedOption);
          }
          break;

        case 'Escape':
          e.preventDefault();
          setIsOpen(false);
          setSearchTerm('');
          setHighlightedIndex(-1);
          break;
      }
    };

    // Toggle selection of an option (with client availability check)
    const toggleOption = async (optionText: string, optionObj?: OptionItem) => {
      if (!optionObj) return;

      // Use the new handleOptionSelect function that includes client availability check
      await handleOptionSelect(optionObj);
      setSearchTerm('');
    };

    // Remove a selected item
    const removeItem = (itemToRemove: string) => {
      const newValues = selectedValues.filter(val => val !== itemToRemove);
      onChange(name, newValues.join(', '));
    };

    // Handler for modal OK (submit)

    const handleClientModalCancel = () => {
      setShowClientModal(false);
      setPendingClientOption(null);
    };

    const checkClientAvailability = async (clientId: string): Promise<boolean> => {
      try {
        const response = await axios.post(
          `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/check-client-availability`,
          {
            client_id: clientId,
            tenant_id: '1',
          }
        );

        if (response.data.statusCode === 201) {
          return response.data.data.available;
        }
        return true;
      } catch (error) {
        console.error('Error checking client availability:', error);
        return true;
      }
    };

    const handleOptionSelect = async (option: OptionItem) => {
      if (disabled) return;
      console.log(option, 'option');
      if (option.text.includes('(Client)')) {
        const clientId = option._id;
        if (typeof clientId === 'string') {
          const isAvailable = await checkClientAvailability(clientId);

          if (!isAvailable) {
            setPendingClientOption(option);
            setShowClientModal(true);
            return;
          }
        } else {
          console.error('Client ID is undefined, cannot check availability.');
          return;
        }
      }
      const isSelected = selectedValues.includes(option.value);
      let newValues: string[];

      if (isSelected) {
        newValues = selectedValues.filter(val => val !== option.value);
      } else {
        newValues = [...selectedValues, option.value];
      }

      onChange(name, newValues.join(', '));
    };

    return (
      <>
        <div className="relative w-full" ref={dropdownRef}>
          <div
            onClick={() => !disabled && setIsOpen(!isOpen)}
            onKeyDown={handleKeyDown}
            tabIndex={disabled ? -1 : 0}
            role="button"
            aria-haspopup="listbox"
            aria-expanded={isOpen}
            className={`
              w-full p-3 pr-10 border font-normal rounded-[12px] text-[#2a2e34] bg-white min-h-[48px] flex items-center flex-wrap gap-1
              focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-200 focus-within:border-[#3F73F6] ${hasError ? 'border-red-500' : 'border-[#DCE2EB]'} ${disabled ? 'bg-gray-100 text-gray-500 cursor-not-allowed' : 'cursor-pointer'}
            `}
          >
            {selectedValues.length > 0 ? (
              selectedValues.map((val, index) => (
                <span
                  key={index}
                  className="bg-[#F3F5F9] px-2 py-1 rounded-md text-sm flex items-center"
                >
                  {val}
                  {!disabled && (
                    <button
                      type="button"
                      onClick={e => {
                        e.stopPropagation();
                        removeItem(val);
                      }}
                      className="ml-1 text-gray-500 hover:text-gray-700"
                    >
                      <X size={14} />
                    </button>
                  )}
                </span>
              ))
            ) : (
              <span className="text-[#5F6F84]">{placeholder}</span>
            )}
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3">
              <ChevronDown size={20} className="text-[#5F6F84]" />
            </div>
          </div>

          {isOpen && !disabled && (
            <div className="absolute z-50 mt-1 w-full bg-white border border-[#DCE2EB] rounded-[12px] shadow-lg max-h-80 overflow-hidden">
              {/* Search input */}
              <div className="p-3 border-b border-[#DCE2EB]">
                <input
                  ref={searchInputRef}
                  type="text"
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Search attendees..."
                  className="w-full p-2 border border-[#DCE2EB] rounded-[8px] text-sm focus:outline-none focus:ring-2 placeholder:text-[#5F6F84] focus:ring-blue-200 focus:border-[#3F73F6]"
                  onClick={e => e.stopPropagation()}
                />
              </div>

              {/* Options list */}
              <div
                className="max-h-[180px] overflow-y-auto overflow-x-hidden \
                [scrollbar-width:thin] [scrollbar-color:#cbd5e0_#f7fafc] \
                [&::-webkit-scrollbar]:w-2 \
                [&::-webkit-scrollbar-thumb]:rounded-full \
                [&::-webkit-scrollbar-thumb]:bg-[#cbd5e0] \
                [&::-webkit-scrollbar-track]:bg-gray-100"
              >
                {isLoading ? (
                  <div className="p-3 text-gray-500 text-sm text-center">Loading...</div>
                ) : apiOptions.length > 0 ? (
                  apiOptions.map((option, index) => (
                    <div
                      key={`${option._id}-${index}`}
                      data-api-option-index={index}
                      onClick={async () => await toggleOption(option.text, option)}
                      onMouseEnter={() => setHighlightedIndex(index)}
                      className={`p-3 text-[14px] text-[#2A2E34] cursor-pointer flex items-center transition-colors duration-150 ${selectedValues.includes(option.text)
                        ? 'bg-[#D9E3FD] text-[#3F73F6] mt-2 mb-2'
                        : highlightedIndex === index
                          ? 'bg-gray-50'
                          : 'hover:bg-gray-50'
                        }`}
                    >
                      <div className="flex items-center w-full">
                        <span className="text-[16px] text-[#2A2E34]">{option.text}</span>
                      </div>
                    </div>
                  ))
                ) : searchTerm ? (
                  <div className="p-3 text-gray-500 text-sm text-center">No users found</div>
                ) : (
                  <div className="p-3 text-gray-500 text-sm text-center">No users available</div>
                )}
              </div>
            </div>
          )}
        </div>
        {/* Modal for client selection */}
        <ClientEmailAlertModal isOpen={showClientModal} onClose={handleClientModalCancel} />
      </>
    );
  };

  // Function to cleanup unsaved uploaded files
  const cleanupUnsavedFiles = async () => {
    try {
      // Get files that were uploaded in this session but not saved
      const unsavedFiles = uploadedFiles.filter(file => {
        // Check if this is a newly uploaded file (not from existing event)
        const isNewUpload = !originalUploadedFiles.some(
          original => original.s3Key === file.s3Key || original.uniqueId === file.uniqueId
        );

        // Only cleanup files that are completed uploads and are new (not from existing event)
        return isNewUpload && file.status === 'completed' && file.s3Key && !file.isDeleted;
      });

      if (unsavedFiles.length > 0) {
        const fileKeys = unsavedFiles.map(file => file.s3Key).filter(Boolean) as string[];

        console.log('Cleaning up unsaved files:', fileKeys);

        // Call the cleanup API
        const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/workflow/cleanup-files`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ fileKeys }),
        });

        if (response.ok) {
          const result = await response.json();
          console.log('Cleanup result:', result);
        } else {
          console.error('Failed to cleanup files:', response.statusText);
        }
      }
    } catch (error) {
      console.error('Error during file cleanup:', error);
    }
  };

  // Enhanced onClose handler with cleanup
  const handleClose = async () => {
    await cleanupUnsavedFiles();
    onClose();
  };

  // Handle browser tab close/refresh with unsaved files
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (isOpen && uploadedFiles.length > 0) {
        const unsavedFiles = uploadedFiles.filter(file => {
          // Check if this is a newly uploaded file (not from existing event)
          const isNewUpload = !originalUploadedFiles.some(
            original => original.s3Key === file.s3Key || original.uniqueId === file.uniqueId
          );
          return isNewUpload && file.status === 'completed' && file.s3Key && !file.isDeleted;
        });

        if (unsavedFiles.length > 0) {
          // Attempt cleanup with sendBeacon for better reliability during page unload
          const fileKeys = unsavedFiles.map(file => file.s3Key).filter(Boolean) as string[];

          try {
            // Use sendBeacon for better reliability during page unload
            const data = JSON.stringify({ fileKeys });
            navigator.sendBeacon(
              `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/cleanup-files`,
              new Blob([data], { type: 'application/json' })
            );
          } catch (error) {
            console.error('Error during beacon cleanup:', error);
          }

          // Show warning message to user
          event.preventDefault();
          event.returnValue = 'You have unsaved uploaded files. Are you sure you want to leave?';
          return 'You have unsaved uploaded files. Are you sure you want to leave?';
        }
      }
    };

    if (isOpen) {
      window.addEventListener('beforeunload', handleBeforeUnload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeUnload);
      };
    }
  }, [isOpen, uploadedFiles, originalUploadedFiles]);

  // Add useEffect to reset uploadedFiles when modal opens or isNew changes
  useEffect(() => {
    if (isOpen) {
      if (isNew) {
        if (event?.files && event.files.length > 0) {
          // For new events with files (like duplicated events), populate with existing files
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const existingFiles: UploadedFile[] = event.files.map((file: any, index: number) => ({
            id: `existing-${index}`,
            name: file.name?.trim(), // Display name
            s3Key: file.key || file.s3Key, // Current S3 key
            originalS3Key: file.key || file.s3Key, // Original S3 key
            s3FileName: file.s3FileName || file.name, // S3 filename
            displayName: file.name?.trim(), // Display name for UI
            url: file.url || '', // File URL
            type: file.type || 'application/pdf', // File type
            size: file.size || 0, // File size
            status: 'completed' as const, // Mark as completed
            progress: 100,
            isDeleted: false,
            error: undefined,
            originalName: file.originalName || file.name, // Original filename
            uniqueId: file.uniqueId,
            isNameChanged: false,
          }));
          setUploadedFiles(existingFiles);
          setOriginalUploadedFiles(existingFiles);
        } else {
          setUploadedFiles([]);
          setOriginalUploadedFiles([]);
        }
      } else if (event?.files && event.files.length > 0) {
        // For existing events, populate with existing files
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const existingFiles: UploadedFile[] = event.files.map((file: any, index: number) => ({
          id: `existing-${index}`,
          name: file.name?.trim(), // Display name
          s3Key: file.key || file.s3Key, // Current S3 key
          originalS3Key: file.key || file.s3Key, // Original S3 key
          s3FileName: file.s3FileName || file.name, // S3 filename
          displayName: file.name?.trim(), // Display name for UI
          url: file.url || '', // File URL
          type: file.type || 'application/pdf', // File type
          size: file.size || 0, // File size
          status: 'completed' as const, // Mark as completed
          progress: 100,
          isDeleted: false,
          error: undefined,
          originalName: file.originalName || file.name, // Original filename
          uniqueId: file.uniqueId,
          isNameChanged: false,
        }));
        setUploadedFiles(existingFiles);
        setOriginalUploadedFiles(existingFiles);
      } else {
        // No existing files
        setUploadedFiles([]);
        setOriginalUploadedFiles([]);
      }
    }
  }, [isOpen, isNew, event?.files]);

  // Initialize initial values when event changes
  useEffect(() => {
    if (event) {
      // For edit mode, determine if subject/description are original user input or auto-generated
      // If the subject/description only contain client name and no other details,
      // they were likely auto-generated and should be treated as such
      const clientNamePart =
        event?.clientName?.split(' ')[0] + ' ' + event?.clientName?.split(' ')[1] || '';

      // Check if subject is just the client name or very basic
      const isSubjectBasic =
        !event.subject ||
        event.subject.trim() === '' ||
        event.subject.trim() === clientNamePart ||
        event.subject === generateSubject(event);

      // Check if description is just the client name or very basic
      const isDescriptionBasic =
        !event.description ||
        event.description.trim() === '' ||
        event.description.trim() === clientNamePart ||
        event.description === generateDescription(event);

      // Set initial values only if they appear to be genuine user input, not auto-generated
      setIntialEventDescription(!isDescriptionBasic ? event?.description || null : null);
      setIntialEventSubject(!isSubjectBasic ? event?.subject || null : null);

      // Smart detection: Check if current values differ from auto-generated ones
      // If they differ, it means user has manually edited them previously
      const autoGeneratedSubject =
        event.appointmentAction === 'Cancel' && event.appointmentToReschedule
          ? event.subject // For cancel appointments, we can't easily regenerate without async call
          : generateSubject(event);
      const autoGeneratedDescription = generateDescription(event);

      const isSubjectManuallyEdited = Boolean(
        event.subject &&
        event.subject.trim() !== '' &&
        event.subject !== autoGeneratedSubject &&
        !isSubjectBasic
      );

      const isDescriptionManuallyEdited = Boolean(
        event.description &&
        event.description.trim() !== '' &&
        event.description !== autoGeneratedDescription &&
        !isDescriptionBasic
      );

      // Set manual edit flags based on whether values differ from auto-generated
      setIsFieldManuallyEdited({
        subject: isSubjectManuallyEdited,
        description: isDescriptionManuallyEdited,
      });

      // Clean the event data to remove any unwanted auto-populated values
      const cleanedEvent = { ...event };

      // If this is an edit of an existing event and certain fields contain default/placeholder values,
      // reset them to empty to prevent displaying incorrect auto-filled data
      if (!isNew) {
        // Reset appointment action if it was auto-set to "New" but user originally left it blank
        if (cleanedEvent.appointmentAction === 'New' && !cleanedEvent.eventStatus) {
          cleanedEvent.appointmentAction = '';
        }

        // Reset meeting location if it appears to be auto-selected
        // (check if it contains values that suggest auto-population)
        if (
          cleanedEvent.meetingLocation &&
          (cleanedEvent.meetingLocation.includes('Virtual meeting link') ||
            cleanedEvent.meetingLocation.includes('Phone') ||
            cleanedEvent.meetingLocation.includes('Meeting in person') ||
            cleanedEvent.meetingLocation.includes('Virtual meeting link - Link TBD') ||
            cleanedEvent.meetingLocation.includes('Phone - Phone TBD') ||
            cleanedEvent.meetingLocation.includes('Meeting in person - Location TBD')) &&
          !cleanedEvent.meetingLink &&
          !cleanedEvent.phoneDetails &&
          !cleanedEvent.meetingAddress
        ) {
          // If meeting location is set but no corresponding details are filled, likely auto-populated
          cleanedEvent.meetingLocation = '';
        }
      }

      // Normalize client attendance selection from backend values
      cleanedEvent.clientAttendance = normalizeClientAttendance(
        cleanedEvent.clientAttendance,
        clientAttendanceOptions
      );

      setFormData(cleanedEvent);
      // Check if it's an all-day event
      setAllDay(event.allDay || false);
      setAllDayDeadLine(event.allDayDeadLine || true);

      // Initialize date states
      if (event.startDate) {
        const parsedStartDate = parseDateString(event.startDate);
        setStartDate(parsedStartDate);
      }

      if (event.endDate) {
        const parsedEndDate = parseDateString(event.endDate);
        setEndDate(parsedEndDate);
      }

      // Initialize time states - convert UTC times to local timezone for display
      if (event.startTime) {
        const currentDate = event.startDate || getCurrentDateInUserTimezone();
        // Convert UTC time to local timezone for display
        const localStartTime = convertTimeFromUTCReliable(event.startTime, currentDate);
        const parsedStartTime = parseTimeString(localStartTime);
        setStartTime(parsedStartTime);
      }

      if (event.endTime) {
        const currentDate = event.endDate || event.startDate || getCurrentDateInUserTimezone();
        // Convert UTC time to local timezone for display
        const localEndTime = convertTimeFromUTCReliable(event.endTime, currentDate);
        const parsedEndTime = parseTimeString(localEndTime);
        setEndTime(parsedEndTime);
      }
    }
  }, [event, isNew, options]); // Remove memoized arrays from dependency to prevent infinite loops

  // Initialize default dates for new events in user's timezone
  useEffect(() => {
    if (isOpen && isNew && formData) {
      // Check if we need to set default dates (for new events or when dates are missing/invalid)
      const needsDefaultStartDate = !startDate || isNaN(startDate.getTime());
      const needsDefaultEndDate = !endDate || isNaN(endDate.getTime());

      if (needsDefaultStartDate || needsDefaultEndDate) {
        const currentDateInUserTimezone = getCurrentDateObjectInUserTimezone();
        const currentDateString = getCurrentDateInUserTimezone();

        if (needsDefaultStartDate) {
          setStartDate(currentDateInUserTimezone);
        }

        if (needsDefaultEndDate) {
          setEndDate(currentDateInUserTimezone);
        }

        // Update formData with timezone-aware dates
        setFormData(prev => {
          if (!prev) return null;
          return {
            ...prev,
            startDate: needsDefaultStartDate ? currentDateString : prev.startDate,
            endDate: needsDefaultEndDate ? currentDateString : prev.endDate,
          };
        });

        console.log('🌍 Initialized default dates in user timezone:', {
          timezone: getUserTimezone(),
          date: currentDateString,
          dateObject: currentDateInUserTimezone
        });
      }
    }
  }, [isOpen, isNew, formData, startDate, endDate]);

  // Get options from API response or fallback to workflowData - memoized to prevent infinite loops
  const appointmentActions = useMemo(
    () => ensureOptionFormat(options?.appointmentActions),
    [options?.appointmentActions]
  );
  const counties = useMemo(() => ensureOptionFormat(options?.counties), [options?.counties]);
  const courtLocations = useMemo(() => {
    const formatted = ensureOptionFormat(options?.courtLocations);
    console.log('🏛️ Court Locations Options:', formatted);
    return formatted;
  }, [options?.courtLocations]);

  const clientAttendanceOptions = useMemo(
    () => ensureOptionFormat(options?.clientAttendanceOptions),
    [options?.clientAttendanceOptions]
  );
  const meetingLocations = useMemo(
    () => ensureOptionFormat(options?.meetingLocations),
    [options?.meetingLocations]
  );

  const courtNoticeActions = useMemo(
    () => ensureOptionFormat(options?.courtNoticeActions),
    [options?.courtNoticeActions]
  );

  // Normalize backend client attendance values to valid dropdown options
  const normalizeClientAttendance = (
    value: string | undefined,
    availableOptions: OptionItem[]
  ): string => {
    const raw = (value || '').trim();
    if (raw === '') return '';

    const lower = raw.toLowerCase();

    const findByIncludes = (needle: string): string | null => {
      const match = availableOptions.find(opt => {
        const t = (opt.text || '').trim().toLowerCase();
        const v = (opt.value || '').trim().toLowerCase();
        return t.includes(needle) || v.includes(needle);
      });
      return match ? match.text : null;
    };

    if (lower === 'required') {
      return findByIncludes('client must appear') || 'Client must appear';
    }

    if (lower === 'tbd') {
      return findByIncludes('appearance tbd') || findByIncludes('tbd') || 'Appearance TBD';
    }

    const exact = availableOptions.find(opt => {
      const t = (opt.text || '').trim().toLowerCase();
      const v = (opt.value || '').trim().toLowerCase();
      return t === lower || v === lower;
    });
    if (exact) return exact.text;

    return raw;
  };

  // Validation function to check if a value exists in dropdown options using EXACT matching only
  const validateLocationField = (
    value: string,
    availableOptions: OptionItem[],
    fieldName: string
  ): string => {
    if (!value || !availableOptions || availableOptions.length === 0) {
      return '';
    }

    // Normalize the input value for comparison (remove extra spaces, convert to lowercase)
    const normalizedValue = value.trim().toLowerCase();

    // Skip validation for empty, placeholder, or "select" type values
    if (
      normalizedValue === '' ||
      normalizedValue.toLowerCase().includes('select') ||
      normalizedValue.toLowerCase().includes('choose') ||
      normalizedValue.toLowerCase().includes('pick')
    ) {
      return '';
    }

    // Check if the value exists in the dropdown options using flexible matching
    const matchingOption = availableOptions.find(option => {
      const normalizedOptionValue = (option.value || '').trim().toLowerCase();
      const normalizedOptionText = (option.text || '').trim().toLowerCase();

      // Skip empty options and placeholder options
      if (
        normalizedOptionValue === '' ||
        normalizedOptionText === '' ||
        normalizedOptionText.toLowerCase().includes('select') ||
        normalizedOptionText.toLowerCase().includes('choose') ||
        normalizedOptionText.toLowerCase().includes('pick')
      ) {
        return false;
      }

      // ONLY exact match (case-insensitive, trimmed) - no partial matching
      if (normalizedOptionValue === normalizedValue || normalizedOptionText === normalizedValue) {
        console.log(`🎯 EXACT MATCH FOUND: "${value}" matches "${option.text}"`);
        return true;
      }

      return false;
    });

    // Log for debugging
    if (matchingOption) {
      console.log(
        `✅ ${fieldName} value "${value}" matched with option: "${matchingOption.text}" (${matchingOption.value})`
      );
      // Return the exact option text for consistency (since ensureOptionFormat makes value = text)
      return matchingOption.text;
    } else {
      console.log(
        `⚠️ ${fieldName} value "${value}" not found in dropdown options. Setting to empty string.`
      );
      console.log(
        `Available ${fieldName} options:`,
        availableOptions.map(opt => ({ value: opt.value, text: opt.text }))
      );
      // Return empty string to ensure no option is selected when no match is found
      return '';
    }
  };

  // Function to fetch appointments

  // Log options for debugging
  useEffect(() => {
    if (options) {
      console.log('Using options from API:', options);
    } else {
      console.log('Using fallback options from workflowsData');
    }
  }, [options]);

  // Initialize form data when event changes
  useEffect(() => {
    if (event) {
      // Clean the event data to remove any unwanted auto-populated values
      const cleanedEvent = { ...event };

      // If this is an edit of an existing event and certain fields contain default/placeholder values,
      // reset them to empty to prevent displaying incorrect auto-filled data
      if (!isNew) {
        // Reset appointment action if it was auto-set to "New" but user originally left it blank
        if (cleanedEvent.appointmentAction === 'New' && !cleanedEvent.eventStatus) {
          cleanedEvent.appointmentAction = '';
        }

        // Reset meeting location if it appears to be auto-selected
        // (check if it contains values that suggest auto-population)
        // if (
        //   cleanedEvent.meetingLocation &&
        //   (cleanedEvent.meetingLocation.includes('Virtual meeting link') ||
        //     cleanedEvent.meetingLocation.includes('Phone') ||
        //     cleanedEvent.meetingLocation.includes('Meeting in person') ||
        //     cleanedEvent.meetingLocation.includes('Virtual meeting link - Link TBD') ||
        //     cleanedEvent.meetingLocation.includes('Phone - Phone TBD') ||
        //     cleanedEvent.meetingLocation.includes('Meeting in person - Location TBD')) &&
        //   !cleanedEvent.meetingLink &&
        //   !cleanedEvent.phoneDetails &&
        //   !cleanedEvent.meetingAddress
        // ) {
        //   // If meeting location is set but no corresponding details are filled, likely auto-populated
        //   cleanedEvent.meetingLocation = '';
        // }
      }

      // Validate location fields against dropdown options
      // This ensures that values from the backend API exist in the frontend dropdown options
      if (options) {
        console.log(
          '🔍 VALIDATION - Original court location from Python:',
          cleanedEvent.courtLocation
        );
        console.log('🔍 VALIDATION - Available court location options:', courtLocations);

        cleanedEvent.courtLocation = validateLocationField(
          cleanedEvent.courtLocation || '',
          courtLocations || [],
          'courtLocation'
        );

        console.log(
          '🔍 VALIDATION - Final court location after validation:',
          cleanedEvent.courtLocation
        );

        cleanedEvent.meetingLocation = validateLocationField(
          cleanedEvent.meetingLocation || '',
          meetingLocations || [],
          'meetingLocation'
        );
      }

      // Normalize client attendance selection from backend values
      cleanedEvent.clientAttendance = normalizeClientAttendance(
        cleanedEvent.clientAttendance,
        clientAttendanceOptions
      );

      setFormData(cleanedEvent);
      // Check if it's an all-day event
      setAllDay(event.allDay || false);
      setAllDayDeadLine(event.allDayDeadLine || true);

      // Initialize date states
      if (event.startDate) {
        const parsedStartDate = parseDateString(event.startDate);
        setStartDate(parsedStartDate);
      }

      if (event.endDate) {
        const parsedEndDate = parseDateString(event.endDate);
        setEndDate(parsedEndDate);
      }

    }
  }, [event, isNew, options]); // Remove memoized arrays from dependency to prevent infinite loops

  console.log('🚀 ~ formData:', formData);

  // Handle initial population in edit mode - set original values then allow auto-updating
  useEffect(() => {
    if (!isNew && formData && intialEventSubject && intialEventDescription) {
      // Small delay to ensure form is fully initialized
      const timer = setTimeout(() => {
        console.log('Setting initial values for edit mode');
        setFormData(prev => {
          if (!prev) return null;
          return {
            ...prev,
            subject: intialEventSubject,
            description: intialEventDescription,
          };
        });

        // Also set the manual edit flags to true since we're setting manually edited values
        setIsFieldManuallyEdited(prev => ({
          ...prev,
          subject: Boolean(intialEventSubject),
          description: Boolean(intialEventDescription),
        }));
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [isNew, formData?.id, intialEventSubject, intialEventDescription]);

  // Reset isFieldManuallyEdited flags when relevant fields change
  useEffect(() => {
    if (!formData) return;

    if (isFirstRender.current < 4) {
      return;
    }

    // Reset subject manual edit flag when subject-related fields change
    setIsFieldManuallyEdited(prev => ({
      ...prev,
      subject: false,
      description: false,
    }));
  }, [
    formData?.clientName,
    formData?.charge,
    formData?.county,
    formData?.caseNumber,
    formData?.clientAttendance,
    formData?.courtNoticeType,
    formData?.courtNoticeActions,
    formData?.appointmentAction,
    formData?.appointmentToReschedule,
  ]);

  // Reset description manual edit flag when description-related fields change
  useEffect(() => {
    if (!formData) return;

    // Reset description manual edit flag when description-related fields change
    setIsFieldManuallyEdited(prev => ({
      ...prev,
      description: false,
    }));
  }, [
    formData?.courtNoticeType,
    formData?.courtNoticeActions,
    formData?.clientName,
    formData?.charge,
    formData?.county,
    formData?.caseNumber,
    formData?.clientAttendance,
    formData?.meetingLocation,
    formData?.meetingLink,
    formData?.phoneDetails,
    formData?.meetingAddress,
  ]);

  useEffect(() => {
    isFirstRender.current = isFirstRender.current + 1;

    console.log(
      isFieldManuallyEdited.subject,
      isFirstRender.current,
      'dhyey isFieldManuallyEdited.subject'
    );

    // Don't auto-update if user has manually edited the subject
    if (isFieldManuallyEdited.subject || !formData) {
      return;
    }

    // Handle Cancel appointment case with async subject generation
    if (formData.appointmentAction === 'Cancel' && formData.appointmentToReschedule) {
      generateCancelSubject(formData).then(newSubject => {
        if (newSubject !== formData.subject) {
          setFormData(prev => {
            if (!prev) return null;
            return { ...prev, subject: newSubject };
          });
        }
      });
      return;
    }

    // Handle normal case with synchronous subject generation
    const newSubject = generateSubject(formData);
    if (newSubject !== formData.subject) {
      setFormData(prev => {
        if (!prev) return null;
        return { ...prev, subject: newSubject };
      });
    }
  }, [
    isFieldManuallyEdited.subject,
    formData?.clientName,
    formData?.charge,
    formData?.county,
    formData?.caseNumber,
    formData?.clientAttendance,
    formData?.courtNoticeType,
    formData?.courtNoticeActions,
    formData?.appointmentAction,
    formData?.appointmentToReschedule,
  ]);

  // Update description field when meeting location details change
  useEffect(() => {
    // Don't auto-update if user has manually typed in the field
    if (isFieldManuallyEdited.description || !formData) {
      return;
    }

    const newDescription = generateDescription(formData);

    if (newDescription !== formData.description) {
      setFormData(prev => {
        if (!prev) return null;
        return { ...prev, description: newDescription };
      });
    }
  }, [
    isFieldManuallyEdited.description,
    formData?.courtNoticeType,
    formData?.courtNoticeActions,
    formData?.clientName,
    formData?.charge,
    formData?.county,
    formData?.caseNumber,
    formData?.clientAttendance,
    formData?.meetingLocation,
    formData?.meetingLink,
    formData?.phoneDetails,
    formData?.meetingAddress,
  ]);

  // With this modified version:
  useEffect(() => {
    if (isOpen) {
      if (isNew) {
        if (event?.files && event.files.length > 0) {
          // For new events with files (like duplicated events), populate with existing files
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const existingFiles: UploadedFile[] = event.files.map((file: any, index: number) => ({
            id: `existing-${index}`,
            name: file.name?.trim(), // Display name
            s3Key: file.key || file.s3Key, // Current S3 key
            originalS3Key: file.key || file.s3Key, // Original S3 key
            s3FileName: file.s3FileName || file.name, // S3 filename
            displayName: file.name?.trim(), // Display name for UI
            url: file.url || '', // File URL
            type: file.type || 'application/pdf', // File type
            size: file.size || 0, // File size
            status: 'completed' as const, // Mark as completed
            progress: 100,
            isDeleted: false,
            error: undefined,
            originalName: file.originalName || file.name, // Original filename
            uniqueId: file.uniqueId,
            isNameChanged: false,
          }));
          setUploadedFiles(existingFiles);
          setOriginalUploadedFiles(existingFiles);
        } else {
          setUploadedFiles([]);
          setOriginalUploadedFiles([]);
        }
        // For new events, reset to empty array
      } else if (event?.files && event.files.length > 0) {
        // For existing events, populate with existing files
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const existingFiles: UploadedFile[] = event.files.map((file: any, index: number) => ({
          id: `existing-${index}`,
          name: file.name?.trim(), // Display name
          s3Key: file.key || file.s3Key, // Current S3 key
          originalS3Key: file.key || file.s3Key, // Original S3 key
          s3FileName: file.s3FileName || file.name, // S3 filename
          displayName: file.name?.trim(), // Display name for UI
          url: file.url || '', // File URL
          type: file.type || 'application/pdf', // File type
          size: file.size || 0, // File size
          status: 'completed' as const, // Mark as completed
          progress: 100,
          isDeleted: false,
          error: undefined,
          originalName: file.originalName || file.name, // Original filename
          uniqueId: file.uniqueId,
          isNameChanged: false,
        }));
        setUploadedFiles(existingFiles);
        setOriginalUploadedFiles(existingFiles);
      } else {
        // No existing files
        setUploadedFiles([]);
        setOriginalUploadedFiles([]);
      }
    }
  }, [isOpen, isNew, event?.files]);

  // Function to format events to options
  const formatEventsToOptions = (responseData: Events[]) => {
    if (!responseData || responseData.length === 0) {
      return [];
    }

    // Create options array by iterating through events
    const options = responseData.map((event: Events) => {
      // Safely handle undefined values
      const startDate = event.startDate || event.date || '';
      const endDate = event.endDate || event.startDate || event.date || '';
      const startTime = event.startTime || '';
      const endTime = event.endTime || '';
      const subject = event.subject || 'Untitled Event';

      // Format start and end date/time only if we have valid data
      const formattedStartDateTime =
        startDate && startTime ? `${formatDates(startDate)} ${convertTo12HourTime(startTime)}` : '';
      const formattedEndDateTime =
        endDate && endTime ? `${formatDates(endDate)} ${convertTo12HourTime(endTime)}` : '';

      const formattedDateRange =
        formattedStartDateTime && formattedEndDateTime
          ? `${formattedStartDateTime} to ${formattedEndDateTime}`
          : formattedStartDateTime;

      return {
        text: formattedDateRange ? `${subject} | ${formattedDateRange}` : subject,
        value: event.id || event._id || '',
        workflow_id: '',
      };
    });

    return options;
  };

  // Helper: Convert "14:00" to "2:00 PM"
  const convertTo12HourTime = (time: string) => {
    try {
      // Return empty string if time is empty or invalid
      if (!time || typeof time !== 'string') {
        return '';
      }

      // Check if time is in valid format (HH:MM)
      const timeRegex = /^\d{1,2}:\d{2}$/;
      if (!timeRegex.test(time)) {
        console.warn('Invalid time format:', time);
        return '';
      }

      const [hour, minute] = time.split(':').map(Number);

      // Validate hour and minute values
      if (isNaN(hour) || isNaN(minute) || hour < 0 || hour > 23 || minute < 0 || minute > 59) {
        console.warn('Invalid time values:', time);
        return '';
      }

      const ampm = hour >= 12 ? 'PM' : 'AM';
      const hour12 = hour % 12 || 12;
      return `${hour12}:${minute.toString().padStart(2, '0')} ${ampm}`;
    } catch (e) {
      console.error('Error converting time:', e, time);
      return '';
    }
  };

  // Helper: Safely parse time string and return Date object or null
  const parseTimeString = (timeString: string): Date | null => {
    try {
      if (!timeString || typeof timeString !== 'string') {
        return null;
      }

      // Validate time format before parsing
      const timeRegex = /^\d{1,2}:\d{2}$/;
      if (!timeRegex.test(timeString)) {
        console.warn('Invalid time format:', timeString);
        return null;
      }

      const [hours, minutes] = timeString.split(':').map(Number);

      // Validate time values
      if (
        isNaN(hours) ||
        isNaN(minutes) ||
        hours < 0 ||
        hours > 23 ||
        minutes < 0 ||
        minutes > 59
      ) {
        console.warn('Invalid time values:', timeString);
        return null;
      }

      const date = new Date();
      date.setHours(hours);
      date.setMinutes(minutes);
      return date;
    } catch (e) {
      console.error('Error parsing time:', e, timeString);
      return null;
    }
  };

  // Helper: Safely parse date string and return Date object or null
  const parseDateString = (dateString: string): Date | null => {
    return parseDateInUserTimezone(dateString);
  };

  // Helper: Format date from "2025-06-27" to "06/27/2025"
  const formatDates = (dateString: string) => {
    return formatDateInUserTimezone(dateString);
  };

  // Helper: Get selected action value (use .value, not .text, for logic)
  const selectedAction = courtNoticeActions.find(
    a => a.text === formData?.courtNoticeActions
  )?.value;
  console.log('🚀 ~ selectedAction:', selectedAction);

  const selectedAppointmentActions = appointmentActions.find(
    a => a.text === formData?.appointmentAction
  )?.value;
  console.log('🚀 ~ selectedAppointmentActions:', selectedAppointmentActions);

  const fetchAppointments = async () => {
    try {
      // Try multiple sources for client_matter_id with comprehensive fallback
      const client_matter_id =
        event?.client_matter_id || // Direct from event
        selectedMatter?.matter_id || // From selected matter object
        selectedMatter?._id || // Fallback from selected matter
        event?.selectedMatterId || // Original fallback
        clientId; // Current matter ID being edited

      if (!client_matter_id) {
        console.error('❌ No client_matter_id found from any source!');
        setErrors(prev => ({
          ...prev,
          appointmentToReschedule: 'No client matter ID found - cannot load appointments',
        }));
        return;
      }

      const apiUrl = `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/appointments?workflow_id=${workflow_id}&client_matter_id=${client_matter_id}`;
      console.log('🚀 ~ fetchAppointments ~ API URL:', apiUrl);

      const response = await axios.get(apiUrl);

      console.log('🚀 ~ fetchAppointments ~ response:', response.data);

      // Use the formatting function to transform the API response
      const appointmentOptions = formatEventsToOptions(response.data.data);

      console.log('🚀 ~ formatted appointmentOptions:', appointmentOptions);

      // Set the formatted options to state
      setRescheduleAppointments(appointmentOptions);
    } catch (error) {
      console.error('❌ Error fetching appointments:', error);
      setErrors(prev => ({
        ...prev,
        appointmentToReschedule: 'Failed to load appointments',
      }));
    } finally {
      // setIsLoadingAppointments(false);
    }
  };
  // Effect to fetch appointments when Reschedule is selected
  useEffect(() => {
    if (selectedAppointmentActions === 'Reschedule' || selectedAppointmentActions === 'Cancel') {
      fetchAppointments();
    } else {
      // Clear appointments when not rescheduling
      setRescheduleAppointments([]);
      setFormData(prev => {
        if (!prev) return null;
        return {
          ...prev,
          rescheduleAppointment: '',
        };
      });
    }
  }, [selectedAppointmentActions]);

  // Effect to handle first-time initialization when editing events
  useEffect(() => {
    if (titleOfEvent === 'Edit event' && event && !isInitializedRef.current) {
      isInitializedRef.current = true;
      setAllDay(event?.allDay || false);
      setAllDayDeadLine(false);
    }
  }, [titleOfEvent, event]);

  // Reset initialization ref when modal opens/closes or event changes
  useEffect(() => {
    if (isOpen && event) {
      isInitializedRef.current = false;
    }
  }, [isOpen, event?.id]);

  // Effect to automatically enable "All day" toggle when Calendar DEADLINE is selected
  useEffect(() => {
    if (selectedAction === 'Calendar DEADLINE and save court notice') {
      if (titleOfEvent == 'Edit event' && !isInitializedRef.current) {
        isInitializedRef.current = true;
        setAllDay(event?.allDay || false);
        setAllDayDeadLine(false);
      } else {
        setAllDay(true);
        setAllDayDeadLine(true);
        // Set start time to midnight (00:00) and end time to midnight same day (00:00)
        const startOfDay = new Date();
        startOfDay.setHours(0, 0, 0, 0);
        setStartTime(startOfDay);

        const endOfDay = new Date();
        endOfDay.setHours(0, 0, 0, 0);
        setEndTime(endOfDay);

        // Set end date to same day as start date
        if (startDate) {
          setEndDate(startDate);
        }

        setFormData(prev => {
          if (!prev) return null;

          const sameDayFormatted = startDate ? formatDateToTimezoneString(startDate) : prev.endDate;

          return {
            ...prev,
            allDay: true,
            startTime: '00:00',
            endTime: '00:00',
            endDate: sameDayFormatted,
            appointmentToReschedule: event?.appointmentToReschedule,
          };
        });
      }
    }
  }, [selectedAction, startDate]);

  // Effect to reset "All day" toggle when Calendar event is selected
  useEffect(() => {
    if (selectedAction === 'Calendar event and save court notice') {
      if (titleOfEvent == 'Edit event' && !isInitializedRef.current) {
        isInitializedRef.current = true;
        setAllDay(event?.allDay || false);
        setAllDayDeadLine(false);
      } else {
        setAllDay(false);
        setAllDayDeadLine(false);
      }

      setFormData(prev => {
        if (!prev) return null;

        const sameDayFormatted = startDate ? formatDateToTimezoneString(startDate) : prev.startDate;

        return {
          ...prev,
          allDay: false,
          endDate: sameDayFormatted,
        };
      });
    }
  }, [selectedAction, startDate]);

  // Effect to ensure end date matches start date when all day is enabled
  useEffect(() => {
    if (allDay && startDate) {
      // When all day is enabled, always ensure end date matches start date
      setEndDate(startDate);

      setFormData(prev => {
        if (!prev) return null;

        const sameDayFormatted = formatDateToTimezoneString(startDate);

        return {
          ...prev,
          endDate: sameDayFormatted,
        };
      });
    }
  }, [allDay, startDate]);

  // Clear fields that are not visible when Calendar DEADLINE option is selected
  useEffect(() => {
    if (selectedAction === 'Calendar DEADLINE and save court notice') {
      setFormData(prev => {
        if (!prev) return null;
        return {
          ...prev,
          // Clear fields that are not shown in deadline view
          charge: '',
          // county: '', // Removed - county field is actually shown in deadline view
          courtLocation: '',
          clientAttendance: '',
          meetingLocation: '',
          meetingLink: '',
          phoneDetails: '',
          meetingAddress: '',
        };
      });
    }
  }, [selectedAction]);

  // Add new useEffect to handle court notice action changes and initialize required fields
  useEffect(() => {
    if (!formData || !selectedAction) return;

    setFormData(prev => {
      if (!prev) return null;

      const updates: Partial<EventType> = {};

      // Handle different court notice actions
      switch (selectedAction) {
        case 'Save court notice only':
          // Clear appointment-related fields for save-only option
          if (prev.appointmentAction) {
            updates.appointmentAction = '';
          }
          updates.eventStatus = 'New';
          break;

        case 'Calendar event and save court notice':
        case 'Calendar DEADLINE and save court notice':
          // Only initialize appointmentAction to "New" if it's a new event AND the field is empty
          // Don't auto-set for existing events being edited
          if (isNew && (!prev.appointmentAction || prev.appointmentAction === '')) {
            updates.appointmentAction = 'New';
            updates.eventStatus = 'New';
          }
          break;

        default:
          break;
      }

      // Only update if there are actual changes to prevent unnecessary re-renders
      const hasChanges = Object.keys(updates).some(
        key => prev[key as keyof EventType] !== updates[key as keyof EventType]
      );

      if (hasChanges) {
        return { ...prev, ...updates };
      }

      return prev;
    });
  }, [selectedAction, isNew]); // Add isNew to dependency array

  const renderFields = () => {
    // Helper function to check if all fields below appointment selection should be hidden
    const shouldHideFieldsBelowAppointmentSelection = () => {
      return selectedAppointmentActions === 'Cancel';
    };

    // Helper function to render appointment selection field
    const renderAppointmentSelectionField = (actionType: 'Reschedule' | 'Cancel') => {
      const labelText =
        actionType === 'Reschedule'
          ? 'Select the appointment to be modified'
          : 'Select the appointment to be modified';

      return (
        <div className="mb-[20px]">
          <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">{labelText}</label>
          <CustomSelectBasedOnValue
            name="appointmentToReschedule"
            value={formData?.appointmentToReschedule || ''}
            disabled={
              isTaskReviewed ||
              (titleOfEvent === 'Edit event' && (!isEditMode || isChildWorkflow)) ||
              isChildWorkflow
            }
            onChange={handleChange}
            options={
              rescheduleAppointments && rescheduleAppointments.length > 0
                ? rescheduleAppointments.map(item => ({
                  ...item,
                  value: item.value ?? '', // ensure value is a string
                }))
                : [{ text: 'No records found', value: '' }]
            }
            hasError={!!errors.appointmentOptionItemToReschedule}
          />
          {errors.appointmentToReschedule && (
            <p className="text-red-500 text-xs mt-1">{errors.appointmentToReschedule}</p>
          )}
        </div>
      );
    };

    // Helper function to render common form fields (used by both calendar event and deadline)
    const renderCommonFormFields = () => {
      if (shouldHideFieldsBelowAppointmentSelection()) {
        return null;
      }

      return (
        <>
          <div className="mb-[20px] relative">
            <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">Subject</label>

            <textarea
              name="subject"
              value={formData?.subject}
              rows={3}
              onChange={handleChange}
              placeholder="Enter subject"
              disabled={(titleOfEvent == 'Edit event' && !isEditMode && !isChildWorkflow) || isTaskReviewed}
              className={`w-full p-4 pr-10 border font-normal placeholder:text-[#5F6F84] placeholder:font-normal focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6] text-[#2a2e34] rounded-[12px] ${errors.subject ? 'border-red-500' : 'border-[#DCE2EB]'}`}
            />

            <button
              type="button"
              onClick={handleSubjectReset}
              className="absolute right-4 bottom-[16px]"
            >
              <Image
                src="/assets/resetIcon.svg"
                alt="Reset subject"
                width={20}
                height={20}
                className="hover:opacity-80"
              />
            </button>

            {errors.subject && <p className="text-red-500 text-xs mt-1">{errors.subject}</p>}
          </div>

          <div className="mb-4 relative">
            <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">Description</label>

            <textarea
              name="description"
              value={formData?.description || ''}
              placeholder="Enter description"
              onChange={handleChange}
              disabled={(titleOfEvent == 'Edit event' && !isEditMode && !isChildWorkflow) || isTaskReviewed}
              rows={3}
              className={`w-full p-4 pr-10 text-[#2a2e34] placeholder:text-[#5F6F84] placeholder:font-normal border focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6] rounded-[12px] ${errors.description ? 'border-red-500' : 'border-[#DCE2EB]'}`}
            />

            <button
              type="button"
              onClick={handleDescriptionReset}
              className="absolute right-4 bottom-[16px]"
            >
              <Image
                src="/assets/resetIcon.svg"
                alt="Reset description"
                width={20}
                height={20}
                className="hover:opacity-80"
              />
            </button>

            {errors.description && (
              <p className="text-red-500 text-xs mt-1">{errors.description}</p>
            )}
          </div>

          {/* Horizontal Line */}
          <hr className="border-t border-[#C7D1DF] mb-4" />

          <div className="mb-[20px]">
            <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">
              Court notice type
            </label>
            <input
              type="text"
              name="courtNoticeType"
              value={formData?.courtNoticeType || ''}
              onChange={handleChange}
              placeholder="Enter court notice type"
              disabled={isTaskReviewed || (titleOfEvent == 'Edit event' && !isEditMode && !isChildWorkflow)}
              className={`w-full p-3 border font-normal text-[#2a2e34] rounded-[12px]
                focus:outline-none focus:ring-2 placeholder:text-[#5F6F84] focus:ring-blue-200 focus:border-[#3F73F6]
                ${errors.charge ? 'border-red-500' : 'border-[#DCE2EB]'}`}
            />
            {errors.courtNoticeType && (
              <p className="text-red-500 text-xs mt-1">{errors.courtNoticeType}</p>
            )}
          </div>
        </>
      );
    };

    // Helper function to render common form fields (used by both calendar event and deadline)
    const render2ndCommonFormFields = () => {
      if (shouldHideFieldsBelowAppointmentSelection()) {
        return null;
      }

      return (
        <>
          {selectedAction === 'Calendar DEADLINE and save court notice' && (
            <div className="mb-[20px]">
              <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">
                Case number
              </label>
              <input
                type="text"
                name="caseNumber"
                // disabled={(titleOfEvent == 'Edit event' && !isEditMode) || isTaskReviewed}
                disabled={true}
                value={formData?.caseNumber}
                onChange={handleChange}
                className={`w-full p-3 border font-normal bg-[#F3F5F9] rounded-[12px]
                text-[#5F6F84]
                focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6]
                ${errors.charge ? 'border-red-500' : 'border-[#DCE2EB]'}`}
              />
              {errors.caseNumber && (
                <p className="text-red-500 text-xs mt-1">{errors.caseNumber}</p>
              )}
            </div>
          )}
          <div className="mb-[20px]">
            <div className="flex items-center justify-between">
              <div className="">
                <div className="grid grid-cols-2 gap-3 mb-[20px]">
                  <div>
                    <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">
                      Start date
                    </label>
                    <CustomDatePicker
                      selected={startDate}
                      onChange={date => handleDateChange(date, 'startDate')}
                      name="startDate"
                      hasError={!!errors.startDate}
                      disabled={(titleOfEvent == 'Edit event' && !isEditMode && !isChildWorkflow) || isTaskReviewed}
                    />
                    {errors.startDate && (
                      <p className="text-red-500 text-xs mt-1">{errors.startDate}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">
                      Start time
                    </label>
                    <CustomTimePicker
                      selected={startTime}
                      onChange={time => handleTimeChange(time, 'startTime')}
                      name="startTime"
                      hasError={!!errors.startTime}
                      disabled={
                        isTaskReviewed
                          ? true
                          : titleOfEvent === 'Edit event'
                            ? isEditMode
                              ? allDay
                              : !isChildWorkflow
                            : allDay
                      }
                      maxTime={getMaxEndTime()}
                    />
                    {!allDay && errors.startTime && (
                      <p className="text-red-500 text-xs mt-1">{errors.startTime}</p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-3 mb-[20px]">
                  <div>
                    <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">
                      End date
                    </label>
                    <CustomDatePicker
                      selected={endDate}
                      onChange={date => handleDateChange(date, 'endDate')}
                      name="endDate"
                      hasError={!!errors.endDate}
                      minDate={startDate}
                      disabled={(titleOfEvent == 'Edit event' && !isEditMode && !isChildWorkflow) || isTaskReviewed}
                    />
                    {errors.endDate && (
                      <p className="text-red-500 text-xs mt-1">{errors.endDate}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">
                      End time
                    </label>
                    <CustomTimePicker
                      selected={endTime}
                      onChange={time => handleTimeChange(time, 'endTime')}
                      name="endTime"
                      hasError={!!errors.endTime}
                      disabled={
                        isTaskReviewed
                          ? true
                          : titleOfEvent == 'Edit event'
                            ? isEditMode
                              ? allDay
                              : !isChildWorkflow
                            : allDay
                      }
                      minTime={getMinEndTime()}
                      maxTime={getMaxEndTime()}
                    />
                    {!allDay && errors.endTime && (
                      <p className="text-red-500 text-xs mt-1">{errors.endTime}</p>
                    )}
                  </div>
                </div>
              </div>

              <div className="h-[120px]">
                <div className="flex items-start space-x-2">
                  <div
                    className={`w-[39px] h-[20px] rounded-full p-0.5 flex items-center transition-colors duration-300 ${titleOfEvent == 'Edit event'
                      ? titleOfEvent == 'Edit event' && isEditMode
                        ? `${allDay ? 'bg-[#3F73F6]' : 'bg-[#5F6F84]'} cursor-pointer`
                        : 'bg-gray-300 cursor-not-allowed opacity-50'
                      : `${allDay ? 'bg-[#3F73F6]' : 'bg-[#5F6F84]'} cursor-pointer`
                      }`}
                    onClick={() => {
                      if (isTaskReviewed || (titleOfEvent == 'Edit event' && !isEditMode && !isChildWorkflow)) return; // Prevent click if not in edit mode

                      const newValue = !allDay;
                      setAllDay(newValue);

                      if (newValue) {
                        // When enabling all day, set start time to 00:00 and end time to 00:00 same day
                        const startOfDay = new Date();
                        startOfDay.setHours(0, 0, 0, 0);
                        setStartTime(startOfDay);

                        const endOfDay = new Date();
                        endOfDay.setHours(0, 0, 0, 0);
                        setEndTime(endOfDay);

                        // Set end date to same day as start date
                        if (startDate) {
                          setEndDate(startDate);
                        }
                      }

                      setFormData(prev => {
                        if (!prev) return null;
                        if (newValue) {
                          // All day enabled: end date same as start date
                          const sameDayFormatted = startDate
                            ? formatDateToTimezoneString(startDate)
                            : prev.endDate;

                          return {
                            ...prev,
                            allDay: newValue,
                            startTime: '00:00',
                            endTime: '00:00',
                            endDate: sameDayFormatted,
                          };
                        } else {
                          // All day disabled: keep end date same as start date
                          const sameDayFormatted = startDate
                            ? formatDateToTimezoneString(startDate)
                            : prev.startDate;

                          return {
                            ...prev,
                            allDay: newValue,
                            endDate: sameDayFormatted,
                          };
                        }
                      });
                    }}
                  >
                    <div
                      className={`bg-white w-4 h-4 rounded-full shadow-md transition-transform duration-300 ${titleOfEvent == 'Edit event' && !isEditMode ? 'opacity-75' : ''
                        }`}
                      style={{ transform: allDay ? 'translateX(20px)' : 'translateX(0)' }}
                    ></div>
                  </div>
                  <span
                    className={`text-sm font-medium ${titleOfEvent == 'Edit event'
                      ? titleOfEvent == 'Edit event' && isEditMode
                        ? 'text-[#2A2E34]'
                        : 'text-gray-400'
                      : allDay
                        ? 'text-[#2A2E34]'
                        : 'text-gray-400'
                      }`}
                  >
                    All day
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="mb-[20px]">
            <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">
              Required attendees
              <span className="text-[#5f6f84] font-normal ml-1">(Attend)</span>
            </label>
            <SearchableMultiSelectWithAPI
              name="requiredAttendees"
              value={formData?.requiredAttendees || ''}
              disabled={(titleOfEvent == 'Edit event' && !isEditMode && !isChildWorkflow) || isTaskReviewed}
              onChange={(name, value) => {
                setFormData(prev => {
                  if (!prev) return null;
                  return { ...prev, [name]: value };
                });
                // Clear error for this field
                if (errors.requiredAttendees) {
                  setErrors(prev => {
                    const newErrors = { ...prev };
                    delete newErrors.requiredAttendees;
                    return newErrors;
                  });
                }
              }}
              hasError={!!errors.requiredAttendees}
              placeholder="Select"
            />
            {errors.requiredAttendees && (
              <p className="text-red-500 text-xs mt-1">{errors.requiredAttendees}</p>
            )}
          </div>

          <div className="mb-[20px]">
            <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">
              Optional attendees
              <span className="text-[#5f6f84] font-normal ml-1">(Invite)</span>
            </label>
            <SearchableMultiSelectWithAPI
              name="optionalAttendees"
              value={formData?.optionalAttendees || ''}
              disabled={(titleOfEvent == 'Edit event' && !isEditMode && !isChildWorkflow) || isTaskReviewed}
              onChange={(name, value) => {
                setFormData(prev => {
                  if (!prev) return null;
                  return { ...prev, [name]: value };
                });
                // Clear error for this field
                if (errors.optionalAttendees) {
                  setErrors(prev => {
                    const newErrors = { ...prev };
                    delete newErrors.optionalAttendees;
                    return newErrors;
                  });
                }
              }}
              hasError={!!errors.optionalAttendees}
              placeholder="Select"
            />
            {errors.optionalAttendees && (
              <p className="text-red-500 text-xs mt-1">{errors.optionalAttendees}</p>
            )}
          </div>

          {selectedAction === 'Calendar event and save court notice' && (
            <>
              {/* Client Attendance */}
              <div className="mb-[20px]">
                <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">
                  Client attendance
                </label>
                <CustomSelect
                  name="clientAttendance"
                  value={formData?.clientAttendance || ''}
                  onChange={handleChange}
                  options={clientAttendanceOptions}
                  hasError={!!errors.clientAttendance}
                  disabled={(titleOfEvent == 'Edit event' && !isEditMode && !isChildWorkflow) || isTaskReviewed}
                />
                {errors.clientAttendance && (
                  <p className="text-red-500 text-xs mt-1">{errors.clientAttendance}</p>
                )}
              </div>

              {/* Meeting Location */}
              <div className="mb-[20px]">
                <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">
                  Meeting location
                </label>
                <CustomSelect
                  name="meetingLocation"
                  value={formData?.meetingLocation || ''}
                  onChange={handleChange}
                  options={meetingLocations}
                  hasError={!!errors.meetingLocation}
                  disabled={(titleOfEvent == 'Edit event' && !isEditMode && !isChildWorkflow) || isTaskReviewed}
                />
                {errors.meetingLocation && (
                  <p className="text-red-500 text-xs mt-1">{errors.meetingLocation}</p>
                )}

                {/* Conditional subfields based on meetingLocation value */}
                {formData?.meetingLocation && !isTbdValue(formData.meetingLocation) && (
                  <div className="mt-3">
                    {formData.meetingLocation.includes('Virtual meeting link') && (
                      <div>
                        <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">
                          Virtual meeting link
                        </label>
                        <textarea
                          name="meetingLink"
                          disabled={(titleOfEvent == 'Edit event' && !isEditMode && !isChildWorkflow) || isTaskReviewed}
                          value={formData.meetingLink || ''}
                          onChange={handleChange}
                          placeholder="Enter virtual meeting information"
                          className={`w-full px-[14px] py-[16px] border font-normal text-[#2a2e34] rounded-[12px]
                  focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6]
                  ${isTbdValue(formData.meetingLink || '') ? 'border-[#F6B175] ring-2 ring-[#F6B17533] text-[#B54708]' : 'border-[#DCE2EB]'}`}
                        />
                        {isTbdValue(formData.meetingLink || '') && (
                          <div className="text-[#B54708] text-xs font-medium mt-1 bg-[#FFFAEB] inline-block px-2 py-0.5 rounded">
                            TBD
                          </div>
                        )}
                      </div>
                    )}

                    {formData.meetingLocation.includes('Phone') && (
                      <div>
                        <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">
                          Phone information
                        </label>
                        <textarea
                          name="phoneDetails"
                          value={formData.phoneDetails || ''}
                          onChange={handleChange}
                          disabled={(titleOfEvent == 'Edit event' && !isEditMode && !isChildWorkflow) || isTaskReviewed}
                          placeholder="Enter phone number"
                          className={`w-full py-[16px] px-[14px] border font-normal placeholder:text-[#5F6F84] text-[#2a2e34] rounded-[12px]
                  focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6]
                  ${isTbdValue(formData.phoneDetails || '') ? 'border-[#F6B175] ring-2 ring-[#F6B17533] text-[#B54708]' : 'border-[#DCE2EB]'}`}
                        />
                        {isTbdValue(formData.phoneDetails || '') && (
                          <div className="text-xs font-medium mt-1 bg-[#FFFAEB] inline-block px-2 py-0.5 rounded">
                            TBD
                          </div>
                        )}
                      </div>
                    )}

                    {formData.meetingLocation.includes('Meeting in person') && (
                      <div>
                        <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">
                          Address information
                        </label>
                        <textarea
                          name="meetingAddress"
                          value={formData.meetingAddress || ''}
                          onChange={handleChange}
                          rows={2}
                          disabled={(titleOfEvent == 'Edit event' && !isEditMode && !isChildWorkflow) || isTaskReviewed}
                          placeholder="Enter meeting address or location name"
                          className={`w-full px-[14px] py-[16px] border font-normal text-[#2a2e34] rounded-[12px]
                  focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6]
                  ${isTbdValue(formData.meetingAddress || '') ? 'border-[#F6B175] ring-2 ring-[#F6B17533]' : 'border-[#DCE2EB]'}`}
                        />
                        {isTbdValue(formData.meetingAddress || '') && (
                          <div className=" text-xs font-medium mt-1 bg-[#FFFAEB] inline-block px-2 py-0.5 rounded">
                            TBD
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </>
          )}
        </>
      );
    };

    // Helper function to render full form fields (only for calendar event)
    const renderFullFormFields = () => {
      if (shouldHideFieldsBelowAppointmentSelection()) {
        return null;
      }

      return (
        <>
          <div className="mb-[20px]">
            <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">Charge</label>
            <input
              type="text"
              name="charge"
              value={formData?.charge}
              placeholder="Enter charge"
              onChange={handleChange}
              disabled={(titleOfEvent == 'Edit event' && !isEditMode && !isChildWorkflow) || isTaskReviewed}
              className={`w-full p-3 border font-normal text-[#2a2e34] rounded-[12px]
        focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6] placeholder:text-[#5F6F84]
        ${errors.charge ? 'border-red-500' : 'border-[#DCE2EB]'}`}
            />
            {errors.charge && <p className="text-red-500 text-xs mt-1">{errors.charge}</p>}
          </div>

          <div className="mb-[20px]">
            <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">
              EX county of arrest
            </label>
            <input
              type="text"
              name="county"
              value={
                formData?.county !== undefined || formData?.county !== 'undefined'
                  ? formData?.county
                  : ''
              }
              onChange={handleChange}
              // options={counties}
              // hasError={!!errors.county}
              // disabled={(titleOfEvent == 'Edit event' && !isEditMode) || isTaskReviewed}
              className={`w-full p-3 border font-normal bg-[#F3F5F9] rounded-[12px]
                text-[#5F6F84]
                focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6]
                ${errors.charge ? 'border-red-500' : 'border-[#DCE2EB]'}`}
              disabled={true}
            />
            {errors.county && <p className="text-red-500 text-xs mt-1">{errors.county}</p>}
          </div>

          <div className="mb-[20px]">
            <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">Case number</label>
            <input
              type="text"
              name="caseNumber"
              // disabled={(titleOfEvent == 'Edit event' && !isEditMode) || isTaskReviewed}
              disabled={true}
              value={formData?.caseNumber}
              onChange={handleChange}
              className={`w-full p-3 border font-normal bg-[#F3F5F9] rounded-[12px]
                text-[#5F6F84]
                focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6]
                ${errors.charge ? 'border-red-500' : 'border-[#DCE2EB]'}`}
            />
            {errors.caseNumber && <p className="text-red-500 text-xs mt-1">{errors.caseNumber}</p>}
          </div>

          <div className="mb-[20px]">
            <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">
              Court location
            </label>
            <CustomSelect
              name="courtLocation"
              value={formData?.courtLocation || ''}
              onChange={handleChange}
              options={courtLocations}
              disabled={(titleOfEvent == 'Edit event' && !isEditMode && !isChildWorkflow) || isTaskReviewed}
              hasError={!!errors.courtLocation}
            />
            {errors.courtLocation && (
              <p className="text-red-500 text-xs mt-1">{errors.courtLocation}</p>
            )}
          </div>
        </>
      );
    };

    // Helper function to render files field
    const renderFilesField = () => {
      // if (shouldHideFieldsBelowAppointmentSelection()) {
      //   return null;
      // }

      return (
        <div className="mb-[20px]">
          <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">Files</label>
          <FileUpload
            value={uploadedFiles}
            onChange={setUploadedFiles}
            allowedFileTypes={[
              'application/pdf',
              'application/msword',
              'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
              'application/vnd.ms-excel',
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
              'image/jpeg',
              'image/jpg',
              'image/png',
              'image/svg+xml',
              'image/tiff',
              'image/bmp',
              'image/gif',
            ]}
            maxFiles={50}
            path={'event/court-notice/'}
            courtNoticeType={formData?.courtNoticeType || ''}
            clientLastName={clientName || formData?.clientName?.split('|')[0]?.trim() || ''}
            actionType={selectedAction}
            startDate={startDate ? formatDateForFilename(startDate) : ''}
            disabled={isTaskReviewed || (titleOfEvent === 'Edit event' && !isEditMode && !isChildWorkflow)}
          />
        </div>
      );
    };
    console.log(isTaskReviewed, 'isTaskReviewedDDDDD');
    // Always show Client/Matter and Court notice action
    // Save_court_notice_only: Only show Files
    if (selectedAction === 'Save court notice only') {
      return (
        <>
          {/* Court notice type field */}
          <div className="mb-[20px]">
            <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">
              Court notice type
            </label>
            <input
              type="text"
              name="courtNoticeType"
              disabled={isTaskReviewed || (titleOfEvent === 'Edit event' && !isEditMode && !isChildWorkflow)}
              value={formData?.courtNoticeType || ''}
              onChange={handleChange}
              placeholder="Enter court notice type"
              className={`w-full p-3 border font-normal text-[#2a2e34] rounded-[12px]
                focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6] placeholder:text-[#5F6F84]
                ${errors.courtNoticeType ? 'border-red-500' : 'border-[#DCE2EB]'}`}
            />
            {errors.courtNoticeType && (
              <p className="text-red-500 text-xs mt-1">{errors.courtNoticeType}</p>
            )}
          </div>

          {/* Files field */}
          <div className="mb-[20px]">
            <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">Files</label>
            <FileUpload
              value={uploadedFiles}
              onChange={setUploadedFiles}
              allowedFileTypes={[
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'image/jpeg',
                'image/jpg',
                'image/png',
                'image/svg+xml',
                'image/tiff',
                'image/bmp',
                'image/gif',
              ]}
              maxFiles={5}
              path={'event/court-notice/'}
              courtNoticeType={formData?.courtNoticeType || ''}
              clientLastName={clientName || formData?.clientName?.split('|')[0]?.trim() || ''}
              actionType={selectedAction}
              startDate={startDate ? formatDateForFilename(startDate) : ''}
              disabled={isTaskReviewed || (titleOfEvent === 'Edit event' && !isEditMode && !isChildWorkflow)}
            />
            {uploadedFiles.some(file => file.error) && (
              <div className="mt-2 text-red-500 text-sm">
                {uploadedFiles
                  .filter(file => file.error)
                  .map((file, index) => (
                    <div key={index}>{file.error}</div>
                  ))}
              </div>
            )}
          </div>
        </>
      );
    }

    console.log(
      '==============selectedAction=======',
      selectedAction === 'Calendar event and save court notice'
    );

    // Calendar_event_and_save_court_notice: Show all fields (full form)
    if (selectedAction === 'Calendar event and save court notice') {
      return (
        <>

          <div className="mb-[20px]">
            <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">
              Appointment action
              <span className="text-[#5f6f84] font-normal ml-1">(New/Reschedule/Cancel)</span>
            </label>
            <CustomSelect
              name="appointmentAction"
              value={formData?.appointmentAction || ''}
              onChange={handleChange}
              options={appointmentActions}
              hasError={!!errors.appointmentAction}
              disabled={isTaskReviewed || (titleOfEvent === 'Edit event' && !isEditMode) || isChildWorkflow}
            />
            {errors.appointmentAction && (
              <p className="text-red-500 text-xs mt-1">{errors.appointmentAction}</p>
            )}
          </div>


          {selectedAppointmentActions &&
            selectedAppointmentActions == 'Reschedule' &&
            renderAppointmentSelectionField('Reschedule')}

          {selectedAppointmentActions &&
            selectedAppointmentActions == 'Cancel' &&
            renderAppointmentSelectionField('Cancel')}

          {/* Court notice type field for Cancel appointment action */}
          {selectedAppointmentActions === 'Cancel' && (
            <div className="mb-[20px]">
              <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">
                Court notice type
              </label>
              <input
                type="text"
                name="courtNoticeType"
                disabled={isTaskReviewed || (titleOfEvent == 'Edit event' && !isEditMode && !isChildWorkflow)}
                value={formData?.courtNoticeType || ''}
                onChange={handleChange}
                placeholder="Enter court notice type"
                className={`w-full p-3 border font-normal text-[#2a2e34] rounded-[12px]
                  focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6] placeholder:text-[#5F6F84]
                  ${errors.courtNoticeType ? 'border-red-500' : 'border-[#DCE2EB]'}`}
              />
              {errors.courtNoticeType && (
                <p className="text-red-500 text-xs mt-1">{errors.courtNoticeType}</p>
              )}
            </div>
          )}

          {renderCommonFormFields()}
          {renderFullFormFields()}
          {render2ndCommonFormFields()}
          {renderFilesField()}
        </>
      );
    }

    // Calendar_DEADLINE_and_save_court_notice: Show only deadline-related fields
    if (selectedAction === 'Calendar DEADLINE and save court notice') {
      return (
        <>
          {!(titleOfEvent === 'Edit event' && isChildWorkflow) && (
            <div className="mb-[20px]">
              <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">
                Appointment action
                <span className="text-[#5f6f84] font-normal ml-1">(New/Reschedule/Cancel)</span>
              </label>
              <CustomSelect
                name="appointmentAction"
                value={formData?.appointmentAction || ''}
                onChange={handleChange}
                options={appointmentActions}
                hasError={!!errors.appointmentAction}
                disabled={titleOfEvent == 'Edit event'}
              />
              {errors.appointmentAction && (
                <p className="text-red-500 text-xs mt-1">{errors.appointmentAction}</p>
              )}
            </div>
          )}

          {selectedAppointmentActions &&
            selectedAppointmentActions == 'Reschedule' &&
            renderAppointmentSelectionField('Reschedule')}

          {selectedAppointmentActions &&
            selectedAppointmentActions == 'Cancel' &&
            renderAppointmentSelectionField('Cancel')}

          {renderCommonFormFields()}
          {render2ndCommonFormFields()}
          {renderFilesField()}
        </>
      );
    }
    return null;
  };

  if (!isOpen || !formData) return null;

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    console.log('🚀 ~ value:', value, name);

    // Track if subject or description are manually edited
    if (name === 'subject' || name === 'description') {
      setIsFieldManuallyEdited(prev => ({
        ...prev,
        [name]: true,
      }));
    }

    if (name == 'meetingLocation') {
      formData.meetingLink = '';
      formData.phoneDetails = '';
      formData.meetingAddress = '';
    }

    setFormData(prev => {
      if (!prev) return null;
      return { ...prev, [name]: value };
    });

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleSubjectReset = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    // Clear the manual edit flag so the field can be auto-updated
    setIsFieldManuallyEdited(prev => ({
      ...prev,
      subject: false,
    }));

    // In edit mode, reset to original subject if available
    if (!isNew && intialEventSubject) {
      console.log('Resetting to original subject for edit mode');
      setFormData(prev => {
        if (!prev) return null;
        return { ...prev, subject: intialEventSubject };
      });
    } else {
      // In new mode or no original subject, trigger regeneration by clearing and letting useEffect handle it
      console.log('Triggering subject regeneration');

      // Handle Cancel appointment case with async subject generation
      if (formData?.appointmentAction === 'Cancel' && formData?.appointmentToReschedule) {
        generateCancelSubject(formData || ({} as EventType)).then(regeneratedSubject => {
          setFormData(prev => {
            if (!prev) return null;
            return { ...prev, subject: regeneratedSubject };
          });
        });
      } else {
        // Handle normal case with synchronous subject generation
        const regeneratedSubject = generateSubject(formData || ({} as EventType));
        setFormData(prev => {
          if (!prev) return null;
          return { ...prev, subject: regeneratedSubject };
        });
      }
    }
  };

  const handleDescriptionReset = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    // Clear the manual edit flag so the field can be auto-updated
    setIsFieldManuallyEdited(prev => ({
      ...prev,
      description: false,
    }));

    // In edit mode, reset to original description if available
    if (!isNew && intialEventDescription) {
      console.log('Resetting to original description for edit mode');
      setFormData(prev => {
        if (!prev) return null;
        return { ...prev, description: intialEventDescription };
      });
    } else {
      // In new mode or no original description, trigger regeneration by clearing and letting useEffect handle it
      console.log('Triggering description regeneration');
      // Force regeneration by temporarily clearing and then letting useEffect regenerate
      const regeneratedDescription = generateDescription(formData || ({} as EventType));
      setFormData(prev => {
        if (!prev) return null;
        return { ...prev, description: regeneratedDescription };
      });
    }
  };

  const handleDateChange = (date: Date | null, fieldName: string) => {
    if (!date) return;

    let newStartDate = startDate;
    let newEndDate = endDate;

    // Update state date objects
    if (fieldName === 'startDate') {
      setStartDate(date);
      newStartDate = date;
      // Automatically update end date to match start date
      setEndDate(date);
      newEndDate = date;
    } else if (fieldName === 'endDate') {
      setEndDate(date);
      newEndDate = date;
    }

    // Check if end date is greater than start date
    const isMultiDay = newStartDate && newEndDate && newEndDate > newStartDate;

    // If it's a multi-day event, automatically disable "All day" toggle
    if (isMultiDay && allDay) {
      setAllDay(false);
      setAllDayDeadLine(false);
    }

    // Update form data with formatted date string
    const userTimezone = getUserTimezone();
    // Format date in user's timezone
    const timezoneAwareDate = convertDateToUserTimezone(date, userTimezone);
    const formattedDate = timezoneAwareDate.toISOString().split('T')[0];
    setFormData(prev => {
      if (!prev) return null;

      if (fieldName === 'startDate') {
        // When start date changes, also update end date to match
        return {
          ...prev,
          [fieldName]: formattedDate,
          endDate: formattedDate, // Set end date same as start date
          allDay: isMultiDay ? false : prev.allDay, // Disable all day if multi-day
        };
      } else if (fieldName === 'endDate') {
        return {
          ...prev,
          [fieldName]: formattedDate,
          allDay: isMultiDay ? false : prev.allDay, // Disable all day if multi-day
        };
      } else {
        return { ...prev, [fieldName]: formattedDate };
      }
    });

    // Clear error for this field
    if (errors[fieldName]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
    }

    // Also clear end date error if start date was changed (since end date is auto-updated)
    if (fieldName === 'startDate' && errors.endDate) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.endDate;
        return newErrors;
      });
    }
  };

  const handleTimeChange = (time: Date | null, fieldName: string) => {
    if (!time) return;

    // Update state time objects (these remain in local timezone for UI display)
    if (fieldName === 'startTime') {
      setStartTime(time);
    } else if (fieldName === 'endTime') {
      setEndTime(time);
    }

    // Format time as HH:MM in local timezone
    const hours = time.getHours().toString().padStart(2, '0');
    const minutes = time.getMinutes().toString().padStart(2, '0');
    const localTimeString = `${hours}:${minutes}`;

    // Convert to UTC for storage
    const currentDate = formData?.startDate || getCurrentDateInUserTimezone();
    const utcTimeString = convertTimeToUTCReliable(localTimeString, currentDate);

    // Update form data with UTC time
    setFormData(prev => {
      if (!prev) return null;
      return { ...prev, [fieldName]: utcTimeString };
    });

    // Clear error for this field
    if (errors[fieldName]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required fields validation
    if (!formData.clientName?.trim()) {
      newErrors.clientName = 'Client name is required';
    }

    // Validation based on selected action
    if (selectedAction === 'Save court notice only') {
      // For "Save court notice only", validate court notice type and date
      if (!formData.courtNoticeType?.trim()) {
        newErrors.courtNoticeType = VALIDATIONS.COURT_TYPE_REQUIRED;
      }
    } else {
      // For calendar events and deadlines, validate start/end dates
      if (!formData.startDate) {
        newErrors.startDate = 'Start date is required';
      }

      if (!formData.endDate) {
        newErrors.endDate = 'End date is required';
      }

      // Validate attendees for calendar events and deadlines
      // const requiredAttendees =
      //   formData.requiredAttendees
      //     ?.split(',')
      //     .map(val => val.trim())
      //     .filter(val => val) || [];
      // const optionalAttendees =
      //   formData.optionalAttendees
      //     ?.split(',')
      //     .map(val => val.trim())
      //     .filter(val => val) || [];

      // if (requiredAttendees.length === 0) {
      //   newErrors.requiredAttendees = 'At least one required attendee must be selected';
      // }

      // if (optionalAttendees.length === 0) {
      //   newErrors.optionalAttendees = 'At least one optional attendee must be selected';
      // }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    console.log(isChildWorkflow, 'isChildWorkflowINLAST');
    e.preventDefault();
    if (validateForm() && formData) {
      // Get successfully uploaded files
      const successfullyUploadedFiles = uploadedFiles.filter(
        file => file.status === 'completed' && !file.isDeleted
      );

      // Get files marked for deletion
      const deletedFiles = uploadedFiles
        .filter(file => file.isDeleted && file.name && file.s3Key)
        .map(file => ({
          name: file.name!,
          key: file.s3Key!,
        }));

      // Get renamed files - only include files that have actually been renamed
      const renameFiles = uploadedFiles
        .filter(
          file =>
            file.isNameChanged &&
            file.name &&
            file.originalS3Key &&
            file.s3Key &&
            file.originalS3Key !== file.s3Key // Ensure the S3 key actually changed
        )
        .map(file => ({
          old_fileName: file.originalName!,
          old_s3FileName: file.originalS3Key!.split('/').pop()!, // Extract original filename from originalS3Key
          new_fileName: file.name!,
          key: file.s3Key, // Include the key for proper file identification
          uniqueId: file.uniqueId, // Add uniqueId for proper file identification
          old_s3Key: file.originalS3Key!, // Include the full original S3 key
          new_s3Key: file.s3Key!, // Include the full new S3 key
        }));

      // Prepare the updated form data with proper typing
      const updatedFormData = {
        ...formData,
        // Ensure required fields from EventType are properly set
        caseNumber: formData.caseNumber || '',
        startTime: formData.startTime || '00:00',
        endTime: formData.endTime || '00:00',
        files: successfullyUploadedFiles
          .filter(file => file.name) // Ensure name exists
          .map(file => ({
            name: file.name!,
            url: file.url,
            type: file.type,
            size: file.size,
            key: file.s3Key,
            uniqueId: file.uniqueId, // Add uniqueId for proper file identification
          })),
        deleteFiles: deletedFiles, // Add deleted files to payload
        renameFiles: renameFiles, // Add renamed files to payload
      } as EventType & {
        deleteFiles: Array<{ name: string; key: string }>;
        renameFiles: Array<{
          old_fileName: string;
          new_fileName: string;
          key: string | undefined;
          uniqueId: string | undefined;
        }>;
      };

      // Log the payload for debugging
      console.log('Submitting form with payload:', {
        files: updatedFormData.files,
        deleteFiles: updatedFormData.deleteFiles,
        renameFiles: updatedFormData.renameFiles,
      });

      onSave(clientId, updatedFormData as EventType);

      // Update AdvisorPanel event count if the global function is available
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      if (typeof window !== 'undefined' && (window as any).updateAdvisorPanelEventCount) {
        // Get the client name from formData - prioritize clientName over subject
        const eventClientName =
          updatedFormData.clientName || clientName || updatedFormData.subject || 'Unknown Client';

        // Only update if we have a valid client name
        if (eventClientName && eventClientName !== 'Unknown Client') {
          // Call the global function to notify the AdvisorPanel of the event update
          // The AdvisorPanel will handle the logic for counting and displaying
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (window as any).updateAdvisorPanelEventCount(eventClientName, 1);
          console.log('🔄 Updated AdvisorPanel event count for:', eventClientName);
        }
      }

      if (isChildWorkflow) {
        localStorage.setItem(`isEventAdded_${workflow_id}`, 'true');
      }

      onClose();
    }
  };

  // Custom select component with styled dropdown that uses portals for overflow
  const CustomSelect1 = ({
    name,
    value,
    onChange,
    options,
    hasError = false,
    disabled = false,
  }: {
    name: string;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
    options: OptionItem[];
    hasError?: boolean;
    disabled?: boolean;
  }) => {
    const shouldApplyTbdStyling = isTbdValue(value);
    const [isOpen, setIsOpen] = React.useState(false);
    const [highlightedIndex, setHighlightedIndex] = React.useState<number>(-1);
    const [dropdownPosition, setDropdownPosition] = React.useState({ top: 0, left: 0, width: 0 });
    const dropdownRef = React.useRef<HTMLDivElement>(null);
    const triggerRef = React.useRef<HTMLDivElement>(null);

    // Helper function to truncate text
    const truncate = (text: string, maxLength: number = 60) => {
      return text.length > maxLength ? text.slice(0, maxLength) + '…' : text;
    };

    // Find selected option
    const selectedOption = options.find(option => option.text === value);

    // Calculate dropdown position
    const updateDropdownPosition = useCallback(() => {
      if (triggerRef.current && isOpen) {
        const rect = triggerRef.current.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const dropdownHeight = 240; // max-h-[180px] = 240px

        // Check if there's enough space below
        const spaceBelow = viewportHeight - rect.bottom - 10; // Add some padding
        const spaceAbove = rect.top - 10; // Add some padding

        let top = rect.bottom + window.scrollY + 4; // Add small gap

        // If not enough space below and more space above, show dropdown above
        if (spaceBelow < dropdownHeight && spaceAbove > spaceBelow) {
          top = rect.top + window.scrollY - dropdownHeight - 4; // Add small gap
        }

        setDropdownPosition({
          top,
          left: rect.left + window.scrollX,
          width: rect.width,
        });
      }
    }, [isOpen]);

    // Update position when opening dropdown or on scroll/resize
    useEffect(() => {
      if (isOpen) {
        updateDropdownPosition();

        const handleScroll = (e: Event) => {
          // Check if the scroll is happening within the dropdown itself
          if (dropdownRef.current && dropdownRef.current.contains(e.target as Node)) {
            return; // Don't update position for internal dropdown scrolling
          }
          updateDropdownPosition();
        };

        const handleResize = () => {
          updateDropdownPosition();
        };

        // Use passive listeners for better performance
        window.addEventListener('scroll', handleScroll, { passive: true, capture: true });
        window.addEventListener('resize', handleResize, { passive: true });
        document.addEventListener('scroll', handleScroll, { passive: true, capture: true });

        return () => {
          window.removeEventListener('scroll', handleScroll, true);
          window.removeEventListener('resize', handleResize);
          document.removeEventListener('scroll', handleScroll, true);
        };
      }
    }, [isOpen, updateDropdownPosition]);

    // Handle keyboard navigation
    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (!isOpen) {
        if (e.key === 'Enter' || e.key === ' ' || e.key === 'ArrowDown' || e.key === 'ArrowUp') {
          e.preventDefault();
          setIsOpen(true);
          // Set highlighted index to current selection or first option
          const currentIndex = options.findIndex(option => option.text === value);
          setHighlightedIndex(currentIndex >= 0 ? currentIndex : 0);
        }
        return;
      }

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setHighlightedIndex(prev => {
            const nextIndex = prev < options.length - 1 ? prev + 1 : 0;
            // Scroll highlighted option into view with better timing
            requestAnimationFrame(() => {
              const dropdownElement = dropdownRef.current?.querySelector(
                '.dropdown-scroll-container'
              );
              const highlightedElement = dropdownElement?.querySelector(
                `[data-custom-option-index="${nextIndex}"]`
              ) as HTMLElement;

              if (highlightedElement && dropdownElement) {
                const containerRect = dropdownElement.getBoundingClientRect();
                const elementRect = highlightedElement.getBoundingClientRect();

                if (elementRect.bottom > containerRect.bottom) {
                  highlightedElement.scrollIntoView({
                    block: 'end',
                    behavior: 'smooth',
                  });
                } else if (elementRect.top < containerRect.top) {
                  highlightedElement.scrollIntoView({
                    block: 'start',
                    behavior: 'smooth',
                  });
                }
              }
            });
            return nextIndex;
          });
          break;

        case 'ArrowUp':
          e.preventDefault();
          setHighlightedIndex(prev => {
            const nextIndex = prev > 0 ? prev - 1 : options.length - 1;
            // Scroll highlighted option into view with better timing
            requestAnimationFrame(() => {
              const dropdownElement = dropdownRef.current?.querySelector(
                '.dropdown-scroll-container'
              );
              const highlightedElement = dropdownElement?.querySelector(
                `[data-custom-option-index="${nextIndex}"]`
              ) as HTMLElement;

              if (highlightedElement && dropdownElement) {
                const containerRect = dropdownElement.getBoundingClientRect();
                const elementRect = highlightedElement.getBoundingClientRect();

                if (elementRect.top < containerRect.top) {
                  highlightedElement.scrollIntoView({
                    block: 'start',
                    behavior: 'smooth',
                  });
                } else if (elementRect.bottom > containerRect.bottom) {
                  highlightedElement.scrollIntoView({
                    block: 'end',
                    behavior: 'smooth',
                  });
                }
              }
            });
            return nextIndex;
          });
          break;

        case 'Enter':
          e.preventDefault();
          if (highlightedIndex >= 0 && highlightedIndex < options.length) {
            const selectedOption = options[highlightedIndex];
            handleOptionSelect(selectedOption);
          }
          break;

        case 'Escape':
          e.preventDefault();
          setIsOpen(false);
          setHighlightedIndex(-1);
          break;
      }
    };

    // Toggle selection of an option
    const handleOptionSelect = (option: OptionItem) => {
      if (disabled) return;

      // Create a synthetic event to maintain compatibility with existing onChange handler
      const syntheticEvent = {
        target: {
          name: name,
          value: option.text,
        },
      } as React.ChangeEvent<HTMLSelectElement>;

      onChange(syntheticEvent);
      setIsOpen(false);
      setHighlightedIndex(-1);
    };

    // Handle click outside to close dropdown
    React.useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (
          triggerRef.current &&
          !triggerRef.current.contains(event.target as Node) &&
          dropdownRef.current &&
          !dropdownRef.current.contains(event.target as Node) &&
          isOpen
        ) {
          setIsOpen(false);
          setHighlightedIndex(-1);
        }
      };

      if (isOpen) {
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
          document.removeEventListener('mousedown', handleClickOutside);
        };
      }
    }, [isOpen]);

    // Dropdown component to be rendered in portal
    const DropdownContent = () => (
      <div
        ref={dropdownRef}
        className="fixed bg-white border border-[#DCE2EB] rounded-[12px] shadow-lg max-h-[180px] z-[9999] overflow-hidden"
        style={{
          top: `${dropdownPosition.top}px`,
          left: `${dropdownPosition.left}px`,
          width: `${dropdownPosition.width}px`,
        }}
      >
        <div
          className={`dropdown-scroll-container max-h-[180px] overflow-y-auto ${poppins.className}`}
        >
          {options.length > 0 ? (
            options.map((option: OptionItem, index: number) => (
              <div
                key={`${name}-${index}`}
                data-custom-option-index={index}
                className={`p-3 cursor-pointer transition-colors duration-150 first:mt-2 last:mb-2 ${value && value === option.text
                  ? 'bg-[#D9E3FD] text-[#3F73F6]'
                  : highlightedIndex === index
                    ? 'bg-[#F1F5F9] text-[#2A2E34]'
                    : 'text-[#2A2E34] hover:bg-[#F8F9FC]'
                  }`}
                onClick={() => handleOptionSelect(option)}
                onMouseEnter={() => setHighlightedIndex(index)}
                title={option.text} // Add title for hover tooltip
              >
                <div className="flex items-center justify-between">
                  <span>{option.text}</span>
                </div>
              </div>
            ))
          ) : (
            <div className="p-4 text-center text-gray-500">No options available</div>
          )}
        </div>
      </div>
    );

    return (
      <div className="relative w-full">
        <div
          ref={triggerRef}
          className={`
          w-full appearance-none p-3 pr-10 border font-normal rounded-[12px] text-[#2a2e34]
          focus:outline-none cursor-pointer transition-all duration-200
          ${hasError
              ? 'border-red-500 focus:ring-red-200 focus:border-red-500'
              : shouldApplyTbdStyling
                ? 'border-[#F6B175] ring-2 ring-[#F6B17533] focus:border-[#F6B175]'
                : 'border-[#DCE2EB] focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6]'
            }
          ${disabled ? 'cursor-not-allowed bg-[#F3F5F9] text-[#A2AFC2]' : 'bg-white cursor-pointer'}
          ${!value ? 'text-[#5F6F84]' : 'text-[#2a2e34]'}
        `}
          onClick={() => {
            if (!disabled) {
              setIsOpen(!isOpen);
              if (!isOpen) {
                // Set highlighted index to current selection or first option
                const currentIndex = options.findIndex(option => option.text === value);
                setHighlightedIndex(currentIndex >= 0 ? currentIndex : 0);
              }
            }
          }}
          onKeyDown={handleKeyDown}
          tabIndex={disabled ? -1 : 0}
          role="button"
          aria-haspopup="listbox"
          aria-expanded={isOpen}
        >
          {value ? value : 'Select'}
        </div>

        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3">
          <ChevronDown
            size={20}
            className={`text-[#5F6F84] transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          />
        </div>

        {/* Render dropdown in portal if open and not disabled */}
        {isOpen &&
          !disabled &&
          typeof window !== 'undefined' &&
          createPortal(<DropdownContent />, document.body)}
      </div>
    );
  };

  const CustomSelect = ({
    name,
    value,
    onChange,
    options,
    hasError = false,
    disabled = false,
  }: {
    name: string;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
    options: OptionItem[];
    hasError?: boolean;
    disabled?: boolean;
  }) => {
    const shouldApplyTbdStyling = isTbdValue(value);
    const [isOpen, setIsOpen] = React.useState(false);
    const [highlightedIndex, setHighlightedIndex] = React.useState<number>(-1);
    const dropdownRef = React.useRef<HTMLDivElement>(null);

    // Helper function to truncate text - only used for the main input display
    const truncate = (text: string, maxLength: number = 60) => {
      return text.length > maxLength ? text.slice(0, maxLength) + '…' : text;
    };

    // Find selected option
    const selectedOption = options.find(option => option.text === value);

    // Handle keyboard navigation
    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (!isOpen) {
        if (e.key === 'Enter' || e.key === ' ' || e.key === 'ArrowDown' || e.key === 'ArrowUp') {
          e.preventDefault();
          setIsOpen(true);
          setHighlightedIndex(0);
        }
        return;
      }

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setHighlightedIndex(prev => {
            const nextIndex = prev < options.length - 1 ? prev + 1 : 0;
            // Scroll highlighted option into view
            setTimeout(() => {
              const highlightedElement = document.querySelector(
                `[data-custom-option-index="${nextIndex}"]`
              );
              if (highlightedElement) {
                highlightedElement.scrollIntoView({
                  block: 'nearest',
                  behavior: 'smooth',
                });
              }
            }, 0);
            return nextIndex;
          });
          break;

        case 'ArrowUp':
          e.preventDefault();
          setHighlightedIndex(prev => {
            const nextIndex = prev > 0 ? prev - 1 : options.length - 1;
            // Scroll highlighted option into view
            setTimeout(() => {
              const highlightedElement = document.querySelector(
                `[data-custom-option-index="${nextIndex}"]`
              );
              if (highlightedElement) {
                highlightedElement.scrollIntoView({
                  block: 'nearest',
                  behavior: 'smooth',
                });
              }
            }, 0);
            return nextIndex;
          });
          break;

        case 'Enter':
          e.preventDefault();
          if (highlightedIndex >= 0 && highlightedIndex < options.length) {
            const selectedOption = options[highlightedIndex];
            handleOptionSelect(selectedOption);
          }
          break;

        case 'Escape':
          e.preventDefault();
          setIsOpen(false);
          setHighlightedIndex(-1);
          break;
      }
    };

    // Handle option selection
    const handleOptionSelect = (option: OptionItem) => {
      if (disabled) return;

      // Create a synthetic event to maintain compatibility with existing onChange handler
      const syntheticEvent = {
        target: {
          name: name,
          value: option.text,
        },
      } as React.ChangeEvent<HTMLSelectElement>;

      onChange(syntheticEvent);
      setIsOpen(false);
      setHighlightedIndex(-1);
    };

    // Handle click outside to close dropdown
    React.useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) && isOpen) {
          setIsOpen(false);
          setHighlightedIndex(-1);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [isOpen]);

    return (
      <div className="relative w-full" ref={dropdownRef}>
        <div
          className={`
            w-full appearance-none p-3 pr-10 border font-normal rounded-[12px] text-[#2a2e34]
            focus:outline-none cursor-pointer
            ${hasError
              ? 'border-red-500 focus:ring-red-200 focus:border-red-500'
              : shouldApplyTbdStyling
                ? 'border-[#F6B175] ring-2 ring-[#F6B17533] focus:border-[#F6B175]'
                : 'border-[#DCE2EB] focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6]'
            }
            ${disabled ? 'cursor-not-allowed bg-[#F3F5F9] text-[#A2AFC2]' : 'bg-white cursor-pointer'}
            ${!value ? 'text-[#5F6F84]' : 'text-[#2a2e34]'}
          `}
          onClick={() => {
            if (!disabled) {
              setIsOpen(!isOpen);
              if (!isOpen) {
                setHighlightedIndex(0);
              }
            }
          }}
          onKeyDown={handleKeyDown}
          tabIndex={disabled ? -1 : 0}
          role="button"
          aria-haspopup="listbox"
          aria-expanded={isOpen}
        >
          <div className="truncate">
            {selectedOption ? (
              selectedOption.text.includes('|') ? (
                <span>
                  <span>{selectedOption.text.split('|')[0]}</span>
                  <span className="text-[#5F6F84]">
                    {' | ' + selectedOption.text.split('|')[1]}
                  </span>
                </span>
              ) : (
                selectedOption.text
              )
            ) : (
              <span className="text-[#5F6F84]">Select</span>
            )}
          </div>
        </div>

        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3">
          <ChevronDown
            size={20}
            className={`text-[#5F6F84] transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          />
        </div>

        {/* Dropdown options */}
        {isOpen && !disabled && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-[#DCE2EB] rounded-[12px] shadow-sm max-h-[180px]">
            <div className="max-h-[180px] overflow-y-auto">
              {options.length > 0 ? (
                options.map((option: OptionItem, index: number) => (
                  <div
                    key={`${name}-${index}`}
                    data-custom-option-index={index}
                    className={`p-3 mt-2 cursor-pointer transition-colors duration-150 mb-2 ${value && value === option.text
                      ? 'bg-[#D9E3FD] text-[#3F73F6]'
                      : highlightedIndex === index
                        ? 'bg-[#F1F5F9] text-[#2A2E34]'
                        : 'text-[#2A2E34] hover:bg-[#F8F9FC]'
                      }`}
                    onClick={() => handleOptionSelect(option)}
                    onMouseEnter={() => setHighlightedIndex(index)}
                    title={option.text} // Add title for hover tooltip
                  >
                    <div className="flex items-center justify-between">
                      {option.text.includes('|') ? (
                        <span className="">
                          <span>{option.text.split('|')[0]}</span>
                          <span className="text-[#5F6F84]">
                            {' | ' + option.text.split('|')[1]}
                          </span>
                        </span>
                      ) : (
                        <span>{option.text}</span>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <div className="p-4 text-center text-gray-500">No options available</div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  const CustomSelectBasedOnValue = ({
    name,
    value,
    onChange,
    options,
    hasError = false,
    disabled = false,
  }: {
    name: string;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
    options: { text: string; value: string; workflow_id?: string }[];
    hasError?: boolean;
    disabled?: boolean;
  }) => {
    const shouldApplyTbdStyling = isTbdValue(value);
    const [isOpen, setIsOpen] = React.useState(false);
    const [highlightedIndex, setHighlightedIndex] = React.useState<number>(-1);
    const dropdownRef = React.useRef<HTMLDivElement>(null);

    // Helper function to truncate text - only used for the main input display
    const truncate = (text: string, maxLength: number = 60) => {
      return text.length > maxLength ? text.slice(0, maxLength) + '…' : text;
    };

    // Find selected option
    const selectedOption = options.find(option => option.value === value);

    // Handle keyboard navigation
    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (!isOpen) {
        if (e.key === 'Enter' || e.key === ' ' || e.key === 'ArrowDown' || e.key === 'ArrowUp') {
          e.preventDefault();
          setIsOpen(true);
          setHighlightedIndex(0);
        }
        return;
      }

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setHighlightedIndex(prev => {
            const nextIndex = prev < options.length - 1 ? prev + 1 : 0;
            // Scroll highlighted option into view
            setTimeout(() => {
              const highlightedElement = document.querySelector(
                `[data-custom-option-index="${nextIndex}"]`
              );
              if (highlightedElement) {
                highlightedElement.scrollIntoView({
                  block: 'nearest',
                  behavior: 'smooth',
                });
              }
            }, 0);
            return nextIndex;
          });
          break;

        case 'ArrowUp':
          e.preventDefault();
          setHighlightedIndex(prev => {
            const nextIndex = prev > 0 ? prev - 1 : options.length - 1;
            // Scroll highlighted option into view
            setTimeout(() => {
              const highlightedElement = document.querySelector(
                `[data-custom-option-index="${nextIndex}"]`
              );
              if (highlightedElement) {
                highlightedElement.scrollIntoView({
                  block: 'nearest',
                  behavior: 'smooth',
                });
              }
            }, 0);
            return nextIndex;
          });
          break;

        case 'Enter':
          e.preventDefault();
          if (highlightedIndex >= 0 && highlightedIndex < options.length) {
            const selectedOption = options[highlightedIndex];
            handleOptionSelect(selectedOption);
          }
          break;

        case 'Escape':
          e.preventDefault();
          setIsOpen(false);
          setHighlightedIndex(-1);
          break;
      }
    };

    // Handle option selection
    const handleOptionSelect = (option: { text: string; value: string; workflow_id?: string }) => {
      if (disabled) return;

      // Create a synthetic event to maintain compatibility with existing onChange handler
      const syntheticEvent = {
        target: {
          name: name,
          value: option.value,
        },
      } as React.ChangeEvent<HTMLSelectElement>;

      onChange(syntheticEvent);
      setIsOpen(false);
      setHighlightedIndex(-1);
    };

    // Handle click outside to close dropdown
    React.useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) && isOpen) {
          setIsOpen(false);
          setHighlightedIndex(-1);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [isOpen]);

    return (
      <div className="relative w-full" ref={dropdownRef}>
        <div
          className={`
            w-full appearance-none p-3 pr-10 border font-normal rounded-[12px] text-[#2a2e34]
            focus:outline-none cursor-pointer
            ${hasError
              ? 'border-red-500 focus:ring-red-200 focus:border-red-500'
              : shouldApplyTbdStyling
                ? 'border-[#F6B175] ring-2 ring-[#F6B17533] focus:border-[#F6B175]'
                : 'border-[#DCE2EB] focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6]'
            }
            ${disabled ? 'cursor-not-allowed bg-[#F3F5F9] text-[#A2AFC2]' : 'bg-white cursor-pointer'}
            ${!value ? 'text-[#5F6F84]' : 'text-[#2a2e34]'}
          `}
          onClick={() => {
            if (!disabled) {
              setIsOpen(!isOpen);
              if (!isOpen) {
                setHighlightedIndex(0);
              }
            }
          }}
          onKeyDown={handleKeyDown}
          tabIndex={disabled ? -1 : 0}
          role="button"
          aria-haspopup="listbox"
          aria-expanded={isOpen}
        >
          <div className="truncate">
            {selectedOption ? (
              selectedOption.text.includes('|') ? (
                <span>
                  <span>{selectedOption.text.split('|')[0]}</span>
                  <span className="text-[#5F6F84]">
                    {' | ' + selectedOption.text.split('|')[1]}
                  </span>
                </span>
              ) : (
                selectedOption.text
              )
            ) : (
              <span className="text-[#5F6F84]">Select</span>
            )}
          </div>
        </div>

        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3">
          <ChevronDown
            size={20}
            className={`text-[#5F6F84] transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          />
        </div>

        {/* Hidden actual select for form submission */}
        <select
          name={name}
          value={value}
          onChange={onChange}
          disabled={disabled}
          className="sr-only"
          aria-hidden="true"
        >
          <option value="" disabled></option>
          {options.map((option, index) => (
            <option
              className="bg-white"
              key={`${name}-select-${index}`}
              value={option.value}
            ></option>
          ))}
        </select>

        {/* Dropdown options */}
        {isOpen && !disabled && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-[#DCE2EB] rounded-[12px] shadow-sm max-h-[180px]">
            <div className="max-h-[180px] overflow-y-auto">
              {options.length > 0 ? (
                options.map((option, index: number) => (
                  <div
                    key={`${name}-${index}`}
                    data-custom-option-index={index}
                    className={`p-3 mt-2 cursor-pointer transition-colors duration-150 mb-2 ${value === option.value
                      ? 'bg-[#D9E3FD] text-[#3F73F6]'
                      : highlightedIndex === index
                        ? 'bg-[#F1F5F9] text-[#2A2E34]'
                        : 'text-[#2A2E34] hover:bg-[#F8F9FC]'
                      }`}
                    onClick={() => handleOptionSelect(option)}
                    onMouseEnter={() => setHighlightedIndex(index)}
                    title={option.text} // Add title for hover tooltip
                  >
                    <div className="flex items-center justify-between">
                      {option.text.includes('|') ? (
                        <span className="">
                          <span>{option.text.split('|')[0]}</span>
                          <span className="text-[#5F6F84]">
                            {' | ' + option.text.split('|')[1]}
                          </span>
                        </span>
                      ) : (
                        <span>{option.text}</span>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <div className="p-4 text-center text-gray-500">No options available</div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  // Custom MultiSelect component for attendees
  // const CustomMultiSelect = ({
  //   name,
  //   value,
  //   onChange,
  //   options,
  //   hasError = false,
  //   disabled = false,
  // }: {
  //   name: string;
  //   value: string;
  //   onChange: (name: string, value: string) => void;
  //   options: OptionItem[];
  //   hasError?: boolean;
  //   disabled?: boolean;
  // }) => {
  //   const [isOpen, setIsOpen] = useState(false);
  //   const dropdownRef = React.useRef<HTMLDivElement>(null);

  //   // Convert comma-separated string to array of selected values
  //   const selectedValues = value ? value.split(',').map(val => val.trim()) : [];

  //   // Handle outside click to close dropdown
  //   useEffect(() => {
  //     const handleClickOutside = (event: MouseEvent) => {
  //       if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
  //         setIsOpen(false);
  //       }
  //     };

  //     document.addEventListener('mousedown', handleClickOutside);
  //     return () => {
  //       document.removeEventListener('mousedown', handleClickOutside);
  //     };
  //   }, []);

  //   // Toggle selection of an option
  //   const toggleOption = (optionText: string) => {
  //     const isSelected = selectedValues.includes(optionText);
  //     let newValues: string[];

  //     if (isSelected) {
  //       // Remove if already selected
  //       newValues = selectedValues.filter(val => val !== optionText);
  //     } else {
  //       // Add if not selected
  //       newValues = [...selectedValues, optionText];
  //     }

  //     // Convert back to comma-separated string and update state
  //     onChange(name, newValues.join(', '));
  //   };

  //   return (
  //     <div className="relative w-full" ref={dropdownRef}>
  //       <div
  //         onClick={() => !disabled && setIsOpen(!isOpen)}
  //         className={`
  //           w-full p-3 pr-10 border font-normal rounded-[12px] text-[#2a2e34] bg-white min-h-[48px] flex items-center flex-wrap gap-1
  //           focus:outline-none
  //           ${hasError ? 'border-red-500' : 'border-[#DCE2EB]'}
  //           ${disabled ? 'bg-gray-100 text-gray-500 cursor-not-allowed' : 'cursor-pointer'}
  //         `}
  //       >
  //         {selectedValues.length > 0 ? (
  //           selectedValues.map((val, index) => (
  //             <span
  //               key={index}
  //               className="bg-[#F3F5F9] px-2 py-1 rounded-md text-sm flex items-center"
  //             >
  //               {val}
  //               {!disabled && (
  //                 <button
  //                   type="button"
  //                   onClick={e => {
  //                     e.stopPropagation();
  //                     toggleOption(val);
  //                   }}
  //                   className="ml-1 text-gray-500 hover:text-gray-700"
  //                 >
  //                   <X size={14} />
  //                 </button>
  //               )}
  //             </span>
  //           ))
  //         ) : (
  //           <span>Select</span>
  //         )}
  //         <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3">
  //           <ChevronDown size={20} className="text-[#5F6F84]" />
  //         </div>
  //       </div>

  //       {isOpen && !disabled && (
  //         <div className="absolute z-10 mt-1 w-full bg-white border border-[#DCE2EB] rounded-[12px] shadow-lg max-h-[180px] overflow-auto">
  //           {options.map((option, index) => (
  //             <div
  //               key={index}
  //               onClick={() => toggleOption(option.text)}
  //               className={`p-3 hover:bg-[#F3F5F9] cursor-pointer flex items-center ${selectedValues.includes(option.text) ? 'bg-[#F3F5F9]' : ''
  //                 }`}
  //             >
  //               <input
  //                 type="checkbox"
  //                 checked={selectedValues.includes(option.text)}
  //                 onChange={() => { }} // Handled by parent div onClick
  //                 className="mr-2 h-4 w-4 rounded border-gray-300 text-[#3F73F6] focus:ring-[#3F73F6]"
  //               />
  //               {option.text}
  //             </div>
  //           ))}
  //         </div>
  //       )}
  //     </div>
  //   );
  // };

  // Searchable MultiSelect component for attendees
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const SearchableMultiSelect = ({
    name,
    value,
    onChange,
    options,
    hasError = false,
    disabled = false,
    placeholder = 'Search and select...',
  }: {
    name: string;
    value: string;
    onChange: (name: string, value: string) => void;
    options: OptionItem[];
    hasError?: boolean;
    disabled?: boolean;
    placeholder?: string;
  }) => {
    const [isOpen, setIsOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [highlightedIndex, setHighlightedIndex] = useState<number>(-1);
    const [dropdownPosition, setDropdownPosition] = React.useState({ top: 0, left: 0, width: 0 });
    const dropdownRef = React.useRef<HTMLDivElement>(null);
    const searchInputRef = React.useRef<HTMLInputElement>(null);
    const triggerRef = React.useRef<HTMLDivElement>(null);

    // Convert comma-separated string to array of selected values
    const selectedValues = value ? value.split(',').map(val => val.trim()) : [];

    // Filter options based on search term
    const filteredOptions = options.filter(option =>
      option.text.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Calculate dropdown position
    const updateDropdownPosition = useCallback(() => {
      if (triggerRef.current && isOpen) {
        const rect = triggerRef.current.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const dropdownHeight = 320; // Approximate height including search input

        // Check if there's enough space below
        const spaceBelow = viewportHeight - rect.bottom;
        const spaceAbove = rect.top;

        let top = rect.bottom + window.scrollY;

        // If not enough space below and more space above, show dropdown above
        if (spaceBelow < dropdownHeight && spaceAbove > spaceBelow) {
          top = rect.top + window.scrollY - dropdownHeight;
        }

        setDropdownPosition({
          top,
          left: rect.left + window.scrollX,
          width: rect.width,
        });
      }
    }, [isOpen]);

    // Update position when opening dropdown or on scroll/resize
    useEffect(() => {
      if (isOpen) {
        updateDropdownPosition();

        const handleScroll = () => updateDropdownPosition();
        const handleResize = () => updateDropdownPosition();

        window.addEventListener('scroll', handleScroll, true);
        window.addEventListener('resize', handleResize);

        return () => {
          window.removeEventListener('scroll', handleScroll, true);
          window.removeEventListener('resize', handleResize);
        };
      }
    }, [isOpen, updateDropdownPosition]);

    // Handle outside click to close dropdown
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (
          triggerRef.current &&
          !triggerRef.current.contains(event.target as Node) &&
          dropdownRef.current &&
          !dropdownRef.current.contains(event.target as Node) &&
          isOpen
        ) {
          setIsOpen(false);
          setSearchTerm('');
          setHighlightedIndex(-1);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [isOpen]);

    // Focus search input when dropdown opens
    useEffect(() => {
      if (isOpen && searchInputRef.current) {
        searchInputRef.current.focus();
        setHighlightedIndex(0); // Start with first option highlighted
      }
    }, [isOpen]);

    // Reset highlighted index when filtered options change
    useEffect(() => {
      if (filteredOptions.length > 0) {
        setHighlightedIndex(0);
      } else {
        setHighlightedIndex(-1);
      }
    }, [filteredOptions.length, searchTerm]);

    // Handle keyboard navigation
    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (!isOpen) {
        if (e.key === 'Enter' || e.key === ' ' || e.key === 'ArrowDown' || e.key === 'ArrowUp') {
          e.preventDefault();
          setIsOpen(true);
        }
        return;
      }

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setHighlightedIndex(prev => {
            const nextIndex = prev < filteredOptions.length - 1 ? prev + 1 : 0;
            // Scroll highlighted option into view
            setTimeout(() => {
              const highlightedElement = document.querySelector(
                `[data-searchable-option-index="${nextIndex}"]`
              );
              if (highlightedElement) {
                highlightedElement.scrollIntoView({
                  block: 'nearest',
                  behavior: 'smooth',
                });
              }
            }, 0);
            return nextIndex;
          });
          break;

        case 'ArrowUp':
          e.preventDefault();
          setHighlightedIndex(prev => {
            const nextIndex = prev > 0 ? prev - 1 : filteredOptions.length - 1;
            // Scroll highlighted option into view
            setTimeout(() => {
              const highlightedElement = document.querySelector(
                `[data-searchable-option-index="${nextIndex}"]`
              );
              if (highlightedElement) {
                highlightedElement.scrollIntoView({
                  block: 'nearest',
                  behavior: 'smooth',
                });
              }
            }, 0);
            return nextIndex;
          });
          break;

        case 'Enter':
          e.preventDefault();
          if (highlightedIndex >= 0 && highlightedIndex < filteredOptions.length) {
            const selectedOption = filteredOptions[highlightedIndex];
            toggleOption(selectedOption.text);
          }
          break;

        case 'Escape':
          e.preventDefault();
          setIsOpen(false);
          setSearchTerm('');
          setHighlightedIndex(-1);
          break;
      }
    };

    // Toggle selection of an option
    const toggleOption = (optionText: string) => {
      const isSelected = selectedValues.includes(optionText);
      let newValues: string[];

      if (isSelected) {
        // Remove if already selected
        newValues = selectedValues.filter(val => val !== optionText);
      } else {
        // Add if not selected
        newValues = [...selectedValues, optionText];
      }

      // Convert back to comma-separated string and update state
      onChange(name, newValues.join(', '));

      // Clear search term after selection
      setSearchTerm('');
    };

    // Remove a selected item
    const removeItem = (itemToRemove: string) => {
      const newValues = selectedValues.filter(val => val !== itemToRemove);
      onChange(name, newValues.join(', '));
    };

    // Dropdown component to be rendered in portal
    const DropdownContent = () => (
      <div
        ref={dropdownRef}
        className="fixed bg-white border border-[#DCE2EB] rounded-[12px] shadow-lg max-h-80 z-[9999] overflow-hidden"
        style={{
          top: `${dropdownPosition.top}px`,
          left: `${dropdownPosition.left}px`,
          width: `${dropdownPosition.width}px`,
        }}
      >
        {/* Search input */}
        <div className="p-3 border-b border-[#DCE2EB]">
          <input
            ref={searchInputRef}
            type="text"
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Search attendees..."
            className="w-full p-2 border border-[#DCE2EB] rounded-[8px] text-sm focus:outline-none focus:ring-2 placeholder:text-[#5F6F84] focus:ring-blue-200 focus:border-[#3F73F6]"
            onClick={e => e.stopPropagation()}
          />
        </div>

        {/* Options list */}
        <div
          className="max-h-[180px] overflow-y-auto overflow-x-hidden 
          [scrollbar-width:thin] [scrollbar-color:#cbd5e0_#f7fafc] 
          [&::-webkit-scrollbar]:w-2 
          [&::-webkit-scrollbar-thumb]:rounded-full 
          [&::-webkit-scrollbar-thumb]:bg-[#cbd5e0] 
          [&::-webkit-scrollbar-track]:bg-gray-100"
        >
          {filteredOptions.length > 0 ? (
            filteredOptions.map((option, index) => (
              <div
                key={index}
                data-searchable-option-index={index}
                onClick={() => toggleOption(option.text)}
                onMouseEnter={() => setHighlightedIndex(index)}
                className={`p-3 cursor-pointer flex items-center transition-colors duration-150 ${selectedValues.includes(option.text)
                  ? 'bg-[#D9E3FD] text-[#3F73F6]'
                  : highlightedIndex === index
                    ? 'bg-[#F1F5F9] text-[#2A2E34]'
                    : 'text-[#2A2E34] hover:bg-[#F8F9FC]'
                  }`}
              >
                <input
                  type="checkbox"
                  checked={selectedValues.includes(option.text)}
                  onChange={() => { }} // Handled by parent div onClick
                  className="mr-2 h-4 w-4 rounded border-gray-300 text-[#3F73F6] focus:ring-[#3F73F6]"
                />
                <span className="text-sm">{option.text}</span>
              </div>
            ))
          ) : (
            <div className="p-3 text-gray-500 text-sm text-center">
              {searchTerm ? 'No attendees found' : 'No attendees available'}
            </div>
          )}
        </div>
      </div>
    );

    return (
      <div className="relative w-full">
        <div
          ref={triggerRef}
          onClick={() => !disabled && setIsOpen(!isOpen)}
          onKeyDown={handleKeyDown}
          tabIndex={disabled ? -1 : 0}
          role="button"
          aria-haspopup="listbox"
          aria-expanded={isOpen}
          className={`
            w-full p-3 pr-10 border font-normal rounded-[12px] text-[#2a2e34] bg-white min-h-[48px] flex items-center flex-wrap gap-1 cursor-pointer
            focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-200 focus-within:border-[#3F73F6]
            ${hasError ? 'border-red-500' : 'border-[#DCE2EB]'}
            ${disabled ? 'bg-gray-100 text-gray-500 cursor-not-allowed' : 'cursor-pointer'}
          `}
        >
          {selectedValues.length > 0 ? (
            selectedValues.map((val, index) => (
              <span
                key={index}
                className="bg-[#F3F5F9] px-2 py-1 rounded-md text-sm flex items-center"
              >
                {val}
                {!disabled && (
                  <button
                    type="button"
                    onClick={e => {
                      e.stopPropagation();
                      removeItem(val);
                    }}
                    className="ml-1 text-gray-500 hover:text-gray-700"
                  >
                    <X size={14} />
                  </button>
                )}
              </span>
            ))
          ) : (
            <span className="text-[#5F6F84]">{placeholder}</span>
          )}
          <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3">
            <ChevronDown
              size={20}
              className={`text-[#5F6F84] transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
            />
          </div>
        </div>

        {/* Render dropdown in portal if open and not disabled */}
        {isOpen &&
          !disabled &&
          typeof window !== 'undefined' &&
          createPortal(<DropdownContent />, document.body)}
      </div>
    );
  };

  // Custom date picker with styled components
  const CustomDatePicker = ({
    selected,
    onChange,
    hasError = false,
    minDate = undefined,
    disabled = false,
  }: {
    selected: Date | null | string;
    onChange: (date: Date | null) => void;
    name: string;
    hasError?: boolean;
    minDate?: Date | null;
    disabled?: boolean;
  }) => {
    console.log('🚀 ~ disabled:', disabled);
    return (
      <div className={`relative w-[190px] ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}>
        <DatePicker
          selected={typeof selected === 'string' ? parseDateString(selected) : selected || null}
          onChange={disabled ? () => { } : onChange}
          dateFormat="MM/dd/yyyy"
          className={`w-full p-3 border font-normal text-[#2a2e34] rounded-[12px]
          focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6]
          placeholder:text-[#5F6F84]
          ${hasError ? 'border-red-500' : 'border-[#DCE2EB]'}
          ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
          placeholderText="Select"
          minDate={minDate || undefined}
          isClearable={false}
          showYearDropdown
          dropdownMode="select"
          disabled={disabled}
        />
        <Image
          src="/calendar.svg"
          alt="calendar"
          width={20}
          height={20}
          className={`absolute right-3 top-1/2 transform -translate-y-1/2 text-[#5F6F84] pointer-events-none
          ${disabled ? 'opacity-50' : ''}`}
        />
      </div>
    );
  };

  // Calculate minimum end time based on start time and date
  const getMinEndTime = (): Date | undefined => {
    if (!startDate || !startTime || !endDate) return undefined;

    // If end date is the same as start date, end time must be after start time
    if (endDate.toDateString() === startDate.toDateString()) {
      return startTime;
    }

    return undefined;
  };

  // Create a max time for end time picker to avoid the "Both minTime and maxTime props required" error
  const getMaxEndTime = (): Date | undefined => {
    // Return a time at the end of the day (11:59 PM)
    const maxTime = new Date();
    maxTime.setHours(23, 59, 0, 0);
    return maxTime;
  };

  // Custom time picker with styled components
  const CustomTimePicker = ({
    selected,
    onChange,
    hasError = false,
    disabled = false,
    minTime,
    maxTime,
  }: {
    selected: Date | null;
    onChange: (time: Date | null) => void;
    name: string;
    hasError?: boolean;
    disabled?: boolean;
    minTime?: Date;
    maxTime?: Date;
  }) => {
    console.log('🚀 ~ disabled:*****', disabled);
    // Create default values if either min or max is missing to avoid the DatePicker error
    const safeMinTime =
      minTime ||
      (() => {
        const min = new Date();
        min.setHours(0, 0, 0, 0);
        return min;
      })();

    const safeMaxTime =
      maxTime ||
      (() => {
        const max = new Date();
        max.setHours(23, 59, 0, 0);
        return max;
      })();

    return (
      <div className={`relative w-[190px] ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}>
        <DatePicker
          selected={selected}
          onChange={disabled ? () => { } : onChange}
          showTimeSelect
          showTimeSelectOnly
          timeIntervals={15}
          timeCaption="Time"
          dateFormat="h:mm aa"
          timeFormat="h:mm aa"
          className={`w-full p-3 border font-normal text-[#2a2e34] rounded-[12px]
          focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6]
          ${hasError ? 'border-red-500' : 'border-[#DCE2EB]'} 
          ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
          placeholderText="Select"
          disabled={disabled}
          popperPlacement="bottom-start"
          popperClassName="time-picker-popper"
          minTime={safeMinTime}
          maxTime={safeMaxTime}
        // Remove injectTimes as it's not needed with consistent min/max
        />
        <Image
          src="/assets/clock.svg"
          alt="clock"
          width={20}
          height={20}
          className={`absolute right-3 top-1/2 transform -translate-y-1/2 text-[#5F6F84] pointer-events-none
          ${disabled ? 'opacity-50' : ''}`}
        />
      </div>
    );
  };
  const isUploading = uploadedFiles.some(file => file.status === 'uploading');

  // Helper function to format date without timezone conversion
  const formatDateForFilename = (date: Date | string | null): string => {
    console.log('🚀 ~ formatDateForFilename ~ raw date:', date);

    if (!date) return '';

    const userTimezone = getUserTimezone();
    const parsedDate = typeof date === 'string' ? new Date(date) : date;
    if (isNaN(parsedDate.getTime())) {
      console.error('Invalid date passed to formatDateForFilename:', date);
      return '';
    }

    // Convert to user's timezone
    const timezoneAwareDate = convertDateToUserTimezone(parsedDate, userTimezone);
    const year = timezoneAwareDate.getFullYear();
    const month = String(timezoneAwareDate.getMonth() + 1).padStart(2, '0');
    const day = String(timezoneAwareDate.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  };

  // Helper function to check if attendees validation is satisfied
  const areAttendeesValid = (): boolean => {
    // Return true if formData is null or if it's "Save court notice only" action
    if (!formData || selectedAction === 'Save court notice only') {
      return true;
    }

    console.log('🚀 ~ areAttendeesValid dhyey ~ formData:', formData);

    if (formData.appointmentAction === 'Cancel') {
      return true;
    }

    const requiredAttendees =
      formData.requiredAttendees
        ?.split(',')
        .map(val => val.trim())
        .filter(val => !val.includes('(Client)') && val.length > 0) || [];

    const optionalAttendees =
      formData.optionalAttendees
        ?.split(',')
        .map(val => val.trim())
        .filter(val => !val.includes('(Client)') && val.length > 0) || [];

    console.log(
      '🚀 ~ areAttendeesValid ~ requiredAttendees:',
      formData.requiredAttendees,
      requiredAttendees.length
    );
    console.log(
      '🚀 ~ areAttendeesValid ~ optionalAttendees:',
      formData.optionalAttendees,
      ' ********** ',
      optionalAttendees.length
    );

    return requiredAttendees.length > 0 || optionalAttendees.length > 0;
  };

  const formatDateToTimezoneString = (date: Date, timezone?: string): string => {
    const userTimezone = timezone || getUserTimezone();
    const timezoneAwareDate = convertDateToUserTimezone(date, userTimezone);
    return timezoneAwareDate.toISOString().split('T')[0];
  };

  // Make sure to return JSX at the end
  return isOpen && formData ? (
    <div
      className="fixed inset-0 rounded-[20px] flex items-center justify-center z-50"
      style={{ backgroundColor: 'rgba(128, 128, 128, 0.3)' }}
    >
      <div
        className={`bg-white rounded-[20px] shadow-lg w-full max-w-xl p-7 max-h-[90vh] overflow-y-auto overflow-x-hidden 
        [scrollbar-width:thin] [scrollbar-color:#cbd5e0_#f7fafc] 
        [&::-webkit-scrollbar]:w-2 
        [&::-webkit-scrollbar-thumb]:rounded-full 
        [&::-webkit-scrollbar-thumb]:bg-[#5F6F84] 
        [&::-webkit-scrollbar-track]:bg-gray-100 ${poppins.className}`}
        style={{
          maskImage: 'linear-gradient(white, white)',
          WebkitMaskImage: 'linear-gradient(white, white)',
        }}
      >
        <div className="flex justify-between items-center mb-[30px]">
          <h2 className="text-[18px] font-medium text-[#2A2E34]">
            {isTaskReviewed
              ? 'View event'
              : titleOfEvent == 'Edit event'
                ? isEditMode
                  ? titleOfEvent
                  : 'View event'
                : titleOfEvent}
          </h2>
          <button
            onClick={handleClose}
            className="flex items-center justify-center text-gray-500 w-[30px] h-[30px] bg-[#F3F5F9] rounded-[8px] cursor-pointer hover:text-[#2A2E34]"
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="mb-[20px]">
            <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">
              Client/Matter
            </label>
            <div className="w-full py-2">
              <a href="#" className="text-[#3F73F6] underline">
                {formData.clientName?.split('|')[0]?.trim() || ''}
              </a>
              {formData.clientName?.includes('|') && (
                <>
                  <span className="mx-2 text-[14px] text-[#5F6F84] underline-0">|</span>
                  <a href="#" className="text-[#3F73F6] underline hover:underline">
                    {formData.clientName?.split('|')[1]?.trim() || ''}
                  </a>
                </>
              )}
            </div>
          </div>

          <div className="mb-[20px] ">
            <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">
              Court notice action
            </label>

            <div className="flex items-center gap-4">
              <CustomSelect1
                name="courtNoticeActions"
                value={formData.courtNoticeActions || ''}
                onChange={handleChange}
                options={courtNoticeActions}
                hasError={!!errors.courtNoticeAction}
                disabled={isTaskReviewed || (titleOfEvent === 'Edit event' && !isEditMode) || isChildWorkflow}
              />
            </div>

            {errors.courtNoticeAction && (
              <p className="text-red-500 text-xs mt-1">{errors.courtNoticeAction}</p>
            )}
          </div>
          {renderFields()}

          <div className="flex justify-between mt-4">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 border border-[#3F73F6] cursor-pointer text-[#3F73F6] rounded-[12px] font-medium bg-white hover:bg-gray-50"
            >
              Close
            </button>
            <button
              type="submit"
              className="flex items-center px-4 py-2 cursor-pointer bg-[#3F73F6] text-white rounded-[12px] font-medium hover:bg-blue-700 disabled:text-[#A2AFC2] disabled:bg-[#EFF2F7] disabled:cursor-not-allowed"
              disabled={
                isTaskReviewed
                  ? true
                  : isUploading || !formData?.courtNoticeActions || !areAttendeesValid()
              }
            >
              {isNew && titleOfEvent != 'Edit event' ? 'Add a new event' : 'Save changes'}
            </button>
          </div>
          {isUploading && (
            <p className="text-xs text-red-500 mt-2">
              Please wait for all files to finish uploading.
            </p>
          )}
        </form>
      </div>
    </div>
  ) : null;
};

export default CourtNoticeFormModal;
