import React, { useState, useEffect, useRef } from 'react';
import { ChevronDown, X, Search } from 'lucide-react';
import { 
  FilterRow as FilterRowType, 
  FilterFieldName, 
  FilterCriteria, 
  FilterOption,
  FILTER_FIELD_CONFIG,
  CRITERIA_LABELS,
  STATUS_OPTIONS,
  TEMPLATE_OPTIONS,
  isDateRangeCriteria,
  isMultiValueCriteria,
} from '@/types/workflowFilter';
import DatePickerSimple from '@/components/common/DatePickerSimple';

interface FilterRowProps {
  filter: FilterRowType;
  onUpdate: (filter: FilterRowType) => void;
  onRemove: () => void;
  canRemove?: boolean;
}

const FilterRow: React.FC<FilterRowProps> = ({
  filter,
  onUpdate,
  onRemove,
  canRemove = true,
}) => {
  const [fieldOptions, setFieldOptions] = useState<FilterOption[]>([]);
  const [isLoadingOptions, setIsLoadingOptions] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isValueDropdownOpen, setIsValueDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Available field names
  const fieldNames = Object.keys(FILTER_FIELD_CONFIG) as FilterFieldName[];

  // Get available criteria for selected field
  const availableCriteria = filter.fieldName 
    ? FILTER_FIELD_CONFIG[filter.fieldName].availableCriteria 
    : [];

  // Get field config
  const fieldConfig = filter.fieldName ? FILTER_FIELD_CONFIG[filter.fieldName] : null;

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsValueDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Fetch options for multi-select fields
  const fetchOptions = async (searchQuery: string = '') => {
    if (!fieldConfig || !fieldConfig.apiEndpoint) return;

    setIsLoadingOptions(true);
    try {
      // Use filterService for API calls
      const { default: filterService } = await import('@/services/api/filterService');

      let options: FilterOption[] = [];

      if (filter.fieldName === 'assignee' || filter.fieldName === 'contact') {
        options = await filterService.getUsersAndRoles(searchQuery);
      } else if (filter.fieldName === 'matter') {
        options = await filterService.getMatters(searchQuery);
      } else if (filter.fieldName === 'attorney') {
        options = await filterService.getAttorneys(searchQuery);
      }

      // Add "Unassigned" option for assignee field
      if (filter.fieldName === 'assignee') {
        options.unshift({ value: 'unassigned', label: 'Unassigned', id: 'unassigned' });
      }

      setFieldOptions(options);
    } catch (error) {
      console.error('Error fetching filter options:', error);
      setFieldOptions([]);
    } finally {
      setIsLoadingOptions(false);
    }
  };

  // Handle field name change
  const handleFieldNameChange = (fieldName: FilterFieldName) => {
    onUpdate({
      ...filter,
      fieldName,
      criteria: '',
      value: null,
      selectedOptions: [],
    });
  };

  // Handle criteria change
  const handleCriteriaChange = (criteria: FilterCriteria) => {
    let newValue: string | string[] | { from: string; to: string } | null = null;

    if (criteria === 'between') {
      newValue = { from: '', to: '' };
    } else if (isMultiValueCriteria(criteria)) {
      newValue = [];
    } else if (criteria === 'unassigned' || criteria === 'none') {
      newValue = criteria;
    }

    onUpdate({
      ...filter,
      criteria,
      value: newValue,
      selectedOptions: [],
    });
  };

  // Handle value change for text inputs
  const handleTextValueChange = (value: string) => {
    onUpdate({
      ...filter,
      value,
    });
  };

  // Handle date value change
  const handleDateValueChange = (date: Date | null, field?: 'from' | 'to') => {
    if (filter.criteria && isDateRangeCriteria(filter.criteria)) {
      const currentValue = (filter.value as { from: string; to: string }) || { from: '', to: '' };
      const dateString = date ? date.toISOString().split('T')[0] : '';

      onUpdate({
        ...filter,
        value: {
          ...currentValue,
          [field!]: dateString,
        },
      });
    } else {
      onUpdate({
        ...filter,
        value: date ? date.toISOString().split('T')[0] : null,
      });
    }
  };

  // Handle multi-select option toggle
  const handleOptionToggle = (option: FilterOption) => {
    const currentValues = Array.isArray(filter.value) ? filter.value : [];
    const currentOptions = filter.selectedOptions || [];
    
    const isSelected = currentValues.includes(option.value);
    
    if (isSelected) {
      // Remove option
      onUpdate({
        ...filter,
        value: currentValues.filter(v => v !== option.value),
        selectedOptions: currentOptions.filter(o => o.value !== option.value),
      });
    } else {
      // Add option
      onUpdate({
        ...filter,
        value: [...currentValues, option.value],
        selectedOptions: [...currentOptions, option],
      });
    }
  };

  // Get display text for selected values
  const getSelectedValuesDisplay = (): string => {
    if (!filter.value) return '';

    if (Array.isArray(filter.value)) {
      const count = filter.value.length;
      if (count === 0) return '';
      if (count === 1) {
        const arrayValue = filter.value as string[];
        const option = filter.selectedOptions?.find(o => o.value === arrayValue[0]);
        return option?.label || arrayValue[0];
      }
      return `${count} Selected`;
    }

    if (typeof filter.value === 'object' && 'from' in filter.value) {
      const { from, to } = filter.value;
      if (from && to) return `${from} - ${to}`;
      if (from) return `From ${from}`;
      if (to) return `To ${to}`;
      return '';
    }

    return filter.value.toString();
  };

  // Load options when field changes to multi-select type
  useEffect(() => {
    if (fieldConfig?.valueType === 'multiSelect') {
      if (filter.fieldName === 'status') {
        setFieldOptions(STATUS_OPTIONS);
      } else if (filter.fieldName === 'templates') {
        setFieldOptions(TEMPLATE_OPTIONS);
      } else if (fieldConfig.apiEndpoint) {
        fetchOptions();
      }
    }
  }, [filter.fieldName]);

  // Filter options based on search term
  const filteredOptions = fieldOptions.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg bg-gray-50">
      {/* Field Name Dropdown */}
      <div className="flex-1 min-w-[120px]">
        <select
          value={filter.fieldName}
          onChange={(e) => handleFieldNameChange(e.target.value as FilterFieldName)}
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">Select Field</option>
          {fieldNames.map(fieldName => (
            <option key={fieldName} value={fieldName}>
              {FILTER_FIELD_CONFIG[fieldName].label}
            </option>
          ))}
        </select>
      </div>

      {/* Criteria Dropdown */}
      <div className="flex-1 min-w-[120px]">
        <select
          value={filter.criteria}
          onChange={(e) => handleCriteriaChange(e.target.value as FilterCriteria)}
          disabled={!filter.fieldName}
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
        >
          <option value="">Select Criteria</option>
          {availableCriteria.map(criteria => (
            <option key={criteria} value={criteria}>
              {CRITERIA_LABELS[criteria]}
            </option>
          ))}
        </select>
      </div>

      {/* Value Selection */}
      <div className="flex-1 min-w-[150px]">
        {filter.criteria === 'unassigned' || filter.criteria === 'none' ? (
          <div className="p-2 text-sm text-gray-500 italic">
            No value required
          </div>
        ) : fieldConfig?.valueType === 'text' ? (
          <input
            type="text"
            value={filter.value as string || ''}
            onChange={(e) => handleTextValueChange(e.target.value)}
            placeholder="Enter value"
            className="w-full p-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        ) : fieldConfig?.valueType === 'date' ? (
          filter.criteria && isDateRangeCriteria(filter.criteria) ? (
            <div className="flex gap-2">
              <DatePickerSimple
                selectedDate={(filter.value as { from: string; to: string })?.from ? new Date((filter.value as { from: string; to: string }).from) : null}
                onDateChange={(date) => handleDateValueChange(date, 'from')}
                className="flex-1"
              />
              <DatePickerSimple
                selectedDate={(filter.value as { from: string; to: string })?.to ? new Date((filter.value as { from: string; to: string }).to) : null}
                onDateChange={(date) => handleDateValueChange(date, 'to')}
                className="flex-1"
              />
            </div>
          ) : (
            <DatePickerSimple
              selectedDate={filter.value ? new Date(filter.value as string) : null}
              onDateChange={(date) => handleDateValueChange(date)}
            />
          )
        ) : fieldConfig?.valueType === 'multiSelect' ? (
          <div className="relative" ref={dropdownRef}>
            <div
              onClick={() => setIsValueDropdownOpen(!isValueDropdownOpen)}
              className="w-full p-2 border border-gray-300 rounded-md text-sm cursor-pointer bg-white flex items-center justify-between"
            >
              <span className={getSelectedValuesDisplay() ? 'text-gray-900' : 'text-gray-500'}>
                {getSelectedValuesDisplay() || 'Search & select'}
              </span>
              <ChevronDown size={16} />
            </div>

            {isValueDropdownOpen && (
              <div className="absolute z-50 mt-1 w-full bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-hidden">
                {fieldConfig.searchable && (
                  <div className="p-2 border-b border-gray-200">
                    <div className="relative">
                      <Search size={16} className="absolute left-2 top-2 text-gray-400" />
                      <input
                        type="text"
                        value={searchTerm}
                        onChange={(e) => {
                          setSearchTerm(e.target.value);
                          if (fieldConfig.apiEndpoint) {
                            fetchOptions(e.target.value);
                          }
                        }}
                        placeholder="Search..."
                        className="w-full pl-8 pr-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                )}
                
                <div className="max-h-40 overflow-y-auto">
                  {isLoadingOptions ? (
                    <div className="p-3 text-center text-gray-500">Loading...</div>
                  ) : filteredOptions.length > 0 ? (
                    filteredOptions.map(option => {
                      const isSelected = Array.isArray(filter.value) && filter.value.includes(option.value);
                      return (
                        <div
                          key={option.value}
                          onClick={() => handleOptionToggle(option)}
                          className={`p-2 cursor-pointer hover:bg-gray-50 flex items-center gap-2 ${
                            isSelected ? 'bg-blue-50 text-blue-600' : ''
                          }`}
                        >
                          <input
                            type="checkbox"
                            checked={isSelected}
                            onChange={() => {}} // Handled by onClick
                            className="rounded"
                          />
                          <span className="text-sm">{option.label}</span>
                        </div>
                      );
                    })
                  ) : (
                    <div className="p-3 text-center text-gray-500">No options found</div>
                  )}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="p-2 text-sm text-gray-500 italic">
            Select criteria first
          </div>
        )}
      </div>

      {/* Remove Button */}
      {canRemove && (
        <button
          onClick={onRemove}
          className="p-2 text-gray-400 hover:text-red-500 transition-colors"
          title="Remove filter"
        >
          <X size={16} />
        </button>
      )}
    </div>
  );
};

export default FilterRow;
