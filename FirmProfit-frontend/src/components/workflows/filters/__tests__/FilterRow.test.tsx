import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import FilterRow from '../FilterRow';
import { createEmptyFilterRow } from '@/types/workflowFilter';

// Mock the filter service
jest.mock('@/services/api/filterService', () => ({
  default: {
    getUsersAndRoles: jest.fn().mockResolvedValue([
      { value: 'user1', label: '<PERSON>', id: 'user1' },
      { value: 'user2', label: '<PERSON>', id: 'user2' },
    ]),
    getMatters: jest.fn().mockResolvedValue([
      { value: 'matter1', label: 'Client A / Matter 1', id: 'matter1' },
      { value: 'matter2', label: 'Client B / Matter 2', id: 'matter2' },
    ]),
    getAttorneys: jest.fn().mockResolvedValue([
      { value: 'attorney1', label: '<PERSON> <PERSON>', id: 'attorney1' },
    ]),
  },
}));

describe('FilterRow', () => {
  const mockOnUpdate = jest.fn();
  const mockOnRemove = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders empty filter row correctly', () => {
    const filter = createEmptyFilterRow();
    
    render(
      <FilterRow
        filter={filter}
        onUpdate={mockOnUpdate}
        onRemove={mockOnRemove}
      />
    );

    expect(screen.getByDisplayValue('Select Field')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Select Criteria')).toBeInTheDocument();
  });

  it('updates field name when selected', () => {
    const filter = createEmptyFilterRow();
    
    render(
      <FilterRow
        filter={filter}
        onUpdate={mockOnUpdate}
        onRemove={mockOnRemove}
      />
    );

    const fieldSelect = screen.getByDisplayValue('Select Field');
    fireEvent.change(fieldSelect, { target: { value: 'name' } });

    expect(mockOnUpdate).toHaveBeenCalledWith({
      ...filter,
      fieldName: 'name',
      criteria: '',
      value: null,
      selectedOptions: [],
    });
  });

  it('shows criteria options when field is selected', () => {
    const filter = {
      ...createEmptyFilterRow(),
      fieldName: 'name' as const,
    };
    
    render(
      <FilterRow
        filter={filter}
        onUpdate={mockOnUpdate}
        onRemove={mockOnRemove}
      />
    );

    const criteriaSelect = screen.getByDisplayValue('Select Criteria');
    expect(criteriaSelect).not.toBeDisabled();
  });

  it('shows text input for name field with text criteria', () => {
    const filter = {
      ...createEmptyFilterRow(),
      fieldName: 'name' as const,
      criteria: 'contains' as const,
    };
    
    render(
      <FilterRow
        filter={filter}
        onUpdate={mockOnUpdate}
        onRemove={mockOnRemove}
      />
    );

    expect(screen.getByPlaceholderText('Enter value')).toBeInTheDocument();
  });

  it('shows "No value required" for unassigned criteria', () => {
    const filter = {
      ...createEmptyFilterRow(),
      fieldName: 'assignee' as const,
      criteria: 'unassigned' as const,
    };
    
    render(
      <FilterRow
        filter={filter}
        onUpdate={mockOnUpdate}
        onRemove={mockOnRemove}
      />
    );

    expect(screen.getByText('No value required')).toBeInTheDocument();
  });

  it('calls onRemove when remove button is clicked', () => {
    const filter = createEmptyFilterRow();
    
    render(
      <FilterRow
        filter={filter}
        onUpdate={mockOnUpdate}
        onRemove={mockOnRemove}
        canRemove={true}
      />
    );

    const removeButton = screen.getByTitle('Remove filter');
    fireEvent.click(removeButton);

    expect(mockOnRemove).toHaveBeenCalled();
  });

  it('does not show remove button when canRemove is false', () => {
    const filter = createEmptyFilterRow();
    
    render(
      <FilterRow
        filter={filter}
        onUpdate={mockOnUpdate}
        onRemove={mockOnRemove}
        canRemove={false}
      />
    );

    expect(screen.queryByTitle('Remove filter')).not.toBeInTheDocument();
  });
});
