import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import AdvancedFilterModal from '../AdvancedFilterModal';
import { DEFAULT_MY_WORKFLOWS_FILTER } from '@/types/workflowFilter';

// Mock the filter service
jest.mock('@/services/api/filterService', () => ({
  default: {
    getUsersAndRoles: jest.fn().mockResolvedValue([
      { value: 'user1', label: '<PERSON>', id: 'user1' },
      { value: 'user2', label: '<PERSON>', id: 'user2' },
    ]),
  },
}));

describe('AdvancedFilterModal', () => {
  const mockOnClose = jest.fn();
  const mockOnApply = jest.fn();
  const mockCurrentUser = { id: 'user1', name: '<PERSON>' };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('does not render when isOpen is false', () => {
    render(
      <AdvancedFilterModal
        isOpen={false}
        onClose={mockOnClose}
        onApply={mockOnApply}
        currentUser={mockCurrentUser}
      />
    );

    expect(screen.queryByText('Filter Workflows')).not.toBeInTheDocument();
  });

  it('renders with default filter when opened', () => {
    render(
      <AdvancedFilterModal
        isOpen={true}
        onClose={mockOnClose}
        onApply={mockOnApply}
        currentUser={mockCurrentUser}
      />
    );

    expect(screen.getByText('Filter Workflows')).toBeInTheDocument();
    expect(screen.getByText('1 Filter')).toBeInTheDocument();
  });

  it('shows initial filters when provided', () => {
    const initialFilters = [
      {
        ...DEFAULT_MY_WORKFLOWS_FILTER,
        value: ['user1'],
        selectedOptions: [{ value: 'user1', label: 'John Doe' }],
      },
    ];

    render(
      <AdvancedFilterModal
        isOpen={true}
        onClose={mockOnClose}
        onApply={mockOnApply}
        initialFilters={initialFilters}
        currentUser={mockCurrentUser}
      />
    );

    expect(screen.getByText('1 Filter')).toBeInTheDocument();
  });

  it('adds new filter row when Add Filter button is clicked', () => {
    render(
      <AdvancedFilterModal
        isOpen={true}
        onClose={mockOnClose}
        onApply={mockOnApply}
        currentUser={mockCurrentUser}
      />
    );

    const addFilterButton = screen.getByText('Add Filter');
    fireEvent.click(addFilterButton);

    // Should now show 2 filters (default + new one)
    expect(screen.getByText('2 Filters')).toBeInTheDocument();
  });

  it('calls onApply with filters when Apply button is clicked', () => {
    render(
      <AdvancedFilterModal
        isOpen={true}
        onClose={mockOnClose}
        onApply={mockOnApply}
        currentUser={mockCurrentUser}
      />
    );

    const applyButton = screen.getByText('Apply');
    fireEvent.click(applyButton);

    expect(mockOnApply).toHaveBeenCalled();
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('calls onClose when Cancel button is clicked', () => {
    render(
      <AdvancedFilterModal
        isOpen={true}
        onClose={mockOnClose}
        onApply={mockOnApply}
        currentUser={mockCurrentUser}
      />
    );

    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('calls onClose when X button is clicked', () => {
    render(
      <AdvancedFilterModal
        isOpen={true}
        onClose={mockOnClose}
        onApply={mockOnApply}
        currentUser={mockCurrentUser}
      />
    );

    const closeButton = screen.getByRole('button', { name: '' }); // X button
    fireEvent.click(closeButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('resets to default filter when Reset to Default button is clicked', () => {
    render(
      <AdvancedFilterModal
        isOpen={true}
        onClose={mockOnClose}
        onApply={mockOnApply}
        currentUser={mockCurrentUser}
      />
    );

    // Add a filter first
    const addFilterButton = screen.getByText('Add Filter');
    fireEvent.click(addFilterButton);

    // Should show 2 filters
    expect(screen.getByText('2 Filters')).toBeInTheDocument();

    // Reset to default
    const resetButton = screen.getByText('Reset to Default');
    fireEvent.click(resetButton);

    // Should be back to 1 filter
    expect(screen.getByText('1 Filter')).toBeInTheDocument();
  });
});
