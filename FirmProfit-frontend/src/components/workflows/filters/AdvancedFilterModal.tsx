import React, { useState, useEffect, useRef } from 'react';
import { X, Plus } from 'lucide-react';
import FilterRow from './FilterRow';
import { 
  FilterState, 
  FilterRow as FilterRowType,
  createEmptyFilterRow,
  validateFilterRow,
  DEFAULT_MY_WORKFLOWS_FILTER,
} from '@/types/workflowFilter';

interface AdvancedFilterModalProps {
  isOpen: boolean;
  onClose: () => void;
  onApply: (filters: FilterRowType[]) => void;
  initialFilters?: FilterRowType[];
  currentUser?: { id: string; name: string };
}

const AdvancedFilterModal: React.FC<AdvancedFilterModalProps> = ({
  isOpen,
  onClose,
  onApply,
  initialFilters = [],
  currentUser,
}) => {
  const [filterState, setFilterState] = useState<FilterState>({
    rows: [],
    isActive: false,
    appliedFilters: [],
  });
  const modalRef = useRef<HTMLDivElement>(null);

  // Initialize filters when modal opens
  useEffect(() => {
    if (isOpen) {
      if (initialFilters.length > 0) {
        setFilterState({
          rows: [...initialFilters],
          isActive: true,
          appliedFilters: [...initialFilters],
        });
      } else {
        // Set default "My Workflows" filter
        const defaultFilter = {
          ...DEFAULT_MY_WORKFLOWS_FILTER,
          value: currentUser ? [currentUser.id] : [],
          selectedOptions: currentUser ? [{ value: currentUser.id, label: currentUser.name }] : [],
        };
        
        setFilterState({
          rows: [defaultFilter],
          isActive: true,
          appliedFilters: [defaultFilter],
        });
      }
    }
  }, [isOpen, initialFilters, currentUser]);

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen, onClose]);

  // Add new filter row
  const addFilterRow = () => {
    const newRow = createEmptyFilterRow();
    setFilterState(prev => ({
      ...prev,
      rows: [...prev.rows, newRow],
    }));
  };

  // Update filter row
  const updateFilterRow = (index: number, updatedRow: FilterRowType) => {
    setFilterState(prev => ({
      ...prev,
      rows: prev.rows.map((row, i) => i === index ? updatedRow : row),
    }));
  };

  // Remove filter row
  const removeFilterRow = (index: number) => {
    setFilterState(prev => ({
      ...prev,
      rows: prev.rows.filter((_, i) => i !== index),
    }));
  };

  // Reset to default filter
  const resetFilters = () => {
    const defaultFilter = {
      ...DEFAULT_MY_WORKFLOWS_FILTER,
      value: currentUser ? [currentUser.id] : [],
      selectedOptions: currentUser ? [{ value: currentUser.id, label: currentUser.name }] : [],
    };
    
    setFilterState({
      rows: [defaultFilter],
      isActive: true,
      appliedFilters: [defaultFilter],
    });
  };

  // Apply filters
  const handleApply = () => {
    // Validate all filter rows
    const validRows = filterState.rows.filter(row => {
      const validation = validateFilterRow(row);
      return validation.isValid;
    });

    if (validRows.length === 0) {
      // If no valid filters, apply default filter
      resetFilters();
      onApply([{
        ...DEFAULT_MY_WORKFLOWS_FILTER,
        value: currentUser ? [currentUser.id] : [],
        selectedOptions: currentUser ? [{ value: currentUser.id, label: currentUser.name }] : [],
      }]);
    } else {
      setFilterState(prev => ({
        ...prev,
        appliedFilters: validRows,
      }));
      onApply(validRows);
    }
    
    onClose();
  };

  // Get filter count for display
  const getFilterCount = (): number => {
    return filterState.rows.filter(row => validateFilterRow(row).isValid).length;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div
        ref={modalRef}
        className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[80vh] overflow-hidden"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <h2 className="text-xl font-semibold text-gray-900">Filter Workflows</h2>
            <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
              {getFilterCount()} {getFilterCount() === 1 ? 'Filter' : 'Filters'}
            </span>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 max-h-[60vh] overflow-y-auto">
          <div className="space-y-4">
            {filterState.rows.map((row, index) => (
              <FilterRow
                key={row.id}
                filter={row}
                onUpdate={(updatedRow) => updateFilterRow(index, updatedRow)}
                onRemove={() => removeFilterRow(index)}
                canRemove={filterState.rows.length > 1}
              />
            ))}
          </div>

          {/* Add Filter Button */}
          <button
            onClick={addFilterRow}
            className="mt-4 flex items-center gap-2 px-4 py-2 text-blue-600 border border-blue-600 rounded-md hover:bg-blue-50 transition-colors"
          >
            <Plus size={16} />
            Add Filter
          </button>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={resetFilters}
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            Reset to Default
          </button>
          
          <div className="flex gap-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleApply}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Apply
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdvancedFilterModal;
