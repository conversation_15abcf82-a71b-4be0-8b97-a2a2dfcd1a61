import React, { useState, useEffect, useRef } from 'react';
import { X, Plus } from 'lucide-react';
import FilterRow from './FilterRow';
import { 
  FilterState, 
  FilterRow as FilterRowType,
  createEmptyFilterRow,
  validateFilterRow,
  DEFAULT_MY_WORKFLOWS_FILTER,
} from '@/types/workflowFilter';

interface AdvancedFilterModalProps {
  isOpen: boolean;
  onClose: () => void;
  onApply: (filters: FilterRowType[]) => void;
  initialFilters?: FilterRowType[];
  currentUser?: { id: string; name: string };
}

const AdvancedFilterModal: React.FC<AdvancedFilterModalProps> = ({
  isOpen,
  onClose,
  onApply,
  initialFilters = [],
  currentUser,
}) => {
  const [filterState, setFilterState] = useState<FilterState>({
    rows: [],
    isActive: false,
    appliedFilters: [],
  });
  const modalRef = useRef<HTMLDivElement>(null);

  // Initialize filters when modal opens
  useEffect(() => {
    if (isOpen) {
      if (initialFilters.length > 0) {
        setFilterState({
          rows: [...initialFilters],
          isActive: true,
          appliedFilters: [...initialFilters],
        });
      } else {
        // Set default "My Workflows" filter
        const defaultFilter = {
          ...DEFAULT_MY_WORKFLOWS_FILTER,
          value: currentUser ? [currentUser.id] : [],
          selectedOptions: currentUser ? [{ value: currentUser.id, label: currentUser.name }] : [],
        };
        
        setFilterState({
          rows: [defaultFilter],
          isActive: true,
          appliedFilters: [defaultFilter],
        });
      }
    }
  }, [isOpen, initialFilters, currentUser]);

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen, onClose]);

  // Add new filter row
  const addFilterRow = () => {
    const newRow = createEmptyFilterRow();
    setFilterState(prev => ({
      ...prev,
      rows: [...prev.rows, newRow],
    }));
  };

  // Update filter row
  const updateFilterRow = (index: number, updatedRow: FilterRowType) => {
    setFilterState(prev => ({
      ...prev,
      rows: prev.rows.map((row, i) => i === index ? updatedRow : row),
    }));
  };

  // Remove filter row
  const removeFilterRow = (index: number) => {
    setFilterState(prev => ({
      ...prev,
      rows: prev.rows.filter((_, i) => i !== index),
    }));
  };

  // Reset to default filter
  const resetFilters = () => {
    const defaultFilter = {
      ...DEFAULT_MY_WORKFLOWS_FILTER,
      value: currentUser ? [currentUser.id] : [],
      selectedOptions: currentUser ? [{ value: currentUser.id, label: currentUser.name }] : [],
    };
    
    setFilterState({
      rows: [defaultFilter],
      isActive: true,
      appliedFilters: [defaultFilter],
    });
  };

  // Apply filters
  const handleApply = () => {
    // Validate all filter rows
    const validRows = filterState.rows.filter(row => {
      const validation = validateFilterRow(row);
      return validation.isValid;
    });

    if (validRows.length === 0) {
      // If no valid filters, apply default filter
      resetFilters();
      onApply([{
        ...DEFAULT_MY_WORKFLOWS_FILTER,
        value: currentUser ? [currentUser.id] : [],
        selectedOptions: currentUser ? [{ value: currentUser.id, label: currentUser.name }] : [],
      }]);
    } else {
      setFilterState(prev => ({
        ...prev,
        appliedFilters: validRows,
      }));
      onApply(validRows);
    }
    
    onClose();
  };

  // Get filter count for display
  const getFilterCount = (): number => {
    return filterState.rows.filter(row => validateFilterRow(row).isValid).length;
  };

  if (!isOpen) return null;

  return (
    <div
      ref={modalRef}
      className="absolute top-full right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg w-[800px] z-50"
    >
      {/* Filter Rows */}
      <div className="p-4 space-y-3">
        {filterState.rows.map((row, index) => (
          <FilterRow
            key={row.id}
            filter={row}
            onUpdate={(updatedRow) => updateFilterRow(index, updatedRow)}
            onRemove={() => removeFilterRow(index)}
            canRemove={filterState.rows.length > 1}
          />
        ))}

        {/* Add Filter Button */}
        <button
          onClick={addFilterRow}
          className="flex items-center gap-2 px-3 py-2 text-blue-600 text-sm hover:bg-blue-50 transition-colors"
        >
          <Plus size={16} />
          Add Filter
        </button>
      </div>

      {/* Apply Button */}
      <div className="flex justify-end p-4 border-t border-gray-200">
        <button
          onClick={handleApply}
          className="px-6 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
        >
          Apply
        </button>
      </div>
    </div>
  );
};

export default AdvancedFilterModal;
