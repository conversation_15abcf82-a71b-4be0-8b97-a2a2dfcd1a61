import React, { useState, useEffect } from 'react';
import { workflowService } from '@/services/api';
import filterService from '@/services/api/filterService';
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll';
import { formatDate } from '@/utils/dateUtils';
import { DataTable, PageHeader, StatusBadge, Column } from '@/components/common';
import { Workflow } from '@/types/workflow';
import {
  FilterRow as FilterRowType,
  WorkflowFilterParams,
  DEFAULT_MY_WORKFLOWS_FILTER,
} from '@/types/workflowFilter';
import { AdvancedFilterModal } from './filters';
import Image from 'next/image';
import { mapStatusVariant } from '@/types/courtNotice';
import { useRouter } from 'next/router';

interface WorkflowsAssignedProps {
  onNameClick: (id: string) => void;
}

const WorkflowsAssigned: React.FC<WorkflowsAssignedProps> = () => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [selectedWorkflows, setSelectedWorkflows] = useState<string[]>([]);
  const [currentUser, setCurrentUser] = useState<{ id: string; name: string } | null>(null);
  const [appliedFilters, setAppliedFilters] = useState<FilterRowType[]>([]);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [filterCount, setFilterCount] = useState(1); // Default filter count
  const limit = 10;

  const fetchWorkflows = async (pageNum: number, filters?: FilterRowType[]) => {
    try {
      setLoading(true);

      // Convert filters to API format
      const filterParams: WorkflowFilterParams = {
        page: pageNum,
        limit,
      };

      if (filters && filters.length > 0) {
        filterParams.filters = filters.map(filter => ({
          fieldName: filter.fieldName,
          criteria: filter.criteria,
          value: filter.value,
        }));
      }

      const response = await workflowService.getMyWorkflows(pageNum, limit, filterParams);
      const newWorkflows = response.data.workflows;

      // If it's the first page, replace the workflows
      // Otherwise append the new workflows to the existing ones
      if (pageNum === 1) {
        setWorkflows(newWorkflows);
      } else {
        setWorkflows(prev => [...prev, ...newWorkflows]);
      }

      setHasMore(newWorkflows.length === limit);
    } catch (error) {
      console.error('Error fetching workflows:', error);
    } finally {
      setLoading(false);
    }
  };

  // Initialize current user and default filter
  useEffect(() => {
    const initializeComponent = async () => {
      try {
        // Get current user (in a real app, this would come from auth context)
        const user = await filterService.getCurrentUser();
        if (user) {
          setCurrentUser(user);

          // Set default "My Workflows" filter
          const defaultFilter = {
            ...DEFAULT_MY_WORKFLOWS_FILTER,
            value: [user.id],
            selectedOptions: [{ value: user.id, label: user.name }],
          };

          setAppliedFilters([defaultFilter]);
          setFilterCount(1);

          // Fetch workflows with default filter
          setPage(1);
          setWorkflows([]);
          fetchWorkflows(1, [defaultFilter]);
        } else {
          // Fallback: fetch without filters
          setPage(1);
          setWorkflows([]);
          fetchWorkflows(1);
        }
      } catch (error) {
        console.error('Error initializing component:', error);
        // Fallback: fetch without filters
        setPage(1);
        setWorkflows([]);
        fetchWorkflows(1);
      }
    };

    initializeComponent();
  }, []);

  // Fetch more data when page changes
  useEffect(() => {
    if (page > 1) {
      fetchWorkflows(page, appliedFilters);
    }
  }, [page, appliedFilters]);

  // Filter workflows based on search term
  const filteredWorkflows = workflows.filter(
    workflow =>
      workflow.work_flow_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      workflow.view.some((v: string) => v.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const lastWorkflowElementRef = useInfiniteScroll({
    loading,
    hasMore,
    onLoadMore: () => setPage(prev => prev + 1),
  });

  const handleSelectWorkflow = (id: string, selected: boolean) => {
    if (selected) {
      setSelectedWorkflows(prev => [...prev, id]);
    } else {
      setSelectedWorkflows(prev => prev.filter(wfId => wfId !== id));
    }
  };

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedWorkflows(filteredWorkflows.map(workflow => workflow.id));
    } else {
      setSelectedWorkflows([]);
    }
  };

  const handleFilter = () => {
    setIsFilterModalOpen(true);
  };

  const handleApplyFilters = (filters: FilterRowType[]) => {
    setAppliedFilters(filters);
    setFilterCount(filters.length);

    // Reset pagination and fetch with new filters
    setPage(1);
    setWorkflows([]);
    fetchWorkflows(1, filters);
  };

  const handleColumns = () => {
    console.log('Columns button clicked');
  };

  const handleSaveView = () => {
    console.log('Save view button clicked');
  };

  const handleRowClick = (rowData: Workflow) => {
    // khushbu add taskId and work_flow_id
    // router.push(`/workflowrun?taskId=${rowData.id}&work_flow_id=${rowData.id}`);
    router.push(`/workflowrun?taskId=${rowData.latest_task_id}&work_flow_id=${rowData.id}`);
  };

  const columns: Column[] = [
    {
      id: 'name',
      header: 'Name',
      cell: (row: Workflow) => (
        <div
          className="text-[14px] font-medium cursor-pointer text-[#2A2E34]"
          onClick={() => handleRowClick(row)}
        >
          <span>
            {row.work_flow_name.split(',').length > 2
              ? row.work_flow_name.split(',').slice(0, 2).join('   &') + '...'
              : row.work_flow_name.split(',').join('   &')}
          </span>
        </div>
      ),
    },
    {
      id: 'started',
      header: 'Started',
      cell: (row: Workflow) => (
        <div
          className="text-[14px] text-[#5F6F84] cursor-pointer"
          onClick={() => handleRowClick(row)}
        >
          {formatDate(row.start_date)}
        </div>
      ),
    },
    {
      id: 'dueOn',
      header: 'Due On',
      cell: (row: Workflow) => (
        <div
          className="text-[14px] text-[#5F6F84] cursor-pointer"
          onClick={() => handleRowClick(row)}
        >
          {formatDate(row.end_date)}
        </div>
      ),
    },
    {
      id: 'taskCompleted',
      header: 'Task Completed',
      cell: (row: Workflow) => (
        <div
          className="text-[14px] text-[#5F6F84] cursor-pointer"
          onClick={() => handleRowClick(row)}
        >
          {row.task_complete}
        </div>
      ),
    },
    {
      id: 'status',
      header: 'Status',
      cell: (row: Workflow) => (
        <div onClick={() => handleRowClick(row)} className="cursor-pointer">
          <StatusBadge status={mapStatusVariant(row.status)} />
        </div>
      ),
    },
    {
      id: 'activity',
      header: 'Activity',
      cell: (row: Workflow) => (
        <div className="text-[14px] text-[#5F6F84]">{row.last_activity}</div>
      ),
    },
    {
      id: 'assignee',
      header: 'Assignee',
      cell: (row: Workflow) => (
        <div
          className="relative bg-[#3f73f65e] w-7 h-7 rounded-full flex items-center justify-center cursor-pointer"
          onClick={() => handleRowClick(row)}
        >
          <Image src="/assets/ai-robot-new-2.svg" alt="AI assistant" width={25} height={25} />
          <span className="absolute -bottom-1 -right-1 bg-[#3F73F6] text-white text-[10px] w-4 h-4 flex items-center justify-center rounded-full">
            3
          </span>
        </div>
      ),
    },
    // {
    //   id: 'notes',
    //   header: 'Notes',
    //   cell: (row: Workflow) => (
    //     <div className="text-[14px] text-[#5F6F84]">
    //       {row.notes}
    //     </div>
    //   ),
    // },
  ];

  return (
    <div className="p-8 h-full">
      <PageHeader
        title="Workflows Assigned to Me"
        searchValue={searchTerm}
        onSearchChange={setSearchTerm}
        onFilter={handleFilter}
        onColumns={handleColumns}
        onSaveView={handleSaveView}
        filterCount={filterCount}
      />

      <DataTable
        columns={columns}
        data={filteredWorkflows}
        selectedIds={selectedWorkflows}
        onSelectRow={handleSelectWorkflow}
        onSelectAll={handleSelectAll}
        isAllSelected={
          selectedWorkflows.length === filteredWorkflows.length && filteredWorkflows.length > 0
        }
        isLoading={loading}
        lastItemRef={lastWorkflowElementRef}
        className="mt-8"
      />

      {/* Advanced Filter Modal */}
      <AdvancedFilterModal
        isOpen={isFilterModalOpen}
        onClose={() => setIsFilterModalOpen(false)}
        onApply={handleApplyFilters}
        initialFilters={appliedFilters}
        currentUser={currentUser}
      />
    </div>
  );
};

export default WorkflowsAssigned;
