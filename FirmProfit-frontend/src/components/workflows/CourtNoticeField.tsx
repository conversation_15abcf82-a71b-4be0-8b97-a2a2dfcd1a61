import React, { useState, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import CourtNotic<PERSON>, { ClientMatter, EventType } from './CourtNotice';

export interface CourtNoticeFieldProps {
  field: {
    id: string;
    _id: string;
    type: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    value?: any;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    options?: any;
    workflow_id: string;
  };
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onInputChange: (fieldId: string, value: any) => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onBlur?: (fieldId: string, value: any) => void;
}

const CourtNoticeField: React.FC<CourtNoticeFieldProps> = ({ field, onInputChange, onBlur }) => {
  // Extract initial data from field
  const initialValue = field.value || {
    clients: [],
    events: {},
  };

  // Extract court notice options if they exist
  const getCourtNoticeData = () => {
    if (field.options && Array.isArray(field.options)) {
      for (const option of field.options) {
        if (option.clients || option.events) {
          return {
            clients: option.clients || [],
            events: option.events || {},
          };
        }
      }
    }
    return { clients: [], events: {} };
  };

  const { clients: optionClients, events: optionEvents } = getCourtNoticeData();

  // Initialize from field value or options
  const initialClients = initialValue.clients || optionClients;
  const initialEvents = initialValue.events || optionEvents;

  // State for clients and events
  const [clients, setClients] = useState<ClientMatter[]>(initialClients);
  const [events, setEvents] = useState<Record<string, EventType[]>>(initialEvents);

  // Update the field value when clients or events change
  useEffect(() => {
    const updatedValue = {
      clients,
      events,
    };

    onInputChange(field._id, updatedValue);

    // Call onBlur to trigger save if needed
    if (onBlur) {
      onBlur(field._id, updatedValue);
    }
  }, [clients, events, field._id, onInputChange, onBlur]);

  // Handler for saving events
  const handleSaveEvents = (clientId: string, updatedEvents: EventType[]) => {
    setEvents(prev => ({
      ...prev,
      [clientId]: updatedEvents,
    }));
  };

  // Handler for adding a new event
  const handleAddEvent = (clientId: string) => {
    const client = clients.find(c => c.id === clientId);
    if (!client) return;

    const newEvent: EventType = {
      id: uuidv4(),
      clientMatterId: clientId,
      caseNumber: 'New Case',
      clientName: client.name.split('|')[0].trim(),
      description: 'Client must appear',
      date: new Date().toLocaleDateString('en-US', {
        month: '2-digit',
        day: '2-digit',
        year: 'numeric',
      }),
      startTime: '10:00 am',
      endTime: '11:00 am',
      isCompleted: false,
      _id: ''
    };

    setEvents(prev => {
      const updatedEvents = {
        ...prev,
        [clientId]: [...(prev[clientId] || []), newEvent],
      };
      
      const updatedValue = {
        clients,
        events: updatedEvents,
      };
      
      onInputChange(field._id, updatedValue);
      
      if (onBlur) {
        onBlur(field._id, updatedValue);
      }
      
      return updatedEvents;
    });
  };

  // Handler for deleting an event
  const handleDeleteEvent = (clientId: string, eventId: string) => {
    setEvents(prev => {
      const clientEvents = prev[clientId] || [];
      const updatedEvents = {
        ...prev,
        [clientId]: clientEvents.filter(event => event.id !== eventId),
      };
      
      // Update parent component's state immediately
      const updatedValue = {
        clients,
        events: updatedEvents,
      };
      
      // Notify parent component of the change
      onInputChange(field._id, updatedValue);
      
      // If onBlur exists, call it to trigger any save operations
      if (onBlur) {
        onBlur(field._id, updatedValue);
      }
      
      return updatedEvents;
    });
  };

  // Handler for adding a new contact
  const handleAddContact = (status: boolean) => {
    // Typically this would open a modal to select a client
    // For demo purposes, we'll add a dummy client
    // console.log('🚀 ~ handleAddContact ~ dhyey newClient:', status);
    if (status) {
      const newClientId = uuidv4();
      const newClient: ClientMatter = {
        id: newClientId,
        name: `New Client ${clients.length + 1} | Sample Case`,
        matters: [],
      };


      // First update the clients
      const updatedClients = [...clients, newClient];
      setClients(updatedClients);
      
      // Then update the events with the new client
      setEvents(prev => {
        const updatedEvents = {
          ...prev,
          [newClientId]: [], // Initialize with empty array
        };
        
        // Update parent component's state immediately
        const updatedValue = {
          clients: updatedClients,
          events: updatedEvents,
        };
        
        // Notify parent component of the change
        onInputChange(field._id, updatedValue);
        
        // If onBlur exists, call it to trigger any save operations
        if (onBlur) {
          onBlur(field._id, updatedValue);
        }
        
        return updatedEvents;
      });
    }
  };



  return (
    <div className="mb-5">
      <CourtNotice
        clients={clients}
        events={events}
        setClients={setClients}
        setEvents={setEvents}
        options={field.options}
        onSave={handleSaveEvents}
        onAddEvent={handleAddEvent}
        onDeleteEvent={handleDeleteEvent}
        onAddContact={handleAddContact}
      />
    </div>
  );
};

export default CourtNoticeField;
