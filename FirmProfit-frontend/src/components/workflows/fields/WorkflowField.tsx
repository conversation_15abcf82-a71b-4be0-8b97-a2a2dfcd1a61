import React from 'react';
import { ChevronDown, User, Mail, Phone, FileText } from 'lucide-react';
import { workflowStyles } from '@/styles/workflow';
import { FormSection, FormField, FormDataType } from '@/types/workflow';
import { FIELD_TYPES } from '@/constants/workflow';

interface WorkflowFieldProps {
  section: FormSection;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onInputChange: (fieldId: string, value: any) => void;
  formData: FormDataType;
}

/**
 * WorkflowField Component
 * Renders a section of form fields in a workflow
 */
const WorkflowField: React.FC<WorkflowFieldProps> = ({ section, onInputChange, formData }) => {
  // Get the appropriate icon based on field type
  const getFieldIcon = (type: string) => {
    switch (type) {
      case FIELD_TYPES.FULLNAME:
        return <User className={workflowStyles.titleIcon} size={18} />;
      case FIELD_TYPES.EMAIL:
        return <Mail className={workflowStyles.titleIcon} size={18} />;
      case FIELD_TYPES.PHONE:
        return <Phone className={workflowStyles.titleIcon} size={18} />;
      case FIELD_TYPES.NOTES:
      case FIELD_TYPES.TEXTAREA:
        return <FileText className={workflowStyles.titleIcon} size={18} />;
      case FIELD_TYPES.CONTACT_TYPE:
        return <User className={workflowStyles.titleIcon} size={18} />;
      default:
        return <User className={workflowStyles.titleIcon} size={18} />;
    }
  };

  // Render a single form field based on its type
  const renderField = (field: FormField) => {
    // Ensure field has a unique id
    const fieldId = field.id;

    switch (field.type) {
      case FIELD_TYPES.TEXT:
        return (
          <input
            key={fieldId}
            type="text"
            id={fieldId}
            placeholder={field.placeholder}
            className={workflowStyles.textInput}
            onChange={e => onInputChange(fieldId, e.target.value)}
            value={formData[fieldId] || ''}
          />
        );

      case FIELD_TYPES.EMAIL:
        return (
          <input
            key={fieldId}
            type="email"
            id={fieldId}
            placeholder={field.placeholder}
            className={workflowStyles.emailInput}
            onChange={e => onInputChange(fieldId, e.target.value)}
            value={formData[fieldId] || ''}
          />
        );

      case FIELD_TYPES.PHONE:
        return (
          <input
            key={fieldId}
            type="tel"
            id={fieldId}
            placeholder={field.placeholder}
            className={workflowStyles.phoneInput}
            onChange={e => onInputChange(fieldId, e.target.value)}
            value={formData[fieldId] || ''}
          />
        );

      case FIELD_TYPES.TEXTAREA:
        return (
          <textarea
            key={fieldId}
            id={fieldId}
            placeholder={field.placeholder}
            className={workflowStyles.textareaInput}
            onChange={e => onInputChange(fieldId, e.target.value)}
            // value={formData[fieldId] || ''}
          />
        );

      case FIELD_TYPES.CHECKBOX:
        return (
          <div key={fieldId} className={workflowStyles.checkboxContainer}>
            <input
              type="checkbox"
              id={fieldId}
              className={workflowStyles.checkbox}
              onChange={e => onInputChange(fieldId, e.target.checked)}
              checked={formData[fieldId] || false}
            />
            <label htmlFor={fieldId} className={workflowStyles.label}>
              {field.label}
            </label>
          </div>
        );

      case FIELD_TYPES.SELECT:
        return (
          <div key={fieldId} className={workflowStyles.selectContainer}>
            <select
              id={fieldId}
              className={workflowStyles.selectInput}
              onChange={e => onInputChange(fieldId, e.target.value)}
              value={formData[fieldId] || ''}
            >
              {field.options?.map((option, optionIndex) => (
                <option key={`${fieldId}-option-${optionIndex}`} value={option.value}>
                  {option.text}
                </option>
              ))}
            </select>
            <div className={workflowStyles.selectArrow}>
              <ChevronDown className={workflowStyles.selectArrowIcon} />
            </div>
          </div>
        );

      case FIELD_TYPES.DATE:
        return (
          <input
            key={fieldId}
            type="date"
            id={fieldId}
            placeholder={field.placeholder}
            className={workflowStyles.textInput}
            onChange={e => onInputChange(fieldId, e.target.value)}
            value={formData[fieldId] || ''}
          />
        );

      case FIELD_TYPES.TIME:
        return (
          <input
            key={fieldId}
            type="time"
            id={fieldId}
            placeholder={field.placeholder}
            className={workflowStyles.textInput}
            onChange={e => onInputChange(fieldId, e.target.value)}
            value={formData[fieldId] || ''}
          />
        );

      case FIELD_TYPES.NUMBER:
        return (
          <input
            key={fieldId}
            type="number"
            id={fieldId}
            placeholder={field.placeholder}
            className={workflowStyles.textInput}
            onChange={e => onInputChange(fieldId, e.target.value)}
            value={formData[fieldId] || ''}
          />
        );

      case FIELD_TYPES.RADIO:
        return (
          <div key={fieldId} className={workflowStyles.checkboxContainer}>
            <input
              type="radio"
              id={fieldId}
              name={fieldId.split('-')[0]} // Group radios by the prefix of their ID
              className={workflowStyles.checkbox}
              onChange={e => onInputChange(fieldId, field.value || e.target.value)}
              checked={formData[fieldId] === (field.value || true)}
              value={field.value?.toString() || 'true'}
            />
            <label htmlFor={fieldId} className={workflowStyles.label}>
              {field.label}
            </label>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={workflowStyles.fieldContainer}>
      <div className="relative">
        {getFieldIcon(section.type)}
        <div className={workflowStyles.fieldTitle}>{section.title}</div>
      </div>
      <div className={workflowStyles.inputsContainer}>
        {section.type === FIELD_TYPES.FULLNAME ? (
          <>
            {renderField(section.fields[0])}
            <div className={workflowStyles.multiPartContainer}>
              {section.fields.slice(1).map(field => renderField(field))}
            </div>
          </>
        ) : (
          section.fields.map(field => renderField(field))
        )}
      </div>
    </div>
  );
};

export default WorkflowField;
