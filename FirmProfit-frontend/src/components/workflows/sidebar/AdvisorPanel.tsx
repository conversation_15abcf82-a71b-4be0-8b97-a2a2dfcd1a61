import React from 'react';
import Image from 'next/image';
import { useRouter } from 'next/router';

/**
 * AdvisorPanel Component
 * Displays the AI advisor panel with side navigation
 */
interface AdvisorPanelProps {
  emailInfo?: {
    email_from?: string;
    email_to?: string;
    email_subject?: string;
    email_body?: string;
    document?: Array<{
      filename?: string;
      name?: string;
      size?: number | string;
      s3Url?: string;
      contentType?: string;
    }>;
  };
  noticeSummary?: Record<string, Record<string, number>>;
  clientNotFound?: boolean;
  panelRef?: React.Ref<HTMLDivElement>;
  style?: React.CSSProperties;
  className?: string;
  screenSize?: 'sm' | 'md' | 'lg' | 'xl' | '2xl'; // Add screenSize prop for responsive design
  onNoticeSummaryUpdate?: (taskId: string, clientName: string, count: number) => void; // Callback to update notice summary
}

interface DocumentType {
  filename?: string;
  name?: string;
  size?: number | string | null;
  s3Url?: string;
  contentType?: string;
}

const AdvisorPanel: React.FC<AdvisorPanelProps> = ({
  emailInfo,
  noticeSummary,
  clientNotFound = false,
  panelRef,
  style,
  screenSize = 'xl', // Default to xl if not provided
  onNoticeSummaryUpdate,
}) => {
  const router = useRouter();
  const { taskId } = router.query;

  /**
   * Helper function to get notice summary data for the current active task only
   * Takes a nested structure and returns events only for the current task ID from URL
   *
   * @param noticeSummary - Object with task IDs as keys, containing person names and counts
   * @param currentTaskId - The current task ID from URL query parameters
   * @returns Events for current task only, or null if no data
   *
   * @example
   * Input: noticeSummary = {
   *   "task1": { "John Doe": 2, "Jane Smith": 1 },
   *   "task2": { "John Doe": 3, "Bob Johnson": 1 }
   * }, currentTaskId = "task1"
   * Output: { "John Doe": 2, "Jane Smith": 1 }
   */
  const getCurrentTaskEvents = (
    noticeSummary?: Record<string, Record<string, number>>,
    currentTaskId?: string | string[]
  ): Record<string, number> | null => {
    if (!noticeSummary || !currentTaskId) return null;

    // Handle case where currentTaskId might be an array (from Next.js router)
    const taskIdString = Array.isArray(currentTaskId) ? currentTaskId[0] : currentTaskId;

    // Get events for the specific task
    const taskEvents = noticeSummary[taskIdString];

    if (!taskEvents || typeof taskEvents !== 'object') return null;

    const validatedEvents: Record<string, number> = {};

    // Validate and clean the events data
    Object.entries(taskEvents).forEach(([personName, count]) => {
      // Skip if count is not a valid number
      if (typeof count !== 'number' || count < 0) return;

      // Trim whitespace from person name for consistency
      const cleanName = personName.trim();
      if (!cleanName) return; // Skip empty names

      validatedEvents[cleanName] = count;
    });

    // Return null if no valid data was found
    return Object.keys(validatedEvents).length > 0 ? validatedEvents : null;
  };

  // Get events for the current task only
  const currentTaskEvents = getCurrentTaskEvents(noticeSummary, taskId);

  // Extract RPA links from email body and return them as separate documents
  function extractRpaLinks(body: string | undefined): DocumentType[] {
    if (!body) {
      console.log('❌ AdvisorPanel: No email body provided to extractRpaLinks');
      return [];
    }

    console.log('🔍 AdvisorPanel: Starting RPA extraction...');
    console.log('📧 Email body length:', body.length);
    console.log(
      '📧 Email body preview:',
      body.substring(0, 200) + (body.length > 200 ? '...' : '')
    );

    // Updated regex to handle:
    // 1. Line breaks between "click here" and URL
    // 2. Extra words after "click here" (like "click here fake" or "click here original")
    const regex = /click here[^<]*<([^>]+)>/gi;
    const rpaLinks: DocumentType[] = [];

    try {
      // Use matchAll for safer handling of multiple matches
      const matches = Array.from(body.matchAll(regex));

      console.log(`🔍 AdvisorPanel: matchAll found ${matches.length} RPA links`);

      // Fallback: also try with traditional regex if matchAll finds nothing
      if (matches.length === 0) {
        console.log('🔄 AdvisorPanel: Trying fallback regex extraction...');
        let fallbackMatch;
        let fallbackCount = 0;
        regex.lastIndex = 0; // Reset regex

        while ((fallbackMatch = regex.exec(body)) !== null && fallbackCount < 10) {
          console.log(`🔄 Fallback match ${fallbackCount + 1}:`, fallbackMatch);
          const url = fallbackMatch[1];
          const urlParts = url.split('/');
          const filename =
            urlParts[urlParts.length - 1].split('?')[0] || `Notification_${fallbackCount + 1}.pdf`;

          rpaLinks.push({
            filename: filename,
            name: filename,
            size: null,
            s3Url: url,
            contentType: 'application/pdf',
          });

          fallbackCount++;
        }

        console.log(`🔄 Fallback extraction found ${fallbackCount} links`);
      } else {
        // Process matchAll results
        matches.forEach((match, index) => {
          const url = match[1];
          console.log(`📄 Processing match ${index + 1}:`, match[0]);
          console.log(`🔗 Extracted URL:`, url);

          // Extract filename from URL path
          const urlParts = url.split('/');
          const filename =
            urlParts[urlParts.length - 1].split('?')[0] || `Notification_${index + 1}.pdf`;

          console.log(`📄 AdvisorPanel: Processing RPA link ${index + 1}: ${filename}`);

          rpaLinks.push({
            filename: filename,
            name: filename,
            size: null, // Don't show size for RPA links
            s3Url: url,
            contentType: 'application/pdf',
          });
        });
      }
    } catch (error) {
      console.error('❌ Error during RPA extraction:', error);
    }

    console.log(`✅ AdvisorPanel: Successfully extracted ${rpaLinks.length} RPA document(s)`);
    console.log('📋 Final RPA links:', rpaLinks);
    return rpaLinks;
  }
  /**
   * Helper function to extract client name from various formats
   * Handles clientName with "|" separator and subject lines
   */
  const extractClientName = (rawName: string): string => {
    if (!rawName) return 'Unknown Client';

    // If it contains "|", take the first part (client name)
    if (rawName.includes('|')) {
      return rawName.split('|')[0].trim();
    }

    // If it's a subject line format like "JT; CLIENT NAME (description)"
    if (rawName.includes(';')) {
      const afterSemicolon = rawName.split(';')[1];
      if (afterSemicolon) {
        // Extract the part before the opening parenthesis
        const beforeParen = afterSemicolon.split('(')[0].trim();
        if (beforeParen) {
          return beforeParen;
        }
      }
    }

    // For subject lines with format like "CLIENT NAME (description)" without semicolon
    if (rawName.includes('(')) {
      const beforeParen = rawName.split('(')[0].trim();
      if (beforeParen) {
        return beforeParen;
      }
    }

    // If it's just a plain client name, return it
    return rawName.trim();
  };

  /**
   * Helper function to update notice summary count for a specific client
   * This function is exposed to be called when events are saved
   */
  const updateEventCount = React.useCallback(
    (rawClientName: string, incrementCount: number) => {
      if (!taskId || !onNoticeSummaryUpdate) {
        console.warn('AdvisorPanel: Cannot update event count - missing taskId or callback');
        return;
      }

      const taskIdString = Array.isArray(taskId) ? taskId[0] : taskId;

      // Extract clean client name from various possible formats
      const clientName = extractClientName(rawClientName);

      // Get current count for this client and increment it
      let currentCount = 0;
      if (currentTaskEvents && currentTaskEvents[clientName]) {
        currentCount = currentTaskEvents[clientName];
      }

      const newCount = Math.max(0, currentCount + incrementCount); // Ensure count doesn't go negative

      console.log(
        `📊 AdvisorPanel: Updating event count for "${clientName}" (from "${rawClientName}") from ${currentCount} to ${newCount} (task: ${taskIdString})`
      );

      onNoticeSummaryUpdate(taskIdString, clientName, newCount);
    },
    [taskId, onNoticeSummaryUpdate, currentTaskEvents]
  );

  // Expose the updateEventCount function globally for access from modal components
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (window as any).updateAdvisorPanelEventCount = updateEventCount;
    }

    return () => {
      if (typeof window !== 'undefined') {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        delete (window as any).updateAdvisorPanelEventCount;
      }
    };
  }, [updateEventCount]);

  function formatEmailBody(body: string | undefined) {
    if (!body) return '';

    // Regex to match: click here\n<https://...>
    // Updated regex to handle:
    // 1. Line breaks between "click here" and URL
    // 2. Extra words after "click here" (like "click here fake" or "click here original")
    const regex = /click here[^<]*<([^>]+)>/gi;

    // Replace "click here <url>" with clickable anchor tags (keep them in email body)
    const replaced = body.replace(regex, (_, url) => {
      return `<a href="${url}" target="_blank" rel="noopener noreferrer" class="text-blue-600 underline">click here</a>`;
    });

    // Escape all HTML
    const escapeHtml = (str: string) => str.replace(/</g, '&lt;').replace(/>/g, '&gt;');

    // Now split into segments — avoid escaping our injected <a>
    const segments = replaced.split(/(<a [^>]+>click here<\/a>)/g).map(part => {
      if (part.startsWith('<a ')) return part; // keep anchor tag
      return escapeHtml(part); // escape everything else
    });

    // Join and replace newlines
    return segments.join('').replace(/\n/g, '<br>');
  }

  // Helper function to format file size
  const formatFileSize = (bytes: number | string): string => {
    if (typeof bytes === 'string') return bytes;
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Helper function to truncate filename
  const truncateFilename = (filename: string, maxLength: number = 25): string => {
    if (!filename || filename.length <= maxLength) return filename;

    // Keep file extension visible
    const lastDotIndex = filename.lastIndexOf('.');
    if (lastDotIndex > 0) {
      const name = filename.substring(0, lastDotIndex);
      const extension = filename.substring(lastDotIndex);
      const availableLength = maxLength - extension.length - 3; // 3 for "..."

      if (availableLength > 0) {
        return name.substring(0, availableLength) + '...' + extension;
      }
    }

    return filename.substring(0, maxLength - 3) + '...';
  };

  // Debug logs - Enhanced for RPA link debugging
  console.log('🔍 AdvisorPanel - emailInfo:', emailInfo);
  console.log('🔍 AdvisorPanel - email_body:', emailInfo?.email_body);
  console.log('🔍 AdvisorPanel - emailInfo.document:', emailInfo?.document);

  // Test RPA extraction with current email body
  if (emailInfo?.email_body) {
    console.log('🧪 Testing RPA extraction with current email body...');
    const testRpaLinks = extractRpaLinks(emailInfo.email_body);
    console.log('🧪 Test RPA extraction result:', testRpaLinks);
  }

  if (emailInfo?.document && emailInfo.document.length > 0) {
    console.log('📁 Regular documents found:');
    emailInfo.document.forEach((doc, index) => {
      console.log(`📄 Document ${index}:`, doc);
      console.log(`📄 Document ${index} - filename:`, doc.filename);
      console.log(`📄 Document ${index} - name:`, doc.name);
      console.log(`📄 Document ${index} - size:`, doc.size, typeof doc.size);
      console.log(`📄 Document ${index} - s3Url:`, doc.s3Url);
      console.log(`📄 Document ${index} - contentType:`, doc.contentType);
    });
  }

  return (
    <div
      ref={panelRef}
      className={`flex flex-col w-full overflow-y-auto bg-white border-[#DCE2EB] [scrollbar-width:thin] [scrollbar-color:#cbd5e0_#f7fafc] [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-[#2A2E34] [&::-webkit-scrollbar-track]:bg-gray-100 ${screenSize === '2xl' ? 'text-base' : ''}`}
      style={style}
    >
      {/* Header Section - Enhanced for large screens */}

      <div className={`p-4 flex justify-between items-center ${screenSize === '2xl' ? 'p-5' : ''}`}>
        <h2 className={`font-medium text-gray-800 ${screenSize === '2xl' ? 'text-base' : ''}`}>
          Personal Advisor
        </h2>
        <button
          className={`p-2 cursor-pointer text-[#5F6F84] hover:text-gray-700 hover:bg-[#F3F5F9] rounded-full transition ${screenSize === '2xl' ? 'p-2' : ''}`}
        >
          <svg
            width={screenSize === '2xl' ? '18' : '16'}
            height={screenSize === '2xl' ? '18' : '16'}
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      {/* Chat Messages - AI Generated - Enhanced for large screens */}
      <div className="pl-[7px] pr-[14px]">
        <div
          className={`p-4 space-y-4 text-[#2A2E34] flex-1 overflow-y-auto ${screenSize === '2xl' ? 'p-8 space-y-5' : ''}`}
        >
          {/* First message - Introduction */}
          <div className="flex">
            <div className="flex self-end mr-2">
              <Image
                src="/assets/ai-robot-new-2.svg"
                alt="Bot"
                width={screenSize === '2xl' ? 28 : 24}
                height={screenSize === '2xl' ? 28 : 24}
              />
            </div>
            <div className="flex-1 max-w-[90%]">
              <div
                className={`talk-bubble-green ${clientNotFound ? 'tri-right-red' : 'tri-right-green'} round btm-left ${screenSize === '2xl' ? 'scale-105 origin-left' : ''}`}
                style={{ backgroundColor: clientNotFound ? '#EF8B8B' : '#8CF1BD' }}
              >
                <div className="talktext">
                  <p
                    className={`text-gray-800 leading-relaxed ${screenSize === '2xl' ? 'text-sm' : 'text-sm'}`}
                    style={{ color: '#2a2e34' }}
                  >
                    {clientNotFound
                      ? 'Failed: AI Agent was not able to create an event in the system due to Client/Matter not found in system. Please review the email below, download the file/s and complete this workflow manually'
                      : 'The following court notice workflow below was processed by AI. Please review.'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Second message - Email preview */}
          <div className="flex">
            <div className="flex self-end mr-2">
              <Image
                src="/assets/ai-robot-new-2.svg"
                alt="Bot"
                width={screenSize === '2xl' ? 28 : 24}
                height={screenSize === '2xl' ? 28 : 24}
              />
            </div>
            <div className="flex-1 max-w-[90%]">
              <div
                className={`talk-bubble-green tri-right-green-third round btm-left ${screenSize === '2xl' ? 'p-5 scale-105 origin-left' : 'p-4'}`}
                style={{ backgroundColor: '#f3f5f9' }}
              >
                <div
                  className={`talktext-third text-gray-800 ${screenSize === '2xl' ? 'text-sm' : 'text-sm'}`}
                >
                  <p
                    className={`font-medium text-gray-800 ${screenSize === '2xl' ? 'mb-4 text-base' : 'mb-2'}`}
                    style={{
                      color: '#2a2e34',
                      marginBottom: screenSize === '2xl' ? '16px' : '20px',
                      fontWeight: '500',
                    }}
                  >
                    Court Notice Email:
                  </p>
                  <p
                    className={`${screenSize === '2xl' ? 'mb-2' : 'mb-2'}`}
                    style={{ color: '#2a2e34' }}
                  >
                    From: {emailInfo?.email_from}
                  </p>
                  <p
                    className={`${screenSize === '2xl' ? 'mb-2' : 'mb-2'}`}
                    style={{ color: '#2a2e34' }}
                  >
                    To: {emailInfo?.email_to}
                  </p>
                  <p
                    className={`${screenSize === '2xl' ? 'mb-4' : 'mb-2'}`}
                    style={{
                      color: '#2a2e34',
                      marginBottom: screenSize === '2xl' ? '16px' : '20px',
                    }}
                  >
                    Subject: {emailInfo?.email_subject}
                  </p>
                  <div
                    className={`whitespace-pre-line ${screenSize === '2xl' ? 'mb-4 leading-relaxed' : 'mb-2'}`}
                    dangerouslySetInnerHTML={{
                      __html: formatEmailBody(emailInfo?.email_body),
                    }}
                  ></div>

                  {/* Attachment section - Enhanced for large screens */}
                  {(() => {
                    // Combine regular attachments with RPA links
                    const regularAttachments = emailInfo?.document || [];
                    const rpaLinks = extractRpaLinks(emailInfo?.email_body);
                    const allDocuments = [...regularAttachments, ...rpaLinks];

                    return allDocuments.length > 0
                      ? allDocuments.map((doc: DocumentType, index: number) => (
                          <div
                            key={index}
                            onClick={() => {
                              if (doc.s3Url) {
                                window.open(doc.s3Url, '_blank');
                              } else {
                                window.open('/NoticeofHearing.pdf', '_blank');
                              }
                            }}
                            className={`border w-fit bg-white border-gray-200 cursor-pointer rounded-xl flex items-center ${screenSize === '2xl' ? 'mt-3 p-3' : 'mt-3 p-3'}`}
                            style={{ color: '#2a2e34' }}
                          >
                            {doc.contentType === 'application/pdf' ? (
                              <Image
                                src="/assets/pdf-filled.svg"
                                alt="PDF"
                                width={screenSize === '2xl' ? 32 : 30}
                                height={screenSize === '2xl' ? 32 : 30}
                                className="mr-2"
                              />
                            ) : (
                              <svg
                                className={`text-red-500 mr-2 ${screenSize === '2xl' ? 'w-5 h-5' : 'w-5 h-5'}`}
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                />
                              </svg>
                            )}
                            <div className="">
                              <p
                                className={`font-medium ${screenSize === '2xl' ? 'text-xs' : 'text-xs'}`}
                                style={{ color: '#2a2e34' }}
                                title={doc.filename || doc.name} // Show full name on hover
                              >
                                {truncateFilename(doc.filename || doc.name || '')}
                              </p>
                              {doc.size && (
                                <p
                                  className={`${screenSize === '2xl' ? 'text-xs' : 'text-xs'}`}
                                  style={{ color: '#2a2e34' }}
                                >
                                  {typeof doc.size === 'number'
                                    ? formatFileSize(doc.size)
                                    : doc.size}
                                </p>
                              )}
                            </div>
                          </div>
                        ))
                      : null;
                  })()}
                </div>
              </div>
            </div>
          </div>

          {/* Third message - Notice Summary */}
          <div className="flex">
            <div className="flex self-end mr-2">
              <Image
                src="/assets/ai-robot-new-2.svg"
                alt="Bot"
                width={screenSize === '2xl' ? 28 : 24}
                height={screenSize === '2xl' ? 28 : 24}
              />
            </div>
            <div className="flex-1 max-w-[90%]">
              <div
                className={`talk-bubble-green tri-right-green-third round btm-left ${screenSize === '2xl' ? 'scale-105 origin-left' : ''}`}
                style={{ backgroundColor: '#f3f5f9' }}
              >
                <div className={`talktext ${screenSize === '2xl' ? 'text-sm' : 'text-sm'}`}>
                  <p
                    className={`font-medium ${screenSize === '2xl' ? 'mb-2 text-base' : 'mb-2'}`}
                    style={{ color: '#2a2e34', fontWeight: '500' }}
                  >
                    Notice Summary:
                  </p>
                  {/* {taskId && (
                  <p
                    className="text-xs mb-2 text-gray-500"
                    style={{ color: '#6B7280', fontSize: '11px', marginBottom: '8px' }}
                  >
                    Task ID: {Array.isArray(taskId) ? taskId[0] : taskId}
                  </p>
                )} */}
                  <p
                    className={`text-gray-800 ${screenSize === '2xl' ? 'mb-2 mt-4' : 'mb-1 mt-5'}`}
                    style={{
                      color: '#2a2e34',
                      marginBottom: screenSize === '2xl' ? '8px' : '10px',
                    }}
                  >
                    Scheduled events
                  </p>
                  <ul
                    className={`list-disc pl-5 ${screenSize === '2xl' ? 'space-y-2' : 'space-y-2'}`}
                  >
                    {currentTaskEvents ? (
                      Object.entries(currentTaskEvents).map(([name, count], index) => {
                        // Ensure client name is in uppercase and properly formatted
                        const formattedClientName = name.toUpperCase();
                        return (
                          <li
                            key={index}
                            className={screenSize === '2xl' ? 'text-sm' : ''}
                            style={{ color: '#2a2e34' }}
                          >
                            {formattedClientName} - {count} {count === 1 ? 'Event' : 'Events'}
                          </li>
                        );
                      })
                    ) : (
                      <li
                        className={screenSize === '2xl' ? 'text-sm' : ''}
                        style={{ color: '#2a2e34' }}
                      >
                        No events found for current task
                      </li>
                    )}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Input Section */}
      {/* <div className="p-4 bg-[#F3F5F9] border-gray-200 mt-auto">
        <div className="relative flex items-center">
          <input
            type="text"
            placeholder="Write your prompt here..."
            className="w-full border border-[#DCE2EB] bg-white rounded-[12px] py-2 px-3 pr-10 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={prompt}
            onChange={e => setPrompt(e.target.value)}
            onKeyDown={e => {
              if (e.key === 'Enter') {
                handleSendPrompt();
              }
            }}
          />
          <button
            className="absolute right-3 top-1/2 cursor-pointer transform -translate-y-1/2 text-blue-[#3F73F6]"
            onClick={handleSendPrompt}
          >
            <Image src="/IconsBar/ai-blue.svg" alt="Send" width={20} height={20} />
          </button>
        </div>
      </div> */}

      <style>{`
        
        .talk-bubble-green,
        .talk-bubble-green-instruction {
          margin: 10px 0;
          display: inline-block;
          position: relative;
          width: auto;
          min-width: ${screenSize === '2xl' ? '200px' : '200px'};
          height: auto;
          border-radius: 12px;
          border-bottom-left-radius: 0px;
        }
 
        .talk-bubble-green {
          background-color: #8CF1BD;
        }
 
        .talk-bubble-green-instruction {
          background-color: #f3f5f9;
        }
 
        /* Success message triangle override */
        .talk-bubble-green[style*="background-color: #8cf1bd"] .tri-right-green.btm-left:after {
          border-color: transparent transparent transparent #8cf1bd;
        }

        /* Client not found message triangle override */
        .talk-bubble-green[style*="background-color: #ef8b8b"] .tri-right-red.btm-left:after {
          border-color: transparent transparent transparent #ef8b8b;
        }
 
 
        .talk-bubble-green-instruction .talktext p {
          color: #2a2e34;
        }
 
        .talk-bubble-green-instruction .talktext-third p {
          color: #2a2e34;
        }
 
        /* Success message triangle override */
        .talk-bubble-green[style*="background-color: #8cf1bd"] .tri-right-green-third.btm-left:after {
          border-color: transparent transparent transparent #8cf1bd;
        }
 
                /* Bubble triangle */
        .tri-right-green.btm-left:after,
        .tri-right-red.btm-left:after,
        .tri-right-green-instruction.btm-left:after {
          content: ' ';
          position: absolute;
          width: 0;
          height: 0;
          left: 0px;
          bottom: -8px;
          border: ${screenSize === '2xl' ? '10px' : '10px'} solid;
        }

        .tri-right-green.btm-left:after {
          border-color: transparent transparent transparent #8CF1BD;
        }

        .tri-right-red.btm-left:after {
          border-color: transparent transparent transparent #EF8B8B;
        }

        .tri-right-green-instruction.btm-left:after {
          border-color: transparent transparent transparent #f3f5f9;
        }
 
 
        .talktext {
          padding: ${screenSize === '2xl' ? '10px 14px' : '8px 14px'};
          text-align: left;
        }
 
        .talktext p {
          margin: 0;
          font-size: ${screenSize === '2xl' ? '14px' : '14px'};
          font-weight: normal;
          color: #fff;
        }
 
        ///////////////////
 
        .talktext-third {
          padding: ${screenSize === '2xl' ? '14px 16px' : '12px 16px'};
          text-align: left;
          line-height: ${screenSize === '2xl' ? '20px' : '20px'};
        }
 
        .talktext-third p {
          margin: 0;
          font-size: ${screenSize === '2xl' ? '14px' : '14px'};
          font-weight: normal;
          color: #fff;
        }
 
 
 
 
        /* Bubble triangle */
        .tri-right-green-third.btm-left:after,
        .tri-right-green-third-instruction.btm-left:after {
          content: ' ';
          position: absolute;
          width: 0;
          height: 0;
          left: 0px;
          bottom: ${screenSize === '2xl' ? '-10px' : '-10px'};
          border: ${screenSize === '2xl' ? '10px' : '10px'} solid;
        }
 
        .tri-right-green-third.btm-left:after {
          border-color: transparent transparent transparent #f3f5f9;
        }
 
        .tri-right-green-third-instruction.btm-left:after {
          border-color: transparent transparent transparent #f3f5f9;
        }
 
 
 
      `}</style>
    </div>
  );
};

export default AdvisorPanel;
