import React from 'react';
import Image from 'next/image';
import { toast } from 'react-hot-toast';

interface UserData {
  email: string;
  [key: string]: string | number | boolean;
}

interface DetailsPanelProps {
  panelRef?: React.Ref<HTMLDivElement>;
  style?: React.CSSProperties;
  className?: string;
  work_flow_id: string;
  onClose?: () => void;
  is_archive?: boolean;
  workflow_status?: string;
  onNotificationChange?: (show: boolean) => void;
  onArchiveChange?: (archiveInfo: {
    is_archive: boolean;
    archive_by: string;
    archive_at: string;
  }) => void;
  onArchiveSuccess?: () => void;
  onStatusChange?: (status: string) => void;
  canComplete?: boolean;
  onCompleteAllTasks?: () => void;
  onTaskAction?: (action: 'complete') => void;
  onActiveRunTask?: (action: 'active') => void;
  task_id: string;
}

const DetailsPanel: React.FC<DetailsPanelProps> = ({
  panelRef,
  style,
  work_flow_id,
  is_archive,
  workflow_status,
  onNotificationChange,
  onArchiveChange,
  onArchiveSuccess,
  onStatusChange,
  canComplete = false,
  onActiveRunTask,
  task_id,
}) => {
  const handleArchiveWorkflow = async (isArchive: boolean) => {
    try {
      const params = new URLSearchParams();
      const userDataString = localStorage.getItem('user');
      const userData: UserData | null = userDataString ? JSON.parse(userDataString) : null;
      const userEmail = userData?.email || '';
      const archive_at = new Date().toISOString();

      params.append('work_flow_execution_id', work_flow_id);
      params.append('type', 'archive');
      params.append('archive_at', archive_at);
      params.append('archive_by', userEmail);
      params.append('task_id', task_id ?? '');

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/archive-work-flow`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: params.toString(),
        }
      );

      if (response.ok) {
        await response.json();
        if (isArchive) {
          onArchiveChange?.({
            is_archive: true,
            archive_by: userEmail,
            archive_at,
          });
          onArchiveSuccess?.(); // trigger re-fetch in parent
        } else {
          onNotificationChange?.(true);
          onActiveRunTask?.('active');
          onArchiveChange?.({
            is_archive: false,
            archive_by: '',
            archive_at: '',
          });
        }
      } else {
        toast.error('Failed to update workflow status');
      }
    } catch {
      toast.error('Error updating workflow');
    }
  };

  const handleReactivateWorkflow = async () => {
    try {
      const params = new URLSearchParams();

      params.append('work_flow_execution_id', work_flow_id);
      params.append('type', 'reactive');
      params.append('archive_at', '');
      params.append('archive_by', '');
      params.append('task_id', task_id ?? '');

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/archive-work-flow`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: params.toString(),
        }
      );

      if (response.ok) {
        await response.json();
        onNotificationChange?.(true);
        onActiveRunTask?.('active');
        onStatusChange?.('ACTIVE');
      } else {
        toast.error('Failed to reactivate workflow');
      }
    } catch {
      toast.error('Error reactivating workflow');
    }
  };

  const handleCompleteRun = async () => {
    try {
      const userDataString = localStorage.getItem('user');
      const userData: UserData | null = userDataString ? JSON.parse(userDataString) : null;
      const userEmail = userData?.email || '';
      const archive_at = new Date().toISOString();

      const params = new URLSearchParams();
      params.append('work_flow_execution_id', work_flow_id);
      params.append('type', 'complete');
      params.append('archive_at', archive_at);
      params.append('archive_by', userEmail);
      params.append('task_id', task_id);

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/archive-work-flow`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: params.toString(),
        }
      );

      if (response.ok) {
        await response.json();
        onStatusChange?.('COMPLETED');
        onNotificationChange?.(true);
      } else {
        toast.error('Failed to complete workflow');
      }
    } catch {
      toast.error('Error completing workflow');
    }
  };

  return (
    <div
      ref={panelRef}
      className={`flex flex-col w-full bg-white border-l border-gray-200`}
      style={style}
    >
      {/* Content Section */}
      <div className="p-4 space-y-4 flex-1 overflow-y-auto">
        {/* Task Information */}
        <div className="space-y-4">
          {!is_archive && (
            <>
              {workflow_status === 'COMPLETED' ? (
                <button
                  type="button"
                  className="px-4 py-2 border border-[#3F73F6] cursor-pointer text-white text-[14px] rounded-[12px] bg-[#3F73F6] hover:bg-[#2F5FE6] w-full flex items-center justify-center"
                  onClick={handleReactivateWorkflow}
                >
                  <Image
                    src="/IconsBar/refresh.svg"
                    alt="Reactivate"
                    width={20}
                    height={20}
                    className="mr-3"
                  />
                  Reactivate
                </button>
              ) : (
                <button
                  type="button"
                  className="px-4 py-2 border border-[#3F73F6] cursor-pointer text-[#3F73F6] text-[14px] rounded-[12px] bg-white hover:bg-gray-50 w-full disabled:text-[#A2AFC2] disabled:bg-[#EFF2F7] disabled:cursor-not-allowed disabled:border-[#EFF2F7]"
                  onClick={handleCompleteRun}
                  disabled={!canComplete}
                >
                  Complete run
                </button>
              )}
            </>
          )}

          {is_archive && workflow_status !== 'COMPLETED' && (
            <button
              type="button"
              className="px-4 py-2 border border-[#3F73F6] cursor-pointer text-white text-[14px] rounded-[12px] bg-[#3F73F6] hover:bg-[#2F5FE6] w-full flex items-center justify-center"
              onClick={() => handleArchiveWorkflow(false)}
            >
              <Image
                src="/IconsBar/unarchive.svg"
                alt="Unarchive"
                width={20}
                height={20}
                className="mr-3"
              />
              Unarchive
            </button>
          )}

          {/* Activity and Archive Menu */}
          <div className="flex flex-col space-y-2 bg-white rounded-[12px] border border-[#DCE2EB]">
            <button className="flex items-center cursor-pointer px-4 py-2 hover:bg-[#F3F5F9] transition-colors rounded-t-[12px]">
              <Image
                src="/IconsBar/clock-fast-forward.svg"
                alt="Activity"
                width={20}
                height={20}
                className="mr-3"
              />
              <span className="text-[14px] text-[#2A2E34]">Activity</span>
            </button>
            {!is_archive && workflow_status !== 'COMPLETED' && (
              <button
                className="flex items-center px-4 py-2 cursor-pointer hover:bg-[#F3F5F9] transition-colors rounded-b-[12px]"
                onClick={() => handleArchiveWorkflow(true)}
              >
                <Image
                  src="/IconsBar/archive.svg"
                  alt="Archive"
                  width={20}
                  height={20}
                  className="mr-3"
                />
                <span className="text-[14px] text-[#2A2E34]">Archive</span>
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailsPanel;
