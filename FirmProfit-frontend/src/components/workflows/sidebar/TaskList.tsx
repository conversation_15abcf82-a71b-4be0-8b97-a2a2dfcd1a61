import React from 'react';
import { Plus } from 'lucide-react';
import { workflowStyles } from '@/styles/workflow';
import { Task, Workflow } from '@/types/workflow';

interface TaskListProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  workflow: Workflow | any;
  selectedTask: Task | null;
  onSelectTask: (task: Task) => void;
}

/**
 * TaskList Component
 * Displays the list of tasks in a workflow sidebar
 */
const TaskList: React.FC<TaskListProps> = ({ workflow, selectedTask, onSelectTask }) => {
  return (
    <div className="w-[354px] border-r border-gray-200 bg-white h-full flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <h2 className="font-medium text-lg"><PERSON></h2>
        <div className={workflowStyles.taskList}>
          {workflow &&
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            workflow.tasks.map((task: any) => {
              // Determine the styling based on task status
              let itemStyles = workflowStyles.taskItemInactive;
              let dotStyles = workflowStyles.taskStatusInactive;
              let textStyles = '';

              if (selectedTask?.id === task.id) {
                itemStyles = workflowStyles.taskItemActive;
                dotStyles = workflowStyles.taskStatusActive;
              } else if (task.status === 'completed') {
                itemStyles = workflowStyles.taskItemCompleted;
                dotStyles = workflowStyles.taskStatusCompleted;
                textStyles = workflowStyles.taskCompletedText;
              } else if (task.status === 'skipped') {
                itemStyles = workflowStyles.taskItemSkipped;
                dotStyles = workflowStyles.taskStatusSkipped;
                textStyles = workflowStyles.taskSkippedText;
              } else if (task.status === 'active') {
                dotStyles = workflowStyles.taskStatusActive;
              }

              return (
                <div
                  key={task.id}
                  className={`${workflowStyles.taskItem} ${itemStyles}`}
                  onClick={() => onSelectTask(task)}
                >
                  <div className={`${workflowStyles.taskStatusDot} ${dotStyles}`}></div>
                  <button
                    className={` cursor-pointer ${workflowStyles.taskTextContainer} ${textStyles}`}
                  >
                    <span>{task.name}</span>
                  </button>
                </div>
              );
            })}
        </div>
      </div>
      <div className="p-4">
        <button
          className={`w-[320px] h-[40px] flex items-center cursor-pointer justify-center text-gray-500 border border-gray-200 rounded-[12px] px-3 py-2 bg-white`}
        >
          <Plus size={16} className="mr-2" />
          <span>Create a Task</span>
        </button>
      </div>
    </div>
  );
};

export default TaskList;
