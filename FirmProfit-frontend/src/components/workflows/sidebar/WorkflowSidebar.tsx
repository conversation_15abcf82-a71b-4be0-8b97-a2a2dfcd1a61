import React, { useState, useCallback, useEffect } from 'react';
import Link from 'next/link';
import { FiChevronDown, FiChevronUp } from 'react-icons/fi';
import Image from 'next/image';
import { usePathname } from 'next/navigation';

// Types
interface MenuItemProps {
  href: string;
  icon?: string;
  children: React.ReactNode;
  className?: string;
  indent?: boolean;
  variant?: 'default' | 'primary';
}

interface CollapsibleSectionProps {
  title: string;
  isCollapsed: boolean;
  onToggle: () => void;
  children: React.ReactNode;
  icon?: string;
  variant?: 'header' | 'folder';
}

interface WorkflowSidebarProps {
  className?: string;
}

interface CollapsedSections {
  companyViews: boolean;
  legalOperations: boolean;
  allWorkflows: boolean;
}

// Constants
const STORAGE_KEY = 'workflow-sidebar-state';
const DEFAULT_COLLAPSED_STATE: CollapsedSections = {
  companyViews: true,
  legalOperations: true,
  allWorkflows: false,
};

// Utility functions for localStorage
const getStoredState = (): CollapsedSections => {
  if (typeof window === 'undefined') {
    return DEFAULT_COLLAPSED_STATE;
  }
  
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const parsed = JSON.parse(stored);
      // Ensure all required keys exist
      return {
        companyViews: parsed.companyViews ?? DEFAULT_COLLAPSED_STATE.companyViews,
        legalOperations: parsed.legalOperations ?? DEFAULT_COLLAPSED_STATE.legalOperations,
        allWorkflows: parsed.allWorkflows ?? DEFAULT_COLLAPSED_STATE.allWorkflows,
      };
    }
  } catch (error) {
    console.warn('Failed to parse stored sidebar state:', error);
  }
  
  return DEFAULT_COLLAPSED_STATE;
};

const setStoredState = (state: CollapsedSections): void => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
  } catch (error) {
    console.warn('Failed to store sidebar state:', error);
  }
};

// Reusable MenuItem Component
const MenuItem: React.FC<MenuItemProps> = ({
  href,
  icon,
  children,
  className = '',
  indent = false,
  variant = 'default',
}) => {
  const pathname = usePathname();
  const isActive = pathname === href;

  const getItemClasses = () => {
    const baseClasses = 'flex items-center gap-2 text-sm';
    const indentClasses = indent ? 'pl-5' : '';
    const variantClasses = variant === 'primary' ? 'px-4 py-2' : 'p-2';
    
    return `${baseClasses} ${variantClasses} ${indentClasses} ${className}`;
  };

  const getSpanClasses = () => {
    const baseClasses = 'w-full font-medium';
    const paddingClasses = variant === 'primary' ? 'py-2 px-4' : 'py-2 px-3';
    const activeClasses = isActive 
      ? 'bg-[#F3F5F9] text-[#3F73F6] rounded-[12px]' 
      : 'text-[#5F6F84] hover:bg-gray-50 hover:rounded-[12px]';
    
    return `${baseClasses} ${paddingClasses} ${activeClasses}`;
  };

  // Prevent event propagation to avoid triggering parent handlers
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
  }, []);

  return (
    <Link href={href} className={getItemClasses()} onClick={handleClick}>
      {icon && <Image src={icon} alt="icon" width={16} height={16} />}
      <span className={getSpanClasses()}>{children}</span>
    </Link>
  );
};

// Reusable Collapsible Section Component
const CollapsibleSection: React.FC<CollapsibleSectionProps> = ({
  title,
  isCollapsed,
  onToggle,
  children,
  icon,
  variant = 'header',
}) => {
  const getButtonClasses = () => {
    const baseClasses = 'w-full flex items-center justify-between text-sm';
    
    if (variant === 'header') {
      return `${baseClasses} px-4 py-2 font-medium text-[#5F6F84]`;
    }
    
    return `${baseClasses} gap-2 p-4 text-[#5F6F84] hover:bg-gray-50 ${
      isCollapsed ? 'text-[#2A2E34]' : ''
    }`;
  };

  const getTitleClasses = () => {
    if (variant === 'header') {
      return `font-medium text-[14px] ${isCollapsed ? '' : 'text-[#2A2E34]'}`;
    }
    
    return `${isCollapsed ? '' : 'text-[#2A2E34]'} font-medium`;
  };

  const renderIcon = () => {
    if (!icon) return null;
    
    const iconSrc = isCollapsed ? "/folder.svg" : "/folder-dark.svg";
    return <Image src={iconSrc} alt={title.toLowerCase()} width={16} height={16} />;
  };

  const renderChevron = () => {
    return isCollapsed ? <FiChevronDown size={16} /> : <FiChevronUp size={16} />;
  };

  // Ensure only the button click triggers the toggle
  const handleToggle = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onToggle();
  }, [onToggle]);

  // Prevent clicks on the content area from propagating
  const handleContentClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
  }, []);

  return (
    <div onClick={handleContentClick}>
      <button 
        className={getButtonClasses()} 
        onClick={handleToggle}
        type="button"
      >
        {variant === 'folder' ? (
          <div className="flex items-center gap-2">
            {renderIcon()}
            <span className={getTitleClasses()}>{title}</span>
          </div>
        ) : (
          <span className={getTitleClasses()}>{title}</span>
        )}
        {renderChevron()}
      </button>
      
      {!isCollapsed && (
        <div className={variant === 'folder' ? 'ml-2' : 'mt-1'}>
          {children}
        </div>
      )}
    </div>
  );
};

// Main Sidebar Component
const WorkflowSidebar: React.FC<WorkflowSidebarProps> = ({ className = '' }) => {
  // State management with localStorage persistence
  const [collapsedSections, setCollapsedSections] = useState<CollapsedSections>(DEFAULT_COLLAPSED_STATE);
  const [isClient, setIsClient] = useState(false);

  // Initialize state from localStorage on client mount
  useEffect(() => {
    setIsClient(true);
    const storedState = getStoredState();
    setCollapsedSections(storedState);
  }, []);

  // Update localStorage whenever state changes
  useEffect(() => {
    if (isClient) {
      setStoredState(collapsedSections);
    }
  }, [collapsedSections, isClient]);

  // Handlers with proper isolation and persistence
  const handleCompanyViewsToggle = useCallback(() => {
    setCollapsedSections(prev => ({
      ...prev,
      companyViews: !prev.companyViews,
    }));
  }, []);

  const handleLegalOperationsToggle = useCallback(() => {
    setCollapsedSections(prev => ({
      ...prev,
      legalOperations: !prev.legalOperations,
    }));
  }, []);

  const handleAllWorkflowsToggle = useCallback(() => {
    setCollapsedSections(prev => ({
      ...prev,
      allWorkflows: !prev.allWorkflows,
    }));
  }, []);

  // Menu items data with stable references
  const primaryMenuItems = [
    { id: 'workflows', href: '#', icon: '/assets/workflow.svg', label: 'My Workflows' },
    { id: 'tasks', href: '#', icon: '/assets/task.svg', label: 'My Tasks' },
    { id: 'inbound', href: '#', icon: '/assets/inboud.svg', label: 'Inbound' },
  ];

  const legalOperationsItems = [
    { id: 'new-court-notice', href: '/court-notice/new', label: 'New Court Notice' },
    { id: 'court-notice-followup', href: '/court-notice/follow-up', label: 'Court Notice Follow up' },
  ];

  const allWorkflowsItems = [
    { id: 'completed', href: '/all-work-flow/completed', label: 'Completed' },
    { id: 'archived', href: '/all-work-flow/archive', label: 'Archived' },
  ];

  // Prevent any clicks on the sidebar container from propagating
  const handleSidebarClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
  }, []);

  return (
    <div
      className={`h-full px-2 py-5 bg-white border-r border-gray-200 w-[260px] overflow-y-auto ${className}`}
      onClick={handleSidebarClick}
    >
      <div className="flex flex-col">
        <div className="mb-2">
          <CollapsibleSection
            title="COMPANY VIEWS"
            isCollapsed={collapsedSections.companyViews}
            onToggle={handleCompanyViewsToggle}
            variant="header"
          >
            {/* Primary Menu Items */}
            {primaryMenuItems.map((item) => (
              <MenuItem
                key={item.id}
                href={item.href}
                icon={item.icon}
                variant="primary"
              >
                {item.label}
              </MenuItem>
            ))}

            {/* Legal Operations Submenu */}
            <CollapsibleSection
              title="Legal Operations"
              isCollapsed={collapsedSections.legalOperations}
              onToggle={handleLegalOperationsToggle}
              icon="/folder.svg"
              variant="folder"
            >
              {legalOperationsItems.map((item) => (
                <MenuItem
                  key={item.id}
                  href={item.href}
                  indent
                >
                  {item.label}
                </MenuItem>
              ))}
            </CollapsibleSection>

            {/* All Workflows Submenu */}
            <CollapsibleSection
              title="All Workflows"
              isCollapsed={collapsedSections.allWorkflows}
              onToggle={handleAllWorkflowsToggle}
              icon="/folder.svg"
              variant="folder"
            >
              {allWorkflowsItems.map((item) => (
                <MenuItem
                  key={item.id}
                  href={item.href}
                  indent
                >
                  {item.label}
                </MenuItem>
              ))}
            </CollapsibleSection>
          </CollapsibleSection>
        </div>
      </div>
    </div>
  );
};

export default WorkflowSidebar;
