# Dynamic Workflow Component

This component dynamically renders UI elements based on a JSON structure. It provides a flexible and maintainable way to create complex UIs without writing repetitive code.

## Features

- Dynamically renders HTML elements based on JSON configuration
- Supports nested elements and complex structures
- <PERSON>les special attributes and event handlers
- Validates element configurations
- <PERSON><PERSON><PERSON> handles self-closing tags
- Modular and reusable design

## Usage

```jsx
import React from 'react';
import Layout from '@/components/layout/Layout';
import Element from '@/components/workflow/Element';
import workflowData from '@/data/workflowData';

const Workflow = () => {
  return (
    <Layout>
      <div className="workflow-container">
        {workflowData.elements.map((element, index) => (
          <Element key={index} element={element} />
        ))}
      </div>
    </Layout>
  );
};

export default Workflow;
```

## JSON Structure

The component expects a JSON structure with the following format:

```json
{
  "elements": [
    {
      "tagName": "div",
      "description": "Description of the element",
      "attributes": { "class": "example-class" },
      "children": [
        {
          "tagName": "p",
          "content": "Text content"
        }
      ]
    }
  ]
}
```

### Element Properties

- `tagName`: The HTML tag name (required)
- `description`: A description of the element (optional)
- `attributes`: An object of HTML attributes (optional)
- `children`: An array of child elements (optional)
- `content`: Text content for the element (optional)

### Self-Closing Tags

The component automatically handles self-closing tags like `<input>`, `<img>`, etc. These tags should not have children in the JSON structure:

```json
{
  "tagName": "input",
  "attributes": {
    "type": "text",
    "placeholder": "Enter your name"
  }
}
```

## Utilities

The component uses utility functions to handle element creation:

- `processAttributes`: Processes attributes for React elements
- `createEventHandler`: Safely evaluates event handler strings
- `isValidElement`: Validates an element configuration
- `isSelfClosingTag`: Checks if a tag is self-closing

## Best Practices

1. Keep the JSON structure clean and well-organized
2. Use descriptive descriptions for elements
3. Group related elements together
4. Use consistent naming conventions
5. Validate the JSON structure before rendering
6. Don't include children for self-closing tags

## Accessibility

The component supports accessibility attributes through the `attributes` property:

```json
{
  "tagName": "button",
  "attributes": {
    "aria-label": "Submit form",
    "role": "button"
  },
  "content": "Submit"
}
```

## Styling

The component uses Tailwind CSS for styling. You can add custom classes through the `attributes.class` property:

```json
{
  "tagName": "div",
  "attributes": {
    "class": "bg-blue-500 text-white p-4 rounded-lg"
  }
}
```
