import React from 'react';
import { Matter } from '@/types/matter';
import { DataTable, Column } from '@/components/common';

interface MattersTableProps {
  matters: Matter[];
  title?: string;
  emptyMessage?: string;
  className?: string;
  onMatterClick?: (matter: Matter) => void;
}

const MattersTable: React.FC<MattersTableProps> = ({ 
  matters, 
  title = "Matters",
  emptyMessage = "No matters found for this contact.",
  className = '',
  onMatterClick
}) => {
  // Create a wrapper component for clickable cells
  const ClickableCell: React.FC<{ matter: Matter; children: React.ReactNode; className?: string }> = ({ 
    matter, 
    children, 
    className: cellClassName = '' 
  }) => {
    if (onMatterClick) {
      return (
        <div 
          className={`cursor-pointer hover:opacity-75 transition-opacity ${cellClassName}`}
          onClick={(e) => {
            e.stopPropagation();
            onMatterClick(matter);
          }}
        >
          {children}
        </div>
      );
    }
    return <div className={cellClassName}>{children}</div>;
  };

  // Define columns using the DataTable Column interface
  const columns: Column[] = [
    {
      id: 'type',
      header: 'TYPE',
      sortable: true,
      cell: (matter: Matter) => (
        <ClickableCell matter={matter} className="text-[14px] text-[#5F6F84]">
          {matter.type}
        </ClickableCell>
      ),
    },
    {
      id: 'attorney',
      header: 'ATTORNEY',
      sortable: true,
      cell: (matter: Matter) => (
        <ClickableCell matter={matter} className="text-[14px] text-[#2A2E34]">
          {matter.attorney}
        </ClickableCell>
      ),
    },
    {
      id: 'description',
      header: 'MATTER DESCRIPTION',
      sortable: false,
      cell: (matter: Matter) => (
        <ClickableCell 
          matter={matter} 
          className="text-[14px] text-[#5F6F84] truncate"
        >
          <div title={matter.description}>
            {matter.description}
          </div>
        </ClickableCell>
      ),
    },
    {
      id: 'court',
      header: 'COURT',
      sortable: true,
      cell: (matter: Matter) => (
        <ClickableCell matter={matter} className="text-[14px] text-[#5F6F84]">
          {matter.court || '-'}
        </ClickableCell>
      ),
    },
  ];

  if (!matters || matters.length === 0) {
    return (
      <div className={`bg-white rounded-[12px] p-6 ${className}`}>
        <h3 className="text-[16px] font-semibold text-[#2A2E34] mb-4">{title}</h3>
        <p className="text-[14px] text-[#5F6F84]">{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div className={`bg-white ${className}`}>
      {title && (
        <div className="mb-4">
          <h3 className="text-[16px] font-semibold text-[#2A2E34]">{title}</h3>
        </div>
      )}
      
      <DataTable
        columns={columns}
        data={matters}
        showCheckbox={false}
        className=""
        idField="id"
        rowClassName={onMatterClick ? 'hover:bg-[#F8F9FC] transition-colors' : 'hover:bg-[#F8F9FC] transition-colors'}
      />
    </div>
  );
};

export default MattersTable; 