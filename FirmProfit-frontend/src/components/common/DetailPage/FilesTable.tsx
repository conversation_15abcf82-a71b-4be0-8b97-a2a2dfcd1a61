import React from 'react';
import { File } from '@/types/file';
import { DataTable, Column } from '@/components/common';

interface FilesTableProps {
  files: File[];
  title?: string;
  emptyMessage?: string;
  className?: string;
  onFileClick?: (file: File) => void;
  onDownload?: (file: File) => void;
}

const FilesTable: React.FC<FilesTableProps> = ({ 
  files, 
  title = "Files",
  emptyMessage = "No files available.",
  className = '',
  onFileClick,
}) => {
  // Create a wrapper component for clickable cells
  const ClickableCell: React.FC<{ 
    file: File; 
    children: React.ReactNode; 
    className?: string 
  }> = ({ 
    file, 
    children, 
    className: cellClassName = '' 
  }) => {
    if (onFileClick) {
      return (
        <div 
          className={`cursor-pointer hover:opacity-75 transition-opacity ${cellClassName}`}
          onClick={(e) => {
            e.stopPropagation();
            onFileClick(file);
          }}
        >
          {children}
        </div>
      );
    }
    return <div className={cellClassName}>{children}</div>;
  };

  // File type indicator component
  const FileTypeIndicator: React.FC<{ documentType: string }> = ({ documentType }) => {
    const getTypeStyle = (type: string) => {
      switch (type.toUpperCase()) {
        case 'PDF':
          return {
            backgroundColor: '#EF4444',
            color: '#FFFFFF',
          };
        case 'DOC':
        case 'DOCX':
          return {
            backgroundColor: '#2563EB',
            color: '#FFFFFF',
          };
        case 'XLS':
        case 'XLSX':
          return {
            backgroundColor: '#16A34A',
            color: '#FFFFFF',
          };
        case 'TXT':
          return {
            backgroundColor: '#6B7280',
            color: '#FFFFFF',
          };
        case 'IMG':
          return {
            backgroundColor: '#7C3AED',
            color: '#FFFFFF',
          };
        default:
          return {
            backgroundColor: '#F3F4F6',
            color: '#374151',
          };
      }
    };

    const style = getTypeStyle(documentType);

    return (
      <span
        className="inline-flex items-center justify-center px-2 py-1 rounded text-[12px] font-medium"
        style={{
          backgroundColor: style.backgroundColor,
          color: style.color,
        }}
      >
        {documentType}
      </span>
    );
  };

  // Define columns using the DataTable Column interface
  const columns: Column[] = [
    {
      id: 'fileName',
      header: 'FILE NAME',
      sortable: true,
      cell: (file: File) => (
        <ClickableCell file={file} className="text-[14px] font-medium text-[#2A2E34]">
          <div className="flex items-center space-x-2">
            <span className="truncate">{file.fileName}</span>
            {file.fileSize && (
              <span className="text-[12px] text-[#5F6F84] whitespace-nowrap">
                ({file.fileSize})
              </span>
            )}
          </div>
        </ClickableCell>
      ),
    },
    {
      id: 'documentType',
      header: 'DOCUMENT TYPE',
      sortable: true,
      cell: (file: File) => (
        <ClickableCell file={file}>
          <FileTypeIndicator documentType={file.documentType} />
        </ClickableCell>
      ),
    },
    {
      id: 'createDate',
      header: 'CREATE DATE',
      sortable: true,
      cell: (file: File) => (
        <ClickableCell file={file} className="text-[14px] text-[#5F6F84]">
          {file.createDate}
        </ClickableCell>
      ),
    },
  ];

  // Add download action column if onDownload is provided
  // if (onDownload) {
  //   columns.push({
  //     id: 'actions',
  //     header: 'ACTIONS',
  //     sortable: false,
  //     cell: (file: File) => (
  //       <div className="flex items-center space-x-2">
  //         <button
  //           onClick={(e) => {
  //             e.stopPropagation();
  //             onDownload(file);
  //           }}
  //           className="text-[#3F73F6] hover:text-[#2563EB] text-[14px] font-medium transition-colors"
  //         >
  //           Download
  //         </button>
  //       </div>
  //     ),
  //   });
  // }

  if (!files || files.length === 0) {
    return (
      <div className={`bg-white rounded-[12px] p-6 ${className}`}>
        <h3 className="text-[16px] font-semibold text-[#2A2E34] mb-4">{title}</h3>
        <p className="text-[14px] text-[#5F6F84]">{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div className={`bg-white ${className}`}>
      {title && (
        <div className="mb-4">
          <h3 className="text-[16px] font-semibold text-[#2A2E34]">{title}</h3>
        </div>
      )}
      
      <DataTable
        columns={columns}
        data={files}
        showCheckbox={false}
        className=""
        idField="id"
        rowClassName={onFileClick ? 'hover:bg-[#F8F9FC] transition-colors' : 'hover:bg-[#F8F9FC] transition-colors'}
      />
    </div>
  );
};

export default FilesTable; 