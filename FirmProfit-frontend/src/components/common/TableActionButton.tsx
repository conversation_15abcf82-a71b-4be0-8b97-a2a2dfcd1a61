import React, { ReactNode } from 'react';
import Button from './Button';

interface TableActionButtonProps {
  label: string;
  icon: ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  width?: string;
  className?: string;
  children?: React.ReactNode;
}

const TableActionButton: React.FC<TableActionButtonProps> = ({
  label,
  icon,
  onClick,
  variant = 'secondary',
  width = 'w-auto',
  className,
  children,
}) => {
  return (
    <>
      <Button
        variant={variant}
        width={width}
        icon={icon}
        onClick={onClick}
        className={className}
      >
        {children || label}
      </Button>
    </>
  );
};

export default TableActionButton;
