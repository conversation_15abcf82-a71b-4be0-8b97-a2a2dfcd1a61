import React, { ReactNode } from 'react';
import Button from './Button';

interface TableActionButtonProps {
  label: string;
  icon: ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  width?: string;
  className?: string;
  children?: React.ReactNode;
}

const TableActionButton: React.FC<TableActionButtonProps> = ({
  label,
  icon,
  variant = 'secondary',
  width = 'w-auto',
  children,
}) => {
  return (
    <>
      <Button variant={variant} width={width} icon={icon}>
        {children || label}
      </Button>
    </>
  );
};

export default TableActionButton;
