import React from 'react';
import SearchInput from './SearchInput';
import TableActionButton from './TableActionButton';
import Image from 'next/image';
import { ChevronDown } from 'lucide-react';

interface TableActionsProps {
  searchValue: string;
  onSearchChange: (value: string) => void;
  onFilter?: () => void;
  onColumns?: () => void;
  onSaveView?: () => void;
  showFilter?: boolean;
  showColumns?: boolean;
  showSaveView?: boolean;
  className?: string;
}

const TableActions: React.FC<TableActionsProps> = ({
  searchValue,
  onSearchChange,
  onFilter,
  onColumns,
  onSaveView,
  showFilter = true,
  showColumns = true,
  showSaveView = true,
  className = '',
}) => {
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <SearchInput value={searchValue} onChange={onSearchChange} placeholder="Search" />

      {showFilter && (
        <TableActionButton
          label="Filter"
          icon={<Image src="/assets/filter.svg" alt="Filter" width={20} height={20} />}
          onClick={onFilter}
          variant="secondary"
          width="w-[94px]"
        />
      )}

      {showColumns && (
        <TableActionButton
          label="Columns"
          icon={<ChevronDown size={20} />}
          onClick={onColumns}
          variant="secondary"
          width="w-[124px]"
        />
      )}

      {showSaveView && (
        <TableActionButton
          label="Save view"
          icon={<Image aria-hidden src="/assets/save.svg" alt="File icon" width={20} height={20} />}
          onClick={onSaveView}
          variant="outline"
          width="w-[134px]"
        />
      )}
    </div>
  );
};

export default TableActions;
