import React from 'react';
import SearchInput from './SearchInput';
import TableActionButton from './TableActionButton';
import Image from 'next/image';
import { ChevronDown } from 'lucide-react';

interface TableActionsProps {
  searchValue: string;
  onSearchChange: (value: string) => void;
  onFilter?: () => void;
  onColumns?: () => void;
  onSaveView?: () => void;
  showFilter?: boolean;
  showColumns?: boolean;
  showSaveView?: boolean;
  filterCount?: number;
  className?: string;
}

const TableActions: React.FC<TableActionsProps> = ({
  searchValue,
  onSearchChange,
  onFilter,
  onColumns,
  onSaveView,
  showFilter = true,
  showColumns = true,
  showSaveView = true,
  filterCount,
  className = '',
}) => {
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <SearchInput value={searchValue} onChange={onSearchChange} placeholder="Search" />

      {showFilter && (
        <div className="relative">
          <TableActionButton
            label="Filter"
            icon={<Image src="/assets/filter.svg" alt="Filter" width={20} height={20} />}
            onClick={onFilter}
            variant="secondary"
            width="w-[94px]"
          />
          {filterCount && filterCount > 0 && (
            <span className="absolute -top-2 -right-2 bg-blue-600 text-white text-xs font-medium px-1.5 py-0.5 rounded-full min-w-[20px] text-center">
              {filterCount}
            </span>
          )}
        </div>
      )}

      {showColumns && (
        <TableActionButton
          label="Columns"
          icon={<ChevronDown size={20} />}
          onClick={onColumns}
          variant="secondary"
          width="w-[124px]"
        />
      )}

      {showSaveView && (
        <TableActionButton
          label="Save view"
          icon={<Image aria-hidden src="/assets/save.svg" alt="File icon" width={20} height={20} />}
          onClick={onSaveView}
          variant="outline"
          width="w-[134px]"
        />
      )}
    </div>
  );
};

export default TableActions;
