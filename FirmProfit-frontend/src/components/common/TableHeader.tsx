import React from 'react';

export interface TableHeaderProps {
  label: string;
  sortable?: boolean;
  className?: string;
  minWidth?: number;
  sortField?: string;
  currentSortBy?: string;
  currentSortOrder?: 'asc' | 'desc';
  onSort?: (field: string, order: 'asc' | 'desc') => void;
}

const TableHeader: React.FC<TableHeaderProps> = ({
  label,
  sortable = false,
  className = '',
  minWidth,
  sortField = '',
  currentSortBy,
  currentSortOrder,
  onSort,
}) => {
  const handleSort = () => {
    if (!sortable || !onSort || !sortField) return;
    
    // If this column is already sorted, toggle the order
    if (currentSortBy === sortField) {
      const newOrder = currentSortOrder === 'asc' ? 'desc' : 'asc';
      onSort(sortField, newOrder);
    } else {
      // If not sorted, start with ascending
      onSort(sortField, 'asc');
    }
  };

  const isActiveSort = currentSortBy === sortField;
  
  return (
    <th
      scope="col"
      // className={`px-3 py-3 text-left text-[14px] leading-[20px] font-medium text-[#5F6F84] bg-[#F3F5F9] uppercase tracking-wider whitespace-nowrap ${className}`}
      // style={{ minWidth }}
      className={`px-3 py-3 text-left text-[14px] leading-[20px] font-medium text-[#5F6F84] bg-[#F3F5F9] uppercase tracking-wider whitespace-nowrap ${
        sortable ? 'cursor-pointer hover:bg-[#E8EDF7] select-none' : ''
      } ${className}`}
      style={{ minWidth }}
      onClick={handleSort}
    >
      <div className="flex items-center gap-1">
        <span>{label}</span>
        {sortable && (
          <div className="flex flex-col ml-1">
            {/* Up arrow */}
            <svg 
              className={`w-3 h-3 ${
                isActiveSort && currentSortOrder === 'asc' 
                  ? 'text-[#3F73F6]' 
                  : 'text-[#C0C4CC]'
              }`}
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M7 14l5-5 5 5z"/>
            </svg>
            {/* Down arrow */}
            <svg 
              className={`w-3 h-3 -mt-1 ${
                isActiveSort && currentSortOrder === 'desc' 
                  ? 'text-[#3F73F6]' 
                  : 'text-[#C0C4CC]'
              }`}
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M7 10l5 5 5-5z"/>
            </svg>
          </div>
        )}
      </div>
    </th>
  );
};

export default TableHeader;
