# Reusable Table Components

This directory contains a set of reusable components designed to create consistent data tables throughout the application.

## Main Components

### DataTable

The core component for displaying tabular data. It handles row selection, column rendering, and other common table features.

```tsx
import { DataTable, Column } from '@/components/common';

// Define your data columns
const columns: Column[] = [
  {
    id: 'name',
    header: 'Name',
    cell: row => <div>{row.name}</div>,
  },
  // Add more columns...
];

// Use the DataTable component
<DataTable
  columns={columns}
  data={yourData}
  selectedIds={selectedItems}
  onSelectRow={handleSelectRow}
  onSelectAll={handleSelectAll}
  isAllSelected={allSelected}
  isLoading={loading}
  lastItemRef={lastItemRef} // For infinite scrolling
/>;
```

### PageHeader

A consistent page header with title and table actions.

```tsx
import { PageHeader } from '@/components/common';

<PageHeader
  title="Your Page Title"
  searchValue={searchTerm}
  onSearchChange={setSearchTerm}
  onFilter={handleFilter}
  onColumns={handleColumns}
  onSaveView={handleSaveView}
/>;
```

### TableActions

The search, filter, columns, and save view buttons used in page headers.

```tsx
import { TableActions } from '@/components/common';

<TableActions
  searchValue={searchTerm}
  onSearchChange={setSearchTerm}
  onFilter={handleFilter}
  onColumns={handleColumns}
  onSaveView={handleSaveView}
  showFilter={true}
  showColumns={true}
  showSaveView={true}
/>;
```

### StatusBadge

Consistent status indicators.

```tsx
import { StatusBadge } from '@/components/common';

<StatusBadge status="On Track" />
<StatusBadge status="Delayed" variant="outline" />
```

### Avatar

User avatars for tables.

```tsx
import { Avatar } from '@/components/common';

<Avatar name="User Name" />
<Avatar imageUrl="/path/to/image.jpg" />
```

## Helper Components

- `TableHeader`: Column headers with optional sorting indicators
- `TableCheckbox`: Consistent checkboxes for row selection
- `TableActionButton`: Buttons for table actions
- `SearchInput`: Standardized search input with icon

## Example Usage

For a complete example, see:

- `src/components/workflows/WorkflowsAssigned.tsx`
- `src/pages/court-notice/index.tsx`
- `src/pages/court-notice/follow-up.tsx`

These components demonstrate how to combine the reusable table components to create consistent user interfaces throughout the application.
