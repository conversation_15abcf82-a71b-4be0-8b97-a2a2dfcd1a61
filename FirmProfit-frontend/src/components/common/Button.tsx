import React, { ReactNode } from 'react';
import { COLORS } from '@/constants/theme';

interface ButtonProps {
  children: ReactNode;
  onClick?: () => void;
  className?: string;
  variant?: 'primary' | 'secondary' | 'outline';
  icon?: ReactNode;
  width?: string;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
  testId?: string;
}

/**
 * Reusable button component
 */
export default function Button({
  children,
  onClick,
  className = '',
  variant = 'primary',
  icon,
  width = 'w-auto',
  disabled = false,
  type = 'button',
  testId,
}: ButtonProps) {
  const getVariantClasses = () => {
    switch (variant) {
      case 'primary':
        return `bg-[${COLORS.PRIMARY}] text-white border border-[${COLORS.PRIMARY}]`;
      case 'secondary':
        return `bg-white border font-medium border-[${COLORS.BORDER}] text-[${COLORS.TEXT_SECONDARY}]`;
      case 'outline':
        return `bg-white border font-medium border-[${COLORS.PRIMARY}] text-[${COLORS.PRIMARY}]`;
      default:
        return '';
    }
  };

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      data-testid={testId}
      className={`${width} h-[40px] cursor-pointer px-[12px] py-[16px] rounded-[12px] flex items-center justify-between gap-1 ${getVariantClasses()} ${
        disabled ? 'opacity-50 cursor-not-allowed' : ''
      } ${className}`}
    >
      <span>{children}</span>
      {icon && icon}
    </button>
  );
}
