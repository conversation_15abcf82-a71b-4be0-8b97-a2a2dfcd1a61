import { useRouter } from 'next/router';
import { useEffect, useState, ReactNode } from 'react';

// Public paths that don't require authentication
const publicPaths = [
  '/',
  '/signin',
  '/signup',
  '/auth/resetpassword',
  '/auth/accountlocked',
  '/auth/verifymfa',
];

interface RouteGuardProps {
  children: ReactNode;
}

/**
 * Component that guards routes based on authentication status
 */
export default function RouteGuard({ children }: RouteGuardProps) {
  const router = useRouter();
  const [authorized, setAuthorized] = useState(false);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);

    // Auth check function
    const authCheck = (url: string) => {
      // Get token and MFA status from local storage - only runs client-side
      const token = typeof window !== 'undefined' ? localStorage.getItem('authToken') : null;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      let mfa: any = typeof window !== 'undefined' ? localStorage.getItem('Mfa') : null;
      mfa = mfa ? JSON.parse(mfa) : false; // Ensure mfa is a boolean

      // Check if the path is public or starts with a public path
      const isPublicPath = publicPaths.some(
        path =>
          url === path ||
          url.startsWith(path + '/') ||
          (path.includes('/auth/resetpassword') && url.startsWith('/auth/resetpassword'))
      );

      if (token && mfa) {
        if (isPublicPath) {
          setAuthorized(false);
          router.push('/court-notice/new'); // Redirect to dashboard if logged in and trying to access public page
          return;
        }

        if (!mfa) {
          setAuthorized(false);
          router.push('/signin'); // If MFA is required but not enabled, redirect to sign-in
          return;
        }

        setAuthorized(true); // Allow access to protected routes if MFA is enabled
      } else {
        if (!isPublicPath) {
          setAuthorized(false);
          router.push('/signin'); // Redirect unauthenticated users to sign-in
        } else {
          setAuthorized(true); // Allow public paths
        }
      }
    };

    // Run auth check on initial load
    authCheck(router.asPath);

    // Listen for route changes
    const hideContent = () => setAuthorized(false);
    router.events.on('routeChangeStart', hideContent);

    // Run auth check on route change
    router.events.on('routeChangeComplete', authCheck);

    // Cleanup event listeners
    return () => {
      router.events.off('routeChangeStart', hideContent);
      router.events.off('routeChangeComplete', authCheck);
    };
  }, [router]);

  // During SSR, don't block rendering, but protect client-side
  if (!isClient) {
    return <>{children}</>;
  }

  return authorized ? <>{children}</> : null;
}
