import React from 'react';
// import { useSelector } from 'react-redux';
// import { RootState } from '@/redux/types';
import Link from 'next/link';
// import { useRouter } from 'next/router';
import Image from 'next/image';

const Sidebar: React.FC = () => {
  // const sidebar = useSelector((state: RootState) => state.sidebar);
  // const router = useRouter();

  // Define navigation items to match the exact appearance in the image
  const navItems = [
    {
      title: 'Document',
      icon: '/assets/sidebar-hamburger.svg',
      path: '#',
    },
  ];
  const sidebarIcon = [
    {
      title: 'Home',
      icon: '/assets/home-02.svg',
      path: '#',
    },
    {
      title: 'Home',
      icon: '/assets/eye.svg',
      isActive: true,
      path: '/court-notice/new',
    },
    {
      title: 'Document',
      icon: '/assets/contacts-1.svg',
      path: '/contacts',
    },
    {
      title: 'Home',
      icon: '/assets/bag.svg',
      path: '#',
    },
    {
      title: 'Home',
      icon: '/assets/billing.svg',
      path: '#',
    },
    // patel
    {
      title: 'Home',
      icon: '/assets/pie-chart-report.svg',
      path: '#',
    },
    {
      title: 'Home',
      icon: '/assets/calendar.svg',
      path: '#',
    },
    {
      title: 'Home',
      icon: '/assets/user-admin.svg',
      path: '#',
    },
  ];

  return (
    <aside className="h-screen bg-[#233B5B] text-white flex flex-col w-[60px]">
      <div className="flex flex-col items-center h-[436px] flex-grow">
        {navItems.map((item, index) => (
          <Link
            key={index}
            href={item.path}
            className="flex justify-center border-b-[1px] border-[#ffffff20] w-[60px] h-[60px] items-center p-[20px] gap-[10px]"
          >
            <div className={`flex justify-center items-center rounded-[12px]`}>
              <Image src={item.icon} alt="Sidebar" width={20} height={20} />
            </div>
          </Link>
        ))}
        {sidebarIcon.map((item, index) => (
          <Link
            key={index}
            href={item.path}
            className="flex justify-center w-[60px] h-[60px] items-center"
          >
            <div
              className={`flex justify-center mt-2 items-center rounded-[12px] w-[44px] h-[44px]
                ${item.isActive ? 'bg-[#3F73F6]' : ''}`}
            >
              <Image src={item.icon} alt="Sidebar" width={20} height={20} />
            </div>
          </Link>
        ))}
      </div>
    </aside>
  );
};

{
  /* <aside className="h-screen bg-[#002147] text-white flex flex-col w-[60px] py-4">
<div className="flex flex-col items-center flex-grow gap-2">
  {navItems.map((item, index) => (
    <Link key={index} href={item.path}>
      <div
        className={`flex justify-center items-center w-[40px] h-[40px] rounded-[12px] transition-all 
              ${item.isActive
            ? "bg-[#3F73F6] text-white"
            : "text-[#C5D5FC] hover:bg-blue-800"
          }`}
      >
        <Image src={item.icon} alt="Sidebar Icon" width={24} height={24} />
      </div>
    </Link>
  ))}
</div>
</aside> */
}
export default Sidebar;
