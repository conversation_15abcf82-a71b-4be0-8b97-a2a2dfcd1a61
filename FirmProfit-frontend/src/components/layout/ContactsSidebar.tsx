import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';

const ContactsSidebar: React.FC = () => {
  const router = useRouter();

  const navItems = [
    {
      title: 'Contacts',
      path: '/contacts',
      isActive: router.pathname === '/contacts',
    },
    {
      title: 'Clients',
      path: '/clients',
      isActive: router.pathname === '/clients',
    },
    {
      title: 'Matters',
      path: '/matters',
      isActive: router.pathname === '/matters',
    },
  ];

  return (
    <aside className="w-[260px] px-4 py-6 border-r border-[#E5E9EB] h-full flex flex-col">
      <div className="flex flex-col">
        {navItems.map((item, index) => (
          <div key={index} className="relative">
            <Link
              href={item.path}
              className={`block px-6 py-3 text-[14px] font-medium transition-colors duration-200 ${
                item.isActive
                  ? 'bg-[#F3F5F9] text-[#3F73F6] text-[14px] rounded-[12px]'
                  : 'text-[#5F6F84] text-[14px]'
              }`}
            >
              {item.title}
            </Link>
          </div>
        ))}
      </div>
    </aside>
  );
};

export default ContactsSidebar; 