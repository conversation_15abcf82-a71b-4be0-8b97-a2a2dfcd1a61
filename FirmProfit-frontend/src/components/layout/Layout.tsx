import React, { ReactNode } from 'react';
// import { useSelector } from 'react-redux';
// import { RootState } from '@/redux/types';
import Header from './Header';
import Sidebar from './Sidebar';

interface LayoutProps {
  children: ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  // const { collapsed } = useSelector((state: RootState) => state.sidebar);

  return (
    <div className="flex h-screen overflow-hidden bg-white">
      <Sidebar />
      <div className="flex flex-col flex-1 overflow-hidden">
        <Header />
        <main className={`flex-1 overflow-y-auto transition-all duration-300`}>{children}</main>
      </div>
    </div>
  );
};

export default Layout;
