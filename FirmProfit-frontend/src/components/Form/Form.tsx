import React, { FormEvent, ReactNode } from 'react';

interface FormProps {
  children: ReactNode;
  onSubmit: (e: FormEvent<HTMLFormElement>) => void;
  className?: string;
  id?: string;
  'data-testid'?: string;
}

/**
 * Reusable Form component
 */
const Form: React.FC<FormProps> = ({
  children,
  onSubmit,
  className = '',
  id,
  'data-testid': testId = 'form',
}) => {
  return (
    <form id={id} onSubmit={onSubmit} className={className} data-testid={testId}>
      {children}
    </form>
  );
};

export default Form;
