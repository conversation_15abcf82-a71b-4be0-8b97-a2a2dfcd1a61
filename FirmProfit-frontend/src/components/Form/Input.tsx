import React, { useState } from 'react';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  id: string;
  label?: string;
  error?: string;
  showPasswordToggle?: boolean;
  className?: string;
  labelClassName?: string;
  inputClassName?: string;
  'data-testid'?: string;
}

/**
 * Reusable Input component with standardized styling
 */
const Input: React.FC<InputProps> = ({
  id,
  label,
  error,
  type = 'text',
  showPasswordToggle = false,
  className = '',
  labelClassName = '',
  inputClassName = '',
  'data-testid': testId = `${id}-input`,
  ...props
}) => {
  const [showPassword, setShowPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const inputType = type === 'password' && showPassword ? 'text' : type;

  const baseInputClass =
    'block w-full h-[40px] rounded-[12px] border border-[#DCE2EB] py-[14px] px-[16px] text-[#2A2E34] ring-inset placeholder:text-[14px] placeholder:text-[#5F6F84] placeholder:font-normal focus:ring focus:ring-inset focus:border-[1px] focus:border-[#3f73f650] focus:ring-[#3F73F6] focus:outline-none sm:text-sm';

  const inputClasses = `${baseInputClass} ${error ? 'border-red-500' : ''} ${inputClassName}`;

  return (
    <div className={`${className}`}>
      {label && (
        <label
          htmlFor={id}
          className={`block text-[14px] font-medium leading-6 text-[#2A2E34] mb-1 ${labelClassName}`}
        >
          {label}
        </label>
      )}
      <div className="relative mt-1">
        <input
          id={id}
          name={id}
          type={inputType}
          className={inputClasses}
          data-testid={testId}
          {...props}
        />

        {showPasswordToggle && type === 'password' && (
          <button
            type="button"
            className="absolute inset-y-0 cursor-pointer right-0 pr-3 flex items-center"
            onClick={togglePasswordVisibility}
            data-testid={`${id}-toggle-visibility`}
          >
            {showPassword ? (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-[#5F6F84]"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"
                />
              </svg>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-[#5F6F84]"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                />
              </svg>
            )}
          </button>
        )}
      </div>
      {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
    </div>
  );
};

export default Input;
