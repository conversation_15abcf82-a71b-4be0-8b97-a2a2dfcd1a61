@import 'tailwindcss';
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  font-family: var(--font-poppins), Arial, Helvetica, sans-serif;
  color: rgb(var(--foreground-rgb));
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* React Datepicker Custom Styles */
.react-datepicker {
  font-family: var(--font-poppins), Arial, Helvetica, sans-serif !important;
  border-radius: 12px !important;
  border: 1px solid #dce2eb !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  overflow: hidden !important;
  margin-left: 60px !important;
}

.react-datepicker__header {
  background-color: #f3f5f9 !important;
  border-bottom: 1px solid #dce2eb !important;
  padding-top: 12px !important;
}

.react-datepicker__current-month {
  font-weight: 600 !important;
  margin-bottom: 8px !important;
  color: #2a2e34 !important;
}

.react-datepicker__navigation {
  top: 12px !important;
}

.react-datepicker__day-name {
  color: #5f6f84 !important;
  font-weight: 500 !important;
  margin: 4px !important;
}

.react-datepicker__day {
  margin: 4px !important;
  border-radius: 8px !important;
  color: #2a2e34 !important;
}

.react-datepicker__day:hover {
  background-color: #f3f5f9 !important;
}

.react-datepicker__day--selected,
.react-datepicker__day--keyboard-selected {
  background-color: #3f73f6 !important;
  color: white !important;
  font-weight: 500 !important;
}

.react-datepicker__day--disabled {
  color: #dce2eb !important;
}

.react-datepicker__year-dropdown,
.react-datepicker__year-read-view {
  border-radius: 8px !important;
  border: 1px solid #dce2eb !important;
}

.react-datepicker__year-option {
  padding: 6px 10px !important;
}

.react-datepicker__year-option:hover {
  background-color: #f3f5f9 !important;
}

/* Time Picker Styles */
.react-datepicker__time-container {
  border-left: 1px solid #dce2eb !important;
  width: 110px !important;
}

.react-datepicker__time {
  background-color: white !important;
}

.react-datepicker__time-box {
  width: 100% !important;
  overflow-x: hidden !important;
}

.react-datepicker__time-list {
  padding: 0 !important;
  height: 200px !important;
}

.react-datepicker__time-list-item {
  padding: 8px 12px !important;
  color: #2a2e34 !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  text-align: center !important;
}

.react-datepicker__time-list-item:hover {
  background-color: #f3f5f9 !important;
  color:#2a2e34 !important;
}

.react-datepicker__time-list-item--selected {
  background-color: #3f73f6 !important;
  color: white !important;
  font-weight: 500 !important;
}

.react-datepicker__time-list-item--disabled {
  color: #dce2eb !important;
}

.react-datepicker__header--time {
  padding: 8px 0 !important;
  background-color: #f3f5f9 !important;
  color: #5f6f84 !important;
  font-weight: 500 !important;
  font-size: 14px !important;
}

/* Time only picker modal positioning */
.react-datepicker-popper[data-placement^='bottom'] .react-datepicker__triangle {
  display: none;
}

.react-datepicker-popper[data-placement^='bottom'] {
  margin-top: 10px !important;
}

/* Custom time picker popper class */
.time-picker-popper {
  z-index: 9999 !important;
}

.time-picker-popper .react-datepicker {
  width: 120px !important;
}

.time-picker-popper .react-datepicker__time-container {
  width: 120px !important;
}

.time-picker-popper .react-datepicker__time {
  border-radius: 12px !important;
}

.time-picker-popper .react-datepicker__header--time {
  border-top-left-radius: 12px !important;
  border-top-right-radius: 12px !important;
}
