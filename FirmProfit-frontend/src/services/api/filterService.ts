import { apiGet } from './apiUtils';
import { FilterOption } from '@/types/workflowFilter';

/**
 * Filter API service
 * Handles all filter-related API operations for workflow filtering
 */
const filterService = {
  /**
   * Get users and roles for assignee/contact/attorney filters
   * @param search - Optional search term
   * @returns Promise with user options
   */
  getUsersAndRoles: async (search?: string): Promise<FilterOption[]> => {
    try {
      const params = search ? `?search=${encodeURIComponent(search)}` : '';
      const response = await apiGet<{
        statusCode: number;
        data: {
          users?: Array<{
            id: string;
            name: string;
            email?: string;
            role?: string;
            is_active?: boolean;
          }>;
        };
      }>(`/workflow/users-and-roles${params}`);

      if (response.data.statusCode === 200 && response.data.data.users) {
        const options = response.data.data.users
          .filter(user => user.is_active !== false)
          .map(user => ({
            value: user.id,
            label: user.name,
            id: user.id,
          }));

        return options;
      }
      
      return [];
    } catch (error) {
      console.error('Error fetching users and roles:', error);
      return [];
    }
  },

  /**
   * Get matter list for matter filter
   * @param search - Optional search term
   * @returns Promise with matter options
   */
  getMatters: async (search?: string): Promise<FilterOption[]> => {
    try {
      const params = search ? `?search=${encodeURIComponent(search)}` : '';
      const response = await apiGet<{
        statusCode: number;
        data: Array<{
          id: string;
          clientName: string;
          matterName: string;
          client_id?: string;
        }> | {
          matters?: Array<{
            id: string;
            clientName: string;
            matterName: string;
            client_id?: string;
          }>;
        };
      }>(`/workflow/matter-list${params}`);

      if (response.data.statusCode === 200) {
        let matters: any[] = [];
        
        if (Array.isArray(response.data.data)) {
          matters = response.data.data;
        } else if (response.data.data?.matters) {
          matters = response.data.data.matters;
        }

        const options = matters.map(matter => ({
          value: matter.id,
          label: `${matter.clientName} / ${matter.matterName}`,
          id: matter.id,
        }));

        return options;
      }
      
      return [];
    } catch (error) {
      console.error('Error fetching matters:', error);
      return [];
    }
  },

  /**
   * Get template list for template filter
   * @param search - Optional search term
   * @returns Promise with template options
   */
  getTemplates: async (search?: string): Promise<FilterOption[]> => {
    try {
      // For now, return static template options as per requirements
      // In the future, this could be an API call
      const templates = [
        { value: 'newCourtNotice', label: 'New Court Notice', id: 'newCourtNotice' },
        { value: 'courtNoticeFollowup', label: 'Court Notice Follow-up', id: 'courtNoticeFollowup' },
      ];

      if (search) {
        const searchLower = search.toLowerCase();
        return templates.filter(template => 
          template.label.toLowerCase().includes(searchLower)
        );
      }

      return templates;
    } catch (error) {
      console.error('Error fetching templates:', error);
      return [];
    }
  },

  /**
   * Get status options for status filter
   * @returns Promise with status options
   */
  getStatuses: async (): Promise<FilterOption[]> => {
    try {
      // Return static status options as per requirements
      return [
        { value: 'onTrack', label: 'On Track', id: 'onTrack' },
        { value: 'dueSoon', label: 'Due Soon', id: 'dueSoon' },
        { value: 'overdue', label: 'Overdue', id: 'overdue' },
        { value: 'completed', label: 'Completed', id: 'completed' },
      ];
    } catch (error) {
      console.error('Error fetching statuses:', error);
      return [];
    }
  },

  /**
   * Get current user information
   * @returns Promise with current user data
   */
  getCurrentUser: async (): Promise<{ id: string; name: string } | null> => {
    try {
      // For demo purposes, return a mock user
      // In production, this would come from auth context or user service
      return {
        id: 'current-user-id',
        name: 'Current User',
      };
    } catch (error) {
      console.error('Error fetching current user:', error);
      return null;
    }
  },

  /**
   * Get attorneys for attorney filter
   * @param search - Optional search term
   * @returns Promise with attorney options
   */
  getAttorneys: async (search?: string): Promise<FilterOption[]> => {
    try {
      const params = search ? `?search=${encodeURIComponent(search)}&role=attorney` : '?role=attorney';
      const response = await apiGet<{
        statusCode: number;
        data: {
          users?: Array<{
            id: string;
            name: string;
            role?: string;
            is_active?: boolean;
          }>;
        };
      }>(`/workflow/users-and-roles${params}`);

      if (response.data.statusCode === 200 && response.data.data.users) {
        const options = response.data.data.users
          .filter(user => user.is_active !== false && user.role?.toLowerCase().includes('attorney'))
          .map(user => ({
            value: user.id,
            label: user.name,
            id: user.id,
          }));

        return options;
      }
      
      return [];
    } catch (error) {
      console.error('Error fetching attorneys:', error);
      return [];
    }
  },
};

export default filterService;
