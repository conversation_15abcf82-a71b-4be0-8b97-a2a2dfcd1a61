import apiClient from './config';
import { AxiosResponse } from 'axios';
import { CourtNoticeListResponse, CourtNoticeListParams } from '@/types/courtNotice';

/**
 * Court Notice API service
 * Handles all court notice related API operations
 */
const courtNoticeService = {
  /**
   * Get court notice list
   * @param params - Query parameters for the request (userId will be automatically added from localStorage)
   * @returns Promise with court notice data
   */
  getCourtNoticeList: async (params: Omit<CourtNoticeListParams, 'userId'>): Promise<CourtNoticeListResponse> => {
    // Get userId from localStorage
    const user = localStorage.getItem('user');
    const userData = user ? JSON.parse(user) : null;
    const userId = userData?.id;

    if (!userId) {
      throw new Error('User ID not found in localStorage');
    }

    // Add userId to params and convert sortBy/sortOrder to API format
    const requestParams: Record<string, string | number | undefined> = {
      ...params,
      userId
    };

    // Convert sortBy to sort_by and sortOrder to sort_order for API compatibility
    if (params.sortBy) {
      requestParams.sort_by = params.sortBy;
      delete requestParams.sortBy;
    }
    if (params.sortOrder) {
      requestParams.sort_order = params.sortOrder;
      delete requestParams.sortOrder;
    }

    const response: AxiosResponse<CourtNoticeListResponse> = await apiClient.get(
      '/workflow/court-notice-list',
      { params: requestParams }
    );
    return response.data;
  },

  /**
   * Get court notice details by ID
   * @param id - Court notice ID
   * @returns Promise with court notice details
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  getCourtNoticeById: async (id: string): Promise<any> => {
    const response = await apiClient.get(`/workflow/court-notice/${id}`);
    return response.data;
  },
};

export default courtNoticeService;
