# API Utilities

This directory contains utilities for making API requests in a consistent, centralized way throughout the application.

## Key Files

- `config.ts`: Configures the base API client with interceptors and exports the base URL
- `apiUtils.ts`: Provides utility functions to make API requests without repeating the base URL

## Why Use These Utilities?

1. **DRY (Don't Repeat Yourself)**: Avoid repeating `process.env.NEXT_PUBLIC_BASE_URL` throughout your code
2. **Consistency**: All API calls use the same format and error handling
3. **Maintainability**: If the API URL structure changes, you only need to update it in one place
4. **Type Safety**: Includes TypeScript generics for better type checking of requests and responses

## How to Use

### For API Calls with Authentication

```typescript
import { apiGet, apiPost, apiPut, apiDelete } from '@/services/api/apiUtils';

// GET request
const fetchData = async () => {
  const response = await apiGet<YourResponseType>('/endpoint');
  return response.data;
};

// POST request
const createData = async (data: YourDataType) => {
  const response = await apiPost<YourResponseType, YourDataType>('/endpoint', data);
  return response.data;
};

// PUT request
const updateData = async (id: string, data: YourDataType) => {
  const response = await apiPut<YourResponseType>(`/endpoint/${id}`, data);
  return response.data;
};

// DELETE request
const deleteData = async (id: string) => {
  await apiDelete(`/endpoint/${id}`);
};
```

### For Public API Calls (No Auth Token)

```typescript
import { publicGet, publicPost } from '@/services/api/apiUtils';

// Public GET request
const fetchPublicData = async () => {
  const response = await publicGet<YourResponseType>('/public-endpoint');
  return response.data;
};

// Public POST request
const submitPublicData = async (data: YourDataType) => {
  const response = await publicPost<YourResponseType>('/public-endpoint', data);
  return response.data;
};
```

### Getting the Full URL

If you need the complete URL (base + endpoint) for any reason:

```typescript
import { getFullUrl } from '@/services/api/apiUtils';

const fullUrl = getFullUrl('/endpoint'); // Returns the complete URL
```
