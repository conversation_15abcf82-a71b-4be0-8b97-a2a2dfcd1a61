import { apiGet } from './apiUtils';
import { WorkflowResponse } from '@/types/workflow';
import { WorkflowFilterParams } from '@/types/workflowFilter';

/**
 * Workflow API service
 * Handles all workflow-related API operations
 */
const workflowService = {
  /**
   * Get workflows assigned to the current user
   * @param page - Page number for pagination
   * @param limit - Number of items per page
   * @param filterParams - Optional filter parameters
   * @returns Promise with workflow data
   */
  getMyWorkflows: async (
    page: number = 1,
    limit: number = 10,
    filterParams?: WorkflowFilterParams
  ): Promise<WorkflowResponse> => {
    let url = `/workflow/my-workflow?page=${page}&limit=${limit}`;

    // Add filter parameters if provided
    if (filterParams?.filters && filterParams.filters.length > 0) {
      const filtersParam = encodeURIComponent(JSON.stringify(filterParams.filters));
      url += `&filters=${filtersParam}`;
    }

    const response = await apiGet<WorkflowResponse>(url);
    return response.data;
  },

  /**
   * Get a specific workflow by ID
   * @param id - Workflow ID
   * @returns Promise with workflow data
   */
  getWorkflowById: async (id: string) => {
    const response = await apiGet(`/workflow/${id}`);
    return response.data;
  },
};

export default workflowService;
