/**
 * Matter Service
 * Handles all API operations related to matters
 */

import { Matter, MatterListParams, MatterListResponse } from '@/types/matter';

/**
 * Matter Service Class
 */
export class MatterService {
  /**
   * Get matters for a specific contact/client
   */
  static async getMattersByContact(contactId: string): Promise<Matter[]> {
    // Mock data for demo - in production, this would be an API call
    const mockMatters: Record<string, Matter[]> = {
      '1': [ // <PERSON>
        {
          id: '1',
          matterId: 'M001',
          type: 'Divorce',
          matter: 'Divorce',
          status: 'Open',
          contact: '<PERSON>',
          attorney: '<PERSON><PERSON>',
          description: 'Some description here. Some more description. And more description',
          court: 'Bergen',
        },
        {
          id: '2',
          matterId: 'M002',
          type: 'Child Custody',
          matter: 'Child Custody',
          status: 'Open',
          contact: '<PERSON>',
          attorney: '<PERSON><PERSON>',
          description: 'Some description here. Some more description. And more description',
          court: 'Bergen',
        },
      ],
      '2': [ // <PERSON>
        {
          id: '3',
          matterId: 'M003',
          type: 'Corporate Law',
          matter: 'Corporate Law',
          status: 'Open',
          contact: '<PERSON>',
          attorney: '<PERSON>',
          description: 'Corporate restructuring and compliance matter for Donovan Enterprises',
          court: 'Quebec Superior Court',
        },
      ],
      '5': [ // John Donte
        {
          id: '4',
          matterId: 'M004',
          type: 'Contract Dispute',
          matter: 'Contract Dispute',
          status: 'Closed',
          contact: 'John Donte',
          attorney: 'K. Melendez',
          description: 'Contract dispute resolution completed successfully in 2022',
          court: 'New York State Court',
        },
      ],
    };

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 200));
    
    return mockMatters[contactId] || [];
  }

  /**
   * Get a single matter by ID
   */
  static async getMatter(id: string): Promise<Matter | null> {
    // For demo purposes - in production, this would be an API call
    const allMatters = await Promise.all([
      this.getMattersByContact('1'),
      this.getMattersByContact('2'),
      this.getMattersByContact('5'),
    ]);
    
    const flatMatters = allMatters.flat();
    return flatMatters.find(matter => matter.id === id) || null;
  }

  /**
   * Get all matters with pagination and filters
   */
  static async getMatters(params: MatterListParams = {}): Promise<MatterListResponse> {
    // For demo purposes - in production, this would be an API call
    const allMatters = await Promise.all([
      this.getMattersByContact('1'),
      this.getMattersByContact('2'),
      this.getMattersByContact('5'),
    ]);
    
    const flatMatters = allMatters.flat();
    
    return {
      data: flatMatters,
      total: flatMatters.length,
      page: params.page || 1,
      limit: params.limit || 50,
      totalPages: Math.ceil(flatMatters.length / (params.limit || 50)),
    };
  }
}

// Export individual methods for convenience
export const {
  getMattersByContact,
  getMatter,
  getMatters,
} = MatterService; 