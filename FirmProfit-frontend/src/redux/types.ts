// Type definitions for Redux state
export interface CounterState {
  value: number;
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
  error: string | null;
}

export interface SidebarState {
  collapsed: boolean;
}

export interface UserState {
  name: string;
  avatar: string;
  isLoggedIn: boolean;
}

export interface RootState {
  counter: CounterState;
  sidebar: SidebarState;
  user: UserState;
}
