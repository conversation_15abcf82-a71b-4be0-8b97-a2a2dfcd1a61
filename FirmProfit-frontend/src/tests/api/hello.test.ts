import { createMocks } from 'node-mocks-http';
import handler from '@/pages/api/hello';

describe('Hello API Endpoint', () => {
  test('returns the correct response', async () => {
    const { req, res } = createMocks({
      method: 'GET',
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);

    const data = JSON.parse(res._getData());
    expect(data).toHaveProperty('message', 'Hello from Elite Square!');
    expect(data).toHaveProperty('timestamp');
    expect(typeof data.timestamp).toBe('number');
  });
});
