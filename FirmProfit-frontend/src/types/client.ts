/**
 * Client Type Definitions
 * These types define the structure of client data in the application
 */

// Client status enum for type safety
export type ClientStatus = 'Active' | 'Closed';

// Main Client interface
export interface Client {
  id: string;
  name: string;
  status: ClientStatus;
  type: string; // Will be 'Client' for clients list
  email: string;
  mobile: string;
  attorney: string;
  createdAt?: string;
  updatedAt?: string;
  notes?: string;
  company?: string;
  syncStatus?: string;
}

// Client list query parameters
export interface ClientListParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: ClientStatus;
  sortBy?: keyof Client;
  sortOrder?: 'asc' | 'desc';
  attorney?: string;
}

// Client API response interface
export interface ClientListResponse {
  data: Client[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Client form data interface
export interface ClientFormData {
  name: string;
  email: string;
  mobile: string;
  status: ClientStatus;
  attorney?: string;
  notes?: string;
  company?: string;
}

// Client filter interface
export interface ClientFilters {
  status: ClientStatus[];
  attorney: string[];
  dateRange?: {
    start: string;
    end: string;
  };
} 