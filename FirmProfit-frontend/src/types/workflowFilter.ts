/**
 * Workflow Filter Type Definitions
 * These types define the structure for the advanced workflow filtering system
 */

// Available filter field names
export type FilterFieldName = 
  | 'name'
  | 'assignee'
  | 'status'
  | 'contact'
  | 'matter'
  | 'templates'
  | 'dueDate'
  | 'createDate'
  | 'attorney';

// Available filter criteria based on field type
export type FilterCriteria = 
  | 'contains'
  | 'isEqual'
  | 'isNotEqual'
  | 'doesNotContain'
  | 'none'
  | 'unassigned'
  | 'before'
  | 'after'
  | 'between';

// Filter field configuration
export interface FilterFieldConfig {
  label: string;
  availableCriteria: FilterCriteria[];
  valueType: 'text' | 'select' | 'multiSelect' | 'date' | 'dateRange';
  apiEndpoint?: string;
  searchable?: boolean;
}

// Filter option for dropdowns
export interface FilterOption {
  value: string;
  label: string;
  id?: string;
}

// Individual filter row
export interface FilterRow {
  id: string;
  fieldName: FilterFieldName | '';
  criteria: FilterCriteria | '';
  value: string | string[] | { from: string; to: string } | null;
  selectedOptions?: FilterOption[];
}

// Complete filter state
export interface FilterState {
  rows: FilterRow[];
  isActive: boolean;
  appliedFilters: FilterRow[];
}

// Filter field mappings
export const FILTER_FIELD_CONFIG: Record<FilterFieldName, FilterFieldConfig> = {
  name: {
    label: 'Name',
    availableCriteria: ['contains', 'isEqual', 'isNotEqual', 'doesNotContain', 'none'],
    valueType: 'text',
    searchable: false,
  },
  assignee: {
    label: 'Assignee',
    availableCriteria: ['contains', 'doesNotContain', 'unassigned'],
    valueType: 'multiSelect',
    apiEndpoint: '/workflow/users-and-roles',
    searchable: true,
  },
  status: {
    label: 'Status',
    availableCriteria: ['contains', 'doesNotContain'],
    valueType: 'multiSelect',
    searchable: false,
  },
  contact: {
    label: 'Contact',
    availableCriteria: ['contains', 'doesNotContain'],
    valueType: 'multiSelect',
    apiEndpoint: '/workflow/users-and-roles',
    searchable: true,
  },
  matter: {
    label: 'Matter',
    availableCriteria: ['contains', 'doesNotContain'],
    valueType: 'multiSelect',
    apiEndpoint: '/workflow/matter-list',
    searchable: true,
  },
  templates: {
    label: 'Templates',
    availableCriteria: ['contains', 'doesNotContain'],
    valueType: 'multiSelect',
    searchable: true,
  },
  dueDate: {
    label: 'Due Date',
    availableCriteria: ['before', 'after', 'between'],
    valueType: 'date',
    searchable: false,
  },
  createDate: {
    label: 'Create Date',
    availableCriteria: ['before', 'after', 'between'],
    valueType: 'date',
    searchable: false,
  },
  attorney: {
    label: 'Attorney',
    availableCriteria: ['contains', 'doesNotContain'],
    valueType: 'multiSelect',
    apiEndpoint: '/workflow/users-and-roles',
    searchable: true,
  },
};

// Criteria labels for display
export const CRITERIA_LABELS: Record<FilterCriteria, string> = {
  contains: 'Contains',
  isEqual: 'Is Equal',
  isNotEqual: 'Is Not Equal',
  doesNotContain: 'Does Not Contain',
  none: 'None',
  unassigned: 'Unassigned',
  before: 'Before',
  after: 'After',
  between: 'Between',
};

// Status options
export const STATUS_OPTIONS: FilterOption[] = [
  { value: 'onTrack', label: 'On Track' },
  { value: 'dueSoon', label: 'Due Soon' },
  { value: 'overdue', label: 'Overdue' },
  { value: 'completed', label: 'Completed' },
];

// Template options (based on requirements)
export const TEMPLATE_OPTIONS: FilterOption[] = [
  { value: 'newCourtNotice', label: 'New Court Notice' },
  { value: 'courtNoticeFollowup', label: 'Court Notice Follow-up' },
];

// Default filter for "My Workflows"
export const DEFAULT_MY_WORKFLOWS_FILTER: FilterRow = {
  id: 'default-my-workflows',
  fieldName: 'assignee',
  criteria: 'contains',
  value: [], // Will be populated with current user
  selectedOptions: [],
};

// Filter validation
export interface FilterValidation {
  isValid: boolean;
  errors: string[];
}

// Filter API parameters for backend
export interface WorkflowFilterParams {
  page?: number;
  limit?: number;
  filters?: {
    fieldName: FilterFieldName;
    criteria: FilterCriteria;
    value: string | string[] | { from: string; to: string };
  }[];
}

// Helper function to validate filter row
export const validateFilterRow = (row: FilterRow): FilterValidation => {
  const errors: string[] = [];
  
  if (!row.fieldName) {
    errors.push('Field name is required');
  }
  
  if (!row.criteria) {
    errors.push('Criteria is required');
  }
  
  if (row.criteria !== 'unassigned' && row.criteria !== 'none') {
    if (!row.value || (Array.isArray(row.value) && row.value.length === 0)) {
      errors.push('Value is required');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Helper function to create empty filter row
export const createEmptyFilterRow = (): FilterRow => ({
  id: `filter-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
  fieldName: '',
  criteria: '',
  value: null,
  selectedOptions: [],
});

// Helper function to check if criteria supports date range
export const isDateRangeCriteria = (criteria: FilterCriteria): boolean => {
  return criteria === 'between';
};

// Helper function to check if criteria supports multiple values
export const isMultiValueCriteria = (criteria: FilterCriteria): boolean => {
  return ['contains', 'doesNotContain'].includes(criteria);
};

// Helper function to get field config
export const getFieldConfig = (fieldName: FilterFieldName): FilterFieldConfig => {
  return FILTER_FIELD_CONFIG[fieldName];
};
