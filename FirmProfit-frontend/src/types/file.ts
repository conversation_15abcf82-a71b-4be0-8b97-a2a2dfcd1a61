/**
 * File Type Definitions
 * These types define the structure of file data in the application
 */

// File document types
export type DocumentType = 'PDF' | 'DOC' | 'DOCX' | 'XLS' | 'XLSX' | 'TXT' | 'IMG' | 'OTHER';

// Main File interface
export interface File {
  id: string;
  fileName: string;
  documentType: DocumentType;
  createDate: string;
  contactId: string;
  fileSize?: string;
  filePath?: string;
  uploadedBy?: string;
  description?: string;
  tags?: string[];
  isPublic?: boolean;
  lastModified?: string;
}

// File list query parameters
export interface FileListParams {
  page?: number;
  limit?: number;
  search?: string;
  documentType?: DocumentType;
  contactId?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  sortBy?: keyof File;
  sortOrder?: 'asc' | 'desc';
}

// File list response
export interface FileListResponse {
  data: File[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// File form data interface
export interface FileFormData {
  fileName: string;
  documentType: DocumentType;
  contactId: string;
  description?: string;
  tags?: string[];
  isPublic?: boolean;
} 