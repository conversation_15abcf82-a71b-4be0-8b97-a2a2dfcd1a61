/**
 * Matter Type Definitions
 * These types define the structure of matter data in the application
 */

// Matter status enum for type safety
export type MatterStatus = 'Open' | 'Closed';

// Main Matter interface
export interface Matter {
  id: string;
  matterId: string; // The matter ID column from the image
  type: string; // Matter type (<PERSON><PERSON><PERSON>, Child Custody, etc.)
  matter: string; // The matter type/description
  status: MatterStatus;
  contact: string; // Contact/client name
  attorney: string;
  description?: string; // Matter description
  court?: string; // Court name
  createdAt?: string;
  updatedAt?: string;
  notes?: string;
  syncStatus?: string;
  ex_county_of_arrest?: string;
  case_number?: string;
  
}

// Matter list query parameters
export interface MatterListParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: MatterStatus;
  sortBy?: keyof Matter;
  sortOrder?: 'asc' | 'desc';
  attorney?: string;
  contact?: string;
}

// Matter API response interface
export interface MatterListResponse {
  data: Matter[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Matter form data interface
export interface MatterFormData {
  matterId: string;
  matter: string;
  status: MatterStatus;
  contact: string;
  attorney?: string;
  notes?: string;
  description?: string;
}

// Matter filter interface
export interface MatterFilters {
  status: MatterStatus[];
  attorney: string[];
  contact: string[];
  dateRange?: {
    start: string;
    end: string;
  };
} 