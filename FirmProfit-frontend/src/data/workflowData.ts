const workflowsData = {
  workflows: [
    {
      id: '680760d0e1af8cdccbe00bfa',
      name: '<PERSON><PERSON>, SA',
      description: 'Process court notices and notify relevant parties',
      end_date: '05/03/25',
      tasks: [
        {
          _id: '680b0d2c67a775dd72ddaec0',
          id: '680b0d2c67a775dd72ddaec0',
          is_reviewed: true,
          task_visible_status: 'REVIEWED',
          name: 'Initial court notice review',
          description: 'Process court notices and notify all relevant parties',
          icon: 'mail',
          default_task: true,
          selected_task: false,
          work_flow_id: '68076174e1af8cdccbe00bfc',
          status: 'DRAFT',
          formFields: [
            {
              id: 'instruction-section-1',
              type: 'instruction',
              label: 'Verify the court notice and follow the steps ',
              status: 'DRAFT',
              sequence: 1,
              required: true,
              dynamic_fields: false,
              dynamic_selected_task: false,
              condition: null,
              fields: [
                {
                  _id: '68076794e1af8cdccbe00c0e',
                  id: '68076794e1af8cdccbe00c0e',
                  value: [
                    {
                      id: '',
                      value: '[object Object]',
                      _id: '6818d7d4d8274f7918483375',
                    },
                  ],
                  name: '',
                  label: 'Review the court notice and follow the steps.',
                  type: 'instruction',
                  placeholder: '',
                  required: true,
                  validation: {},
                  options: [],
                },
              ],
            },
            {
              id: 'assign-section',
              type: 'assign',
              label: 'Assign this workflow to',
              status: 'DRAFT',
              sequence: 2,
              required: true,
              dynamic_fields: false,
              dynamic_selected_task: false,
              condition: null,
              fields: [
                {
                  _id: '68076d77e1af8cdccbe00c16',
                  id: '68076d77e1af8cdccbe00c16',
                  value: [
                    {
                      id: '',
                      value: 'jane-doe',
                      _id: '6818d7d4d8274f7918483376',
                    },
                  ],
                  name: '',
                  label: 'Assign this workflow to',
                  type: 'select',
                  placeholder: '',
                  required: true,
                  validation: {},
                  options: [
                    {
                      _id: '6819f8114bf135576bf2155d',
                      value: 'isabelle-juarez',
                      text: 'Isabelle Juarez',
                    },
                    {
                      _id: '6819f8114bf135576bf2155e',
                      value: 'john-smith',
                      text: 'John Smith',
                    },
                    {
                      _id: '6819f8114bf135576bf2155f',
                      value: 'jane-doe',
                      text: 'Jane Doeeee',
                    },
                  ],
                },
              ],
            },
            {
              id: 'has-events-section',
              type: 'radio_button_group',
              label: 'Do you have any events in the calendar in this court notice?',
              status: 'DRAFT',
              sequence: 3,
              dynamic_fields: true,
              dynamic_selected_task: false,
              condition: null,
              fields: [
                {
                  _id: '6807762ae1af8cdccbe00c18',
                  id: '6807762ae1af8cdccbe00c18',
                  value: [
                    {
                      id: '',
                      value: 'yes',
                      _id: '6818d7d4d8274f7918483377',
                    },
                  ],
                  name: '',
                  label: 'Do you have any events in the calendar in this court notice?',
                  type: 'radio_button_group',
                  placeholder: '',
                  required: true,
                  validation: {},
                  options: [
                    {
                      _id: '6819f8114bf135576bf21560',
                      value: 'yes',
                      text: 'Yes',
                    },
                    {
                      _id: '6819f8114bf135576bf21561',
                      value: 'no',
                      text: 'No',
                    },
                  ],
                },
              ],
            },
            {
              id: 'court-notice-section',
              _id: 'court-notice-section',
              type: 'courtNotice',
              label: 'Review the court notice and ensure all the dates are correct.',
              sequence: 7,
              condition: {
                field: '6807762ae1af8cdccbe00c18',
                value: 'yes',
              },
              value: [],
              fields: [
                {
                  id: '',
                  type: 'courtNotice',
                  label: 'Review the court notice and ensure all the dates are correct.',
                  sequence: 7,
                  value: [
                    {
                      value: {
                        clients: [
                          {
                            id: 'client-1',
                            name: 'Jeffery Price',
                            matters: [
                              {
                                id: 'matter-1-1',
                                name: 'Jeffery Price (DWI 2nd-Lubbock) SA',
                                caseDescription: 'DWI 2nd-Lubbock',
                              },
                            ],
                          },
                          {
                            id: 'client-2',
                            name: 'Client 2',
                            matters: [
                              {
                                id: 'matter-2-1',
                                name: 'Client 2 (New Matter) SA',
                                caseDescription: 'New Matter',
                              },
                            ],
                          },
                          {
                            id: 'client-3',
                            name: 'Client 3',
                            matters: [
                              {
                                id: 'matter-3-1',
                                name: 'Client 3 (New Matter) SA',
                                caseDescription: 'New Matter',
                              },
                            ],
                          },
                          {
                            id: 'client-4',
                            name: 'Client 4',
                            matters: [
                              {
                                id: 'matter-4-1',
                                name: 'Client 4 (New Matter) SA',
                                caseDescription: 'New Matter',
                              },
                            ],
                          },
                          {
                            id: 'client-5',
                            name: 'Client 5',
                            matters: [
                              {
                                id: 'matter-5-1',
                                name: 'Client 5 (New Matter) SA',
                                caseDescription: 'New Matter',
                              },
                            ],
                          },
                        ],
                        events: {
                          'matter-1-1': [
                            {
                              id: 'evt-1746167331445',
                              caseNumber: 'CC-2022-CR-3458',
                              clientName: 'New Contact',
                              description:
                                'JT; New Contact (111521-dallas) CC-2022-CR-3458 Appearance Required1',
                              subject:
                                'jury_trial; New Contact (111521-) CC-2022-CR-3458 Appearance Required',
                              courtNoticeType: 'jury_trial',
                              appointmentAction: 'new',
                              charge: '111521',
                              county: '',
                              courtLocation: '123 Court Street',
                              startDate: '2025-05-02',
                              endDate: '2025-05-02',
                              date: '05/02/2025',
                              startTime: '10:00',
                              endTime: '11:00',
                              allDay: false,
                              requiredAttendees: '',
                              optionalAttendees: '',
                              clientAttendance: 'appearance_req',
                              meetingLocation: 'in_person',
                              isCompleted: false,
                            },
                            {
                              id: 'evt-1746184947273',
                              caseNumber: 'CC-2022-CR-7585',
                              clientName: 'Jeffery Price | Jeffery Price (DWI 2nd-Lubbock) SA',
                              description: 'Client MUST appear',
                              subject: 'Jeffery Price (DWI 2nd-Lubbock) SA - New Appointment',
                              courtNoticeType: 'hearing',
                              appointmentAction: 'new',
                              charge: 'DWI 2nd-Lubbock',
                              county: '',
                              courtLocation: '123 Court Street',
                              startDate: '2025-05-02',
                              endDate: '2025-05-02',
                              date: '05/02/2025',
                              startTime: '10:00',
                              endTime: '11:00',
                              allDay: false,
                              requiredAttendees: 'Jeffrey Price, Lisa Spencer',
                              optionalAttendees: 'Jeffrey Price, Lisa Spencer',
                              clientAttendance: 'appearance_req',
                              meetingLocation: 'in_person',
                              isCompleted: false,
                            },
                            {
                              id: 'evt-1746191967493',
                              caseNumber: 'CC-2022-CR-6330',
                              clientName: 'Jeffery Price | Jeffery Price (DWI 2nd-Lubbock) SA',
                              description: 'Client MUST appear',
                              subject:
                                'JT; Jeffery Price (DWI 2nd-Lubbock-lubbock) CC-2022-CR-6330',
                              courtNoticeType: 'bench_trial',
                              appointmentAction: 'new',
                              charge: 'DWI 2nd-Lubbock',
                              county: '',
                              courtLocation: '',
                              startDate: '2025-05-02',
                              endDate: '2025-05-02',
                              date: '05/02/2025',
                              startTime: '10:00',
                              endTime: '11:00',
                              allDay: false,
                              requiredAttendees: '',
                              optionalAttendees: '',
                              clientAttendance: 'appearance_req',
                              meetingLocation: 'in_person',
                              isCompleted: false,
                            },
                          ],
                          'matter-2-1': [
                            {
                              id: 'evt-1746184986056',
                              caseNumber: 'CC-2022-CR-',
                              clientName: 'Client 2 | SA | mtr-002',
                              description: 'Client MUST appear',
                              subject: 'SA | mtr-002 - New Appointment',
                              courtNoticeType: 'jury_trial',
                              appointmentAction: 'new',
                              charge: '',
                              county: '',
                              courtLocation: '',
                              startDate: '2025-05-02',
                              endDate: '2025-05-02',
                              date: '05/02/2025',
                              startTime: '10:00',
                              endTime: '11:00',
                              allDay: false,
                              requiredAttendees: '',
                              optionalAttendees: '',
                              clientAttendance: 'appearance_req',
                              meetingLocation: 'in_person',
                              isCompleted: false,
                            },
                            {
                              id: 'evt-1746450791718',
                              caseNumber: 'CC-2022-CR-',
                              clientName: 'Client 2 | Client 2 (New Matter) SA',
                              description: 'Client MUST appear',
                              subject: 'JT; Client 2 (New Matter-) CC-2022-CR-',
                              courtNoticeType: 'status_conference',
                              appointmentAction: 'new',
                              charge: 'New Matter',
                              county: '',
                              courtLocation: '',
                              startDate: '2025-05-05',
                              endDate: '2025-05-05',
                              date: '05/05/2025',
                              startTime: '10:00',
                              endTime: '11:00',
                              allDay: false,
                              requiredAttendees: '',
                              optionalAttendees: '',
                              clientAttendance: 'appearance_req',
                              meetingLocation: 'in_person',
                              isCompleted: false,
                            },
                          ],
                          'matter-3-1': [],
                          'matter-4-1': [
                            {
                              id: 'evt-1746192455462',
                              caseNumber: 'CC-2022-CR-',
                              clientName: 'Client 4 | Client 4 (New Matter) SA',
                              description: 'Client MUST appear',
                              subject: 'JT; Client 4 (New Matter-) CC-2022-CR-',
                              courtNoticeType: 'status_conference',
                              appointmentAction: 'new',
                              charge: 'New Matter',
                              county: '',
                              courtLocation: '',
                              startDate: '2025-05-02',
                              endDate: '2025-05-02',
                              date: '05/02/2025',
                              startTime: '10:00',
                              endTime: '11:00',
                              allDay: false,
                              requiredAttendees: '',
                              optionalAttendees: '',
                              clientAttendance: 'appearance_req',
                              meetingLocation: 'in_person',
                              isCompleted: false,
                            },
                            {
                              id: 'evt-1746451219262',
                              caseNumber: 'CC-2022-CR-',
                              clientName: 'Client 4 | Client 4 (New Matter) SA',
                              description: 'Client MUST appear',
                              subject: 'JT; Client 4 (New Matter-) CC-2022-CR-',
                              courtNoticeType: 'status_conference',
                              appointmentAction: 'new',
                              charge: 'New Matter',
                              county: '',
                              courtLocation: '',
                              startDate: '2025-05-05',
                              endDate: '2025-05-05',
                              date: '05/05/2025',
                              startTime: '10:00',
                              endTime: '11:00',
                              allDay: false,
                              requiredAttendees: '',
                              optionalAttendees: '',
                              clientAttendance: 'appearance_req',
                              meetingLocation: 'in_person',
                              isCompleted: false,
                            },
                            {
                              id: 'evt-1746451233221',
                              caseNumber: 'CC-2022-CR-',
                              clientName: 'Client 4 | Client 4 (New Matter) SA',
                              description: 'Client MUST appear',
                              subject: 'JT; Client 4 (New Matter-) CC-2022-CR- Appearance Required',
                              courtNoticeType: 'status_conference',
                              appointmentAction: 'new',
                              charge: 'New Matter',
                              county: '',
                              courtLocation: '',
                              startDate: '2025-05-05',
                              endDate: '2025-05-05',
                              date: '05/05/2025',
                              startTime: '10:00',
                              endTime: '11:00',
                              allDay: false,
                              requiredAttendees: '',
                              optionalAttendees: '',
                              clientAttendance: 'appearance_req',
                              meetingLocation: 'in_person',
                              isCompleted: false,
                            },
                          ],
                          'matter-5-1': [
                            {
                              id: 'evt-1746191986762',
                              caseNumber: 'CC-2022-CR-',
                              clientName: 'Client 5 | Jeffery Price | mtr-001',
                              description: 'Client MUST appear',
                              subject: 'JT; Client 5 (-) CC-2022-CR-',
                              courtNoticeType: 'status_conference',
                              appointmentAction: 'new',
                              charge: '',
                              county: '',
                              courtLocation: '',
                              startDate: '2025-05-02',
                              endDate: '2025-05-02',
                              date: '05/02/2025',
                              startTime: '10:00',
                              endTime: '11:00',
                              allDay: false,
                              requiredAttendees: '',
                              optionalAttendees: '',
                              clientAttendance: 'appearance_req',
                              meetingLocation: 'in_person',
                              isCompleted: false,
                            },
                          ],
                        },
                      },
                    },
                  ],
                },
              ],
            },
            {
              id: 'confirm-dates-section',
              type: 'checkbox',
              label: 'Confirm that the date above are correct.',
              status: 'DRAFT',
              sequence: 8,
              dynamic_fields: false,
              dynamic_selected_task: false,
              condition: {
                field: '6807762ae1af8cdccbe00c18',
                value: 'yes',
              },
              fields: [
                {
                  _id: '680778a9e1af8cdccbe00c1e',
                  id: '680778a9e1af8cdccbe00c1e',
                  value: [
                    {
                      id: '',
                      value: '',
                      _id: '6818d7d4d8274f7918483378',
                    },
                  ],
                  name: '',
                  label: 'Confirmed',
                  type: 'checkbox',
                  placeholder: '',
                  required: false,
                  validation: {},
                  options: [],
                },
              ],
            },
          ],
        },
        {
          _id: '681069395be8fd7123867ec8',
          id: '681069395be8fd7123867ec8',
          is_reviewed: true,
          task_visible_status: 'IN_REVIEW',
          name: 'Secondary court notice review',
          description: 'Process court notices and notify all relevant parties',
          icon: 'mail',
          default_task: true,
          selected_task: false,
          work_flow_id: '68076174e1af8cdccbe00bfc',
          status: 'DRAFT',
          formFields: [
            {
              id: 'instruction-section-1',
              type: 'instruction',
              label: 'Verify the court notice and follow the steps ',
              status: 'DRAFT',
              sequence: 1,
              required: true,
              dynamic_fields: false,
              dynamic_selected_task: false,
              condition: null,
              fields: [
                {
                  _id: '68076794e1af8cdccbe00c0e',
                  id: '68076794e1af8cdccbe00c0e',
                  value: [
                    {
                      id: '',
                      value: '[object Object]',
                      _id: '6818d7d4d8274f7918483375',
                    },
                  ],
                  name: '',
                  label: 'Review the court notice and follow the steps.',
                  type: 'instruction',
                  placeholder: '',
                  required: true,
                  validation: {},
                  options: [],
                },
              ],
            },
            {
              id: 'assign-section',
              type: 'assign',
              label: 'Assign this workflow to',
              status: 'DRAFT',
              sequence: 2,
              required: true,
              dynamic_fields: false,
              dynamic_selected_task: false,
              condition: null,
              fields: [
                {
                  _id: '68076d77e1af8cdccbe00c16',
                  id: '68076d77e1af8cdccbe00c16',
                  value: [
                    {
                      id: '',
                      value: 'jane-doe',
                      _id: '6818d7d4d8274f7918483376',
                    },
                  ],
                  name: '',
                  label: 'Assign this workflow to',
                  type: 'select',
                  placeholder: '',
                  required: true,
                  validation: {},
                  options: [
                    {
                      _id: '6819f8114bf135576bf21579',
                      value: 'isabelle-juarez',
                      text: 'Isabelle Juarez',
                    },
                    {
                      _id: '6819f8114bf135576bf2157a',
                      value: 'john-smith',
                      text: 'John Smith',
                    },
                    {
                      _id: '6819f8114bf135576bf2157b',
                      value: 'jane-doe',
                      text: 'Jane Doe',
                    },
                  ],
                },
              ],
            },
            {
              id: 'has-events-section',
              type: 'radio_button_group',
              label: 'Do you have any events in the calendar in this court notice?',
              status: 'DRAFT',
              sequence: 3,
              dynamic_fields: true,
              dynamic_selected_task: false,
              condition: null,
              fields: [
                {
                  _id: '6807762ae1af8cdccbe00c18',
                  id: '6807762ae1af8cdccbe00c18',
                  value: [
                    {
                      id: '',
                      value: 'yes',
                      _id: '6818d7d4d8274f7918483377',
                    },
                  ],
                  name: '',
                  label: 'Do you have any events in the calendar in this court notice?',
                  type: 'radio_button_group',
                  placeholder: '',
                  required: true,
                  validation: {},
                  options: [
                    {
                      _id: '6819f8114bf135576bf2157c',
                      value: 'yes',
                      text: 'Yes',
                    },
                    {
                      _id: '6819f8114bf135576bf2157d',
                      value: 'no',
                      text: 'No',
                    },
                  ],
                },
              ],
            },
            {
              id: 'court-notice-section',
              _id: 'court-notice-section',
              type: 'courtNotice',
              label: 'Review the court notice and ensure all the dates are correct.',
              sequence: 7,
              condition: {
                field: '6807762ae1af8cdccbe00c18',
                value: 'yes',
              },
              value: [],
              fields: [
                {
                  id: '',
                  type: 'courtNotice',
                  label: 'Review the court notice and ensure all the dates are correct.',
                  sequence: 7,
                  value: [
                    {
                      value: {
                        clients: [
                          {
                            id: 'client-1',
                            name: 'Jeffery Price',
                            matters: [
                              {
                                id: 'matter-1-1',
                                name: 'Jeffery Price (DWI 2nd-Lubbock) SA',
                                caseDescription: 'DWI 2nd-Lubbock',
                              },
                            ],
                          },
                          {
                            id: 'client-2',
                            name: 'Client 2',
                            matters: [
                              {
                                id: 'matter-2-1',
                                name: 'Client 2 (New Matter) SA',
                                caseDescription: 'New Matter',
                              },
                            ],
                          },
                          {
                            id: 'client-3',
                            name: 'Client 3',
                            matters: [
                              {
                                id: 'matter-3-1',
                                name: 'Client 3 (New Matter) SA',
                                caseDescription: 'New Matter',
                              },
                            ],
                          },
                          {
                            id: 'client-4',
                            name: 'Client 4',
                            matters: [
                              {
                                id: 'matter-4-1',
                                name: 'Client 4 (New Matter) SA',
                                caseDescription: 'New Matter',
                              },
                            ],
                          },
                          {
                            id: 'client-5',
                            name: 'Client 5',
                            matters: [
                              {
                                id: 'matter-5-1',
                                name: 'Client 5 (New Matter) SA',
                                caseDescription: 'New Matter',
                              },
                            ],
                          },
                        ],
                        events: {
                          'matter-1-1': [
                            {
                              id: 'evt-1746167331445',
                              caseNumber: 'CC-2022-CR-3458',
                              clientName: 'New Contact',
                              description:
                                'JT; New Contact (111521-dallas) CC-2022-CR-3458 Appearance Required1',
                              subject:
                                'jury_trial; New Contact (111521-) CC-2022-CR-3458 Appearance Required',
                              courtNoticeType: 'jury_trial',
                              appointmentAction: 'new',
                              charge: '111521',
                              county: '',
                              courtLocation: '123 Court Street',
                              startDate: '2025-05-02',
                              endDate: '2025-05-02',
                              date: '05/02/2025',
                              startTime: '10:00',
                              endTime: '11:00',
                              allDay: false,
                              requiredAttendees: '',
                              optionalAttendees: '',
                              clientAttendance: 'appearance_req',
                              meetingLocation: 'in_person',
                              isCompleted: false,
                            },
                            {
                              id: 'evt-1746184947273',
                              caseNumber: 'CC-2022-CR-7585',
                              clientName: 'Jeffery Price | Jeffery Price (DWI 2nd-Lubbock) SA',
                              description: 'Client MUST appear',
                              subject: 'Jeffery Price (DWI 2nd-Lubbock) SA - New Appointment',
                              courtNoticeType: 'hearing',
                              appointmentAction: 'new',
                              charge: 'DWI 2nd-Lubbock',
                              county: '',
                              courtLocation: '123 Court Street',
                              startDate: '2025-05-02',
                              endDate: '2025-05-02',
                              date: '05/02/2025',
                              startTime: '10:00',
                              endTime: '11:00',
                              allDay: false,
                              requiredAttendees: 'Jeffrey Price, Lisa Spencer',
                              optionalAttendees: 'Jeffrey Price, Lisa Spencer',
                              clientAttendance: 'appearance_req',
                              meetingLocation: 'in_person',
                              isCompleted: false,
                            },
                            {
                              id: 'evt-1746191967493',
                              caseNumber: 'CC-2022-CR-6330',
                              clientName: 'Jeffery Price | Jeffery Price (DWI 2nd-Lubbock) SA',
                              description: 'Client MUST appear',
                              subject:
                                'JT; Jeffery Price (DWI 2nd-Lubbock-lubbock) CC-2022-CR-6330',
                              courtNoticeType: 'bench_trial',
                              appointmentAction: 'new',
                              charge: 'DWI 2nd-Lubbock',
                              county: '',
                              courtLocation: '',
                              startDate: '2025-05-02',
                              endDate: '2025-05-02',
                              date: '05/02/2025',
                              startTime: '10:00',
                              endTime: '11:00',
                              allDay: false,
                              requiredAttendees: '',
                              optionalAttendees: '',
                              clientAttendance: 'appearance_req',
                              meetingLocation: 'in_person',
                              isCompleted: false,
                            },
                          ],
                          'matter-2-1': [
                            {
                              id: 'evt-1746184986056',
                              caseNumber: 'CC-2022-CR-',
                              clientName: 'Client 2 | SA | mtr-002',
                              description: 'Client MUST appear',
                              subject: 'SA | mtr-002 - New Appointment',
                              courtNoticeType: 'jury_trial',
                              appointmentAction: 'new',
                              charge: '',
                              county: '',
                              courtLocation: '',
                              startDate: '2025-05-02',
                              endDate: '2025-05-02',
                              date: '05/02/2025',
                              startTime: '10:00',
                              endTime: '11:00',
                              allDay: false,
                              requiredAttendees: '',
                              optionalAttendees: '',
                              clientAttendance: 'appearance_req',
                              meetingLocation: 'in_person',
                              isCompleted: false,
                            },
                            {
                              id: 'evt-1746450791718',
                              caseNumber: 'CC-2022-CR-',
                              clientName: 'Client 2 | Client 2 (New Matter) SA',
                              description: 'Client MUST appear',
                              subject: 'JT; Client 2 (New Matter-) CC-2022-CR-',
                              courtNoticeType: 'status_conference',
                              appointmentAction: 'new',
                              charge: 'New Matter',
                              county: '',
                              courtLocation: '',
                              startDate: '2025-05-05',
                              endDate: '2025-05-05',
                              date: '05/05/2025',
                              startTime: '10:00',
                              endTime: '11:00',
                              allDay: false,
                              requiredAttendees: '',
                              optionalAttendees: '',
                              clientAttendance: 'appearance_req',
                              meetingLocation: 'in_person',
                              isCompleted: false,
                            },
                          ],
                          'matter-3-1': [],
                          'matter-4-1': [
                            {
                              id: 'evt-1746192455462',
                              caseNumber: 'CC-2022-CR-',
                              clientName: 'Client 4 | Client 4 (New Matter) SA',
                              description: 'Client MUST appear',
                              subject: 'JT; Client 4 (New Matter-) CC-2022-CR-',
                              courtNoticeType: 'status_conference',
                              appointmentAction: 'new',
                              charge: 'New Matter',
                              county: '',
                              courtLocation: '',
                              startDate: '2025-05-02',
                              endDate: '2025-05-02',
                              date: '05/02/2025',
                              startTime: '10:00',
                              endTime: '11:00',
                              allDay: false,
                              requiredAttendees: '',
                              optionalAttendees: '',
                              clientAttendance: 'appearance_req',
                              meetingLocation: 'in_person',
                              isCompleted: false,
                            },
                            {
                              id: 'evt-1746451219262',
                              caseNumber: 'CC-2022-CR-',
                              clientName: 'Client 4 | Client 4 (New Matter) SA',
                              description: 'Client MUST appear',
                              subject: 'JT; Client 4 (New Matter-) CC-2022-CR-',
                              courtNoticeType: 'status_conference',
                              appointmentAction: 'new',
                              charge: 'New Matter',
                              county: '',
                              courtLocation: '',
                              startDate: '2025-05-05',
                              endDate: '2025-05-05',
                              date: '05/05/2025',
                              startTime: '10:00',
                              endTime: '11:00',
                              allDay: false,
                              requiredAttendees: '',
                              optionalAttendees: '',
                              clientAttendance: 'appearance_req',
                              meetingLocation: 'in_person',
                              isCompleted: false,
                            },
                            {
                              id: 'evt-1746451233221',
                              caseNumber: 'CC-2022-CR-',
                              clientName: 'Client 4 | Client 4 (New Matter) SA',
                              description: 'Client MUST appear',
                              subject: 'JT; Client 4 (New Matter-) CC-2022-CR- Appearance Required',
                              courtNoticeType: 'status_conference',
                              appointmentAction: 'new',
                              charge: 'New Matter',
                              county: '',
                              courtLocation: '',
                              startDate: '2025-05-05',
                              endDate: '2025-05-05',
                              date: '05/05/2025',
                              startTime: '10:00',
                              endTime: '11:00',
                              allDay: false,
                              requiredAttendees: '',
                              optionalAttendees: '',
                              clientAttendance: 'appearance_req',
                              meetingLocation: 'in_person',
                              isCompleted: false,
                            },
                          ],
                          'matter-5-1': [
                            {
                              id: 'evt-1746191986762',
                              caseNumber: 'CC-2022-CR-',
                              clientName: 'Client 5 | Jeffery Price | mtr-001',
                              description: 'Client MUST appear',
                              subject: 'JT; Client 5 (-) CC-2022-CR-',
                              courtNoticeType: 'status_conference',
                              appointmentAction: 'new',
                              charge: '',
                              county: '',
                              courtLocation: '',
                              startDate: '2025-05-02',
                              endDate: '2025-05-02',
                              date: '05/02/2025',
                              startTime: '10:00',
                              endTime: '11:00',
                              allDay: false,
                              requiredAttendees: '',
                              optionalAttendees: '',
                              clientAttendance: 'appearance_req',
                              meetingLocation: 'in_person',
                              isCompleted: false,
                            },
                          ],
                        },
                      },
                    },
                  ],
                },
              ],
            },
            {
              id: 'confirm-dates-section',
              type: 'checkbox',
              label: 'Confirm that the date above are correct.',
              status: 'DRAFT',
              sequence: 8,
              dynamic_fields: false,
              dynamic_selected_task: false,
              condition: {
                field: '6807762ae1af8cdccbe00c18',
                value: 'yes',
              },
              fields: [
                {
                  _id: '680778a9e1af8cdccbe00c1e',
                  id: '680778a9e1af8cdccbe00c1e',
                  value: [
                    {
                      id: '',
                      value: '',
                      _id: '6818d7d4d8274f7918483378',
                    },
                  ],
                  name: '',
                  label: 'Confirmed',
                  type: 'checkbox',
                  placeholder: '',
                  required: false,
                  validation: {},
                  options: [],
                },
              ],
            },
          ],
        },
      ],
    },
  ],
  child_workflow_1: [
    {
      id: '68199d6414e3e2beab51686a',
      name: 'Court Notice Followup - Multiple Clients',
      description: 'court notice followup',
      end_date: '05/07/25',
      tasks: [
        {
          _id: '68199d6414e3e2beab516863',
          id: '68199d6414e3e2beab516863',
          is_reviewed: false,
          task_visible_status: 'IN_REVIEW',
          name: 'Notify Jeffery Price',
          description: '',
          icon: '',
          default_task: true,
          selected_task: false,
          work_flow_id: '68199d6414e3e2beab51686d',
          status: 'DRAFT',
          formFields: [
            {
              id: 'assign-section',
              type: 'assign',
              label: 'Assign this workflow to',
              status: 'DRAFT',
              sequence: 1,
              dynamic_fields: false,
              dynamic_selected_task: false,
              condition: null,
              fields: [],
            },
            {
              id: 'confirm-dates-section',
              type: 'checkbox',
              label: 'Please confirm that the events above are updated appropriately',
              status: 'DRAFT',
              sequence: 3,
              dynamic_fields: false,
              dynamic_selected_task: false,
              condition: null,
              fields: [],
            },
            {
              id: 'has-events-section',
              type: 'radio_button_group',
              label: 'Please confirm that the events above are updated appropriately',
              status: 'DRAFT',
              sequence: 4,
              dynamic_fields: false,
              dynamic_selected_task: false,
              condition: null,
              fields: [],
            },
            {
              id: 'client-notes-section',
              type: 'text_area',
              label: 'Enter notes below if any',
              status: 'DRAFT',
              sequence: 5,
              dynamic_fields: false,
              dynamic_selected_task: false,
              condition: null,
              fields: [],
            },
            {
              id: 'confirm-sms-section',
              type: 'radio_button_group',
              label: 'I confirm that SMS was sent',
              status: 'DRAFT',
              sequence: 6,
              dynamic_fields: false,
              dynamic_selected_task: false,
              condition: null,
              fields: [],
            },
            {
              id: 'client-notes-section',
              type: 'text_area',
              label: 'Enter notes below if any',
              status: 'DRAFT',
              sequence: 7,
              dynamic_fields: false,
              dynamic_selected_task: false,
              condition: null,
              fields: [],
            },
            {
              id: 'court-notice-section',
              _id: 'court-notice-section',
              type: 'courtNotice',
              label: 'Review the court notice and ensure all the dates are correct.',
              sequence: 7,
              condition: null,
              value: [],
              fields: [
                {
                  id: '',
                  type: 'courtNotice',
                  label: 'Review the court notice and ensure all the dates are correct.',
                  sequence: 7,
                  value: [
                    {
                      value: {
                        clients: [
                          {
                            id: 'client-1',
                            name: 'Jeffery Price',
                            matters: [
                              {
                                id: 'matter-1-1',
                                name: 'Jeffery Price (New Matter) SA',
                                caseDescription: 'New Matter',
                              },
                            ],
                          },
                        ],
                        events: {
                          'matter-1-1': [
                            {
                              id: 'evt-1746509036009',
                              caseNumber: '',
                              clientName: 'Jeffery Price | Jeffery Price (New Matter) SA',
                              description:
                                'UPDATED009: JT; Jeffery Price (DWI 2nd-Lubbock) CC-2022-CR-2275 CD appearance TBD',
                              subject: '',
                              courtNoticeType: 'Hearing',
                              appointmentAction: 'new',
                              charge: '',
                              county: '',
                              courtLocation: '',
                              startDate: '2025-05-06',
                              endDate: '2025-05-06',
                              date: '5/6/2025',
                              startTime: '09:00',
                              endTime: '10:00',
                              allDay: false,
                              requiredAttendees: '',
                              optionalAttendees: '',
                              clientAttendance: 'appearance_req',
                              meetingLocation: 'in_person',
                              isCompleted: false,
                            },
                          ],
                        },
                      },
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
  ],
  child_workflow_2: [
    {
      id: '68199d6414e3e2beab51686a',
      name: 'Court Notice Followup - Multiple Clients',
      description: 'court notice followup',
      end_date: '05/07/25',
      tasks: [
        {
          _id: '68199d6414e3e2beab516866',
          id: '68199d6414e3e2beab516866',
          is_reviewed: false,
          task_visible_status: 'IN_REVIEW',
          name: 'Notify SA',
          description: '',
          icon: '',
          default_task: true,
          selected_task: false,
          work_flow_id: '68199d6414e3e2beab516880',
          status: 'DRAFT',
          formFields: [
            {
              id: 'assign-section',
              type: 'assign',
              label: 'Assign this workflow to',
              status: 'DRAFT',
              sequence: 1,
              dynamic_fields: false,
              dynamic_selected_task: false,
              condition: null,
              fields: [],
            },
            {
              id: 'confirm-dates-section',
              type: 'checkbox',
              label: 'Please confirm that the events above are updated appropriately',
              status: 'DRAFT',
              sequence: 3,
              dynamic_fields: false,
              dynamic_selected_task: false,
              condition: null,
              fields: [],
            },
            {
              id: 'has-events-section',
              type: 'radio_button_group',
              label: 'Please confirm that the events above are updated appropriately',
              status: 'DRAFT',
              sequence: 4,
              dynamic_fields: false,
              dynamic_selected_task: false,
              condition: null,
              fields: [],
            },
            {
              id: 'client-notes-section',
              type: 'text_area',
              label: 'Enter notes below if any',
              status: 'DRAFT',
              sequence: 5,
              dynamic_fields: false,
              dynamic_selected_task: false,
              condition: null,
              fields: [],
            },
            {
              id: 'confirm-sms-section',
              type: 'radio_button_group',
              label: 'I confirm that SMS was sent',
              status: 'DRAFT',
              sequence: 6,
              dynamic_fields: false,
              dynamic_selected_task: false,
              condition: null,
              fields: [],
            },
            {
              id: 'client-notes-section',
              type: 'text_area',
              label: 'Enter notes below if any',
              status: 'DRAFT',
              sequence: 7,
              dynamic_fields: false,
              dynamic_selected_task: false,
              condition: null,
              fields: [],
            },
            {
              id: 'court-notice-section',
              _id: 'court-notice-section',
              type: 'courtNotice',
              label: 'Review the court notice and ensure all the dates are correct.',
              sequence: 7,
              condition: null,
              value: [],
              fields: [
                {
                  id: '',
                  type: 'courtNotice',
                  label: 'Review the court notice and ensure all the dates are correct.',
                  sequence: 7,
                  value: [
                    {
                      value: {
                        clients: [
                          {
                            id: 'client-1',
                            name: 'SA',
                            matters: [
                              {
                                id: 'matter-1-1',
                                name: 'SA (New Matter) SA',
                                caseDescription: 'New Matter',
                              },
                            ],
                          },
                        ],
                        events: {
                          'matter-1-1': [
                            {
                              id: 'evt-1746509116211',
                              caseNumber: '',
                              clientName: 'SA | SA (New Matter) SA',
                              description:
                                'UPDATED009: JT; Jeffery Price (DWI 2nd-Lubbock) CC-2022-CR-2275 CD appearance TBD',
                              subject: '',
                              courtNoticeType: 'Hearing',
                              appointmentAction: 'new',
                              charge: '',
                              county: '',
                              courtLocation: '',
                              startDate: '2025-05-06',
                              endDate: '2025-05-06',
                              date: '5/6/2025',
                              startTime: '09:00',
                              endTime: '10:00',
                              allDay: false,
                              requiredAttendees: '',
                              optionalAttendees: '',
                              clientAttendance: 'appearance_req',
                              meetingLocation: 'in_person',
                              isCompleted: false,
                            },
                          ],
                        },
                      },
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
  ],
  options: {
    courtNoticeTypes: [
      { value: '', text: 'Select a type' },
      { value: 'jury_trial', text: 'Jury trial (J)' },
      { value: 'bench_trial', text: 'Bench trial (B)' },
      { value: 'hearing', text: 'Hearing (H)' },
    ],
    appointmentActions: [
      { value: '', text: 'Select an action' },
      { value: 'new', text: 'New' },
      { value: 'reschedule', text: 'Reschedule' },
      { value: 'cancel', text: 'Cancel' },
    ],
    counties: [
      { value: '', text: 'Select a county' },
      { value: 'lubbock', text: 'Lubbock' },
      { value: 'dallas', text: 'Dallas' },
      { value: 'houston', text: 'Houston' },
    ],
    courtLocations: [
      { value: '', text: 'Select a location' },
      { value: '123 Court Street', text: '123 Court Street' },
      { value: '456 Justice Avenue', text: '456 Justice Avenue' },
    ],
    attendees: [
      { value: '', text: 'Select attendees' },
      { value: 'Jeffrey Price, Lisa Spencer', text: 'Jeffrey Price, Lisa Spencer' },
      { value: 'Amanda Burke, John Doe', text: 'Amanda Burke, John Doe' },
      { value: 'Michael Johnson, Sarah Williams', text: 'Michael Johnson, Sarah Williams' },
    ],
    clientAttendanceOptions: [
      {
        value: '',
        text: 'Select option',
      },
      {
        value: 'client_must_appearance',
        text: 'Client must appear',
      },
      {
        value: 'Appearance_not_required',
        text: 'Appearance not required',
      },
      {
        value: 'Appearance_tbd',
        text: 'Appearance TBD',
      },
    ],
    meetingLocations: [
      {
        value: '',
        text: 'Select location',
      },
      {
        value: 'virtual_meeting_link',
        text: 'Virtual meeting link - Link TB',
      },
      {
        value: 'in_person',
        text: 'Phone',
      },
      {
        value: 'phone_tbd',
        text: 'Phone - Phone TBD',
      },
      {
        value: 'meeting_in_person',
        text: 'Meeting in person',
      },
      {
        value: 'meeting_in_person_location_tbd',
        text: 'Meeting in dhyey',
      },
    ],
     optionalAttendees: [
      { value: '', text: 'Select attendees' },
      { value: 'Jeffrey Price, Lisa Spencer', text: 'Jeffrey Price, Lisa Spencer' },
      { value: 'Amanda Burke, John Doe', text: 'Amanda Burke, John Doe' },
      { value: 'Michael Johnson, Sarah Williams', text: 'Michael Johnson, Sarah Williams' },
    ],
  },
};

export default workflowsData;
