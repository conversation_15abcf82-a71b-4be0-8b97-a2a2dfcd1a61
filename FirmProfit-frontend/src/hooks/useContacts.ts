/**
 * useContacts Hook
 * Custom hook for contact management with state, pagination, and API integration
 */

import { useState, useEffect, useCallback } from 'react';
import { Contact, ContactFilters } from '@/types/contact';
// import { ContactService } from '@/services/contactService';

interface UseContactsOptions {
  initialPage?: number;
  initialLimit?: number;
  autoLoad?: boolean;
}

interface UseContactsResult {
  // Data
  contacts: Contact[];
  selectedContacts: string[];
  filteredContacts: Contact[];
  
  // Pagination
  currentPage: number;
  totalPages: number;
  totalContacts: number;
  limit: number;
  
  // Loading and error states
  loading: boolean;
  error: string | null;
  
  // Search and filters
  searchTerm: string;
  filters: ContactFilters;
  
  // Actions
  loadContacts: () => Promise<void>;
  refreshContacts: () => Promise<void>;
  selectContact: (id: string, selected: boolean) => void;
  selectAllContacts: (selected: boolean) => void;
  clearSelection: () => void;
  setSearchTerm: (term: string) => void;
  setFilters: (filters: Partial<ContactFilters>) => void;
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
  
  // CRUD operations
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  createContact: (contactData: any) => Promise<Contact | null>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  updateContact: (id: string, contactData: any) => Promise<Contact | null>;
  deleteContact: (id: string) => Promise<boolean>;
  bulkDeleteContacts: (ids: string[]) => Promise<boolean>;
  
  // Utility
  isAllSelected: boolean;
  hasSelection: boolean;
}

export const useContacts = (options: UseContactsOptions = {}): UseContactsResult => {
  const {
    initialPage = 1,
    initialLimit = 50,
    autoLoad = true,
  } = options;

  // Core state
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [selectedContacts, setSelectedContacts] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [totalPages, setTotalPages] = useState(0);
  const [totalContacts, setTotalContacts] = useState(0);
  const [limit, setLimit] = useState(initialLimit);
  
  // Search and filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<ContactFilters>({
    status: [],
    type: [],
    attorney: [],
  });

  // Filtered contacts based on search and filters
  const filteredContacts = contacts.filter(contact => {
    // Search filter
    const matchesSearch = !searchTerm || 
      contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contact.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contact.mobile.includes(searchTerm) ||
      contact.attorney.toLowerCase().includes(searchTerm.toLowerCase());

    // Status filter
    const matchesStatus = filters.status.length === 0 || 
      filters.status.includes(contact.status);

    // Type filter
    const matchesType = filters.type.length === 0 || 
      filters.type.includes(contact.type);

    // Attorney filter
    const matchesAttorney = filters.attorney.length === 0 || 
      filters.attorney.includes(contact.attorney);

    return matchesSearch && matchesStatus && matchesType && matchesAttorney;
  });

  // Computed values
  const isAllSelected = selectedContacts.length === filteredContacts.length && 
    filteredContacts.length > 0;
  const hasSelection = selectedContacts.length > 0;

  // Load contacts from API
  const loadContacts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // const params: ContactListParams = {
      //   page: currentPage,
      //   limit,
      //   search: searchTerm || undefined,
      //   ...(filters.status.length > 0 && { status: filters.status[0] }),
      //   ...(filters.type.length > 0 && { type: filters.type[0] }),
      // };

      // For demo purposes, use mock data instead of API
      // In production, use: const response = await ContactService.getContacts(params);
      const mockContacts: Contact[] = [
        {
          id: '1',
          name: 'Amanda Burke',
          status: 'Active',
          type: 'Client',
          email: '<EMAIL>',
          mobile: '(*************',
          attorney: 'Grace Lee',
          syncStatus: 'Synced',
        },
        {
          id: '2',
          name: 'Blake Donovan',
          status: 'Active',
          type: 'Client',
          email: '<EMAIL>',
          mobile: '(*************',
          attorney: 'Mark Smith',
          syncStatus: 'Manual',
        },
        {
          id: '3',
          name: 'Gia Clarkson',
          status: 'Closed',
          type: 'Client',
          email: '<EMAIL>',
          mobile: '(*************',
          attorney: 'J. Bell',
          syncStatus: 'Synced',
        },
        {
          id: '4',
          name: 'Jane Donovan',
          status: 'Active',
          type: 'Lead',
          email: '<EMAIL>',
          mobile: '(*************',
          attorney: '',
          syncStatus: 'Synced',
        },
        {
          id: '5',
          name: 'John Donte',
          status: 'Closed',
          type: 'Client',
          email: '<EMAIL>',
          mobile: '(*************',
          attorney: 'K. Melendez',
          syncStatus: 'Manual',
        },
        {
          id: '6',
          name: 'Lucas Butler',
          status: 'Closed',
          type: 'Client',
          email: '<EMAIL>',
          mobile: '(*************',
          attorney: 'T. Fredrick',
          syncStatus: 'Manual',
        },
        {
          id: '7',
          name: 'Lucas Clark',
          status: 'Active',
          type: 'Lead',
          email: '<EMAIL>',
          mobile: '(*************',
          attorney: '',
          syncStatus: 'Synced',
        },
      ];

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      setContacts(mockContacts);
      setTotalContacts(mockContacts.length);
      setTotalPages(Math.ceil(mockContacts.length / limit));

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load contacts');
      console.error('Error loading contacts:', err);
    } finally {
      setLoading(false);
    }
  }, [currentPage, limit, searchTerm, filters]);

  // Refresh contacts
  const refreshContacts = useCallback(async () => {
    await loadContacts();
  }, [loadContacts]);

  // Contact selection handlers
  const selectContact = useCallback((id: string, selected: boolean) => {
    if (selected) {
      setSelectedContacts(prev => [...prev, id]);
    } else {
      setSelectedContacts(prev => prev.filter(contactId => contactId !== id));
    }
  }, []);

  const selectAllContacts = useCallback((selected: boolean) => {
    if (selected) {
      setSelectedContacts(filteredContacts.map(contact => contact.id));
    } else {
      setSelectedContacts([]);
    }
  }, [filteredContacts]);

  const clearSelection = useCallback(() => {
    setSelectedContacts([]);
  }, []);

  // CRUD operations
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const createContact = useCallback(async (contactData: any): Promise<Contact | null> => {
    try {
      setLoading(true);
      console.log('createContact',contactData);
      // const newContact = await ContactService.createContact(contactData);
      // setContacts(prev => [newContact, ...prev]);
      // await refreshContacts();
      // return newContact;
      return null; // Demo placeholder
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create contact');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const updateContact = useCallback(async (id: string, contactData: any): Promise<Contact | null> => {
    try {
      setLoading(true);
      console.log('updateContact',id,contactData);
      // const updatedContact = await ContactService.updateContact(id, contactData);
      // setContacts(prev => prev.map(contact => 
      //   contact.id === id ? updatedContact : contact
      // ));
      // return updatedContact;
      return null; // Demo placeholder
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update contact');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteContact = useCallback(async (id: string): Promise<boolean> => {
    try {
      setLoading(true);
      // await ContactService.deleteContact(id);
      setContacts(prev => prev.filter(contact => contact.id !== id));
      setSelectedContacts(prev => prev.filter(contactId => contactId !== id));
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete contact');
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const bulkDeleteContacts = useCallback(async (ids: string[]): Promise<boolean> => {
    try {
      setLoading(true);
      // await ContactService.bulkDeleteContacts(ids);
      setContacts(prev => prev.filter(contact => !ids.includes(contact.id)));
      setSelectedContacts(prev => prev.filter(contactId => !ids.includes(contactId)));
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete contacts');
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  // Filter handlers
  const handleSetFilters = useCallback((newFilters: Partial<ContactFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1); // Reset to first page when filtering
  }, []);

  // Pagination handlers
  const setPage = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  const handleSetLimit = useCallback((newLimit: number) => {
    setLimit(newLimit);
    setCurrentPage(1); // Reset to first page when changing limit
  }, []);

  // Auto-load on mount and dependency changes
  useEffect(() => {
    if (autoLoad) {
      loadContacts();
    }
  }, [loadContacts, autoLoad]);

  // Reset page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  return {
    // Data
    contacts,
    selectedContacts,
    filteredContacts,
    
    // Pagination
    currentPage,
    totalPages,
    totalContacts,
    limit,
    
    // Loading and error states
    loading,
    error,
    
    // Search and filters
    searchTerm,
    filters,
    
    // Actions
    loadContacts,
    refreshContacts,
    selectContact,
    selectAllContacts,
    clearSelection,
    setSearchTerm,
    setFilters: handleSetFilters,
    setPage,
    setLimit: handleSetLimit,
    
    // CRUD operations
    createContact,
    updateContact,
    deleteContact,
    bulkDeleteContacts,
    
    // Utility
    isAllSelected,
    hasSelection,
  };
}; 