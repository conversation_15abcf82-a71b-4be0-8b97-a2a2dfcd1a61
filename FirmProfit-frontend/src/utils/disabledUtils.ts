/**
 * Disabled Styling Utilities
 * Provides centralized disabled styling functions for consistent UI
 */

import { conditionalDisabled } from '@/styles/workflow';

/**
 * Get disabled classes for input fields
 * @param isDisabled - Whether the field is disabled
 * @param enabledClasses - Classes to apply when enabled (optional)
 * @returns Disabled or enabled classes
 */
export const getDisabledInputClasses = (isDisabled: boolean, enabledClasses = '') => {
  return conditionalDisabled(isDisabled, 'input', enabledClasses);
};

/**
 * Get disabled classes for buttons
 * @param isDisabled - Whether the button is disabled
 * @param enabledClasses - Classes to apply when enabled (optional)
 * @returns Disabled or enabled classes
 */
export const getDisabledButtonClasses = (isDisabled: boolean, enabledClasses = 'cursor-pointer') => {
  return conditionalDisabled(isDisabled, 'button', enabledClasses);
};

/**
 * Get disabled classes for icons
 * @param isDisabled - Whether the icon should appear disabled
 * @param enabledClasses - Classes to apply when enabled (optional)
 * @returns Disabled or enabled classes
 */
export const getDisabledIconClasses = (isDisabled: boolean, enabledClasses = '') => {
  return conditionalDisabled(isDisabled, 'icon', enabledClasses);
};

/**
 * Get disabled classes for dropdown options
 * @param isDisabled - Whether the dropdown option is disabled
 * @param enabledClasses - Classes to apply when enabled (optional)
 * @returns Disabled or enabled classes
 */
export const getDisabledDropdownClasses = (isDisabled: boolean, enabledClasses = 'hover:bg-gray-100 cursor-pointer') => {
  return conditionalDisabled(isDisabled, 'dropdown', enabledClasses);
};

/**
 * Check if a field should be disabled based on common patterns
 * @param isDisabled - Base disabled state
 * @param isUpdateMyCase - Whether we're in update MyCase mode
 * @param fieldLabel - Field label to check for client/matter keywords
 * @param fieldPlaceholder - Field placeholder to check for client/matter keywords
 * @returns Whether the field should be disabled
 */
export const shouldFieldBeDisabled = (
  isDisabled: boolean = false,
  isUpdateMyCase: boolean = false,
  fieldLabel?: string,
  fieldPlaceholder?: string
): boolean => {
  const isClientMatterField = 
    fieldLabel?.toLowerCase().includes('client') || 
    fieldLabel?.toLowerCase().includes('matter') ||
    fieldPlaceholder?.toLowerCase().includes('client') ||
    fieldPlaceholder?.toLowerCase().includes('matter');
    
  return Boolean(isDisabled || (isUpdateMyCase && isClientMatterField));
};

/**
 * Constants for commonly used disabled colors
 */
export const DISABLED_COLORS = {
  TEXT: '#C7D1DF',
  BORDER: '#DCE2EB',
  BACKGROUND: '#F3F5F9',
  ICON: '#C7D1DF',
} as const; 