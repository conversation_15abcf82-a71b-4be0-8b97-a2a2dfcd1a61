import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { UserState } from '@/redux/types';

const initialState: UserState = {
  name: 'Guest User',
  avatar: '/avatars/default.png',
  isLoggedIn: false,
};

export const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<Partial<UserState>>) => {
      return { ...state, ...action.payload };
    },
    logoutUser: state => {
      state.isLoggedIn = false;
      state.name = 'Guest User';
    },
  },
});

export const { setUser, logoutUser } = userSlice.actions;
export default userSlice.reducer;
