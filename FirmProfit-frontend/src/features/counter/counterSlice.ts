import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { CounterState, RootState } from '@/redux/types';

const initialState: CounterState = {
  value: 0,
  status: 'idle',
  error: null,
};

// Example async thunk
export const incrementAsync = createAsyncThunk('counter/incrementAsync', async (amount: number) => {
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 1000));
  return amount;
});

export const counterSlice = createSlice({
  name: 'counter',
  initialState,
  reducers: {
    increment: state => {
      state.value += 1;
    },
    decrement: state => {
      state.value -= 1;
    },
    incrementByAmount: (state, action: PayloadAction<number>) => {
      state.value += action.payload;
    },
    reset: state => {
      state.value = 0;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(incrementAsync.pending, state => {
        state.status = 'loading';
      })
      .addCase(incrementAsync.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.value += action.payload;
      })
      .addCase(incrementAsync.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message || 'Something went wrong';
      });
  },
});

export const { increment, decrement, incrementByAmount, reset } = counterSlice.actions;

// Selectors
export const selectCount = (state: RootState) => state.counter.value;
export const selectStatus = (state: RootState) => state.counter.status;
export const selectError = (state: RootState) => state.counter.error;

export default counterSlice.reducer;
