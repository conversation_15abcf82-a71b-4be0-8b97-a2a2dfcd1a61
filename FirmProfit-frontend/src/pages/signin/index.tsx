import React, { useState , useEffect} from 'react';
import Head from 'next/head';
import Link from 'next/link';
import Image from 'next/image';
import Logo from '../../../public/logo.svg'; // Adjust the path as necessary
import axios from 'axios';
import { useRouter } from 'next/router';
import { Poppins } from 'next/font/google';
import { AxiosError } from 'axios';
import toast from 'react-hot-toast';
import { Button, Form, Input } from '@/components';

// Define types for better type safety
interface ApiErrorResponse {
  message: string;
  statusCode?: number;
}

interface LoginResponse {
  data: {
    user: {
      id: string;
      email: string;
      isMFAEnable: boolean;
      // eslint-disable-next-line
      [key: string]: any;
    };
  };
  access_token: string;
  mfaRequired?: boolean;
  userId?: string;
}

const poppins = Poppins({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
});

const API_URL = process.env.NEXT_PUBLIC_BASE_URL || '';

export default function SignIn() {
  // Form state
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  // Auth state
  // eslint-disable-next-line
  const [failedAttempts, setFailedAttempts] = useState(0);
  const [isAccountLocked, setIsAccountLocked] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [timezone, setTimezone] = useState('');

  const router = useRouter();

  // Validation patterns
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  /**
   * Validates the email field
   * @returns boolean indicating if email is valid
   */
  const validateEmail = (): boolean => {
    if (!email) {
      toast.error('Please enter your email address and password to continue.');
      return false;
    }
    if (!emailRegex.test(email)) {
      toast.error('Please enter a valid email address');
      return false;
    }
    return true;
  };

    useEffect(() => {
    const detectedTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    console.log(detectedTimezone, "detectedTimezone")
    setTimezone(detectedTimezone);
  }, []);

  /**
   * Handles the login form submission
   */
  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email && !password) {
      toast.error('Please enter an email address and password');
      return;
    }

    if (!email) {
      // setEmailError('Email is required');
      toast.error('Please enter an email address');

      return true;
    }

    if (!password) {
      // setEmailError('Email is required');
      toast.error('Please enter a password');

      return true;
    }

    if (!emailRegex.test(email)) {
      toast.error('Please enter a valid email address');
      return;
    }

    // Validate form inputs
    const isEmailValid = validateEmail();

    if (!isEmailValid) {
      return;
    }

    // Check for account lock
    if (isAccountLocked) {
      toast.error(
        'Your account is temporarily locked due to multiple failed sign in attempts. Check your email for instructions to unlock your account.'
      );
      return;
    }

    setIsLoading(true);

    try {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const response: any = await axios.post<LoginResponse>(`${API_URL}/auth/login`, {
        email,
        password,
        timezone
      });

      // Save user data to localStorage
      const userPayload = JSON.stringify(response.data.data.user);
      const token = JSON.stringify(response.data.data.access_token);
      localStorage.setItem('user', userPayload);
      localStorage.setItem('timezone', timezone);
      localStorage.setItem('authToken', token);

      // Handle MFA if required
      if (response.data.mfaRequired) {
        router.push({
          pathname: '/auth/verifymfa',
          query: { userId: response.data.userId },
        });
      } else {
        router.push('/auth/verifymfa');
      }

      // Reset failed attempts on successful login
      setFailedAttempts(0);
    } catch (error) {
      setIsLoading(false);
      handleLoginError(error);
    }
  };

  /**
   * Handles API errors during login
   */
  const handleLoginError = (error: unknown) => {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError<ApiErrorResponse>;
      const { response } = axiosError;

      if (!response) {
        toast.error('Network error. Please try again.');
        return;
      }

      const { status, data } = response;
      const errorMessage = data?.message || 'An unexpected error occurred';

      // Handle specific status codes
      switch (status) {
        case 409:
          router.push('/auth/accountlocked');
          return;

        case 403:
          setIsAccountLocked(true);
          toast.error(
            errorMessage || 'Your account is temporarily locked. Check your email for instructions.'
          );
          break;

        case 500:
          toast.error('Server error. Please try again later.');
          break;

        default:
          toast.error(errorMessage);
      }
    } else {
      toast.error('Network error. Please try again.');
    }
  };

  return (
    <>
      <Head>
        <title>Sign in to FirmProfit</title>
        <meta name="description" content="Sign in to your FirmProfit account" />
      </Head>

      <div
        className={`flex min-h-screen bg-white flex-col font-poppins md:flex-row ${poppins.className}`}
      >
        {/* Left Section - Form */}
        <div className="w-full md:w-1/2 flex flex-col px-6 py-12 lg:px-8">
          {/* Logo in top left */}
          <div className="absolute top-[40px] left-[40px]">
            <Image src={Logo} alt="FirmProfit Logo" width={169} height={40} />
          </div>
          <div className="flex flex-col items-center justify-center flex-1">
            <div className="sm:mx-auto max-w-md sm:w-full sm:max-w-md">
              <h2 className="mt-6 text-center text-[24px] leading-[36px] font-medium text-[#25282C]">
                Sign in to FirmProfit
              </h2>
              <p className="mt-2 text-center font-normal text-[14px] leading-[20px] text-[#5F6F84]">
                Enter your credentials to login to your account
              </p>
            </div>
            <div className="mt-8 max-w-md sm:mx-auto sm:w-full sm:max-w-md">
              {/* Social Sign-in Buttons */}
              <div className="space-y-3 mb-4">
                <Button
                  variant="secondary"
                  fullWidth
                  data-testid="google-signin"
                  icon={
                    <svg width="20" height="20" xmlns="/images/google.svg" viewBox="0 0 24 24">
                      <path
                        fill="#4285F4"
                        d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                      />
                      <path
                        fill="#34A853"
                        d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                      />
                      <path
                        fill="#FBBC05"
                        d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                      />
                      <path
                        fill="#EA4335"
                        d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                      />
                    </svg>
                  }
                  iconPosition="left"
                >
                  Sign in with Google
                </Button>

                <Button
                  variant="secondary"
                  fullWidth
                  data-testid="microsoft-signin"
                  icon={
                    <Image
                      src="/images/Microsoft.svg"
                      alt="Microsoft Logo"
                      width={18}
                      height={18}
                      className="h-[18px] w-[18px]"
                    />
                  }
                  iconPosition="left"
                >
                  Sign in with Microsoft
                </Button>
              </div>

              {/* Divider */}
              <div className="relative my-4">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-[#DCE2EB]" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="bg-white font-normal text-[14px] px-4 py-4 text-[#A2AFC2]">
                    Or
                  </span>
                </div>
              </div>

              <Form onSubmit={handleSignIn} className="space-y-4" data-testid="signin-form">
                <Input
                  id="email"
                  label="Email*"
                  autoComplete="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={e => setEmail(e.target.value.trim())}
                  data-testid="email-input"
                />

                <div>
                  <div className="flex items-center justify-between">
                    <label
                      htmlFor="password"
                      className="block text-sm font-medium leading-6 text-[#2A2E34]"
                    >
                      Password*
                    </label>
                    <div className="">
                      <Link
                        href="/forgot-password"
                        className="font-medium text-[#3F73F6] text-[14px] leading-[20px] hover:text-blue-500"
                        data-testid="forgot-password-link"
                        tabIndex={-1}
                      >
                        Forgot password?
                      </Link>
                    </div>
                  </div>

                  <Input
                    id="password"
                    type="password"
                    autoComplete="current-password"
                    placeholder="Enter your password"
                    value={password}
                    onChange={e => setPassword(e.target.value)}
                    showPasswordToggle
                    data-testid="password-input"
                  />
                </div>

                <Button
                  type="submit"
                  variant="primary"
                  fullWidth
                  isLoading={isLoading}
                  disabled={isLoading}
                  className="mt-4"
                  data-testid="signin-button"
                >
                  Sign in
                </Button>
              </Form>

              <p className="mt-6 text-center text-[14px] leading-[20px] font-normal text-[#5F6F84]">
                <span className="text-[14px] leading-[20px] font-normal text-[#5F6F84]">
                  Don&apos;t have an account?{' '}
                </span>
                <Link
                  href="/signup"
                  className="font-medium leading-6 ml-[1px] text-[#3F73F6] hover:text-blue-500"
                  data-testid="signup-link"
                >
                  Sign up
                </Link>
              </p>
            </div>
          </div>
        </div>

        {/* { right section } */}


        <div className="hidden md:flex md:w-1/2 bg-[#3F73F6] relative overflow-hidden">
        <div className="absolute inset-0 opacity-50 bg-[url('/background_image.jpg')] bg-cover bg-center mix-blend-multiply  scale-[1.1] origin-center"></div>

          <div className="flex items-center justify-center h-full w-full relative z-10 ">
            <div className="w-full max-w-[658px]  flex flex-col align-items-start">
             
              <h1 className="text-white font-poppins text-[48px] font-medium leading-[1.1] ml-8 ">
                <div className="flex flex-col">
                  <span>AI-powered workflow</span>
                  <span>automation</span>
                </div>
              </h1>

              
              <p className="text-white font-poppins text-[20px] font-medium leading-[28px] mt-2 text-left mb-10 ml-8">
                Effortlessly manage your operation with FirmProfitAI.
              </p>

            
              <div className="w-full overflow-hidden "> 
                <img
                  src="/Sign up image.svg"
                  alt="AI workflow"
                  className="w-full max-w-[658px] h-auto  border-red-600"
              
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
