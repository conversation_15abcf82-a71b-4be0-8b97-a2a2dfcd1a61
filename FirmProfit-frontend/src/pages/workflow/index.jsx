import React from 'react';
import Head from 'next/head';
import Layout from '@/components/layout/Layout';
import ContactForm from '@/components/workflows/workflowRun';

/**
 * Workflow component that renders the dynamic form based on JSON structure
 * @returns {React.ReactElement} - The rendered workflow
 */
const Workflow = () => {
  return (
    <>
      <Head>
        <title>Workflow - FirmProfit</title>
        <meta name="description" content="Manage and run workflows in FirmProfit" />
      </Head>
      <Layout>
        <div className="workflow-container">
          <ContactForm />
        </div>
      </Layout>
    </>
  );
};

export default Workflow;
