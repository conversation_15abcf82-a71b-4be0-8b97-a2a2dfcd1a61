import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { Client } from '@/types/client';
import { Matter } from '@/types/matter';
import { Event } from '@/types/event';
import { ContactWorkflow } from '@/types/workflow';
import { File } from '@/types/file';
import { ClientService } from '@/services/clientService';
import { MatterService } from '@/services/matterService';
import { EventService } from '@/services/eventService';
import { WorkflowService } from '@/services/workflowService';
import { FileService } from '@/services/fileService';
import {
  DetailPageLayout,
  InformationCard,
  MattersTable,
  EventsTable,
  WorkflowsTable,
  FilesTable
} from '@/components/common/DetailPage';
import { clientFieldConfig } from '@/config/entityFieldConfigs';

const ClientDetailPage: React.FC = () => {
  const router = useRouter();
  const { id } = router.query;

  // State management
  const [client, setClient] = useState<Client | null>(null);
  const [matters, setMatters] = useState<Matter[]>([]);
  const [events, setEvents] = useState<Event[]>([]);
  const [workflows, setWorkflows] = useState<ContactWorkflow[]>([]);
  const [files, setFiles] = useState<File[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('about');

  // Load data on mount
  useEffect(() => {
    if (id && typeof id === 'string') {
      loadData(id);
    }
  }, [id]);

  const loadData = async (clientId: string) => {
    try {
      setLoading(true);
      setError(null);

      // Load client, matters, events, workflows, and files in parallel
      const [clientData, mattersData, eventsData, workflowsData, filesData] = await Promise.all([
        ClientService.getClient(clientId),
        MatterService.getMattersByContact(clientId),
        EventService.getEventsByContact(clientId),
        WorkflowService.getWorkflowsByContact(clientId),
        FileService.getFilesByContact(clientId)
      ]);

      setClient(clientData);
      setMatters(mattersData);
      setEvents(eventsData);
      setWorkflows(workflowsData);
      setFiles(filesData);
    } catch (err) {
      setError('Failed to load client details');
      console.error('Error loading client:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleWorkflowClick = (workflow: ContactWorkflow) => {
    // Navigate to workflow detail page or handle workflow click
    console.log('Workflow clicked:', workflow);
    // You can add navigation logic here, e.g.:
    // router.push(`/workflowrun?taskId=${workflow.id}&work_flow_id=${workflow.id}`);
  };

  const handleFileClick = (file: File) => {
    // Handle file click (e.g., preview or download)
    console.log('File clicked:', file);
    // You can add file preview logic here
  };

  const handleFileDownload = async (file: File) => {
    try {
      // Handle file download
      console.log('Downloading file:', file.fileName);
      // You can add actual download logic here
    } catch (error) {
      console.error('Error downloading file:', error);
    }
  };

  const renderTabContent = () => {
    if (!client) return null;
    switch (activeTab) {
      case 'about':
        return (
          <InformationCard
            title="Client Information"
            data={client}
            fields={clientFieldConfig}
          />
        );

      case 'matters':
        return (
          <MattersTable
            matters={matters}
            title="Matters"
            emptyMessage="No matters found for this client."
          />
        );

      case 'events':
        return (
          <EventsTable
            events={events}
            title="Events"
            emptyMessage="No events found for this client."
          />
        );

      case 'notes':
        return (
          <div className="bg-white">
            <h3 className="text-[16px] font-semibold text-[#2A2E34] mb-4">Notes</h3>
            <p className="text-[14px] text-[#2A2E34]">{client.notes || 'No notes available.'}</p>
          </div>
        );

      case 'workflows':
        return (
          <WorkflowsTable 
            workflows={workflows}
            title="Workflows"
            emptyMessage="No workflows found for this client."
            onWorkflowClick={handleWorkflowClick}
          />
        );

      case 'files':
        return (
          <FilesTable 
            files={files}
            title="Files"
            emptyMessage="No files found for this client."
            onFileClick={handleFileClick}
            onDownload={handleFileDownload}
          />
        );

      default:
        return (
          <div className="bg-white">
            <h3 className="text-[16px] font-semibold text-[#2A2E34] mb-4">
              {activeTab.charAt(0).toUpperCase() + activeTab.slice(1).replace('-', ' ')}
            </h3>
            <p className="text-[14px] text-[#5F6F84]">
              Content for {activeTab} will be implemented soon.
            </p>
          </div>
        );
    }
  };

  return (
    <>
      <Head>
        <title>{client ? `${client.name} - Client Details` : 'Client Details'} - FirmProfit</title>
        <meta name="description" content={`View details for ${client?.name || 'client'} in FirmProfit`} />
      </Head>
      <DetailPageLayout
        entity={client}
        loading={loading}
        error={error}
        activeTab={activeTab}
        onTabChange={setActiveTab}
        backUrl="/clients"
        backLabel="Back to Clients"
        entityName="Client"
        statusField="type"
      >
        {renderTabContent()}
      </DetailPageLayout>
    </>
  );
};

export default ClientDetailPage; 