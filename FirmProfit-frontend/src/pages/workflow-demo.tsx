import React, { useEffect } from 'react';
import Head from 'next/head';
import ContactForm from '@/components/workflows/workflowRun';
import { useRouter } from 'next/router';

export default function WorkflowDemo() {
  const router = useRouter();

  // Set workflow ID in query params on load
  useEffect(() => {
    if (router.isReady && !router.query.work_flow_id) {
      router.push(
        {
          pathname: router.pathname,
          query: { ...router.query, work_flow_id: 'court-notice-workflow' },
        },
        undefined,
        { shallow: true }
      );
    }
  }, [router.isReady]);

  return (
    <>
      <Head>
        <title>Workflow Demo - FirmProfit</title>
        <meta name="description" content="Workflow demonstration for FirmProfit" />
      </Head>
      <div className="flex h-screen w-screen overflow-hidden">
        <ContactForm />
      </div>
    </>
  );
}
