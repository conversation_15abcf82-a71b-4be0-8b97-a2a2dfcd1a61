import React, { useState } from 'react';
import Layout from '@/components/layout/Layout';
import WorkflowSidebar from '@/components/workflows/sidebar/WorkflowSidebar';
import { Poppins } from 'next/font/google';
import Link from 'next/link';
import {
  DataTable,
  PageHeader,
  StatusBadge,
  // Avatar,
  Column,
} from '@/components/common';
import Image from 'next/image';
import Head from 'next/head';

const poppins = Poppins({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
});

interface CourtNoticeItem {
  id: string;
  workflowRun: string;
  started: string;
  due: string;
  status: 'On Track' | 'Delayed' | 'Completed';
  activity: string;
  assignee: string;
  notes?: string;
  work_flow_runner: string;
  start_date: string;
}

// type StatusType = 'On Track' | 'Delayed' | 'Completed';

const CourtNoticePage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [courtNotices] = useState<CourtNoticeItem[]>([
    {
      id: '1',
      workflowRun: 'Jeffery Price & Amanda Burke',
      started: '04/11/25',
      due: '04/12/25',
      status: 'On Track',
      activity: '10 mins ago',
      assignee: '',
      notes: '',
      work_flow_runner: 'Court Notice',
      start_date: '04/11/25',
    },
  ]);
  const [selectedNotices, setSelectedNotices] = useState<string[]>([]);

  const handleSelectNotice = (id: string, selected: boolean) => {
    if (selected) {
      setSelectedNotices(prev => [...prev, id]);
    } else {
      setSelectedNotices(prev => prev.filter(noticeId => noticeId !== id));
    }
  };

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedNotices(courtNotices.map(notice => notice.id));
    } else {
      setSelectedNotices([]);
    }
  };

  const handleFilter = () => {
    console.log('Filter button clicked');
  };

  const handleColumns = () => {
    console.log('Columns button clicked');
  };

  const handleSaveView = () => {
    console.log('Save view button clicked');
  };

  const columns: Column[] = [
    {
      id: 'workflowRun',
      header: 'Workflow Run',
      cell: (row: CourtNoticeItem) => (
        <Link href={`/court-notice/${row.id}`} className="text-blue-600 hover:underline">
          {row.work_flow_runner}
        </Link>
      ),
    },
    {
      id: 'started',
      header: 'Started',
      cell: (row: CourtNoticeItem) => <div className="text-sm text-gray-500">{row.start_date}</div>,
    },
    {
      id: 'due',
      header: 'Due',
      cell: (row: CourtNoticeItem) => <div className="text-sm text-gray-500">{row.due}</div>,
    },
    {
      id: 'status',
      header: 'Status',
      cell: (row: CourtNoticeItem) => <StatusBadge status={row.status} />,
    },
    {
      id: 'activity',
      header: 'Activity',
      cell: (row: CourtNoticeItem) => <div className="text-sm text-gray-500">{row.activity}</div>,
    },
    {
      id: 'assignee',
      header: 'Assignee',
      cell: (row: CourtNoticeItem) =>
        row.assignee && <Image src="/images/human.png" alt="Assignee" width={32} height={32} />,
    },
    {
      id: 'notes',
      header: 'Notes',
      cell: (row: CourtNoticeItem) => <div className="text-sm text-gray-500">{row.notes}</div>,
    },
  ];

  return (
    <Layout>
      <Head>
        <title>Court Notice - FirmProfit</title>
        <meta name="description" content="Manage court notices in FirmProfit" />
      </Head>
      <div className={`flex ml-0 h-full overflow-hidden ${poppins.className}`}>
        <WorkflowSidebar className="flex-shrink-0" />
        <div className="flex-1 overflow-auto">
          <div className="p-6">
            <PageHeader
              title="Court Notice"
              searchValue={searchTerm}
              onSearchChange={setSearchTerm}
              onFilter={handleFilter}
              onColumns={handleColumns}
              onSaveView={handleSaveView}
            />

            <DataTable
              columns={columns}
              data={courtNotices}
              selectedIds={selectedNotices}
              onSelectRow={handleSelectNotice}
              onSelectAll={handleSelectAll}
              isAllSelected={
                selectedNotices.length === courtNotices.length && courtNotices.length > 0
              }
              className="mt-4"
            />
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default CourtNoticePage;
