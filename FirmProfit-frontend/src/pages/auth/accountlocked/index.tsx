import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Head from 'next/head';
import { LockClosedIcon } from '@heroicons/react/24/outline';
import { Poppins } from 'next/font/google';
import { Button } from '@/components';

const poppins = Poppins({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
});

const AccountLockedPage = () => {
  return (
    <>
      <Head>
        <meta name="description" content="Account locked in FirmProfit" />
        <title>Account Locked | FirmProfit</title>
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="icon" href="/favicon.png" type="image/png" />
      </Head>
      <div className={`flex min-h-screen bg-white ${poppins.className} md:flex-row`}>
        {/* Left Section - Form  background: #2A2E34;
         */}
        <div className="flex flex-1 flex-col px-6 py-12 lg:px-8 justify-center items-center">
          <div className="absolute top-[40px] left-[40px]">
            <Image src="../logo.svg" alt="FirmProfit Logo" width={169} height={40} />
          </div>
          <div className="w-full max-w-md">
            <div className="text-center">
              <div className="flex justify-center mb-4">
                <LockClosedIcon className="h-12 w-12 text-[#3F73F6]" aria-hidden="true" />
              </div>
              <h2 className="text-2xl font-medium text-[#2A2E34] mb-4">Account Locked</h2>
              <p className="text-[#5F6F84] leading-[20px] font-normal text-[14px] mb-6">
                Your account is temporarily locked due to multiple failed sign in attempts. Check
                your email for instructions to unlock your account.
              </p>
              <div className="flex justify-center">
                <Link href="/signin" className="w-full font-medium">
                  <Button variant="primary" fullWidth>
                    Back to Sign in
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Right Section - Blue Background */}
        <div className="hidden md:block md:flex-1 bg-[#3F73F6]">
          {/* Visible only on md screens and above */}
        </div>
      </div>
    </>
  );
};

export default AccountLockedPage;
