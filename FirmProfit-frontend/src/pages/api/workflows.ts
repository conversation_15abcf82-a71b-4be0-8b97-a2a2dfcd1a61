import { NextApiRequest, NextApiResponse } from 'next';

// Types for API responses
interface WorkflowApiResponse {
  data: {
    workflows: Array<{
      id: string;
      name: string;
      description: string;
      tasks: Array<{
        id: string;
        name: string;
        description: string;
        icon: string;
        status: string;
        formFields: Array<{
          id: string;
          type: string;
          title: string;
          fields: Array<{
            type: string;
            placeholder: string;
            id: string;
            label: string;
            options?: Array<{
              value: string;
              text: string;
            }>;
          }>;
        }>;
      }>;
    }>;
  };
  statusCode: number;
  message: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { work_flow_id, view_id } = req.query;

    // Construct the API URL
    let apiUrl = `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/workflow-render`;

    // Add query parameters if they exist
    if (work_flow_id) {
      apiUrl += `?work_flow_id=${work_flow_id}`;

      if (view_id) {
        apiUrl += `&view_id=${view_id}`;
      }
    }

    // In development mode, we'll use a mock response to avoid CORS issues
    // or if the backend service isn't running
    if (process.env.NODE_ENV === 'development' && !process.env.USE_REAL_API) {
      // Mock response based on the example provided
      const mockResponse: WorkflowApiResponse = {
        data: {
          workflows: [
            {
              tasks: [
                {
                  formFields: [
                    {
                      fields: [
                        {
                          options: [],
                          type: 'text',
                          placeholder: 'Enter full name',
                          id: '',
                          label: 'test1',
                        },
                        {
                          options: [],
                          type: 'text',
                          placeholder: 'First name',
                          id: '',
                          label: '',
                        },
                        {
                          options: [],
                          type: 'text',
                          placeholder: 'Middle name',
                          id: '',
                          label: '',
                        },
                        {
                          options: [],
                          type: 'text',
                          placeholder: 'Last name',
                          id: '',
                          label: '',
                        },
                      ],
                      id: 'client-name-section',
                      type: 'fullName',
                      title: 'What is a full name of this client?',
                    },
                    {
                      fields: [
                        {
                          options: [],
                          type: 'email',
                          placeholder: 'Enter email address',
                          id: '',
                          label: '',
                        },
                        {
                          options: [],
                          type: 'checkbox',
                          placeholder: '',
                          id: '',
                          label: 'Opt-in to receive email',
                        },
                      ],
                      id: 'client-email-section',
                      type: 'email',
                      title: 'Enter primary email address',
                    },
                    {
                      fields: [
                        {
                          options: [],
                          type: 'tel',
                          placeholder: 'Enter phone number',
                          id: '',
                          label: '',
                        },
                        {
                          options: [],
                          type: 'checkbox',
                          placeholder: '',
                          id: '',
                          label: 'Opt-in to receive email',
                        },
                      ],
                      id: 'client-phone-section',
                      type: 'tel',
                      title: 'Enter primary phone number',
                    },
                    {
                      fields: [
                        {
                          options: [
                            {
                              value: '',
                              text: 'Select',
                            },
                            {
                              value: 'yes',
                              text: 'Yes',
                            },
                            {
                              value: 'no',
                              text: 'No',
                            },
                            {
                              value: 'voicemail',
                              text: 'Left voicemail',
                            },
                          ],
                          type: 'select',
                          placeholder: 'Was your call answered?',
                          id: '',
                          label: '',
                        },
                      ],
                      id: 'call-section',
                      type: 'call',
                      title: 'Call contact',
                    },
                    {
                      fields: [
                        {
                          options: [],
                          type: 'textarea',
                          placeholder: 'Enter your note here',
                          id: '',
                          label: 'Opt-in to receive messages',
                        },
                      ],
                      id: 'notes-section',
                      type: 'notes',
                      title: 'Enter notes below',
                    },
                    {
                      fields: [
                        {
                          options: [
                            {
                              value: '',
                              text: 'Select',
                            },
                            {
                              value: 'potential-client',
                              text: 'Potential Client',
                            },
                            {
                              value: 'current-client',
                              text: 'Current Client',
                            },
                            {
                              value: 'past-client',
                              text: 'Past Client',
                            },
                            {
                              value: 'vendor',
                              text: 'Vendor',
                            },
                            {
                              value: 'partner',
                              text: 'Partner',
                            },
                            {
                              value: 'other',
                              text: 'Other',
                            },
                          ],
                          type: 'select',
                          placeholder: 'Select contact type',
                          id: '',
                          label: 'Contact Type',
                        },
                      ],
                      id: 'contact-type-section',
                      type: 'contact-type',
                      title: 'Set contact type',
                    },
                  ],
                  id: '1',
                  name: 'Set contact type',
                  description: 'Set the type of contact for this lead',
                  icon: 'user',
                  status: '',
                },
                {
                  formFields: [
                    {
                      fields: [
                        {
                          options: [],
                          type: 'textarea',
                          placeholder: 'Describe the conflict of interest',
                          id: '',
                          label: '',
                        },
                      ],
                      id: 'conflict-details',
                      type: 'notes',
                      title: 'Conflict details',
                    },
                  ],
                  id: '2',
                  name: 'Perform a conflict of interest check',
                  description: 'Check for any conflicts of interest',
                  icon: 'alert-triangle',
                  status: '',
                },
              ],
              id: '1',
              name: 'sales-inbound',
              description: 'This is a description for template 1',
            },
          ],
        },
        statusCode: 200,
        message: 'Nest application successfully started',
      };

      return res.status(200).json(mockResponse);
    }

    // Make request to the actual backend API
    const response = await fetch(apiUrl);

    if (!response.ok) {
      throw new Error(`Failed to fetch from API: ${response.status}`);
    }

    const data = await response.json();
    return res.status(200).json(data);
  } catch (error) {
    console.error('API Error:', error);
    return res.status(500).json({
      statusCode: 500,
      message: 'Failed to fetch workflow data',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
