# FirmProfit Frontend

This project has been refactored to follow best industry practices for scalability and maintainability.

## Key Improvements

### 1. Component Architecture

- **Reusable Components**: Created centralized, reusable components for common UI elements:
  - `Button`: A versatile button component with support for various styles, loading states, and icons.
  - `Form`: A reusable form component for consistent form handling.
  - `Input`: A standardized input component with built-in password toggle, error handling, and styling.

### 2. Code Organization

- **Component Structure**: Components are organized in dedicated folders with proper index files for easy importing.
- **Type Safety**: Improved TypeScript typings throughout the application.
- **Consistent Styling**: Standardized styling using Tailwind CSS with a consistent design language.

### 3. Performance Optimizations

- **Reduced Duplication**: Eliminated duplicated code by centralizing shared components.
- **Simplified Components**: Reduced component complexity by breaking down larger components.

### 4. Maintainability Improvements

- **Documentation**: Added JSDoc comments for better code documentation.
- **Consistent Error Handling**: Standardized approach to form validation and error messaging.
- **Cleaner Code**: Removed unnecessary comments and simplified logic.

## Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── Button/           # Button component
│   ├── Form/             # Form components
│   ├── RouteGuard.tsx    # Authentication guard
│   └── index.ts          # Central export of components
├── pages/                # Next.js pages
├── styles/               # Global styles
├── redux/                # State management
└── utils/                # Utility functions
```

## Getting Started

1. Install dependencies:

```bash
npm install
# or
yarn install
```

2. Run the development server:

```bash
npm run dev
# or
yarn dev
```

3. Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

# FirmProfit Workflow System

## Overview

The FirmProfit Workflow System is a dynamic, data-driven workflow interface that allows law firms to manage client interactions, court notices, and other business processes through configurable workflows.

## Key Features

- Dynamic form rendering based on JSON data
- Conditional fields that appear based on user selections
- Court notice management system
- Resizable interface with persistent user preferences
- Task-based workflow management

## Architecture

### Core Components

1. **`workflowRun.tsx`** - The main container component that handles:

   - Workflow data loading and management
   - Task selection and navigation
   - Form data state management
   - UI layout with resizable panels

2. **`WorkflowField.tsx`** - Renders individual form sections based on their type:

   - Handles different field types (text, select, checkbox, etc.)
   - Manages field validation
   - Provides consistent styling across field types

3. **`CourtNoticeField.tsx`** - Specialized component for managing court notices:
   - Displays client matters and associated events
   - Provides CRUD operations for events
   - Handles data formatting for API interactions

### Data Structure

Workflows are defined in JSON format with the following structure:

```typescript
interface Workflow {
  id: string;
  name: string;
  description: string;
  tasks: Task[];
}

interface Task {
  id: string;
  name: string;
  description: string;
  icon: string;
  status: string;
  formFields: FormSection[];
}

interface FormSection {
  id?: string;
  _id?: string;
  type: string;
  title: string;
  fields: FormField[];
  condition?: Condition;
  dynamic_fields?: boolean;
  required?: boolean;
}

interface FormField {
  type: string;
  placeholder?: string;
  id: string;
  _id: string;
  label?: string;
  value?: string | boolean | number | Array<{ id: string; value: any; _id?: string }>;
  options?: Array<{ value: string; text: string }>;
  required?: boolean;
  validation?: Validation;
}
```

## Supported Field Types

The system supports various field types defined in `fieldTypes`:

- Text (`text`)
- Email (`email`)
- Phone (`tel`)
- Textarea (`textarea`)
- Checkbox (`checkbox`)
- Select (`select`)
- Radio (`radio`)
- Date (`date`)
- Time (`time`)
- Number (`number`)
- Full Name (`fullName`)
- Contact Type (`contactType`)
- Notes (`notes`)
- Court Notice (`courtNotice`)
- Instruction (`instruction`)
- Radio Button Group (`radio_button_group`)
- Button (`button`)
- Success Message (`success_message`)
- Confirmation (`confirmation`)
- Assignment (`assign`)

## Adding New Field Types

To add a new field type:

1. Add the type to `fieldTypes` object in `src/styles/workflow.ts`
2. Add styles for the new field type in `workflowStyles`
3. Implement rendering logic in the `renderField` function in `WorkflowField.tsx`
4. Update the `getSectionIcon` function to provide an appropriate icon

Example:

```typescript
// 1. Add to fieldTypes
export const fieldTypes = {
  // ... existing types
  NEW_TYPE: 'new_type',
};

// 2. Add styles
export const workflowStyles = {
  // ... existing styles
  newTypeContainer: "p-3 bg-purple-50 rounded-lg",
};

// 3. Implement rendering in WorkflowField.tsx
case fieldTypes.NEW_TYPE:
  return (
    <div key={fieldId} className="mb-3">
      <div className={workflowStyles.newTypeContainer}>
        {field.label}
      </div>
    </div>
  );

// 4. Update icon selection
const getSectionIcon = () => {
  switch (section.type) {
    // ... existing cases
    case fieldTypes.NEW_TYPE:
      return <YourIcon className="text-gray-500 mr-2" size={20} />;
    // ...
  }
};
```

## Conditional Logic

Form sections and fields can be shown or hidden based on conditions:

```typescript
// Show a section when case category is "vehicle"
{
  "condition": {
    "field": "case_category_field_id",
    "value": "vehicle"
  }
}

// Show a section with complex conditions (AND)
{
  "condition": {
    "type": "AND",
    "conditions": [
      { "field": "field1", "value": true },
      { "field": "field2", "value": "option1" }
    ]
  }
}
```

## Setup & Development

1. **Installation**:

   ```bash
   npm install
   ```

2. **Development**:

   ```bash
   npm run dev
   ```

3. **Demo page**:
   Navigate to `/workflow-demo` to see a sample workflow.

## Best Practices

1. **Type Safety**: Always use proper TypeScript types for all components and data structures.
2. **Error Handling**: Implement proper error handling for API calls and data parsing.
3. **Performance**: Use memoization for expensive renders and avoid unnecessary re-renders.
4. **Accessibility**: Ensure all form elements have proper labels and ARIA attributes.

## Troubleshooting

### Common Issues

1. **Field not updating**:

   - Check that the field ID matches the data model
   - Ensure the `onInputChange` handler is properly passed

2. **Conditional fields not showing**:

   - Verify condition path matches the form data structure
   - Check that field values match exactly, including data types

3. **Type errors**:
   - Ensure all imports are correctly typed
   - Use explicit typing for function parameters and state

## Disabled Styling System

The application includes a centralized disabled styling system for consistent UI behavior across all form elements and buttons.

### Color Scheme

- **Text Color**: `#C7D1DF` - For disabled text and icons
- **Border Color**: `#DCE2EB` - For disabled field borders  
- **Background Color**: `#F3F5F9` - For disabled field backgrounds

### Usage

#### Import the utilities:

```typescript
import { 
  getDisabledInputClasses, 
  getDisabledButtonClasses, 
  getDisabledIconClasses,
  shouldFieldBeDisabled,
  DISABLED_COLORS 
} from '@/utils/disabledUtils';
import { conditionalDisabled } from '@/styles/workflow';
```

#### For Input Fields:

```typescript
const isFieldDisabled = shouldFieldBeDisabled(isDisabled, isUpdateMyCase, field.label, field.placeholder);

<input 
  className={`base-input-class ${getDisabledInputClasses(isFieldDisabled)}`}
  disabled={isFieldDisabled}
/>
```

#### For Buttons:

```typescript
<button 
  className={`base-button-class ${getDisabledButtonClasses(shouldDisable)}`}
  disabled={shouldDisable}
>
  Button Text
</button>
```

#### For Icons:

```typescript
<Icon 
  className={`base-icon-class ${getDisabledIconClasses(shouldDisable, 'text-blue-500')}`}
/>
```

#### Direct Usage with conditionalDisabled:

```typescript
<div className={`base-class ${conditionalDisabled(isDisabled, 'input', 'enabled-classes')}`}>
```

### Available Types

- `input` - For form inputs (text, select, textarea, etc.)
- `button` - For buttons and clickable elements
- `icon` - For icons and graphics
- `dropdown` - For dropdown menu options

### Benefits

- **Consistency**: Same disabled appearance across the entire application
- **Maintainability**: Single source of truth for disabled colors
- **Scalability**: Easy to update disabled styling globally
- **Type Safety**: TypeScript support with proper typing
- **Reusability**: Simple utility functions for common patterns
