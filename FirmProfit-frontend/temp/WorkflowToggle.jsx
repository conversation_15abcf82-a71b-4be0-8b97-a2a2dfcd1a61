import React, { useState } from 'react';

const WorkflowToggle = () => {
  const [showAllWorkflows, setShowAllWorkflows] = useState(true);
  
  const handleToggleChange = () => {
    setShowAllWorkflows(prev => !prev);
  };

  return (
    <div className="p-4 bg-white">
      <h2 className="text-lg font-medium mb-4">Workflow Toggle Example</h2>
      
      <div className="flex items-center justify-between bg-white p-4 rounded shadow">
        <div className="relative flex items-center">
          <button className="bg-white text-blue-500 font-medium outline outline-gray-200 px-4 py-3 rounded-lg flex items-center gap-2">
            Filter
            <div className="flex items-center justify-center w-5 h-5 bg-blue-500 text-white text-xs font-medium rounded-full">
              2
            </div>
          </button>
        </div>

        <div className="flex items-center">
          <span className="mr-2 text-gray-600 text-sm">All workflows</span>
          <div 
            className={`w-10 h-5 ${showAllWorkflows ? 'bg-blue-500' : 'bg-gray-400'} rounded-full p-0.5 flex items-center cursor-pointer transition-colors duration-300`}
            onClick={handleToggleChange}
          >
            <div 
              className="bg-white w-4 h-4 rounded-full shadow-md transition-transform duration-300"
              style={{ transform: showAllWorkflows ? 'translateX(20px)' : 'translateX(0)' }}
            ></div>
          </div>
        </div>
      </div>
      
      <div className="mt-4 p-4 border rounded">
        <p>Toggle State: {showAllWorkflows ? 'Showing All Workflows' : 'Showing Initial Workflow Only'}</p>
      </div>
    </div>
  );
};

export default WorkflowToggle; 