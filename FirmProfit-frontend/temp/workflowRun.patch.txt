Here are the key changes needed to implement the "All Workflows" toggle functionality:

1. Remove ContactFormProps interface and update function declaration:
```diff
- interface ContactFormProps {
-   initialTaskId?: string;
- }

// Simple resizing handle component
const ResizeHandle = ({
  onMouseDown,
  orientation = 'vertical',
}: {
  onMouseDown: (e: React.MouseEvent) => void;
  orientation?: 'vertical' | 'horizontal';
}) => {
  // ...
};

- export default function ContactForm({ initialTaskId }: ContactFormProps) {
+ export default function ContactForm() {
  const router = useRouter();
  const { work_flow_id, taskId } = router.query;
  // ...
```

2. Add state for showing all workflows and store initial values:
```diff
  // New state for all workflows
  const [workflowData, setWorkflowData] = useState<WorkflowData | null>(null);
  const [activeWorkflowContext, setActiveWorkflowContext] = useState<WorkflowContext | null>(null);
  const [childWorkflows, setChildWorkflows] = useState<{ [key: string]: Workflow[] }>({});

+ // Add state for showing all workflows toggle
+ const [showAllWorkflows, setShowAllWorkflows] = useState(true);
+ 
+ // Store initial task and workflow IDs
+ const [initialTaskIdState, setInitialTaskIdState] = useState<string | null>(null);
+ const [initialWorkflowId, setInitialWorkflowId] = useState<string | null>(null);
+ const [initialActiveContext, setInitialActiveContext] = useState<WorkflowContext | null>(null);
```

3. Update the fetchWorkflowData function to store initial values:
```diff
      // Check if we have a task ID in the URL that might be in a child workflow
      const urlTaskId = router.query.taskId as string;
      let foundInChildWorkflow = false;

      if (urlTaskId) {
+       // Save initial taskId when first loading
+       if (!initialTaskIdState) {
+         setInitialTaskIdState(urlTaskId);
+         setInitialWorkflowId(getWorkflowIdFromUrl());
+       }
+       
        // First check if the task is in any of the child workflows
        for (const key in childWorkflowsObj) {
          const childWorkflows = childWorkflowsObj[key];
          for (let i = 0; i < childWorkflows.length; i++) {
            const childWorkflow = childWorkflows[i];
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            const childTask = childWorkflow.tasks.find((t: any) => t.id === urlTaskId);
            if (childTask) {
              // Set the child workflow as active
-             setActiveWorkflowContext({
+             const context = {
                workflow: childWorkflow,
                type: key,
                index: i,
              });
+             
+             setActiveWorkflowContext(context);
+             
+             // Store initial context for toggle functionality
+             if (!initialActiveContext) {
+               setInitialActiveContext(context);
+             }
```

4. Similarly for the main workflow:
```diff
      // If we didn't find the task in a child workflow, use the main workflow
      if (!foundInChildWorkflow) {
        // Set the main workflow as active by default
        if (allWorkflowData.workflows.length > 0) {
-         setSelectedWorkflow(allWorkflowData.workflows[0]);
-         setActiveWorkflowContext({
+         const context = {
            workflow: allWorkflowData.workflows[0],
            type: 'main',
            index: 0,
          });
+         
+         setSelectedWorkflow(allWorkflowData.workflows[0]);
+         setActiveWorkflowContext(context);
+         
+         // Store initial context for toggle functionality
+         if (!initialActiveContext) {
+           setInitialActiveContext(context);
+         }
        }
      }
```

5. Add a toggle handler function:
```diff
  // New function to toggle section expansion
  const toggleSectionExpansion = (sectionKey: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionKey]: !prev[sectionKey],
    }));
  };

+ // Add the toggle handler function
+ const handleToggleChange = () => {
+   setShowAllWorkflows(prev => !prev);
+   
+   if (showAllWorkflows) {
+     // Switching to show only initial workflow
+     if (initialActiveContext && initialTaskIdState) {
+       // Restore initial context and task
+       setActiveWorkflowContext(initialActiveContext);
+       setSelectedWorkflow(initialActiveContext.workflow);
+       
+       // Find the task from initial taskId
+       const task = initialActiveContext.workflow.tasks.find(t => t.id === initialTaskIdState);
+       if (task) {
+         selectTask(task);
+       }
+     }
+   } else {
+     // Switching back to all workflows - fetch fresh data
+     fetchWorkflowData();
+   }
+ };
```

6. Update the toggle switch UI:
```diff
            <div className="flex items-center">
              <span className="mr-2 text-gray-600 text-sm">All workflows</span>
-             <div className="w-10 h-5 bg-gray-400 rounded-full p-0.5 flex items-center">
-               <div className="bg-white w-4 h-4 rounded-full ml-auto shadow-md"></div>
+             <div 
+               className={`w-10 h-5 ${showAllWorkflows ? 'bg-[#3F73F6]' : 'bg-gray-400'} rounded-full p-0.5 flex items-center cursor-pointer transition-colors duration-300`}
+               onClick={handleToggleChange}
+             >
+               <div 
+                 className="bg-white w-4 h-4 rounded-full shadow-md transition-transform duration-300"
+                 style={{ transform: showAllWorkflows ? 'translateX(20px)' : 'translateX(0)' }}
+               ></div>
              </div>
            </div>
```

7. Filter main workflow tasks when toggle is off:
```diff
                      <>
                        {workflowData.workflows[0].tasks
-                         .filter(task => task.task_visible_status !== 'DRAFT')
+                         .filter(task => {
+                           // When showAllWorkflows is false, only show tasks from the initial context
+                           if (!showAllWorkflows && initialActiveContext) {
+                             return initialActiveContext.type === 'main';
+                           }
+                           return task.task_visible_status !== 'DRAFT';
+                         })
                          .map((task, idx) => {
```

8. Filter child workflows when toggle is off:
```diff
                {Object.entries(childWorkflows).map(([key, workflows]) =>
                  workflows.map((childWorkflow, childIndex) => {
+                   // If showing all is false, only show child workflow if it's part of the initial context
+                   if (!showAllWorkflows && initialActiveContext && initialActiveContext.type !== key) {
+                     return null;
+                   }
+                   
                    console.log('🚀 ~ workflows.map ~ childWorkflow:', childWorkflow);
``` 